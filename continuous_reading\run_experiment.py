#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
连续文本阅读实验启动脚本
"""

import os
import sys
from datetime import datetime

# 添加当前目录到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, current_dir)

# 导入实验模块
try:
    from continuous_reading_experiment import ContinuousReadingExperiment
    from continuous_reading_config import validate_config
    MODULES_AVAILABLE = True
except ImportError as e:
    print(f"模块导入失败: {e}")
    MODULES_AVAILABLE = False

def check_dependencies():
    """检查依赖项"""
    print("检查依赖项...")
    
    if not MODULES_AVAILABLE:
        print("✗ 实验模块不可用")
        return False
    
    # 检查配置
    config_result = validate_config()
    if not config_result['valid']:
        print("✗ 配置验证失败:")
        for error in config_result['errors']:
            print(f"  - {error}")
        return False
    
    if config_result['warnings']:
        print("⚠ 配置警告:")
        for warning in config_result['warnings']:
            print(f"  - {warning}")
    
    print("✓ 依赖项检查通过")
    return True

def get_participant_info():
    """获取被试信息"""
    print("\n=== 被试信息 ===")
    
    try:
        # 被试ID
        participant_id = input("请输入被试ID: ").strip()
        if not participant_id:
            participant_id = f"test_{datetime.now().strftime('%m%d_%H%M')}"
            print(f"使用默认ID: {participant_id}")
        
        # 实验设置
        print("\n实验设置:")
        use_eyelink_input = input("是否使用EyeLink眼动仪? (y/n, 默认y): ").strip().lower()
        use_eyelink = use_eyelink_input != 'n'
        
        fullscreen_input = input("是否全屏显示? (y/n, 默认y): ").strip().lower()
        fullscreen = fullscreen_input != 'n'
        
        return {
            'participant_id': participant_id,
            'use_eyelink': use_eyelink,
            'fullscreen': fullscreen
        }
        
    except KeyboardInterrupt:
        print("\n用户取消输入")
        return None
    except Exception as e:
        print(f"获取被试信息时出错: {e}")
        return None

def run_experiment(participant_info):
    """运行实验"""
    try:
        print(f"\n准备为被试 {participant_info['participant_id']} 启动实验...")
        
        # 创建实验对象
        experiment = ContinuousReadingExperiment(
            participant_id=participant_info['participant_id'],
            use_eyelink=participant_info['use_eyelink'],
            fullscreen=participant_info['fullscreen']
        )
        
        # 显示实验信息
        print(f"\n实验信息:")
        print(f"被试ID: {participant_info['participant_id']}")
        print(f"使用EyeLink: {'是' if participant_info['use_eyelink'] else '否'}")
        print(f"全屏显示: {'是' if participant_info['fullscreen'] else '否'}")
        print(f"数据保存目录: {experiment.data_dir}")
        
        # 确认开始
        input("\n按回车键开始实验...")
        
        # 运行实验
        success = experiment.run_experiment()
        
        if success:
            print("\n✓ 实验成功完成！")
            return True
        else:
            print("\n✗ 实验未能完成")
            return False
            
    except Exception as e:
        print(f"运行实验时出错: {e}")
        import traceback
        traceback.print_exc()
        return False
    finally:
        # 清理资源
        if 'experiment' in locals():
            experiment.cleanup()

def main():
    """主函数"""
    print("连续文本阅读实验启动器")
    print("=" * 50)
    
    try:
        # 检查依赖
        if not check_dependencies():
            print("\n依赖检查失败，无法启动实验")
            return
        
        # 获取被试信息
        participant_info = get_participant_info()
        if not participant_info:
            print("未能获取被试信息，退出程序")
            return
        
        # 运行实验
        success = run_experiment(participant_info)
        
        if success:
            print("\n实验程序正常结束")
        else:
            print("\n实验程序异常结束")
    
    except Exception as e:
        print(f"\n程序运行时出现未处理的错误: {e}")
        import traceback
        traceback.print_exc()
    
    finally:
        # 等待用户确认
        input("\n按回车键退出程序...")

if __name__ == "__main__":
    main()
