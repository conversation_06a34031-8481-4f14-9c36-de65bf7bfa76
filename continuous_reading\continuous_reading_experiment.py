#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
连续文本阅读实验主程序
整合所有模块，实现完整的连续阅读实验流程
"""

import os
import sys
import time
import random
import json
import asyncio
import edge_tts
from datetime import datetime
from typing import Dict, List, Optional, Tuple, Any

# 添加项目根目录到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
root_dir = os.path.dirname(current_dir)
sys.path.insert(0, root_dir)

# 导入自定义模块
from text_materials import TextMaterials
from continuous_reading_config import (
    BASELINE_CONFIG, TIMING_CONFIG, RATING_CONFIG, DISPLAY_CONFIG,
    EYELINK_CONFIG, CAMERA_CONFIG, DATA_CONFIG, EXPERIMENT_CONFIG, DEBUG_CONFIG,
    KEY_CONFIG, MESSAGE_CONFIG, RATING_ORDER, LISTENING_CONFIG
)

# 导入主项目模块
try:
    from eyelink_manager import EyeLinkManager
    from experiment_display import ExperimentDisplay
    from data_manager import DataManager
    MODULES_AVAILABLE = True
except ImportError as e:
    print(f"模块导入失败: {e}")
    MODULES_AVAILABLE = False

class ContinuousReadingExperiment:
    """连续文本阅读实验主控制类"""
    
    def __init__(self, participant_id: str, use_eyelink: bool = True, fullscreen: bool = True):
        """
        初始化实验
        
        Args:
            participant_id: 被试ID
            use_eyelink: 是否使用EyeLink
            fullscreen: 是否全屏显示
        """
        self.participant_id = participant_id
        self.use_eyelink = use_eyelink and EYELINK_CONFIG['use_eyelink']
        self.fullscreen = fullscreen
        self.screen_size = DISPLAY_CONFIG['screen_size']
        
        # 实验组件
        self.materials: Optional[Any] = None
        self.display: Optional[Any] = None
        self.eyelink: Optional[Any] = None
        self.data_manager: Optional[Any] = None
        
        # 实验数据
        self.data_dir: str = "continuous_reading"  # 明确类型注解
        self.current_sentence = None
        self.current_sentence_id = 0
        self.ratings_data = []
        self.experiment_start_time: float = 0.0  # 明确类型注解

        # 创建数据目录
        self._create_data_directory()

        # 听力模式相关
        self.listening_mode = EXPERIMENT_CONFIG.get('listening_mode', False)

    def _create_custom_data_manager(self):
        """创建自定义数据管理器，使用已有目录"""
        # 创建一个简化的数据管理器类
        class CustomDataManager:
            def __init__(self, data_dir, participant_id):
                self.data_dir = data_dir
                self.participant_id = participant_id
                self.log_file = os.path.join(data_dir, "experiment_log.txt")
                self._init_log()

            def _init_log(self):
                """初始化日志文件"""
                try:
                    with open(self.log_file, 'w', encoding='utf-8') as f:
                        f.write(f"连续阅读实验日志 - {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
                        f.write("=" * 50 + "\n\n")
                except Exception as e:
                    print(f"初始化日志失败: {e}")

            def log_event(self, event: str, details: str = ""):
                """记录事件到日志"""
                timestamp = datetime.now().strftime('%H:%M:%S.%f')[:-3]
                log_entry = f"[{timestamp}] {event}"
                if details:
                    log_entry += f" - {details}"
                log_entry += "\n"

                try:
                    with open(self.log_file, 'a', encoding='utf-8') as f:
                        f.write(log_entry)
                except Exception as e:
                    print(f"写入日志失败: {e}")

            def save_rating_data(self, rating_data: dict) -> bool:
                """保存评分数据"""
                try:
                    # 创建评分数据文件名
                    if 'sentence_group_ids' in rating_data:
                        # 多句子模式
                        group_ids = rating_data['sentence_group_ids']
                        group_str = f"{group_ids[0]}_{group_ids[-1]}"
                    else:
                        # 单句子模式
                        group_str = str(rating_data.get('sentence_id', 'unknown'))

                    rating_file = os.path.join(self.data_dir, f"rating_group_{group_str}.json")

                    # 保存JSON格式
                    with open(rating_file, 'w', encoding='utf-8') as f:
                        json.dump(rating_data, f, ensure_ascii=False, indent=2)

                    # 记录日志
                    self.log_event("RATING_SAVED", f"Rating data saved to {rating_file}")
                    return True

                except Exception as e:
                    self.log_event("ERROR", f"Failed to save rating data: {e}")
                    return False

        return CustomDataManager(self.data_dir, self.participant_id)

    async def _play_text_tts(self, text: str) -> bool:
        """
        使用TTS播放文本

        Args:
            text: 要播放的文本

        Returns:
            播放是否成功
        """
        try:
            # 添加前缀防止声音被吞掉
            full_text = LISTENING_CONFIG['prefix_text'] + text

            # 创建TTS对象
            tts = edge_tts.Communicate(
                full_text,
                voice=LISTENING_CONFIG['voice'],
                rate=LISTENING_CONFIG['rate']
            )

            # 启动mpv播放器
            proc = await asyncio.create_subprocess_exec(
                "mpv", "--no-video", "--really-quiet", "--", "-",
                stdin=asyncio.subprocess.PIPE,
                stdout=asyncio.subprocess.DEVNULL,
                stderr=asyncio.subprocess.DEVNULL,
            )

            try:
                # 流式发送音频数据
                async for chunk in tts.stream():
                    if chunk["type"] == "audio":
                        proc.stdin.write(chunk["data"])
                        await proc.stdin.drain()

                # 发送EOF，等待播放完成
                proc.stdin.close()
                rc = await proc.wait()
                return rc == 0

            finally:
                if proc.returncode is None:
                    proc.kill()

        except Exception as e:
            print(f"TTS播放失败: {e}")
            return False

    def _show_listening_dot(self):
        """
        在听力模式下显示白点
        """
        try:
            # 显示白点在屏幕中央
            if hasattr(self.display, 'win'):
                # 使用PsychoPy显示白点
                from psychopy import visual
                dot = visual.Circle(
                    self.display.win,
                    radius=LISTENING_CONFIG['dot_size'],
                    fillColor=LISTENING_CONFIG['dot_color'],
                    lineColor=LISTENING_CONFIG['dot_color']
                )
                dot.draw()
                self.display.win.flip()
            else:
                print("显示白点（听力模式）")
        except Exception as e:
            print(f"显示白点失败: {e}")

    def _hide_listening_dot(self):
        """
        隐藏听力模式的白点
        """
        try:
            if hasattr(self.display, 'win'):
                # 清空屏幕
                self.display.win.flip()
            else:
                print("隐藏白点（听力模式）")
        except Exception as e:
            print(f"隐藏白点失败: {e}")

    def _present_sentences_listening(self, text: str) -> bool:
        """
        听力模式下呈现句子

        Args:
            text: 要播放的文本

        Returns:
            是否继续实验
        """
        try:
            print(f"听力模式播放: {text[:50]}...")

            # 显示白点
            self._show_listening_dot()

            # 播放音频
            success = asyncio.run(self._play_text_tts(text))

            # 隐藏白点
            self._hide_listening_dot()

            if not success:
                print("音频播放失败")
                return False

            # 如果配置为自动进入评分，直接返回True
            if LISTENING_CONFIG.get('auto_advance_to_rating', True):
                return True

            # 否则等待用户按键继续
            if hasattr(self.display, 'wait_for_key'):
                return self.display.wait_for_key()
            else:
                input("按回车继续...")
                return True

        except Exception as e:
            print(f"听力模式播放失败: {e}")
            return False

    def _create_data_directory(self):
        """创建数据保存目录"""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        data_dir_name = f"{timestamp}_{self.participant_id}_continuous_reading"
        self.data_dir = os.path.join(
            root_dir,
            DATA_CONFIG['base_data_dir'],
            data_dir_name
        )

        try:
            os.makedirs(self.data_dir, exist_ok=True)
            print(f"数据将保存到: {self.data_dir}")
        except Exception as e:
            print(f"创建数据目录失败: {e}")
            # 确保data_dir不为None
            fallback_dir = os.path.join(root_dir, DATA_CONFIG['base_data_dir'])
            os.makedirs(fallback_dir, exist_ok=True)
            self.data_dir = fallback_dir
    
    def initialize_components(self) -> bool:
        """
        初始化所有实验组件
        
        Returns:
            初始化是否成功
        """
        print("初始化实验组件...")
        
        # 初始化材料管理器
        try:
            self.materials = TextMaterials()
            if not self.materials.sentences:
                print("错误：无法加载文本材料")
                return False
            print(f"✓ 材料加载成功，共 {len(self.materials.sentences)} 个句子")
        except Exception as e:
            print(f"✗ 材料加载失败: {e}")
            return False
        
        # 初始化显示
        try:
            self.display = ExperimentDisplay(fullscreen=self.fullscreen)
            print("✓ 显示组件初始化成功")
        except Exception as e:
            print(f"✗ 显示组件初始化失败: {e}")
            return False
        
        # 初始化EyeLink
        if self.use_eyelink:
            try:
                display_window = self.display.win if hasattr(self.display, 'win') else None
                self.eyelink = EyeLinkManager(
                    participant_id=self.participant_id,
                    data_dir=self.data_dir,
                    screen_size=self.screen_size,
                    dummy_mode=EYELINK_CONFIG['dummy_mode'],
                    display_window=display_window,
                    enable_camera=CAMERA_CONFIG['enable_camera'],
                    camera_index=CAMERA_CONFIG['camera_index']
                )
                if self.eyelink.connect():
                    print("✓ EyeLink连接成功")
                    if CAMERA_CONFIG['enable_camera']:
                        print("✓ 摄像头录像功能已启用")
                    if self.eyelink.setup_tracker():
                        print("✓ EyeLink配置成功")
                        if self.eyelink.setup_graphics_environment():
                            print("✓ EyeLink图形环境设置成功")
                    print("✓ EyeLink初始化成功")
                else:
                    print("警告：EyeLink连接失败，继续使用虚拟模式")
            except Exception as e:
                print(f"✗ EyeLink初始化失败: {e}")
                if not DEBUG_CONFIG['test_mode']:
                    return False
                print("  在测试模式下继续运行，不使用EyeLink")
                self.use_eyelink = False

        # 初始化数据管理器
        try:
            # 创建一个自定义的数据管理器，直接使用已创建的目录
            self.data_manager = self._create_custom_data_manager()
            print("✓ 数据管理器初始化成功")
        except Exception as e:
            print(f"✗ 数据管理器初始化失败: {e}")
            return False
        
        return True
    
    def run_calibration(self) -> bool:
        """
        运行EyeLink校准
        
        Returns:
            校准是否成功
        """
        if not self.use_eyelink:
            print("未使用EyeLink，跳过校准")
            return True
        
        if DEBUG_CONFIG['skip_calibration']:
            print("调试模式：跳过校准")
            return True
        
        print("\n开始EyeLink校准...")
        
        try:
            self.eyelink.log_calibration_start()
            calibration_success = self.eyelink.calibrate()
            self.eyelink.log_calibration_end(calibration_success)

            if calibration_success:
                print("✓ EyeLink校准成功")
                return True
            else:
                print("✗ EyeLink校准失败")
                return False

        except Exception as e:
            print(f"校准过程出错: {e}")
            return False
    
    def run_experiment(self) -> bool:
        """
        运行完整实验
        
        Returns:
            实验是否成功完成
        """
        print(f"\n开始连续文本阅读实验 - 被试ID: {self.participant_id}")
        print("=" * 60)
        
        # 初始化组件
        if not self.initialize_components():
            print("实验初始化失败")
            return False
        
        # 获取句子总数
        total_sentences = self.materials.get_total_sentences()
        if total_sentences == 0:
            print("错误：没有可用的句子")
            return False
        
        # 确定实验句子数量
        max_sentences = EXPERIMENT_CONFIG['max_sentences']
        if max_sentences is None:
            max_sentences = total_sentences
        elif DEBUG_CONFIG['test_mode'] and DEBUG_CONFIG['max_test_sentences'] > 0:
            max_sentences = min(max_sentences, DEBUG_CONFIG['max_test_sentences'])
        
        max_sentences = min(max_sentences, total_sentences)
        print(f"将显示 {max_sentences} 个句子（共 {total_sentences} 个）")
        
        # 保存实验设置
        self._save_experiment_settings(max_sentences)
        
        # EyeLink校准
        if not self.run_calibration():
            print("EyeLink校准失败")
            return False
        
        # 记录实验开始
        self.experiment_start_time = time.time()
        if self.eyelink:
            self.eyelink.log_experiment_start()
            # 不在这里开始眼动记录，改为每个trial单独记录

        # 显示实验说明
        if not self._show_instructions():
            print("用户跳过了实验说明")
            return False
        
        # 延迟开始
        if TIMING_CONFIG['experiment_start_delay'] > 0:
            self.display.show_question("实验即将开始...", duration=TIMING_CONFIG['experiment_start_delay'])
        
        # 运行实验主循环
        try:
            success = self._run_experiment_loop(max_sentences)
            
            if success:
                # 显示实验结束
                self.display.show_question("实验结束，谢谢参与！", duration=TIMING_CONFIG['experiment_end_delay'])
            else:
                print("实验被中断")
                
            if self.eyelink:
                self.eyelink.log_experiment_end()
                # 不在这里停止眼动记录，每个trial单独管理

            # 保存数据
            self._save_experiment_data()

            return True
            
                
        except Exception as e:
            print(f"实验运行出错: {e}")
            import traceback
            traceback.print_exc()
            return False
    
    def _show_instructions(self) -> bool:
        """
        显示实验说明
        
        Returns:
            用户是否继续实验
        """
        instructions = """
连续文本阅读实验

在本实验中，您将阅读一段连续的文本，每次显示一个句子。

阅读完每个句子后，您需要对该句子进行评分：
- 好奇心：您有多想继续阅读下去
- 有趣度：这个句子有多有趣
- 理解程度：您对这个句子的理解程度
- 费劲程度：阅读这个句子的费劲程度

请按空格键继续阅读下一个句子。
实验过程中请尽量保持头部稳定。

按回车键开始实验...
"""
        return self.display.show_question(instructions, duration=None)
    
    def _run_experiment_loop(self, max_sentences: int) -> bool:
        """
        运行实验主循环

        Args:
            max_sentences: 最大句子数量

        Returns:
            实验是否成功完成
        """
        # 从配置的起始句子开始
        start_sentence_id = max(1, EXPERIMENT_CONFIG['start_from_sentence'])
        sentences_per_display = EXPERIMENT_CONFIG['sentences_per_display']

        # 主循环 - 按显示组进行迭代
        sentence_id = start_sentence_id
        display_count = 0
        trial_number = 1

        while sentence_id <= start_sentence_id + max_sentences - 1:
            # 检查是否需要显示基线
            if (BASELINE_CONFIG['baseline_interval'] > 0 and
                display_count % BASELINE_CONFIG['baseline_interval'] == 0 and display_count > 0):
                if not self._show_baseline():
                    print("用户在基线阶段退出")
                    return False

            # 获取当前显示组的句子
            if EXPERIMENT_CONFIG['use_character_count_mode']:
                # 按字数控制模式
                target_char_count = EXPERIMENT_CONFIG['target_character_count']
                current_sentences = self.materials.get_sentences_by_character_count(sentence_id, target_char_count)
                if not current_sentences:
                    print(f"无法获取从句子 {sentence_id} 开始的字数为 {target_char_count} 的句子，实验结束")
                    break
                # 计算实际字数
                actual_char_count = sum(s['length'] for s in current_sentences)
                print(f"目标字数: {target_char_count}, 实际字数: {actual_char_count}, 句子数: {len(current_sentences)}")
                # 设置当前句子为第一个句子（用于兼容性）
                self.current_sentence = current_sentences[0]
                self.current_sentence_id = sentence_id
            elif sentences_per_display == 1:
                # 单句子显示模式
                self.current_sentence = self.materials.get_sentence(sentence_id)
                if not self.current_sentence:
                    print(f"无法获取句子 {sentence_id}，实验结束")
                    break
                self.current_sentence_id = sentence_id
                current_sentences = [self.current_sentence]
            else:
                # 多句子显示模式
                current_sentences = self.materials.get_multiple_sentences(sentence_id, sentences_per_display)
                if not current_sentences:
                    print(f"无法获取从句子 {sentence_id} 开始的 {sentences_per_display} 个句子，实验结束")
                    break
                # 设置当前句子为第一个句子（用于兼容性）
                self.current_sentence = current_sentences[0]
                self.current_sentence_id = sentence_id

            # 开始当前trial的眼动记录
            print(f"开始Trial {trial_number}...")
            if self.eyelink:
                if not self.eyelink.start_recording(trial_number):
                    print(f"警告：Trial {trial_number} 眼动记录启动失败")
                # 发送trial开始消息
                self.eyelink.send_message(f"TRIAL_{trial_number}_START")

            # 显示句子（组）
            if not self._present_sentences(current_sentences):
                print("用户在句子显示阶段退出")
                # 停止当前trial的记录
                if self.eyelink:
                    self.eyelink.send_message(f"TRIAL_{trial_number}_ABORTED")
                    self.eyelink.stop_recording()
                return False

            # 收集评分（多句子显示时只进行一轮评分）
            if EXPERIMENT_CONFIG['use_character_count_mode'] or sentences_per_display > 1:
                # 字数控制模式或多句子模式：只进行一轮评分，针对整个句子组
                if not self._collect_ratings_for_group(current_sentences):
                    print("用户在评分阶段退出")
                    # 停止当前trial的记录
                    if self.eyelink:
                        self.eyelink.send_message(f"TRIAL_{trial_number}_ABORTED")
                        self.eyelink.stop_recording()
                    return False
            else:
                # 单句子模式：为当前句子收集评分
                if not self._collect_ratings():
                    print("用户在评分阶段退出")
                    # 停止当前trial的记录
                    if self.eyelink:
                        self.eyelink.send_message(f"TRIAL_{trial_number}_ABORTED")
                        self.eyelink.stop_recording()
                    return False

            # 停止当前trial的眼动记录
            if self.eyelink:
                self.eyelink.send_message(f"TRIAL_{trial_number}_END")
                self.eyelink.stop_recording()
            print(f"Trial {trial_number} 完成")

            # 更新句子ID和trial编号
            if EXPERIMENT_CONFIG['use_character_count_mode']:
                # 字数控制模式：跳过当前显示的所有句子
                sentence_id += len(current_sentences)
            else:
                # 固定句子数模式：按配置的句子数跳过
                sentence_id += sentences_per_display
            display_count += 1
            trial_number += 1

            # 句子间间隔
            if TIMING_CONFIG['inter_sentence_interval'] > 0:
                time.sleep(TIMING_CONFIG['inter_sentence_interval'])

            # 显示进度
            if EXPERIMENT_CONFIG['show_progress']:
                processed_sentences = min(sentence_id - start_sentence_id, max_sentences)
                progress = processed_sentences / max_sentences * 100
                print(f"进度: {progress:.1f}% ({processed_sentences}/{max_sentences})")

        return True

    def _collect_ratings_for_group(self, sentences: List[Dict]) -> bool:
        """
        为句子组收集评分（多句子显示模式）

        Args:
            sentences: 句子组列表

        Returns:
            是否继续实验
        """
        if not sentences:
            return False

        # 使用第一个句子作为代表进行评分
        representative_sentence = sentences[0]
        sentence_ids = [s['id'] for s in sentences]

        print(f"为句子组 {sentence_ids} 收集评分...")

        sentence_ratings = {
            'sentence_group_ids': sentence_ids,  # 句子组ID列表
            'representative_sentence_id': representative_sentence['id'],  # 代表句子ID
            'sentence_count': len(sentences),  # 句子数量
            'timestamp': time.time(),
            'ratings': {}
        }

        # 保存句子内容（如果配置启用）
        if DATA_CONFIG['include_sentence_text']:
            max_len = DATA_CONFIG['max_sentence_text_length']
            # 保存所有句子的内容
            sentence_texts = []
            for sentence in sentences:
                text = sentence['text']
                if len(text) > max_len:
                    text = text[:max_len] + '...'
                sentence_texts.append(text)
            sentence_ratings['sentence_texts'] = sentence_texts

            # 也保存组合文本
            combined_text = EXPERIMENT_CONFIG['multi_sentence_separator'].join([s['text'] for s in sentences])
            if len(combined_text) > max_len * 2:  # 对组合文本使用更大的限制
                combined_text = combined_text[:max_len * 2] + '...'
            sentence_ratings['combined_text'] = combined_text

        # 收集各项评分
        for rating_type in RATING_ORDER:
            rating_config = RATING_CONFIG[rating_type]

            # 记录评分开始
            if self.eyelink:
                msg_prefix = f"{MESSAGE_CONFIG['rating_prefix']}_{rating_type.upper()}"
                if MESSAGE_CONFIG['include_sentence_number']:
                    sentence_ids_str = "_".join(map(str, sentence_ids))
                    msg_prefix += f"_GROUP_{sentence_ids_str}"
                self.eyelink.send_message(f"{msg_prefix}_START")

            # 显示评分问题
            full_question = f"{rating_config['question']}\n\n{rating_config['instruction']}"
            labels_list = [rating_config['labels'][i] for i in range(rating_config['scale'][0], rating_config['scale'][1] + 1)]

            rating_value = self.display.get_rating(
                question=full_question,
                scale_range=rating_config['scale'],
                labels=labels_list
            )

            # 记录评分结束
            if self.eyelink:
                if MESSAGE_CONFIG['include_rating_value']:
                    self.eyelink.send_message(f"{msg_prefix}_END_VALUE_{rating_value}")
                else:
                    self.eyelink.send_message(f"{msg_prefix}_END")

            # 检查用户是否退出
            if rating_value is None:
                return False

            sentence_ratings['ratings'][rating_type] = rating_value
            print(f"  {rating_type}: {rating_value}")

            # 评分间暂停
            if EXPERIMENT_CONFIG['pause_between_ratings'] > 0:
                time.sleep(EXPERIMENT_CONFIG['pause_between_ratings'])

        # 保存评分数据
        self.ratings_data.append(sentence_ratings)

        # 保存到数据管理器
        if self.data_manager:
            self.data_manager.save_rating_data(sentence_ratings)

        return True

    def _show_baseline(self) -> bool:
        """
        显示基线十字星
        
        Returns:
            是否继续实验
        """
        print("显示基线十字星...")
        
        # 记录基线开始
        if self.eyelink:
            self.eyelink.send_message(f"{MESSAGE_CONFIG['baseline_prefix']}_START")
        
        # 显示十字星
        result = self.display.show_fixation_cross(
            duration=BASELINE_CONFIG['baseline_duration']
        )
        
        # 记录基线结束
        if self.eyelink:
            self.eyelink.send_message(f"{MESSAGE_CONFIG['baseline_prefix']}_END")
        
        return result
    
    def _present_sentence(self) -> bool:
        """
        呈现当前句子

        Returns:
            是否继续实验
        """
        if not self.current_sentence:
            return False

        sentence_text = self.current_sentence['text']
        sentence_id = self.current_sentence['id']

        print(f"显示句子 {sentence_id}: {sentence_text[:50]}...")

        # 记录句子显示开始
        if self.eyelink:
            msg_prefix = f"{MESSAGE_CONFIG['sentence_prefix']}"
            if MESSAGE_CONFIG['include_sentence_number']:
                msg_prefix += f"_{sentence_id}"
            self.eyelink.send_message(f"{msg_prefix}_START")

        # 根据模式选择显示方式
        if self.listening_mode:
            # 听力模式：显示白点并播放音频
            result = self._present_sentences_listening(sentence_text)
        else:
            # 正常模式：显示文字
            result = self.display.show_question(
                question=sentence_text,
                duration=TIMING_CONFIG['sentence_display']
            )

        # 记录句子显示结束
        if self.eyelink:
            self.eyelink.send_message(f"{msg_prefix}_END")

        return result

    def _present_sentences(self, sentences: List[Dict]) -> bool:
        """
        显示多个句子

        Args:
            sentences: 要显示的句子列表

        Returns:
            是否继续实验（False表示用户退出）
        """
        if not sentences:
            return False

        # 如果只有一个句子，使用原来的方法
        if len(sentences) == 1:
            self.current_sentence = sentences[0]
            return self._present_sentence()

        # 多句子显示
        sentence_ids = [s['id'] for s in sentences]
        combined_text = EXPERIMENT_CONFIG['multi_sentence_separator'].join([s['text'] for s in sentences])

        print(f"显示句子组 {sentence_ids}: {combined_text[:100]}...")

        # 记录句子组显示开始
        if self.eyelink:
            msg_prefix = f"{MESSAGE_CONFIG['sentence_prefix']}_GROUP"
            sentence_ids_str = "_".join(map(str, sentence_ids))
            self.eyelink.send_message(f"{msg_prefix}_{sentence_ids_str}_START")

        # 根据模式选择显示方式
        if self.listening_mode:
            # 听力模式：显示白点并播放音频
            result = self._present_sentences_listening(combined_text)
        else:
            # 正常模式：显示文字
            result = self.display.show_question(
                question=combined_text,
                duration=TIMING_CONFIG['sentence_display']
            )

        # 记录句子组显示结束
        if self.eyelink:
            self.eyelink.send_message(f"{msg_prefix}_{sentence_ids_str}_END")

        return result
    
    def _collect_ratings(self) -> bool:
        """
        收集当前句子的评分
        
        Returns:
            是否继续实验
        """
        if not self.current_sentence:
            return False
        
        sentence_id = self.current_sentence['id']
        sentence_ratings = {
            'sentence_id': sentence_id,
            'timestamp': time.time() - self.experiment_start_time
        }
        
        # 对每种评分类型进行收集
        for rating_type in RATING_ORDER:
            rating_config = RATING_CONFIG[rating_type]
            
            # 如果在调试模式下模拟评分
            if DEBUG_CONFIG['debug_mode'] and DEBUG_CONFIG['simulate_ratings']:
                rating_value = random.randint(rating_config['scale'][0], rating_config['scale'][1])
                print(f"模拟 {rating_type} 评分: {rating_value}")
                sentence_ratings[rating_type] = rating_value
                continue
            
            # 记录评分开始
            if self.eyelink:
                msg_prefix = f"{MESSAGE_CONFIG['rating_prefix']}_{rating_type}"
                self.eyelink.send_message(f"{msg_prefix}_START")
            
            # 获取评分
            # 将instruction合并到question中
            full_question = f"{rating_config['question']}\n\n{rating_config['instruction']}"
            labels_list = [rating_config['labels'][i] for i in range(rating_config['scale'][0], rating_config['scale'][1] + 1)]

            rating_value = self.display.get_rating(
                question=full_question,
                scale_range=rating_config['scale'],
                labels=labels_list
            )
            
            # 记录评分结束
            if self.eyelink:
                self.eyelink.send_message(f"{msg_prefix}_END")
                if rating_value is not None and MESSAGE_CONFIG['include_rating_value']:
                    self.eyelink.send_message(f"{msg_prefix}_VALUE_{rating_value}")
            
            # 检查是否取消
            if rating_value is None:
                if rating_config['required']:
                    print(f"用户跳过了必需的评分 {rating_type}")
                    return False
                else:
                    print(f"用户跳过了可选评分 {rating_type}")
            
            # 保存评分
            sentence_ratings[rating_type] = rating_value
            
            # 评分间暂停
            if EXPERIMENT_CONFIG['pause_between_ratings'] > 0:
                time.sleep(EXPERIMENT_CONFIG['pause_between_ratings'])
        
        # 保存句子文本（可选）
        if DATA_CONFIG['include_sentence_text']:
            max_len = DATA_CONFIG['max_sentence_text_length']
            text = self.current_sentence['text']
            sentence_ratings['text'] = text[:max_len] + ('...' if len(text) > max_len else '')
        
        # 添加到评分数据
        self.ratings_data.append(sentence_ratings)
        
        return True
    
    def _save_experiment_settings(self, max_sentences: int):
        """保存实验设置"""
        settings = {
            'participant_id': self.participant_id,
            'timestamp': datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
            'use_eyelink': self.use_eyelink,
            'fullscreen': self.fullscreen,
            'max_sentences': max_sentences,
            'total_available_sentences': self.materials.get_total_sentences() if self.materials else 0,
            'text_file': self.materials.text_file if self.materials else '',
            'text_statistics': self.materials.get_text_statistics() if self.materials else {},
            'rating_types': list(RATING_CONFIG.keys()),
            'rating_order': RATING_ORDER,
            'baseline_interval': BASELINE_CONFIG['baseline_interval'],
            'use_character_count_mode': EXPERIMENT_CONFIG['use_character_count_mode'],
            'target_character_count': EXPERIMENT_CONFIG['target_character_count'],
            'sentences_per_display': EXPERIMENT_CONFIG['sentences_per_display']
        }
        
        try:
            settings_file = os.path.join(self.data_dir, "experiment_settings.json")
            with open(settings_file, 'w', encoding='utf-8') as f:
                json.dump(settings, f, ensure_ascii=False, indent=2)
            print(f"实验设置已保存到 {settings_file}")
        except Exception as e:
            print(f"保存实验设置失败: {e}")
    
    def _save_experiment_data(self):
        """保存实验数据"""
        try:
            # 保存评分数据
            ratings_file = os.path.join(self.data_dir, "ratings_data.json")
            with open(ratings_file, 'w', encoding='utf-8') as f:
                json.dump(self.ratings_data, f, ensure_ascii=False, indent=2)
            print(f"评分数据已保存到 {ratings_file}")

            # 保存句子ID到内容的映射
            # self._save_sentence_mapping()

            # 保存CSV格式（可选）
            if DATA_CONFIG['save_csv']:
                csv_file = os.path.join(self.data_dir, "ratings_data.csv")
                self._save_csv_data(csv_file)

            # 保存实验摘要（可选）
            if DATA_CONFIG['save_summary']:
                self._save_experiment_summary()

        except Exception as e:
            print(f"保存实验数据失败: {e}")

    def _save_sentence_mapping(self):
        """保存句子ID到内容的映射"""
        if not self.materials or not self.materials.sentences:
            return

        try:
            # 保存JSON格式的句子映射
            mapping_file = os.path.join(self.data_dir, "sentence_mapping.json")
            sentence_mapping = {}

            for sentence in self.materials.sentences:
                sentence_mapping[sentence['id']] = {
                    'text': sentence['text'],
                    'original_text': sentence['original_text'],
                    'length': sentence['length'],
                    'word_count': sentence['word_count'],
                    'delimiter': sentence['delimiter']
                }

            with open(mapping_file, 'w', encoding='utf-8') as f:
                json.dump(sentence_mapping, f, ensure_ascii=False, indent=2)
            print(f"句子映射已保存到 {mapping_file}")

            # 保存文本格式的句子映射（便于阅读）
            text_mapping_file = os.path.join(self.data_dir, "sentence_mapping.txt")
            with open(text_mapping_file, 'w', encoding='utf-8') as f:
                f.write("句子ID到内容的映射\n")
                f.write("=" * 50 + "\n\n")

                for sentence in self.materials.sentences:
                    f.write(f"句子 {sentence['id']:3d}: {sentence['text']}\n")
                    f.write(f"长度: {sentence['length']} 字符, {sentence['word_count']} 词\n\n")

            print(f"句子映射文本版已保存到 {text_mapping_file}")

        except Exception as e:
            print(f"保存句子映射失败: {e}")

    def _save_csv_data(self, csv_file: str):
        """保存CSV格式数据"""
        if not self.ratings_data:
            return
        
        try:
            import csv
            
            # 获取所有可能的列
            columns = ['sentence_id', 'timestamp']
            for rating_type in RATING_ORDER:
                columns.append(rating_type)
            if DATA_CONFIG['include_sentence_text']:
                columns.append('text')
            
            # 写入CSV
            with open(csv_file, 'w', encoding='utf-8', newline='') as f:
                writer = csv.DictWriter(f, fieldnames=columns)
                writer.writeheader()
                for rating in self.ratings_data:
                    # 只保留列表中的字段
                    row = {col: rating.get(col, '') for col in columns}
                    writer.writerow(row)
            
            print(f"CSV数据已保存到 {csv_file}")
            
        except Exception as e:
            print(f"保存CSV数据失败: {e}")
    
    def _save_experiment_summary(self):
        """保存实验摘要"""
        if not self.ratings_data:
            return
        
        try:
            # 计算评分统计
            rating_stats = {}
            for rating_type in RATING_ORDER:
                values = [r[rating_type] for r in self.ratings_data if rating_type in r and r[rating_type] is not None]
                if values:
                    rating_stats[rating_type] = {
                        'count': len(values),
                        'mean': sum(values) / len(values),
                        'min': min(values),
                        'max': max(values)
                    }
            
            # 创建摘要
            summary = {
                'participant_id': self.participant_id,
                'timestamp': datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
                'total_sentences_rated': len(self.ratings_data),
                'experiment_duration': time.time() - self.experiment_start_time,
                'rating_statistics': rating_stats
            }
            
            # 保存摘要
            summary_file = os.path.join(self.data_dir, "experiment_summary.json")
            with open(summary_file, 'w', encoding='utf-8') as f:
                json.dump(summary, f, ensure_ascii=False, indent=2)
            print(f"实验摘要已保存到 {summary_file}")
            
        except Exception as e:
            print(f"保存实验摘要失败: {e}")
    
    def cleanup(self):
        """清理资源"""
        print("清理资源...")

        # 保存已收集的数据（即使实验被中断）
        if hasattr(self, 'ratings_data') and self.ratings_data:
            try:
                self._save_experiment_data()
                print("✓ 已保存收集到的数据")
            except Exception as e:
                print(f"保存数据时出错: {e}")

        # 关闭EyeLink连接
        if self.eyelink:
            try:
                self.eyelink.close()
                print("✓ EyeLink连接已关闭")
            except Exception as e:
                print(f"关闭EyeLink连接时出错: {e}")

        # 关闭显示窗口
        if self.display:
            try:
                self.display.close()
                print("✓ 显示窗口已关闭")
            except Exception as e:
                print(f"关闭显示窗口时出错: {e}")

        print("资源清理完成")

def main():
    """主函数 - 运行实验"""
    print("连续文本阅读实验")
    print("=" * 40)
    
    # 获取被试信息
    participant_id = input("请输入被试ID: ").strip()
    if not participant_id:
        participant_id = f"test_{datetime.now().strftime('%H%M%S')}"
        print(f"使用默认ID: {participant_id}")
    
    # 实验设置
    use_eyelink = input("是否使用EyeLink? (y/n, 默认y): ").strip().lower() != 'n'
    fullscreen = input("是否全屏显示? (y/n, 默认y): ").strip().lower() != 'n'
    listening_mode = input("是否使用听力模式? (y/n, 默认n): ").strip().lower() == 'y'

    # 如果选择听力模式，更新配置
    if listening_mode:
        EXPERIMENT_CONFIG['listening_mode'] = True
        print("已启用听力模式：文本将通过语音播放，屏幕显示白点")
    
    # 创建实验对象
    experiment = ContinuousReadingExperiment(
        participant_id=participant_id,
        use_eyelink=use_eyelink,
        fullscreen=fullscreen
    )
    
    try:
        # 运行实验
        success = experiment.run_experiment()
        
        if success:
            print("\n✓ 实验成功完成！")
        else:
            print("\n✗ 实验未能完成")
        
    except KeyboardInterrupt:
        print("\n实验被用户中断")
    except Exception as e:
        print(f"\n实验出现错误: {e}")
        import traceback
        traceback.print_exc()
    finally:
        # 清理资源
        experiment.cleanup()

if __name__ == "__main__":
    main()
