{"cells": [{"cell_type": "code", "execution_count": 1, "id": "c450d85d", "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import numpy as np\n", "pd.set_option('display.precision', 10)\n", "pd.set_option('display.max_rows', None)\n", "pd.set_option('display.max_columns', None)"]}, {"cell_type": "code", "execution_count": 2, "id": "eaf1d399", "metadata": {"scrolled": true}, "outputs": [{"name": "stdout", "output_type": "stream", "text": [" Volume in drive C has no label.\n", " Volume Serial Number is 643E-E79E\n", "\n", " Directory of C:\\Users\\<USER>\\Desktop\\Semester 5, 2023\\Eye Tracking\\Project\\Lin<PERSON>uer et al. 2019\\study01-vr-tasks\\data\n", "\n", "03/30/2024  02:58 PM    <DIR>          .\n", "03/30/2024  02:58 PM    <DIR>          ..\n", "02/19/2024  04:49 PM    <DIR>          .ipynb_checkpoints\n", "02/28/2019  02:11 PM        35,678,600 p02-pupil_positions.csv\n", "04/16/2019  11:44 AM         9,776,283 p02-pupil_positions-trimmed.csv\n", "04/16/2019  12:26 PM         5,476,801 p02-pupil_positions-trimmed-02.csv\n", "03/01/2019  10:50 AM        33,904,844 p03-pupil_positions.csv\n", "03/01/2019  03:02 PM        33,297,744 p04-pupil_positions.csv\n", "03/01/2019  05:09 PM        35,198,290 p05-pupil_positions.csv\n", "03/01/2019  05:06 PM        35,319,741 p06-pupil_positions.csv\n", "03/30/2024  02:58 PM           168,772 VR Task.ipynb\n", "               8 File(s)    188,821,075 bytes\n", "               3 Dir(s)  1,317,534,265,344 bytes free\n"]}], "source": ["ls"]}, {"cell_type": "code", "execution_count": 3, "id": "da3b30e2", "metadata": {"scrolled": true}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>world_timestamp</th>\n", "      <th>world_index</th>\n", "      <th>eye_id</th>\n", "      <th>confidence</th>\n", "      <th>norm_pos_x</th>\n", "      <th>norm_pos_y</th>\n", "      <th>diameter</th>\n", "      <th>method</th>\n", "      <th>ellipse_center_x</th>\n", "      <th>ellipse_center_y</th>\n", "      <th>ellipse_axis_a</th>\n", "      <th>ellipse_axis_b</th>\n", "      <th>ellipse_angle</th>\n", "      <th>diameter_3d</th>\n", "      <th>model_confidence</th>\n", "      <th>model_id</th>\n", "      <th>sphere_center_x</th>\n", "      <th>sphere_center_y</th>\n", "      <th>sphere_center_z</th>\n", "      <th>sphere_radius</th>\n", "      <th>circle_3d_center_x</th>\n", "      <th>circle_3d_center_y</th>\n", "      <th>circle_3d_center_z</th>\n", "      <th>circle_3d_normal_x</th>\n", "      <th>circle_3d_normal_y</th>\n", "      <th>circle_3d_normal_z</th>\n", "      <th>circle_3d_radius</th>\n", "      <th>theta</th>\n", "      <th>phi</th>\n", "      <th>projected_sphere_center_x</th>\n", "      <th>projected_sphere_center_y</th>\n", "      <th>projected_sphere_axis_a</th>\n", "      <th>projected_sphere_axis_b</th>\n", "      <th>projected_sphere_angle</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>260229.466438</td>\n", "      <td>2</td>\n", "      <td>0</td>\n", "      <td>0.0</td>\n", "      <td>0.5</td>\n", "      <td>0.5</td>\n", "      <td>0.0</td>\n", "      <td>3d c++</td>\n", "      <td>160.0</td>\n", "      <td>120.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>90.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>1</td>\n", "      <td>0.0000000000</td>\n", "      <td>-0.0000000000</td>\n", "      <td>0.0000000000</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>-0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>-0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0000000000</td>\n", "      <td>0.0000000000</td>\n", "      <td>0.0000000000</td>\n", "      <td>0.0000000000</td>\n", "      <td>90.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>260229.474838</td>\n", "      <td>2</td>\n", "      <td>0</td>\n", "      <td>0.0</td>\n", "      <td>0.5</td>\n", "      <td>0.5</td>\n", "      <td>0.0</td>\n", "      <td>3d c++</td>\n", "      <td>160.0</td>\n", "      <td>120.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>90.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>1</td>\n", "      <td>-57.4911221333</td>\n", "      <td>28.0752848978</td>\n", "      <td>754.1563864081</td>\n", "      <td>12.0</td>\n", "      <td>0.0</td>\n", "      <td>-0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>-0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>112.7359359875</td>\n", "      <td>143.0809908267</td>\n", "      <td>19.7306556945</td>\n", "      <td>19.7306556945</td>\n", "      <td>90.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>260229.483238</td>\n", "      <td>2</td>\n", "      <td>0</td>\n", "      <td>0.0</td>\n", "      <td>0.5</td>\n", "      <td>0.5</td>\n", "      <td>0.0</td>\n", "      <td>3d c++</td>\n", "      <td>160.0</td>\n", "      <td>120.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>90.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>1</td>\n", "      <td>-57.4911221333</td>\n", "      <td>28.0752848978</td>\n", "      <td>754.1563864081</td>\n", "      <td>12.0</td>\n", "      <td>0.0</td>\n", "      <td>-0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>-0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>112.7359359875</td>\n", "      <td>143.0809908267</td>\n", "      <td>19.7306556945</td>\n", "      <td>19.7306556945</td>\n", "      <td>90.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>260229.491638</td>\n", "      <td>3</td>\n", "      <td>0</td>\n", "      <td>0.0</td>\n", "      <td>0.5</td>\n", "      <td>0.5</td>\n", "      <td>0.0</td>\n", "      <td>3d c++</td>\n", "      <td>160.0</td>\n", "      <td>120.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>90.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>1</td>\n", "      <td>-57.4911221333</td>\n", "      <td>28.0752848978</td>\n", "      <td>754.1563864081</td>\n", "      <td>12.0</td>\n", "      <td>0.0</td>\n", "      <td>-0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>-0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>112.7359359875</td>\n", "      <td>143.0809908267</td>\n", "      <td>19.7306556945</td>\n", "      <td>19.7306556945</td>\n", "      <td>90.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>260229.500038</td>\n", "      <td>3</td>\n", "      <td>0</td>\n", "      <td>0.0</td>\n", "      <td>0.5</td>\n", "      <td>0.5</td>\n", "      <td>0.0</td>\n", "      <td>3d c++</td>\n", "      <td>160.0</td>\n", "      <td>120.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>90.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>1</td>\n", "      <td>-57.4911221333</td>\n", "      <td>28.0752848978</td>\n", "      <td>754.1563864081</td>\n", "      <td>12.0</td>\n", "      <td>0.0</td>\n", "      <td>-0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>-0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>112.7359359875</td>\n", "      <td>143.0809908267</td>\n", "      <td>19.7306556945</td>\n", "      <td>19.7306556945</td>\n", "      <td>90.0</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["   world_timestamp  world_index  eye_id  confidence  norm_pos_x  norm_pos_y  \\\n", "0    260229.466438            2       0         0.0         0.5         0.5   \n", "1    260229.474838            2       0         0.0         0.5         0.5   \n", "2    260229.483238            2       0         0.0         0.5         0.5   \n", "3    260229.491638            3       0         0.0         0.5         0.5   \n", "4    260229.500038            3       0         0.0         0.5         0.5   \n", "\n", "   diameter  method  ellipse_center_x  ellipse_center_y  ellipse_axis_a  \\\n", "0       0.0  3d c++             160.0             120.0             0.0   \n", "1       0.0  3d c++             160.0             120.0             0.0   \n", "2       0.0  3d c++             160.0             120.0             0.0   \n", "3       0.0  3d c++             160.0             120.0             0.0   \n", "4       0.0  3d c++             160.0             120.0             0.0   \n", "\n", "   ellipse_axis_b  ellipse_angle  diameter_3d  model_confidence  model_id  \\\n", "0             0.0           90.0          0.0               0.0         1   \n", "1             0.0           90.0          0.0               0.0         1   \n", "2             0.0           90.0          0.0               0.0         1   \n", "3             0.0           90.0          0.0               0.0         1   \n", "4             0.0           90.0          0.0               0.0         1   \n", "\n", "   sphere_center_x  sphere_center_y  sphere_center_z  sphere_radius  \\\n", "0     0.0000000000    -0.0000000000     0.0000000000            0.0   \n", "1   -57.4911221333    28.0752848978   754.1563864081           12.0   \n", "2   -57.4911221333    28.0752848978   754.1563864081           12.0   \n", "3   -57.4911221333    28.0752848978   754.1563864081           12.0   \n", "4   -57.4911221333    28.0752848978   754.1563864081           12.0   \n", "\n", "   circle_3d_center_x  circle_3d_center_y  circle_3d_center_z  \\\n", "0                 0.0                -0.0                 0.0   \n", "1                 0.0                -0.0                 0.0   \n", "2                 0.0                -0.0                 0.0   \n", "3                 0.0                -0.0                 0.0   \n", "4                 0.0                -0.0                 0.0   \n", "\n", "   circle_3d_normal_x  circle_3d_normal_y  circle_3d_normal_z  \\\n", "0                 0.0                -0.0                 0.0   \n", "1                 0.0                -0.0                 0.0   \n", "2                 0.0                -0.0                 0.0   \n", "3                 0.0                -0.0                 0.0   \n", "4                 0.0                -0.0                 0.0   \n", "\n", "   circle_3d_radius  theta  phi  projected_sphere_center_x  \\\n", "0               0.0    0.0  0.0               0.0000000000   \n", "1               0.0    0.0  0.0             112.7359359875   \n", "2               0.0    0.0  0.0             112.7359359875   \n", "3               0.0    0.0  0.0             112.7359359875   \n", "4               0.0    0.0  0.0             112.7359359875   \n", "\n", "   projected_sphere_center_y  projected_sphere_axis_a  \\\n", "0               0.0000000000             0.0000000000   \n", "1             143.0809908267            19.7306556945   \n", "2             143.0809908267            19.7306556945   \n", "3             143.0809908267            19.7306556945   \n", "4             143.0809908267            19.7306556945   \n", "\n", "   projected_sphere_axis_b  projected_sphere_angle  \n", "0             0.0000000000                    90.0  \n", "1            19.7306556945                    90.0  \n", "2            19.7306556945                    90.0  \n", "3            19.7306556945                    90.0  \n", "4            19.7306556945                    90.0  "]}, "execution_count": 3, "metadata": {}, "output_type": "execute_result"}], "source": ["df1 = pd.read_csv('p02-pupil_positions.csv')\n", "df1.head()"]}, {"cell_type": "code", "execution_count": 4, "id": "5638a6af", "metadata": {"scrolled": true}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["<class 'pandas.core.frame.DataFrame'>\n", "RangeIndex: 64818 entries, 0 to 64817\n", "Data columns (total 34 columns):\n", " #   Column                     Non-Null Count  Dtype  \n", "---  ------                     --------------  -----  \n", " 0   world_timestamp            64818 non-null  float64\n", " 1   world_index                64818 non-null  int64  \n", " 2   eye_id                     64818 non-null  int64  \n", " 3   confidence                 64818 non-null  float64\n", " 4   norm_pos_x                 64818 non-null  float64\n", " 5   norm_pos_y                 64818 non-null  float64\n", " 6   diameter                   64818 non-null  float64\n", " 7   method                     64818 non-null  object \n", " 8   ellipse_center_x           64818 non-null  float64\n", " 9   ellipse_center_y           64818 non-null  float64\n", " 10  ellipse_axis_a             64818 non-null  float64\n", " 11  ellipse_axis_b             64818 non-null  float64\n", " 12  ellipse_angle              64818 non-null  float64\n", " 13  diameter_3d                64818 non-null  float64\n", " 14  model_confidence           64818 non-null  float64\n", " 15  model_id                   64818 non-null  int64  \n", " 16  sphere_center_x            64818 non-null  float64\n", " 17  sphere_center_y            64818 non-null  float64\n", " 18  sphere_center_z            64818 non-null  float64\n", " 19  sphere_radius              64818 non-null  float64\n", " 20  circle_3d_center_x         64818 non-null  float64\n", " 21  circle_3d_center_y         64818 non-null  float64\n", " 22  circle_3d_center_z         64818 non-null  float64\n", " 23  circle_3d_normal_x         64818 non-null  float64\n", " 24  circle_3d_normal_y         64818 non-null  float64\n", " 25  circle_3d_normal_z         64818 non-null  float64\n", " 26  circle_3d_radius           64818 non-null  float64\n", " 27  theta                      64818 non-null  float64\n", " 28  phi                        64818 non-null  float64\n", " 29  projected_sphere_center_x  64818 non-null  float64\n", " 30  projected_sphere_center_y  64818 non-null  float64\n", " 31  projected_sphere_axis_a    64818 non-null  float64\n", " 32  projected_sphere_axis_b    64818 non-null  float64\n", " 33  projected_sphere_angle     64818 non-null  float64\n", "dtypes: float64(30), int64(3), object(1)\n", "memory usage: 16.8+ MB\n"]}], "source": ["df1.info()"]}, {"cell_type": "code", "execution_count": 5, "id": "b9203d02", "metadata": {"scrolled": true}, "outputs": [{"data": {"text/plain": ["['world_timestamp',\n", " 'world_index',\n", " 'eye_id',\n", " 'confidence',\n", " 'norm_pos_x',\n", " 'norm_pos_y',\n", " 'diameter',\n", " 'method',\n", " 'ellipse_center_x',\n", " 'ellipse_center_y',\n", " 'ellipse_axis_a',\n", " 'ellipse_axis_b',\n", " 'ellipse_angle',\n", " 'diameter_3d',\n", " 'model_confidence',\n", " 'model_id',\n", " 'sphere_center_x',\n", " 'sphere_center_y',\n", " 'sphere_center_z',\n", " 'sphere_radius',\n", " 'circle_3d_center_x',\n", " 'circle_3d_center_y',\n", " 'circle_3d_center_z',\n", " 'circle_3d_normal_x',\n", " 'circle_3d_normal_y',\n", " 'circle_3d_normal_z',\n", " 'circle_3d_radius',\n", " 'theta',\n", " 'phi',\n", " 'projected_sphere_center_x',\n", " 'projected_sphere_center_y',\n", " 'projected_sphere_axis_a',\n", " 'projected_sphere_axis_b',\n", " 'projected_sphere_angle']"]}, "execution_count": 5, "metadata": {}, "output_type": "execute_result"}], "source": ["list(df1.columns)"]}, {"cell_type": "code", "execution_count": 6, "id": "77e7f3a7", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>world_timestamp</th>\n", "      <th>world_index</th>\n", "      <th>eye_id</th>\n", "      <th>confidence</th>\n", "      <th>norm_pos_x</th>\n", "      <th>norm_pos_y</th>\n", "      <th>diameter</th>\n", "      <th>method</th>\n", "      <th>ellipse_center_x</th>\n", "      <th>ellipse_center_y</th>\n", "      <th>ellipse_axis_a</th>\n", "      <th>ellipse_axis_b</th>\n", "      <th>ellipse_angle</th>\n", "      <th>diameter_3d</th>\n", "      <th>model_confidence</th>\n", "      <th>model_id</th>\n", "      <th>sphere_center_x</th>\n", "      <th>sphere_center_y</th>\n", "      <th>sphere_center_z</th>\n", "      <th>sphere_radius</th>\n", "      <th>circle_3d_center_x</th>\n", "      <th>circle_3d_center_y</th>\n", "      <th>circle_3d_center_z</th>\n", "      <th>circle_3d_normal_x</th>\n", "      <th>circle_3d_normal_y</th>\n", "      <th>circle_3d_normal_z</th>\n", "      <th>circle_3d_radius</th>\n", "      <th>theta</th>\n", "      <th>phi</th>\n", "      <th>projected_sphere_center_x</th>\n", "      <th>projected_sphere_center_y</th>\n", "      <th>projected_sphere_axis_a</th>\n", "      <th>projected_sphere_axis_b</th>\n", "      <th>projected_sphere_angle</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>260229.466438</td>\n", "      <td>2</td>\n", "      <td>0</td>\n", "      <td>0.0</td>\n", "      <td>0.5</td>\n", "      <td>0.5</td>\n", "      <td>0.0</td>\n", "      <td>3d c++</td>\n", "      <td>160.0</td>\n", "      <td>120.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>90.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>1</td>\n", "      <td>0.0000000000</td>\n", "      <td>-0.0000000000</td>\n", "      <td>0.0000000000</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>-0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>-0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0000000000</td>\n", "      <td>0.0000000000</td>\n", "      <td>0.0000000000</td>\n", "      <td>0.0000000000</td>\n", "      <td>90.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>260229.474838</td>\n", "      <td>2</td>\n", "      <td>0</td>\n", "      <td>0.0</td>\n", "      <td>0.5</td>\n", "      <td>0.5</td>\n", "      <td>0.0</td>\n", "      <td>3d c++</td>\n", "      <td>160.0</td>\n", "      <td>120.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>90.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>1</td>\n", "      <td>-57.4911221333</td>\n", "      <td>28.0752848978</td>\n", "      <td>754.1563864081</td>\n", "      <td>12.0</td>\n", "      <td>0.0</td>\n", "      <td>-0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>-0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>112.7359359875</td>\n", "      <td>143.0809908267</td>\n", "      <td>19.7306556945</td>\n", "      <td>19.7306556945</td>\n", "      <td>90.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>260229.483238</td>\n", "      <td>2</td>\n", "      <td>0</td>\n", "      <td>0.0</td>\n", "      <td>0.5</td>\n", "      <td>0.5</td>\n", "      <td>0.0</td>\n", "      <td>3d c++</td>\n", "      <td>160.0</td>\n", "      <td>120.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>90.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>1</td>\n", "      <td>-57.4911221333</td>\n", "      <td>28.0752848978</td>\n", "      <td>754.1563864081</td>\n", "      <td>12.0</td>\n", "      <td>0.0</td>\n", "      <td>-0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>-0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>112.7359359875</td>\n", "      <td>143.0809908267</td>\n", "      <td>19.7306556945</td>\n", "      <td>19.7306556945</td>\n", "      <td>90.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>260229.491638</td>\n", "      <td>3</td>\n", "      <td>0</td>\n", "      <td>0.0</td>\n", "      <td>0.5</td>\n", "      <td>0.5</td>\n", "      <td>0.0</td>\n", "      <td>3d c++</td>\n", "      <td>160.0</td>\n", "      <td>120.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>90.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>1</td>\n", "      <td>-57.4911221333</td>\n", "      <td>28.0752848978</td>\n", "      <td>754.1563864081</td>\n", "      <td>12.0</td>\n", "      <td>0.0</td>\n", "      <td>-0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>-0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>112.7359359875</td>\n", "      <td>143.0809908267</td>\n", "      <td>19.7306556945</td>\n", "      <td>19.7306556945</td>\n", "      <td>90.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>260229.500038</td>\n", "      <td>3</td>\n", "      <td>0</td>\n", "      <td>0.0</td>\n", "      <td>0.5</td>\n", "      <td>0.5</td>\n", "      <td>0.0</td>\n", "      <td>3d c++</td>\n", "      <td>160.0</td>\n", "      <td>120.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>90.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>1</td>\n", "      <td>-57.4911221333</td>\n", "      <td>28.0752848978</td>\n", "      <td>754.1563864081</td>\n", "      <td>12.0</td>\n", "      <td>0.0</td>\n", "      <td>-0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>-0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>112.7359359875</td>\n", "      <td>143.0809908267</td>\n", "      <td>19.7306556945</td>\n", "      <td>19.7306556945</td>\n", "      <td>90.0</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["   world_timestamp  world_index  eye_id  confidence  norm_pos_x  norm_pos_y  \\\n", "0    260229.466438            2       0         0.0         0.5         0.5   \n", "1    260229.474838            2       0         0.0         0.5         0.5   \n", "2    260229.483238            2       0         0.0         0.5         0.5   \n", "3    260229.491638            3       0         0.0         0.5         0.5   \n", "4    260229.500038            3       0         0.0         0.5         0.5   \n", "\n", "   diameter  method  ellipse_center_x  ellipse_center_y  ellipse_axis_a  \\\n", "0       0.0  3d c++             160.0             120.0             0.0   \n", "1       0.0  3d c++             160.0             120.0             0.0   \n", "2       0.0  3d c++             160.0             120.0             0.0   \n", "3       0.0  3d c++             160.0             120.0             0.0   \n", "4       0.0  3d c++             160.0             120.0             0.0   \n", "\n", "   ellipse_axis_b  ellipse_angle  diameter_3d  model_confidence  model_id  \\\n", "0             0.0           90.0          0.0               0.0         1   \n", "1             0.0           90.0          0.0               0.0         1   \n", "2             0.0           90.0          0.0               0.0         1   \n", "3             0.0           90.0          0.0               0.0         1   \n", "4             0.0           90.0          0.0               0.0         1   \n", "\n", "   sphere_center_x  sphere_center_y  sphere_center_z  sphere_radius  \\\n", "0     0.0000000000    -0.0000000000     0.0000000000            0.0   \n", "1   -57.4911221333    28.0752848978   754.1563864081           12.0   \n", "2   -57.4911221333    28.0752848978   754.1563864081           12.0   \n", "3   -57.4911221333    28.0752848978   754.1563864081           12.0   \n", "4   -57.4911221333    28.0752848978   754.1563864081           12.0   \n", "\n", "   circle_3d_center_x  circle_3d_center_y  circle_3d_center_z  \\\n", "0                 0.0                -0.0                 0.0   \n", "1                 0.0                -0.0                 0.0   \n", "2                 0.0                -0.0                 0.0   \n", "3                 0.0                -0.0                 0.0   \n", "4                 0.0                -0.0                 0.0   \n", "\n", "   circle_3d_normal_x  circle_3d_normal_y  circle_3d_normal_z  \\\n", "0                 0.0                -0.0                 0.0   \n", "1                 0.0                -0.0                 0.0   \n", "2                 0.0                -0.0                 0.0   \n", "3                 0.0                -0.0                 0.0   \n", "4                 0.0                -0.0                 0.0   \n", "\n", "   circle_3d_radius  theta  phi  projected_sphere_center_x  \\\n", "0               0.0    0.0  0.0               0.0000000000   \n", "1               0.0    0.0  0.0             112.7359359875   \n", "2               0.0    0.0  0.0             112.7359359875   \n", "3               0.0    0.0  0.0             112.7359359875   \n", "4               0.0    0.0  0.0             112.7359359875   \n", "\n", "   projected_sphere_center_y  projected_sphere_axis_a  \\\n", "0               0.0000000000             0.0000000000   \n", "1             143.0809908267            19.7306556945   \n", "2             143.0809908267            19.7306556945   \n", "3             143.0809908267            19.7306556945   \n", "4             143.0809908267            19.7306556945   \n", "\n", "   projected_sphere_axis_b  projected_sphere_angle  \n", "0             0.0000000000                    90.0  \n", "1            19.7306556945                    90.0  \n", "2            19.7306556945                    90.0  \n", "3            19.7306556945                    90.0  \n", "4            19.7306556945                    90.0  "]}, "execution_count": 6, "metadata": {}, "output_type": "execute_result"}], "source": ["df2_trimmed = pd.read_csv(\"p02-pupil_positions-trimmed.csv\")\n", "df2_trimmed.head()"]}, {"cell_type": "code", "execution_count": 7, "id": "21ccd93b", "metadata": {"scrolled": false}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["<class 'pandas.core.frame.DataFrame'>\n", "RangeIndex: 17815 entries, 0 to 17814\n", "Data columns (total 34 columns):\n", " #   Column                     Non-Null Count  Dtype  \n", "---  ------                     --------------  -----  \n", " 0   world_timestamp            17815 non-null  float64\n", " 1   world_index                17815 non-null  int64  \n", " 2   eye_id                     17815 non-null  int64  \n", " 3   confidence                 17815 non-null  float64\n", " 4   norm_pos_x                 17815 non-null  float64\n", " 5   norm_pos_y                 17815 non-null  float64\n", " 6   diameter                   17815 non-null  float64\n", " 7   method                     17815 non-null  object \n", " 8   ellipse_center_x           17815 non-null  float64\n", " 9   ellipse_center_y           17815 non-null  float64\n", " 10  ellipse_axis_a             17815 non-null  float64\n", " 11  ellipse_axis_b             17815 non-null  float64\n", " 12  ellipse_angle              17815 non-null  float64\n", " 13  diameter_3d                17815 non-null  float64\n", " 14  model_confidence           17815 non-null  float64\n", " 15  model_id                   17815 non-null  int64  \n", " 16  sphere_center_x            17815 non-null  float64\n", " 17  sphere_center_y            17815 non-null  float64\n", " 18  sphere_center_z            17815 non-null  float64\n", " 19  sphere_radius              17815 non-null  float64\n", " 20  circle_3d_center_x         17815 non-null  float64\n", " 21  circle_3d_center_y         17815 non-null  float64\n", " 22  circle_3d_center_z         17815 non-null  float64\n", " 23  circle_3d_normal_x         17815 non-null  float64\n", " 24  circle_3d_normal_y         17815 non-null  float64\n", " 25  circle_3d_normal_z         17815 non-null  float64\n", " 26  circle_3d_radius           17815 non-null  float64\n", " 27  theta                      17815 non-null  float64\n", " 28  phi                        17815 non-null  float64\n", " 29  projected_sphere_center_x  17815 non-null  float64\n", " 30  projected_sphere_center_y  17815 non-null  float64\n", " 31  projected_sphere_axis_a    17815 non-null  float64\n", " 32  projected_sphere_axis_b    17815 non-null  float64\n", " 33  projected_sphere_angle     17815 non-null  float64\n", "dtypes: float64(30), int64(3), object(1)\n", "memory usage: 4.6+ MB\n"]}], "source": ["df2_trimmed.info()"]}, {"cell_type": "code", "execution_count": 8, "id": "1221e03e", "metadata": {"scrolled": true}, "outputs": [{"data": {"text/plain": ["['world_timestamp',\n", " 'world_index',\n", " 'eye_id',\n", " 'confidence',\n", " 'norm_pos_x',\n", " 'norm_pos_y',\n", " 'diameter',\n", " 'method',\n", " 'ellipse_center_x',\n", " 'ellipse_center_y',\n", " 'ellipse_axis_a',\n", " 'ellipse_axis_b',\n", " 'ellipse_angle',\n", " 'diameter_3d',\n", " 'model_confidence',\n", " 'model_id',\n", " 'sphere_center_x',\n", " 'sphere_center_y',\n", " 'sphere_center_z',\n", " 'sphere_radius',\n", " 'circle_3d_center_x',\n", " 'circle_3d_center_y',\n", " 'circle_3d_center_z',\n", " 'circle_3d_normal_x',\n", " 'circle_3d_normal_y',\n", " 'circle_3d_normal_z',\n", " 'circle_3d_radius',\n", " 'theta',\n", " 'phi',\n", " 'projected_sphere_center_x',\n", " 'projected_sphere_center_y',\n", " 'projected_sphere_axis_a',\n", " 'projected_sphere_axis_b',\n", " 'projected_sphere_angle']"]}, "execution_count": 8, "metadata": {}, "output_type": "execute_result"}], "source": ["list(df2_trimmed.columns)"]}, {"cell_type": "code", "execution_count": 9, "id": "4bccc4a1", "metadata": {}, "outputs": [{"data": {"text/plain": ["True"]}, "execution_count": 9, "metadata": {}, "output_type": "execute_result"}], "source": ["list(df2_trimmed.columns) == list(df1.columns)"]}, {"cell_type": "code", "execution_count": 10, "id": "b1698eb4", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>world_timestamp</th>\n", "      <th>world_index</th>\n", "      <th>eye_id</th>\n", "      <th>confidence</th>\n", "      <th>norm_pos_x</th>\n", "      <th>norm_pos_y</th>\n", "      <th>diameter</th>\n", "      <th>method</th>\n", "      <th>ellipse_center_x</th>\n", "      <th>ellipse_center_y</th>\n", "      <th>ellipse_axis_a</th>\n", "      <th>ellipse_axis_b</th>\n", "      <th>ellipse_angle</th>\n", "      <th>diameter_3d</th>\n", "      <th>model_confidence</th>\n", "      <th>model_id</th>\n", "      <th>sphere_center_x</th>\n", "      <th>sphere_center_y</th>\n", "      <th>sphere_center_z</th>\n", "      <th>sphere_radius</th>\n", "      <th>circle_3d_center_x</th>\n", "      <th>circle_3d_center_y</th>\n", "      <th>circle_3d_center_z</th>\n", "      <th>circle_3d_normal_x</th>\n", "      <th>circle_3d_normal_y</th>\n", "      <th>circle_3d_normal_z</th>\n", "      <th>circle_3d_radius</th>\n", "      <th>theta</th>\n", "      <th>phi</th>\n", "      <th>projected_sphere_center_x</th>\n", "      <th>projected_sphere_center_y</th>\n", "      <th>projected_sphere_axis_a</th>\n", "      <th>projected_sphere_axis_b</th>\n", "      <th>projected_sphere_angle</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>260229.466438</td>\n", "      <td>2</td>\n", "      <td>0</td>\n", "      <td>0.0</td>\n", "      <td>0.5</td>\n", "      <td>0.5</td>\n", "      <td>0.0</td>\n", "      <td>3d c++</td>\n", "      <td>160.0</td>\n", "      <td>120.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>90.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>1</td>\n", "      <td>0.0000000000</td>\n", "      <td>-0.0000000000</td>\n", "      <td>0.0000000000</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>-0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>-0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0000000000</td>\n", "      <td>0.0000000000</td>\n", "      <td>0.0000000000</td>\n", "      <td>0.0000000000</td>\n", "      <td>90.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>260229.474838</td>\n", "      <td>2</td>\n", "      <td>0</td>\n", "      <td>0.0</td>\n", "      <td>0.5</td>\n", "      <td>0.5</td>\n", "      <td>0.0</td>\n", "      <td>3d c++</td>\n", "      <td>160.0</td>\n", "      <td>120.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>90.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>1</td>\n", "      <td>-57.4911221333</td>\n", "      <td>28.0752848978</td>\n", "      <td>754.1563864081</td>\n", "      <td>12.0</td>\n", "      <td>0.0</td>\n", "      <td>-0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>-0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>112.7359359875</td>\n", "      <td>143.0809908267</td>\n", "      <td>19.7306556945</td>\n", "      <td>19.7306556945</td>\n", "      <td>90.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>260229.483238</td>\n", "      <td>2</td>\n", "      <td>0</td>\n", "      <td>0.0</td>\n", "      <td>0.5</td>\n", "      <td>0.5</td>\n", "      <td>0.0</td>\n", "      <td>3d c++</td>\n", "      <td>160.0</td>\n", "      <td>120.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>90.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>1</td>\n", "      <td>-57.4911221333</td>\n", "      <td>28.0752848978</td>\n", "      <td>754.1563864081</td>\n", "      <td>12.0</td>\n", "      <td>0.0</td>\n", "      <td>-0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>-0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>112.7359359875</td>\n", "      <td>143.0809908267</td>\n", "      <td>19.7306556945</td>\n", "      <td>19.7306556945</td>\n", "      <td>90.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>260229.491638</td>\n", "      <td>3</td>\n", "      <td>0</td>\n", "      <td>0.0</td>\n", "      <td>0.5</td>\n", "      <td>0.5</td>\n", "      <td>0.0</td>\n", "      <td>3d c++</td>\n", "      <td>160.0</td>\n", "      <td>120.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>90.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>1</td>\n", "      <td>-57.4911221333</td>\n", "      <td>28.0752848978</td>\n", "      <td>754.1563864081</td>\n", "      <td>12.0</td>\n", "      <td>0.0</td>\n", "      <td>-0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>-0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>112.7359359875</td>\n", "      <td>143.0809908267</td>\n", "      <td>19.7306556945</td>\n", "      <td>19.7306556945</td>\n", "      <td>90.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>260229.500038</td>\n", "      <td>3</td>\n", "      <td>0</td>\n", "      <td>0.0</td>\n", "      <td>0.5</td>\n", "      <td>0.5</td>\n", "      <td>0.0</td>\n", "      <td>3d c++</td>\n", "      <td>160.0</td>\n", "      <td>120.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>90.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>1</td>\n", "      <td>-57.4911221333</td>\n", "      <td>28.0752848978</td>\n", "      <td>754.1563864081</td>\n", "      <td>12.0</td>\n", "      <td>0.0</td>\n", "      <td>-0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>-0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>112.7359359875</td>\n", "      <td>143.0809908267</td>\n", "      <td>19.7306556945</td>\n", "      <td>19.7306556945</td>\n", "      <td>90.0</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["   world_timestamp  world_index  eye_id  confidence  norm_pos_x  norm_pos_y  \\\n", "0    260229.466438            2       0         0.0         0.5         0.5   \n", "1    260229.474838            2       0         0.0         0.5         0.5   \n", "2    260229.483238            2       0         0.0         0.5         0.5   \n", "3    260229.491638            3       0         0.0         0.5         0.5   \n", "4    260229.500038            3       0         0.0         0.5         0.5   \n", "\n", "   diameter  method  ellipse_center_x  ellipse_center_y  ellipse_axis_a  \\\n", "0       0.0  3d c++             160.0             120.0             0.0   \n", "1       0.0  3d c++             160.0             120.0             0.0   \n", "2       0.0  3d c++             160.0             120.0             0.0   \n", "3       0.0  3d c++             160.0             120.0             0.0   \n", "4       0.0  3d c++             160.0             120.0             0.0   \n", "\n", "   ellipse_axis_b  ellipse_angle  diameter_3d  model_confidence  model_id  \\\n", "0             0.0           90.0          0.0               0.0         1   \n", "1             0.0           90.0          0.0               0.0         1   \n", "2             0.0           90.0          0.0               0.0         1   \n", "3             0.0           90.0          0.0               0.0         1   \n", "4             0.0           90.0          0.0               0.0         1   \n", "\n", "   sphere_center_x  sphere_center_y  sphere_center_z  sphere_radius  \\\n", "0     0.0000000000    -0.0000000000     0.0000000000            0.0   \n", "1   -57.4911221333    28.0752848978   754.1563864081           12.0   \n", "2   -57.4911221333    28.0752848978   754.1563864081           12.0   \n", "3   -57.4911221333    28.0752848978   754.1563864081           12.0   \n", "4   -57.4911221333    28.0752848978   754.1563864081           12.0   \n", "\n", "   circle_3d_center_x  circle_3d_center_y  circle_3d_center_z  \\\n", "0                 0.0                -0.0                 0.0   \n", "1                 0.0                -0.0                 0.0   \n", "2                 0.0                -0.0                 0.0   \n", "3                 0.0                -0.0                 0.0   \n", "4                 0.0                -0.0                 0.0   \n", "\n", "   circle_3d_normal_x  circle_3d_normal_y  circle_3d_normal_z  \\\n", "0                 0.0                -0.0                 0.0   \n", "1                 0.0                -0.0                 0.0   \n", "2                 0.0                -0.0                 0.0   \n", "3                 0.0                -0.0                 0.0   \n", "4                 0.0                -0.0                 0.0   \n", "\n", "   circle_3d_radius  theta  phi  projected_sphere_center_x  \\\n", "0               0.0    0.0  0.0               0.0000000000   \n", "1               0.0    0.0  0.0             112.7359359875   \n", "2               0.0    0.0  0.0             112.7359359875   \n", "3               0.0    0.0  0.0             112.7359359875   \n", "4               0.0    0.0  0.0             112.7359359875   \n", "\n", "   projected_sphere_center_y  projected_sphere_axis_a  \\\n", "0               0.0000000000             0.0000000000   \n", "1             143.0809908267            19.7306556945   \n", "2             143.0809908267            19.7306556945   \n", "3             143.0809908267            19.7306556945   \n", "4             143.0809908267            19.7306556945   \n", "\n", "   projected_sphere_axis_b  projected_sphere_angle  \n", "0             0.0000000000                    90.0  \n", "1            19.7306556945                    90.0  \n", "2            19.7306556945                    90.0  \n", "3            19.7306556945                    90.0  \n", "4            19.7306556945                    90.0  "]}, "execution_count": 10, "metadata": {}, "output_type": "execute_result"}], "source": ["df3_trimmed = pd.read_csv(\"p02-pupil_positions-trimmed-02.csv\")\n", "df3_trimmed.head()"]}, {"cell_type": "code", "execution_count": 11, "id": "408b3838", "metadata": {"scrolled": true}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["<class 'pandas.core.frame.DataFrame'>\n", "RangeIndex: 9999 entries, 0 to 9998\n", "Data columns (total 34 columns):\n", " #   Column                     Non-Null Count  Dtype  \n", "---  ------                     --------------  -----  \n", " 0   world_timestamp            9999 non-null   float64\n", " 1   world_index                9999 non-null   int64  \n", " 2   eye_id                     9999 non-null   int64  \n", " 3   confidence                 9999 non-null   float64\n", " 4   norm_pos_x                 9999 non-null   float64\n", " 5   norm_pos_y                 9999 non-null   float64\n", " 6   diameter                   9999 non-null   float64\n", " 7   method                     9999 non-null   object \n", " 8   ellipse_center_x           9999 non-null   float64\n", " 9   ellipse_center_y           9999 non-null   float64\n", " 10  ellipse_axis_a             9999 non-null   float64\n", " 11  ellipse_axis_b             9999 non-null   float64\n", " 12  ellipse_angle              9999 non-null   float64\n", " 13  diameter_3d                9999 non-null   float64\n", " 14  model_confidence           9999 non-null   float64\n", " 15  model_id                   9999 non-null   int64  \n", " 16  sphere_center_x            9999 non-null   float64\n", " 17  sphere_center_y            9999 non-null   float64\n", " 18  sphere_center_z            9999 non-null   float64\n", " 19  sphere_radius              9999 non-null   float64\n", " 20  circle_3d_center_x         9999 non-null   float64\n", " 21  circle_3d_center_y         9999 non-null   float64\n", " 22  circle_3d_center_z         9999 non-null   float64\n", " 23  circle_3d_normal_x         9999 non-null   float64\n", " 24  circle_3d_normal_y         9999 non-null   float64\n", " 25  circle_3d_normal_z         9999 non-null   float64\n", " 26  circle_3d_radius           9999 non-null   float64\n", " 27  theta                      9999 non-null   float64\n", " 28  phi                        9999 non-null   float64\n", " 29  projected_sphere_center_x  9999 non-null   float64\n", " 30  projected_sphere_center_y  9999 non-null   float64\n", " 31  projected_sphere_axis_a    9999 non-null   float64\n", " 32  projected_sphere_axis_b    9999 non-null   float64\n", " 33  projected_sphere_angle     9999 non-null   float64\n", "dtypes: float64(30), int64(3), object(1)\n", "memory usage: 2.6+ MB\n"]}], "source": ["df3_trimmed.info()"]}, {"cell_type": "code", "execution_count": 12, "id": "11111b7d", "metadata": {}, "outputs": [{"data": {"text/plain": ["['world_timestamp',\n", " 'world_index',\n", " 'eye_id',\n", " 'confidence',\n", " 'norm_pos_x',\n", " 'norm_pos_y',\n", " 'diameter',\n", " 'method',\n", " 'ellipse_center_x',\n", " 'ellipse_center_y',\n", " 'ellipse_axis_a',\n", " 'ellipse_axis_b',\n", " 'ellipse_angle',\n", " 'diameter_3d',\n", " 'model_confidence',\n", " 'model_id',\n", " 'sphere_center_x',\n", " 'sphere_center_y',\n", " 'sphere_center_z',\n", " 'sphere_radius',\n", " 'circle_3d_center_x',\n", " 'circle_3d_center_y',\n", " 'circle_3d_center_z',\n", " 'circle_3d_normal_x',\n", " 'circle_3d_normal_y',\n", " 'circle_3d_normal_z',\n", " 'circle_3d_radius',\n", " 'theta',\n", " 'phi',\n", " 'projected_sphere_center_x',\n", " 'projected_sphere_center_y',\n", " 'projected_sphere_axis_a',\n", " 'projected_sphere_axis_b',\n", " 'projected_sphere_angle']"]}, "execution_count": 12, "metadata": {}, "output_type": "execute_result"}], "source": ["list(df3_trimmed.columns)"]}, {"cell_type": "code", "execution_count": 13, "id": "3b6243c8", "metadata": {}, "outputs": [{"data": {"text/plain": ["True"]}, "execution_count": 13, "metadata": {}, "output_type": "execute_result"}], "source": ["list(df3_trimmed.columns) == list(df2_trimmed.columns)"]}, {"cell_type": "code", "execution_count": 14, "id": "e4514cb6", "metadata": {"scrolled": true}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>world_timestamp</th>\n", "      <th>world_index</th>\n", "      <th>eye_id</th>\n", "      <th>confidence</th>\n", "      <th>norm_pos_x</th>\n", "      <th>norm_pos_y</th>\n", "      <th>diameter</th>\n", "      <th>method</th>\n", "      <th>ellipse_center_x</th>\n", "      <th>ellipse_center_y</th>\n", "      <th>ellipse_axis_a</th>\n", "      <th>ellipse_axis_b</th>\n", "      <th>ellipse_angle</th>\n", "      <th>diameter_3d</th>\n", "      <th>model_confidence</th>\n", "      <th>model_id</th>\n", "      <th>sphere_center_x</th>\n", "      <th>sphere_center_y</th>\n", "      <th>sphere_center_z</th>\n", "      <th>sphere_radius</th>\n", "      <th>circle_3d_center_x</th>\n", "      <th>circle_3d_center_y</th>\n", "      <th>circle_3d_center_z</th>\n", "      <th>circle_3d_normal_x</th>\n", "      <th>circle_3d_normal_y</th>\n", "      <th>circle_3d_normal_z</th>\n", "      <th>circle_3d_radius</th>\n", "      <th>theta</th>\n", "      <th>phi</th>\n", "      <th>projected_sphere_center_x</th>\n", "      <th>projected_sphere_center_y</th>\n", "      <th>projected_sphere_axis_a</th>\n", "      <th>projected_sphere_axis_b</th>\n", "      <th>projected_sphere_angle</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>260229.466438</td>\n", "      <td>2</td>\n", "      <td>0</td>\n", "      <td>0.0000000000</td>\n", "      <td>0.5000000000</td>\n", "      <td>0.5000000000</td>\n", "      <td>0.0000000000</td>\n", "      <td>3d c++</td>\n", "      <td>160.0000000000</td>\n", "      <td>120.0000000000</td>\n", "      <td>0.0000000000</td>\n", "      <td>0.0000000000</td>\n", "      <td>90.0000000000</td>\n", "      <td>0.0000000000</td>\n", "      <td>0.0000000000</td>\n", "      <td>1</td>\n", "      <td>0.0000000000</td>\n", "      <td>-0.0000000000</td>\n", "      <td>0.0000000000</td>\n", "      <td>0.0</td>\n", "      <td>0.0000000000</td>\n", "      <td>-0.0000000000</td>\n", "      <td>0.0000000000</td>\n", "      <td>0.0000000000</td>\n", "      <td>-0.0000000000</td>\n", "      <td>0.0000000000</td>\n", "      <td>0.0000000000</td>\n", "      <td>0.0000000000</td>\n", "      <td>0.0000000000</td>\n", "      <td>0.0000000000</td>\n", "      <td>0.0000000000</td>\n", "      <td>0.0000000000</td>\n", "      <td>0.0000000000</td>\n", "      <td>90.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>260229.474838</td>\n", "      <td>2</td>\n", "      <td>0</td>\n", "      <td>0.0000000000</td>\n", "      <td>0.5000000000</td>\n", "      <td>0.5000000000</td>\n", "      <td>0.0000000000</td>\n", "      <td>3d c++</td>\n", "      <td>160.0000000000</td>\n", "      <td>120.0000000000</td>\n", "      <td>0.0000000000</td>\n", "      <td>0.0000000000</td>\n", "      <td>90.0000000000</td>\n", "      <td>0.0000000000</td>\n", "      <td>0.0000000000</td>\n", "      <td>1</td>\n", "      <td>-57.4911221333</td>\n", "      <td>28.0752848978</td>\n", "      <td>754.1563864081</td>\n", "      <td>12.0</td>\n", "      <td>0.0000000000</td>\n", "      <td>-0.0000000000</td>\n", "      <td>0.0000000000</td>\n", "      <td>0.0000000000</td>\n", "      <td>-0.0000000000</td>\n", "      <td>0.0000000000</td>\n", "      <td>0.0000000000</td>\n", "      <td>0.0000000000</td>\n", "      <td>0.0000000000</td>\n", "      <td>112.7359359875</td>\n", "      <td>143.0809908267</td>\n", "      <td>19.7306556945</td>\n", "      <td>19.7306556945</td>\n", "      <td>90.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>260229.483238</td>\n", "      <td>2</td>\n", "      <td>0</td>\n", "      <td>0.0000000000</td>\n", "      <td>0.5000000000</td>\n", "      <td>0.5000000000</td>\n", "      <td>0.0000000000</td>\n", "      <td>3d c++</td>\n", "      <td>160.0000000000</td>\n", "      <td>120.0000000000</td>\n", "      <td>0.0000000000</td>\n", "      <td>0.0000000000</td>\n", "      <td>90.0000000000</td>\n", "      <td>0.0000000000</td>\n", "      <td>0.0000000000</td>\n", "      <td>1</td>\n", "      <td>-57.4911221333</td>\n", "      <td>28.0752848978</td>\n", "      <td>754.1563864081</td>\n", "      <td>12.0</td>\n", "      <td>0.0000000000</td>\n", "      <td>-0.0000000000</td>\n", "      <td>0.0000000000</td>\n", "      <td>0.0000000000</td>\n", "      <td>-0.0000000000</td>\n", "      <td>0.0000000000</td>\n", "      <td>0.0000000000</td>\n", "      <td>0.0000000000</td>\n", "      <td>0.0000000000</td>\n", "      <td>112.7359359875</td>\n", "      <td>143.0809908267</td>\n", "      <td>19.7306556945</td>\n", "      <td>19.7306556945</td>\n", "      <td>90.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>260229.491638</td>\n", "      <td>3</td>\n", "      <td>0</td>\n", "      <td>0.0000000000</td>\n", "      <td>0.5000000000</td>\n", "      <td>0.5000000000</td>\n", "      <td>0.0000000000</td>\n", "      <td>3d c++</td>\n", "      <td>160.0000000000</td>\n", "      <td>120.0000000000</td>\n", "      <td>0.0000000000</td>\n", "      <td>0.0000000000</td>\n", "      <td>90.0000000000</td>\n", "      <td>0.0000000000</td>\n", "      <td>0.0000000000</td>\n", "      <td>1</td>\n", "      <td>-57.4911221333</td>\n", "      <td>28.0752848978</td>\n", "      <td>754.1563864081</td>\n", "      <td>12.0</td>\n", "      <td>0.0000000000</td>\n", "      <td>-0.0000000000</td>\n", "      <td>0.0000000000</td>\n", "      <td>0.0000000000</td>\n", "      <td>-0.0000000000</td>\n", "      <td>0.0000000000</td>\n", "      <td>0.0000000000</td>\n", "      <td>0.0000000000</td>\n", "      <td>0.0000000000</td>\n", "      <td>112.7359359875</td>\n", "      <td>143.0809908267</td>\n", "      <td>19.7306556945</td>\n", "      <td>19.7306556945</td>\n", "      <td>90.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>260229.500038</td>\n", "      <td>3</td>\n", "      <td>0</td>\n", "      <td>0.0000000000</td>\n", "      <td>0.5000000000</td>\n", "      <td>0.5000000000</td>\n", "      <td>0.0000000000</td>\n", "      <td>3d c++</td>\n", "      <td>160.0000000000</td>\n", "      <td>120.0000000000</td>\n", "      <td>0.0000000000</td>\n", "      <td>0.0000000000</td>\n", "      <td>90.0000000000</td>\n", "      <td>0.0000000000</td>\n", "      <td>0.0000000000</td>\n", "      <td>1</td>\n", "      <td>-57.4911221333</td>\n", "      <td>28.0752848978</td>\n", "      <td>754.1563864081</td>\n", "      <td>12.0</td>\n", "      <td>0.0000000000</td>\n", "      <td>-0.0000000000</td>\n", "      <td>0.0000000000</td>\n", "      <td>0.0000000000</td>\n", "      <td>-0.0000000000</td>\n", "      <td>0.0000000000</td>\n", "      <td>0.0000000000</td>\n", "      <td>0.0000000000</td>\n", "      <td>0.0000000000</td>\n", "      <td>112.7359359875</td>\n", "      <td>143.0809908267</td>\n", "      <td>19.7306556945</td>\n", "      <td>19.7306556945</td>\n", "      <td>90.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5</th>\n", "      <td>260229.508438</td>\n", "      <td>3</td>\n", "      <td>0</td>\n", "      <td>0.0000000000</td>\n", "      <td>0.5000000000</td>\n", "      <td>0.5000000000</td>\n", "      <td>0.0000000000</td>\n", "      <td>3d c++</td>\n", "      <td>160.0000000000</td>\n", "      <td>120.0000000000</td>\n", "      <td>0.0000000000</td>\n", "      <td>0.0000000000</td>\n", "      <td>90.0000000000</td>\n", "      <td>0.0000000000</td>\n", "      <td>0.0000000000</td>\n", "      <td>1</td>\n", "      <td>-57.4911221333</td>\n", "      <td>28.0752848978</td>\n", "      <td>754.1563864081</td>\n", "      <td>12.0</td>\n", "      <td>0.0000000000</td>\n", "      <td>-0.0000000000</td>\n", "      <td>0.0000000000</td>\n", "      <td>0.0000000000</td>\n", "      <td>-0.0000000000</td>\n", "      <td>0.0000000000</td>\n", "      <td>0.0000000000</td>\n", "      <td>0.0000000000</td>\n", "      <td>0.0000000000</td>\n", "      <td>112.7359359875</td>\n", "      <td>143.0809908267</td>\n", "      <td>19.7306556945</td>\n", "      <td>19.7306556945</td>\n", "      <td>90.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6</th>\n", "      <td>260229.516838</td>\n", "      <td>3</td>\n", "      <td>0</td>\n", "      <td>0.0000000000</td>\n", "      <td>0.5000000000</td>\n", "      <td>0.5000000000</td>\n", "      <td>0.0000000000</td>\n", "      <td>3d c++</td>\n", "      <td>160.0000000000</td>\n", "      <td>120.0000000000</td>\n", "      <td>0.0000000000</td>\n", "      <td>0.0000000000</td>\n", "      <td>90.0000000000</td>\n", "      <td>0.0000000000</td>\n", "      <td>0.0000000000</td>\n", "      <td>1</td>\n", "      <td>-57.4911221333</td>\n", "      <td>28.0752848978</td>\n", "      <td>754.1563864081</td>\n", "      <td>12.0</td>\n", "      <td>0.0000000000</td>\n", "      <td>-0.0000000000</td>\n", "      <td>0.0000000000</td>\n", "      <td>0.0000000000</td>\n", "      <td>-0.0000000000</td>\n", "      <td>0.0000000000</td>\n", "      <td>0.0000000000</td>\n", "      <td>0.0000000000</td>\n", "      <td>0.0000000000</td>\n", "      <td>112.7359359875</td>\n", "      <td>143.0809908267</td>\n", "      <td>19.7306556945</td>\n", "      <td>19.7306556945</td>\n", "      <td>90.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>7</th>\n", "      <td>260229.525238</td>\n", "      <td>4</td>\n", "      <td>0</td>\n", "      <td>0.0000000000</td>\n", "      <td>0.5000000000</td>\n", "      <td>0.5000000000</td>\n", "      <td>0.0000000000</td>\n", "      <td>3d c++</td>\n", "      <td>160.0000000000</td>\n", "      <td>120.0000000000</td>\n", "      <td>0.0000000000</td>\n", "      <td>0.0000000000</td>\n", "      <td>90.0000000000</td>\n", "      <td>0.0000000000</td>\n", "      <td>0.0000000000</td>\n", "      <td>1</td>\n", "      <td>-57.4911221333</td>\n", "      <td>28.0752848978</td>\n", "      <td>754.1563864081</td>\n", "      <td>12.0</td>\n", "      <td>0.0000000000</td>\n", "      <td>-0.0000000000</td>\n", "      <td>0.0000000000</td>\n", "      <td>0.0000000000</td>\n", "      <td>-0.0000000000</td>\n", "      <td>0.0000000000</td>\n", "      <td>0.0000000000</td>\n", "      <td>0.0000000000</td>\n", "      <td>0.0000000000</td>\n", "      <td>112.7359359875</td>\n", "      <td>143.0809908267</td>\n", "      <td>19.7306556945</td>\n", "      <td>19.7306556945</td>\n", "      <td>90.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>8</th>\n", "      <td>260229.533638</td>\n", "      <td>4</td>\n", "      <td>0</td>\n", "      <td>0.0000000000</td>\n", "      <td>0.5000000000</td>\n", "      <td>0.5000000000</td>\n", "      <td>0.0000000000</td>\n", "      <td>3d c++</td>\n", "      <td>160.0000000000</td>\n", "      <td>120.0000000000</td>\n", "      <td>0.0000000000</td>\n", "      <td>0.0000000000</td>\n", "      <td>90.0000000000</td>\n", "      <td>0.0000000000</td>\n", "      <td>0.0000000000</td>\n", "      <td>1</td>\n", "      <td>-57.4911221333</td>\n", "      <td>28.0752848978</td>\n", "      <td>754.1563864081</td>\n", "      <td>12.0</td>\n", "      <td>0.0000000000</td>\n", "      <td>-0.0000000000</td>\n", "      <td>0.0000000000</td>\n", "      <td>0.0000000000</td>\n", "      <td>-0.0000000000</td>\n", "      <td>0.0000000000</td>\n", "      <td>0.0000000000</td>\n", "      <td>0.0000000000</td>\n", "      <td>0.0000000000</td>\n", "      <td>112.7359359875</td>\n", "      <td>143.0809908267</td>\n", "      <td>19.7306556945</td>\n", "      <td>19.7306556945</td>\n", "      <td>90.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>9</th>\n", "      <td>260229.542038</td>\n", "      <td>4</td>\n", "      <td>0</td>\n", "      <td>0.0000000000</td>\n", "      <td>0.5000000000</td>\n", "      <td>0.5000000000</td>\n", "      <td>0.0000000000</td>\n", "      <td>3d c++</td>\n", "      <td>160.0000000000</td>\n", "      <td>120.0000000000</td>\n", "      <td>0.0000000000</td>\n", "      <td>0.0000000000</td>\n", "      <td>90.0000000000</td>\n", "      <td>0.0000000000</td>\n", "      <td>0.0000000000</td>\n", "      <td>1</td>\n", "      <td>-57.4911221333</td>\n", "      <td>28.0752848978</td>\n", "      <td>754.1563864081</td>\n", "      <td>12.0</td>\n", "      <td>0.0000000000</td>\n", "      <td>-0.0000000000</td>\n", "      <td>0.0000000000</td>\n", "      <td>0.0000000000</td>\n", "      <td>-0.0000000000</td>\n", "      <td>0.0000000000</td>\n", "      <td>0.0000000000</td>\n", "      <td>0.0000000000</td>\n", "      <td>0.0000000000</td>\n", "      <td>112.7359359875</td>\n", "      <td>143.0809908267</td>\n", "      <td>19.7306556945</td>\n", "      <td>19.7306556945</td>\n", "      <td>90.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>10</th>\n", "      <td>260229.550438</td>\n", "      <td>4</td>\n", "      <td>0</td>\n", "      <td>0.0000000000</td>\n", "      <td>0.5000000000</td>\n", "      <td>0.5000000000</td>\n", "      <td>0.0000000000</td>\n", "      <td>3d c++</td>\n", "      <td>160.0000000000</td>\n", "      <td>120.0000000000</td>\n", "      <td>0.0000000000</td>\n", "      <td>0.0000000000</td>\n", "      <td>90.0000000000</td>\n", "      <td>0.0000000000</td>\n", "      <td>0.0000000000</td>\n", "      <td>1</td>\n", "      <td>-57.4911221333</td>\n", "      <td>28.0752848978</td>\n", "      <td>754.1563864081</td>\n", "      <td>12.0</td>\n", "      <td>0.0000000000</td>\n", "      <td>-0.0000000000</td>\n", "      <td>0.0000000000</td>\n", "      <td>0.0000000000</td>\n", "      <td>-0.0000000000</td>\n", "      <td>0.0000000000</td>\n", "      <td>0.0000000000</td>\n", "      <td>0.0000000000</td>\n", "      <td>0.0000000000</td>\n", "      <td>112.7359359875</td>\n", "      <td>143.0809908267</td>\n", "      <td>19.7306556945</td>\n", "      <td>19.7306556945</td>\n", "      <td>90.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>11</th>\n", "      <td>260229.558838</td>\n", "      <td>5</td>\n", "      <td>0</td>\n", "      <td>0.0000000000</td>\n", "      <td>0.5000000000</td>\n", "      <td>0.5000000000</td>\n", "      <td>0.0000000000</td>\n", "      <td>3d c++</td>\n", "      <td>160.0000000000</td>\n", "      <td>120.0000000000</td>\n", "      <td>0.0000000000</td>\n", "      <td>0.0000000000</td>\n", "      <td>90.0000000000</td>\n", "      <td>0.0000000000</td>\n", "      <td>0.0000000000</td>\n", "      <td>1</td>\n", "      <td>-57.4911221333</td>\n", "      <td>28.0752848978</td>\n", "      <td>754.1563864081</td>\n", "      <td>12.0</td>\n", "      <td>0.0000000000</td>\n", "      <td>-0.0000000000</td>\n", "      <td>0.0000000000</td>\n", "      <td>0.0000000000</td>\n", "      <td>-0.0000000000</td>\n", "      <td>0.0000000000</td>\n", "      <td>0.0000000000</td>\n", "      <td>0.0000000000</td>\n", "      <td>0.0000000000</td>\n", "      <td>112.7359359875</td>\n", "      <td>143.0809908267</td>\n", "      <td>19.7306556945</td>\n", "      <td>19.7306556945</td>\n", "      <td>90.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>12</th>\n", "      <td>260229.567238</td>\n", "      <td>5</td>\n", "      <td>0</td>\n", "      <td>0.0000000000</td>\n", "      <td>0.5000000000</td>\n", "      <td>0.5000000000</td>\n", "      <td>0.0000000000</td>\n", "      <td>3d c++</td>\n", "      <td>160.0000000000</td>\n", "      <td>120.0000000000</td>\n", "      <td>0.0000000000</td>\n", "      <td>0.0000000000</td>\n", "      <td>90.0000000000</td>\n", "      <td>0.0000000000</td>\n", "      <td>0.0000000000</td>\n", "      <td>1</td>\n", "      <td>-57.4911221333</td>\n", "      <td>28.0752848978</td>\n", "      <td>754.1563864081</td>\n", "      <td>12.0</td>\n", "      <td>0.0000000000</td>\n", "      <td>-0.0000000000</td>\n", "      <td>0.0000000000</td>\n", "      <td>0.0000000000</td>\n", "      <td>-0.0000000000</td>\n", "      <td>0.0000000000</td>\n", "      <td>0.0000000000</td>\n", "      <td>0.0000000000</td>\n", "      <td>0.0000000000</td>\n", "      <td>112.7359359875</td>\n", "      <td>143.0809908267</td>\n", "      <td>19.7306556945</td>\n", "      <td>19.7306556945</td>\n", "      <td>90.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>13</th>\n", "      <td>260229.575638</td>\n", "      <td>5</td>\n", "      <td>0</td>\n", "      <td>0.0000000000</td>\n", "      <td>0.5000000000</td>\n", "      <td>0.5000000000</td>\n", "      <td>0.0000000000</td>\n", "      <td>3d c++</td>\n", "      <td>160.0000000000</td>\n", "      <td>120.0000000000</td>\n", "      <td>0.0000000000</td>\n", "      <td>0.0000000000</td>\n", "      <td>90.0000000000</td>\n", "      <td>0.0000000000</td>\n", "      <td>0.0000000000</td>\n", "      <td>1</td>\n", "      <td>-57.4911221333</td>\n", "      <td>28.0752848978</td>\n", "      <td>754.1563864081</td>\n", "      <td>12.0</td>\n", "      <td>0.0000000000</td>\n", "      <td>-0.0000000000</td>\n", "      <td>0.0000000000</td>\n", "      <td>0.0000000000</td>\n", "      <td>-0.0000000000</td>\n", "      <td>0.0000000000</td>\n", "      <td>0.0000000000</td>\n", "      <td>0.0000000000</td>\n", "      <td>0.0000000000</td>\n", "      <td>112.7359359875</td>\n", "      <td>143.0809908267</td>\n", "      <td>19.7306556945</td>\n", "      <td>19.7306556945</td>\n", "      <td>90.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>14</th>\n", "      <td>260229.584038</td>\n", "      <td>5</td>\n", "      <td>0</td>\n", "      <td>0.0000000000</td>\n", "      <td>0.5000000000</td>\n", "      <td>0.5000000000</td>\n", "      <td>0.0000000000</td>\n", "      <td>3d c++</td>\n", "      <td>160.0000000000</td>\n", "      <td>120.0000000000</td>\n", "      <td>0.0000000000</td>\n", "      <td>0.0000000000</td>\n", "      <td>90.0000000000</td>\n", "      <td>0.0000000000</td>\n", "      <td>0.0000000000</td>\n", "      <td>1</td>\n", "      <td>-57.4911221333</td>\n", "      <td>28.0752848978</td>\n", "      <td>754.1563864081</td>\n", "      <td>12.0</td>\n", "      <td>0.0000000000</td>\n", "      <td>-0.0000000000</td>\n", "      <td>0.0000000000</td>\n", "      <td>0.0000000000</td>\n", "      <td>-0.0000000000</td>\n", "      <td>0.0000000000</td>\n", "      <td>0.0000000000</td>\n", "      <td>0.0000000000</td>\n", "      <td>0.0000000000</td>\n", "      <td>112.7359359875</td>\n", "      <td>143.0809908267</td>\n", "      <td>19.7306556945</td>\n", "      <td>19.7306556945</td>\n", "      <td>90.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>15</th>\n", "      <td>260229.592438</td>\n", "      <td>6</td>\n", "      <td>0</td>\n", "      <td>0.0000000000</td>\n", "      <td>0.5000000000</td>\n", "      <td>0.5000000000</td>\n", "      <td>0.0000000000</td>\n", "      <td>3d c++</td>\n", "      <td>160.0000000000</td>\n", "      <td>120.0000000000</td>\n", "      <td>0.0000000000</td>\n", "      <td>0.0000000000</td>\n", "      <td>90.0000000000</td>\n", "      <td>0.0000000000</td>\n", "      <td>0.0000000000</td>\n", "      <td>1</td>\n", "      <td>-57.4911221333</td>\n", "      <td>28.0752848978</td>\n", "      <td>754.1563864081</td>\n", "      <td>12.0</td>\n", "      <td>0.0000000000</td>\n", "      <td>-0.0000000000</td>\n", "      <td>0.0000000000</td>\n", "      <td>0.0000000000</td>\n", "      <td>-0.0000000000</td>\n", "      <td>0.0000000000</td>\n", "      <td>0.0000000000</td>\n", "      <td>0.0000000000</td>\n", "      <td>0.0000000000</td>\n", "      <td>112.7359359875</td>\n", "      <td>143.0809908267</td>\n", "      <td>19.7306556945</td>\n", "      <td>19.7306556945</td>\n", "      <td>90.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>16</th>\n", "      <td>260229.600838</td>\n", "      <td>6</td>\n", "      <td>0</td>\n", "      <td>0.0000000000</td>\n", "      <td>0.5000000000</td>\n", "      <td>0.5000000000</td>\n", "      <td>0.0000000000</td>\n", "      <td>3d c++</td>\n", "      <td>160.0000000000</td>\n", "      <td>120.0000000000</td>\n", "      <td>0.0000000000</td>\n", "      <td>0.0000000000</td>\n", "      <td>90.0000000000</td>\n", "      <td>0.0000000000</td>\n", "      <td>0.0000000000</td>\n", "      <td>1</td>\n", "      <td>-57.4911221333</td>\n", "      <td>28.0752848978</td>\n", "      <td>754.1563864081</td>\n", "      <td>12.0</td>\n", "      <td>0.0000000000</td>\n", "      <td>-0.0000000000</td>\n", "      <td>0.0000000000</td>\n", "      <td>0.0000000000</td>\n", "      <td>-0.0000000000</td>\n", "      <td>0.0000000000</td>\n", "      <td>0.0000000000</td>\n", "      <td>0.0000000000</td>\n", "      <td>0.0000000000</td>\n", "      <td>112.7359359875</td>\n", "      <td>143.0809908267</td>\n", "      <td>19.7306556945</td>\n", "      <td>19.7306556945</td>\n", "      <td>90.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>17</th>\n", "      <td>260229.609238</td>\n", "      <td>6</td>\n", "      <td>0</td>\n", "      <td>0.0000000000</td>\n", "      <td>0.5000000000</td>\n", "      <td>0.5000000000</td>\n", "      <td>0.0000000000</td>\n", "      <td>3d c++</td>\n", "      <td>160.0000000000</td>\n", "      <td>120.0000000000</td>\n", "      <td>0.0000000000</td>\n", "      <td>0.0000000000</td>\n", "      <td>90.0000000000</td>\n", "      <td>0.0000000000</td>\n", "      <td>0.0000000000</td>\n", "      <td>1</td>\n", "      <td>-57.4911221333</td>\n", "      <td>28.0752848978</td>\n", "      <td>754.1563864081</td>\n", "      <td>12.0</td>\n", "      <td>0.0000000000</td>\n", "      <td>-0.0000000000</td>\n", "      <td>0.0000000000</td>\n", "      <td>0.0000000000</td>\n", "      <td>-0.0000000000</td>\n", "      <td>0.0000000000</td>\n", "      <td>0.0000000000</td>\n", "      <td>0.0000000000</td>\n", "      <td>0.0000000000</td>\n", "      <td>112.7359359875</td>\n", "      <td>143.0809908267</td>\n", "      <td>19.7306556945</td>\n", "      <td>19.7306556945</td>\n", "      <td>90.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>18</th>\n", "      <td>260229.617638</td>\n", "      <td>6</td>\n", "      <td>0</td>\n", "      <td>0.0000000000</td>\n", "      <td>0.5000000000</td>\n", "      <td>0.5000000000</td>\n", "      <td>0.0000000000</td>\n", "      <td>3d c++</td>\n", "      <td>160.0000000000</td>\n", "      <td>120.0000000000</td>\n", "      <td>0.0000000000</td>\n", "      <td>0.0000000000</td>\n", "      <td>90.0000000000</td>\n", "      <td>0.0000000000</td>\n", "      <td>0.0000000000</td>\n", "      <td>1</td>\n", "      <td>-57.4911221333</td>\n", "      <td>28.0752848978</td>\n", "      <td>754.1563864081</td>\n", "      <td>12.0</td>\n", "      <td>0.0000000000</td>\n", "      <td>-0.0000000000</td>\n", "      <td>0.0000000000</td>\n", "      <td>0.0000000000</td>\n", "      <td>-0.0000000000</td>\n", "      <td>0.0000000000</td>\n", "      <td>0.0000000000</td>\n", "      <td>0.0000000000</td>\n", "      <td>0.0000000000</td>\n", "      <td>112.7359359875</td>\n", "      <td>143.0809908267</td>\n", "      <td>19.7306556945</td>\n", "      <td>19.7306556945</td>\n", "      <td>90.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>19</th>\n", "      <td>260229.626038</td>\n", "      <td>7</td>\n", "      <td>0</td>\n", "      <td>0.0000000000</td>\n", "      <td>0.5000000000</td>\n", "      <td>0.5000000000</td>\n", "      <td>0.0000000000</td>\n", "      <td>3d c++</td>\n", "      <td>160.0000000000</td>\n", "      <td>120.0000000000</td>\n", "      <td>0.0000000000</td>\n", "      <td>0.0000000000</td>\n", "      <td>90.0000000000</td>\n", "      <td>0.0000000000</td>\n", "      <td>0.0000000000</td>\n", "      <td>1</td>\n", "      <td>-57.4911221333</td>\n", "      <td>28.0752848978</td>\n", "      <td>754.1563864081</td>\n", "      <td>12.0</td>\n", "      <td>0.0000000000</td>\n", "      <td>-0.0000000000</td>\n", "      <td>0.0000000000</td>\n", "      <td>0.0000000000</td>\n", "      <td>-0.0000000000</td>\n", "      <td>0.0000000000</td>\n", "      <td>0.0000000000</td>\n", "      <td>0.0000000000</td>\n", "      <td>0.0000000000</td>\n", "      <td>112.7359359875</td>\n", "      <td>143.0809908267</td>\n", "      <td>19.7306556945</td>\n", "      <td>19.7306556945</td>\n", "      <td>90.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>20</th>\n", "      <td>260229.634438</td>\n", "      <td>7</td>\n", "      <td>0</td>\n", "      <td>0.0000000000</td>\n", "      <td>0.5000000000</td>\n", "      <td>0.5000000000</td>\n", "      <td>0.0000000000</td>\n", "      <td>3d c++</td>\n", "      <td>160.0000000000</td>\n", "      <td>120.0000000000</td>\n", "      <td>0.0000000000</td>\n", "      <td>0.0000000000</td>\n", "      <td>90.0000000000</td>\n", "      <td>0.0000000000</td>\n", "      <td>0.0000000000</td>\n", "      <td>1</td>\n", "      <td>-57.4911221333</td>\n", "      <td>28.0752848978</td>\n", "      <td>754.1563864081</td>\n", "      <td>12.0</td>\n", "      <td>0.0000000000</td>\n", "      <td>-0.0000000000</td>\n", "      <td>0.0000000000</td>\n", "      <td>0.0000000000</td>\n", "      <td>-0.0000000000</td>\n", "      <td>0.0000000000</td>\n", "      <td>0.0000000000</td>\n", "      <td>0.0000000000</td>\n", "      <td>0.0000000000</td>\n", "      <td>112.7359359875</td>\n", "      <td>143.0809908267</td>\n", "      <td>19.7306556945</td>\n", "      <td>19.7306556945</td>\n", "      <td>90.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>21</th>\n", "      <td>260229.642838</td>\n", "      <td>7</td>\n", "      <td>0</td>\n", "      <td>0.0000000000</td>\n", "      <td>0.5000000000</td>\n", "      <td>0.5000000000</td>\n", "      <td>0.0000000000</td>\n", "      <td>3d c++</td>\n", "      <td>160.0000000000</td>\n", "      <td>120.0000000000</td>\n", "      <td>0.0000000000</td>\n", "      <td>0.0000000000</td>\n", "      <td>90.0000000000</td>\n", "      <td>0.0000000000</td>\n", "      <td>0.0000000000</td>\n", "      <td>1</td>\n", "      <td>-57.4911221333</td>\n", "      <td>28.0752848978</td>\n", "      <td>754.1563864081</td>\n", "      <td>12.0</td>\n", "      <td>0.0000000000</td>\n", "      <td>-0.0000000000</td>\n", "      <td>0.0000000000</td>\n", "      <td>0.0000000000</td>\n", "      <td>-0.0000000000</td>\n", "      <td>0.0000000000</td>\n", "      <td>0.0000000000</td>\n", "      <td>0.0000000000</td>\n", "      <td>0.0000000000</td>\n", "      <td>112.7359359875</td>\n", "      <td>143.0809908267</td>\n", "      <td>19.7306556945</td>\n", "      <td>19.7306556945</td>\n", "      <td>90.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>22</th>\n", "      <td>260229.651238</td>\n", "      <td>7</td>\n", "      <td>0</td>\n", "      <td>0.0000000000</td>\n", "      <td>0.5000000000</td>\n", "      <td>0.5000000000</td>\n", "      <td>0.0000000000</td>\n", "      <td>3d c++</td>\n", "      <td>160.0000000000</td>\n", "      <td>120.0000000000</td>\n", "      <td>0.0000000000</td>\n", "      <td>0.0000000000</td>\n", "      <td>90.0000000000</td>\n", "      <td>0.0000000000</td>\n", "      <td>0.0000000000</td>\n", "      <td>1</td>\n", "      <td>-57.4911221333</td>\n", "      <td>28.0752848978</td>\n", "      <td>754.1563864081</td>\n", "      <td>12.0</td>\n", "      <td>0.0000000000</td>\n", "      <td>-0.0000000000</td>\n", "      <td>0.0000000000</td>\n", "      <td>0.0000000000</td>\n", "      <td>-0.0000000000</td>\n", "      <td>0.0000000000</td>\n", "      <td>0.0000000000</td>\n", "      <td>0.0000000000</td>\n", "      <td>0.0000000000</td>\n", "      <td>112.7359359875</td>\n", "      <td>143.0809908267</td>\n", "      <td>19.7306556945</td>\n", "      <td>19.7306556945</td>\n", "      <td>90.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>23</th>\n", "      <td>260229.659638</td>\n", "      <td>8</td>\n", "      <td>0</td>\n", "      <td>0.0000000000</td>\n", "      <td>0.5000000000</td>\n", "      <td>0.5000000000</td>\n", "      <td>0.0000000000</td>\n", "      <td>3d c++</td>\n", "      <td>160.0000000000</td>\n", "      <td>120.0000000000</td>\n", "      <td>0.0000000000</td>\n", "      <td>0.0000000000</td>\n", "      <td>90.0000000000</td>\n", "      <td>0.0000000000</td>\n", "      <td>0.0000000000</td>\n", "      <td>1</td>\n", "      <td>-57.4911221333</td>\n", "      <td>28.0752848978</td>\n", "      <td>754.1563864081</td>\n", "      <td>12.0</td>\n", "      <td>0.0000000000</td>\n", "      <td>-0.0000000000</td>\n", "      <td>0.0000000000</td>\n", "      <td>0.0000000000</td>\n", "      <td>-0.0000000000</td>\n", "      <td>0.0000000000</td>\n", "      <td>0.0000000000</td>\n", "      <td>0.0000000000</td>\n", "      <td>0.0000000000</td>\n", "      <td>112.7359359875</td>\n", "      <td>143.0809908267</td>\n", "      <td>19.7306556945</td>\n", "      <td>19.7306556945</td>\n", "      <td>90.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>24</th>\n", "      <td>260229.668038</td>\n", "      <td>8</td>\n", "      <td>0</td>\n", "      <td>0.0000000000</td>\n", "      <td>0.5000000000</td>\n", "      <td>0.5000000000</td>\n", "      <td>0.0000000000</td>\n", "      <td>3d c++</td>\n", "      <td>160.0000000000</td>\n", "      <td>120.0000000000</td>\n", "      <td>0.0000000000</td>\n", "      <td>0.0000000000</td>\n", "      <td>90.0000000000</td>\n", "      <td>0.0000000000</td>\n", "      <td>0.0000000000</td>\n", "      <td>1</td>\n", "      <td>-57.4911221333</td>\n", "      <td>28.0752848978</td>\n", "      <td>754.1563864081</td>\n", "      <td>12.0</td>\n", "      <td>0.0000000000</td>\n", "      <td>-0.0000000000</td>\n", "      <td>0.0000000000</td>\n", "      <td>0.0000000000</td>\n", "      <td>-0.0000000000</td>\n", "      <td>0.0000000000</td>\n", "      <td>0.0000000000</td>\n", "      <td>0.0000000000</td>\n", "      <td>0.0000000000</td>\n", "      <td>112.7359359875</td>\n", "      <td>143.0809908267</td>\n", "      <td>19.7306556945</td>\n", "      <td>19.7306556945</td>\n", "      <td>90.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>25</th>\n", "      <td>260229.676438</td>\n", "      <td>8</td>\n", "      <td>0</td>\n", "      <td>0.0000000000</td>\n", "      <td>0.5000000000</td>\n", "      <td>0.5000000000</td>\n", "      <td>0.0000000000</td>\n", "      <td>3d c++</td>\n", "      <td>160.0000000000</td>\n", "      <td>120.0000000000</td>\n", "      <td>0.0000000000</td>\n", "      <td>0.0000000000</td>\n", "      <td>90.0000000000</td>\n", "      <td>0.0000000000</td>\n", "      <td>0.0000000000</td>\n", "      <td>1</td>\n", "      <td>-57.4911221333</td>\n", "      <td>28.0752848978</td>\n", "      <td>754.1563864081</td>\n", "      <td>12.0</td>\n", "      <td>0.0000000000</td>\n", "      <td>-0.0000000000</td>\n", "      <td>0.0000000000</td>\n", "      <td>0.0000000000</td>\n", "      <td>-0.0000000000</td>\n", "      <td>0.0000000000</td>\n", "      <td>0.0000000000</td>\n", "      <td>0.0000000000</td>\n", "      <td>0.0000000000</td>\n", "      <td>112.7359359875</td>\n", "      <td>143.0809908267</td>\n", "      <td>19.7306556945</td>\n", "      <td>19.7306556945</td>\n", "      <td>90.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>26</th>\n", "      <td>260229.684838</td>\n", "      <td>8</td>\n", "      <td>0</td>\n", "      <td>0.0000000000</td>\n", "      <td>0.5000000000</td>\n", "      <td>0.5000000000</td>\n", "      <td>0.0000000000</td>\n", "      <td>3d c++</td>\n", "      <td>160.0000000000</td>\n", "      <td>120.0000000000</td>\n", "      <td>0.0000000000</td>\n", "      <td>0.0000000000</td>\n", "      <td>90.0000000000</td>\n", "      <td>0.0000000000</td>\n", "      <td>0.0000000000</td>\n", "      <td>1</td>\n", "      <td>-57.4911221333</td>\n", "      <td>28.0752848978</td>\n", "      <td>754.1563864081</td>\n", "      <td>12.0</td>\n", "      <td>0.0000000000</td>\n", "      <td>-0.0000000000</td>\n", "      <td>0.0000000000</td>\n", "      <td>0.0000000000</td>\n", "      <td>-0.0000000000</td>\n", "      <td>0.0000000000</td>\n", "      <td>0.0000000000</td>\n", "      <td>0.0000000000</td>\n", "      <td>0.0000000000</td>\n", "      <td>112.7359359875</td>\n", "      <td>143.0809908267</td>\n", "      <td>19.7306556945</td>\n", "      <td>19.7306556945</td>\n", "      <td>90.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>27</th>\n", "      <td>260229.693238</td>\n", "      <td>9</td>\n", "      <td>0</td>\n", "      <td>0.0000000000</td>\n", "      <td>0.5000000000</td>\n", "      <td>0.5000000000</td>\n", "      <td>0.0000000000</td>\n", "      <td>3d c++</td>\n", "      <td>160.0000000000</td>\n", "      <td>120.0000000000</td>\n", "      <td>0.0000000000</td>\n", "      <td>0.0000000000</td>\n", "      <td>90.0000000000</td>\n", "      <td>0.0000000000</td>\n", "      <td>0.0000000000</td>\n", "      <td>1</td>\n", "      <td>-57.4911221333</td>\n", "      <td>28.0752848978</td>\n", "      <td>754.1563864081</td>\n", "      <td>12.0</td>\n", "      <td>0.0000000000</td>\n", "      <td>-0.0000000000</td>\n", "      <td>0.0000000000</td>\n", "      <td>0.0000000000</td>\n", "      <td>-0.0000000000</td>\n", "      <td>0.0000000000</td>\n", "      <td>0.0000000000</td>\n", "      <td>0.0000000000</td>\n", "      <td>0.0000000000</td>\n", "      <td>112.7359359875</td>\n", "      <td>143.0809908267</td>\n", "      <td>19.7306556945</td>\n", "      <td>19.7306556945</td>\n", "      <td>90.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>28</th>\n", "      <td>260229.701638</td>\n", "      <td>9</td>\n", "      <td>0</td>\n", "      <td>0.0000000000</td>\n", "      <td>0.5000000000</td>\n", "      <td>0.5000000000</td>\n", "      <td>0.0000000000</td>\n", "      <td>3d c++</td>\n", "      <td>160.0000000000</td>\n", "      <td>120.0000000000</td>\n", "      <td>0.0000000000</td>\n", "      <td>0.0000000000</td>\n", "      <td>90.0000000000</td>\n", "      <td>0.0000000000</td>\n", "      <td>0.0000000000</td>\n", "      <td>1</td>\n", "      <td>-57.4911221333</td>\n", "      <td>28.0752848978</td>\n", "      <td>754.1563864081</td>\n", "      <td>12.0</td>\n", "      <td>0.0000000000</td>\n", "      <td>-0.0000000000</td>\n", "      <td>0.0000000000</td>\n", "      <td>0.0000000000</td>\n", "      <td>-0.0000000000</td>\n", "      <td>0.0000000000</td>\n", "      <td>0.0000000000</td>\n", "      <td>0.0000000000</td>\n", "      <td>0.0000000000</td>\n", "      <td>112.7359359875</td>\n", "      <td>143.0809908267</td>\n", "      <td>19.7306556945</td>\n", "      <td>19.7306556945</td>\n", "      <td>90.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>29</th>\n", "      <td>260229.710038</td>\n", "      <td>9</td>\n", "      <td>0</td>\n", "      <td>0.9962267124</td>\n", "      <td>0.3613892703</td>\n", "      <td>0.4233949946</td>\n", "      <td>44.7212072101</td>\n", "      <td>3d c++</td>\n", "      <td>115.6445664819</td>\n", "      <td>138.3852012984</td>\n", "      <td>35.7816664419</td>\n", "      <td>44.7212072101</td>\n", "      <td>-58.3010590344</td>\n", "      <td>53.6716426016</td>\n", "      <td>0.9184168476</td>\n", "      <td>1</td>\n", "      <td>-57.4911221333</td>\n", "      <td>28.0752848978</td>\n", "      <td>754.1563864081</td>\n", "      <td>12.0</td>\n", "      <td>-52.9970305314</td>\n", "      <td>21.6905188053</td>\n", "      <td>745.0438728899</td>\n", "      <td>0.3745076335</td>\n", "      <td>-0.5320638410</td>\n", "      <td>-0.7593761265</td>\n", "      <td>26.8358213008</td>\n", "      <td>1.0097601236</td>\n", "      <td>-1.1126211604</td>\n", "      <td>112.7359359875</td>\n", "      <td>143.0809908267</td>\n", "      <td>19.7306556945</td>\n", "      <td>19.7306556945</td>\n", "      <td>90.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>30</th>\n", "      <td>260229.718438</td>\n", "      <td>9</td>\n", "      <td>0</td>\n", "      <td>0.8299052680</td>\n", "      <td>0.3611930158</td>\n", "      <td>0.4239705428</td>\n", "      <td>44.5313281117</td>\n", "      <td>3d c++</td>\n", "      <td>115.5817650543</td>\n", "      <td>138.2470697285</td>\n", "      <td>35.3374596090</td>\n", "      <td>44.5313281117</td>\n", "      <td>-59.5798599856</td>\n", "      <td>53.4450390977</td>\n", "      <td>0.9203868279</td>\n", "      <td>1</td>\n", "      <td>-57.4911221333</td>\n", "      <td>28.0752848978</td>\n", "      <td>754.1563864081</td>\n", "      <td>12.0</td>\n", "      <td>-53.0874560204</td>\n", "      <td>21.5231101238</td>\n", "      <td>745.1188684690</td>\n", "      <td>0.3669721761</td>\n", "      <td>-0.5460145645</td>\n", "      <td>-0.7531264949</td>\n", "      <td>26.7225195488</td>\n", "      <td>0.9931966676</td>\n", "      <td>-1.1173885292</td>\n", "      <td>112.7359359875</td>\n", "      <td>143.0809908267</td>\n", "      <td>19.7306556945</td>\n", "      <td>19.7306556945</td>\n", "      <td>90.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>31</th>\n", "      <td>260229.726838</td>\n", "      <td>10</td>\n", "      <td>0</td>\n", "      <td>0.8438979385</td>\n", "      <td>0.3610203835</td>\n", "      <td>0.4239182024</td>\n", "      <td>44.7566062783</td>\n", "      <td>3d c++</td>\n", "      <td>115.5265227056</td>\n", "      <td>138.2596314344</td>\n", "      <td>35.6371738448</td>\n", "      <td>44.7566062783</td>\n", "      <td>-60.0013811033</td>\n", "      <td>53.7114407917</td>\n", "      <td>0.9222307744</td>\n", "      <td>1</td>\n", "      <td>-57.4911221333</td>\n", "      <td>28.0752848978</td>\n", "      <td>754.1563864081</td>\n", "      <td>12.0</td>\n", "      <td>-53.1512918313</td>\n", "      <td>21.5320564245</td>\n", "      <td>745.0815842562</td>\n", "      <td>0.3616525252</td>\n", "      <td>-0.5452690394</td>\n", "      <td>-0.7562335127</td>\n", "      <td>26.8557203959</td>\n", "      <td>0.9940862961</td>\n", "      <td>-1.1247170071</td>\n", "      <td>112.7359359875</td>\n", "      <td>143.0809908267</td>\n", "      <td>19.7306556945</td>\n", "      <td>19.7306556945</td>\n", "      <td>90.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>32</th>\n", "      <td>260229.735238</td>\n", "      <td>10</td>\n", "      <td>0</td>\n", "      <td>0.8565511767</td>\n", "      <td>0.3610999126</td>\n", "      <td>0.4236166220</td>\n", "      <td>44.8551897086</td>\n", "      <td>3d c++</td>\n", "      <td>115.5519720429</td>\n", "      <td>138.3320107175</td>\n", "      <td>35.8854549196</td>\n", "      <td>44.8551897086</td>\n", "      <td>-59.4020243396</td>\n", "      <td>53.8285587004</td>\n", "      <td>0.9207269834</td>\n", "      <td>1</td>\n", "      <td>-57.4911221333</td>\n", "      <td>28.0752848978</td>\n", "      <td>754.1563864081</td>\n", "      <td>12.0</td>\n", "      <td>-53.1129993628</td>\n", "      <td>21.6194582831</td>\n", "      <td>745.0374860507</td>\n", "      <td>0.3648435642</td>\n", "      <td>-0.5379855512</td>\n", "      <td>-0.7599083631</td>\n", "      <td>26.9142793502</td>\n", "      <td>1.0027507940</td>\n", "      <td>-1.1231827187</td>\n", "      <td>112.7359359875</td>\n", "      <td>143.0809908267</td>\n", "      <td>19.7306556945</td>\n", "      <td>19.7306556945</td>\n", "      <td>90.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>33</th>\n", "      <td>260229.743638</td>\n", "      <td>10</td>\n", "      <td>0</td>\n", "      <td>0.9143075032</td>\n", "      <td>0.3649516768</td>\n", "      <td>0.4250905258</td>\n", "      <td>45.0386065650</td>\n", "      <td>3d c++</td>\n", "      <td>116.7845365732</td>\n", "      <td>137.9782737996</td>\n", "      <td>32.0955593110</td>\n", "      <td>45.0386065650</td>\n", "      <td>-51.5893427367</td>\n", "      <td>54.1499054568</td>\n", "      <td>0.7604069093</td>\n", "      <td>1</td>\n", "      <td>-57.4911221333</td>\n", "      <td>28.0752848978</td>\n", "      <td>754.1563864081</td>\n", "      <td>12.0</td>\n", "      <td>-51.6563074178</td>\n", "      <td>21.2509759705</td>\n", "      <td>746.1949955183</td>\n", "      <td>0.4862345596</td>\n", "      <td>-0.5686924106</td>\n", "      <td>-0.6634492408</td>\n", "      <td>27.0749527284</td>\n", "      <td>0.9658810240</td>\n", "      <td>-0.9383365874</td>\n", "      <td>112.7359359875</td>\n", "      <td>143.0809908267</td>\n", "      <td>19.7306556945</td>\n", "      <td>19.7306556945</td>\n", "      <td>90.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>34</th>\n", "      <td>260229.752038</td>\n", "      <td>10</td>\n", "      <td>0</td>\n", "      <td>0.0000000000</td>\n", "      <td>0.5000000000</td>\n", "      <td>0.5000000000</td>\n", "      <td>0.0000000000</td>\n", "      <td>3d c++</td>\n", "      <td>160.0000000000</td>\n", "      <td>120.0000000000</td>\n", "      <td>0.0000000000</td>\n", "      <td>0.0000000000</td>\n", "      <td>90.0000000000</td>\n", "      <td>0.0000000000</td>\n", "      <td>0.7604069093</td>\n", "      <td>1</td>\n", "      <td>-57.4911221333</td>\n", "      <td>28.0752848978</td>\n", "      <td>754.1563864081</td>\n", "      <td>12.0</td>\n", "      <td>0.0000000000</td>\n", "      <td>-0.0000000000</td>\n", "      <td>0.0000000000</td>\n", "      <td>0.0000000000</td>\n", "      <td>-0.0000000000</td>\n", "      <td>0.0000000000</td>\n", "      <td>0.0000000000</td>\n", "      <td>0.0000000000</td>\n", "      <td>0.0000000000</td>\n", "      <td>112.7359359875</td>\n", "      <td>143.0809908267</td>\n", "      <td>19.7306556945</td>\n", "      <td>19.7306556945</td>\n", "      <td>90.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>35</th>\n", "      <td>260229.760438</td>\n", "      <td>11</td>\n", "      <td>0</td>\n", "      <td>0.0000000000</td>\n", "      <td>0.4130360603</td>\n", "      <td>0.4173611959</td>\n", "      <td>48.4634704620</td>\n", "      <td>3d c++</td>\n", "      <td>132.1715393067</td>\n", "      <td>139.8333129883</td>\n", "      <td>34.7094726584</td>\n", "      <td>48.4634704620</td>\n", "      <td>-77.8656997681</td>\n", "      <td>2.0000000000</td>\n", "      <td>0.7604069093</td>\n", "      <td>1</td>\n", "      <td>-57.4911221333</td>\n", "      <td>28.0752848978</td>\n", "      <td>754.1563864081</td>\n", "      <td>12.0</td>\n", "      <td>-1.1439377632</td>\n", "      <td>0.7998330313</td>\n", "      <td>25.6141647827</td>\n", "      <td>0.1796871084</td>\n", "      <td>-0.7042083705</td>\n", "      <td>-0.6868792572</td>\n", "      <td>1.0000000000</td>\n", "      <td>0.7894887799</td>\n", "      <td>-1.3149308530</td>\n", "      <td>112.7359359875</td>\n", "      <td>143.0809908267</td>\n", "      <td>19.7306556945</td>\n", "      <td>19.7306556945</td>\n", "      <td>90.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>36</th>\n", "      <td>260229.768838</td>\n", "      <td>11</td>\n", "      <td>0</td>\n", "      <td>0.0000000000</td>\n", "      <td>0.4426527023</td>\n", "      <td>0.4091581980</td>\n", "      <td>45.7661209020</td>\n", "      <td>3d c++</td>\n", "      <td>141.6488647462</td>\n", "      <td>141.8020324706</td>\n", "      <td>34.5704193050</td>\n", "      <td>45.7661209020</td>\n", "      <td>-78.3986282349</td>\n", "      <td>2.0000000000</td>\n", "      <td>0.7604069093</td>\n", "      <td>1</td>\n", "      <td>-57.4911221333</td>\n", "      <td>28.0752848978</td>\n", "      <td>754.1563864081</td>\n", "      <td>12.0</td>\n", "      <td>-0.7977320594</td>\n", "      <td>0.9347457440</td>\n", "      <td>27.1094271159</td>\n", "      <td>0.1547767800</td>\n", "      <td>-0.6679820371</td>\n", "      <td>-0.7279039405</td>\n", "      <td>1.0000000000</td>\n", "      <td>0.8393025201</td>\n", "      <td>-1.3612831823</td>\n", "      <td>112.7359359875</td>\n", "      <td>143.0809908267</td>\n", "      <td>19.7306556945</td>\n", "      <td>19.7306556945</td>\n", "      <td>90.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>37</th>\n", "      <td>260229.777238</td>\n", "      <td>11</td>\n", "      <td>0</td>\n", "      <td>0.0000000000</td>\n", "      <td>0.5000000000</td>\n", "      <td>0.5000000000</td>\n", "      <td>0.0000000000</td>\n", "      <td>3d c++</td>\n", "      <td>160.0000000000</td>\n", "      <td>120.0000000000</td>\n", "      <td>0.0000000000</td>\n", "      <td>0.0000000000</td>\n", "      <td>90.0000000000</td>\n", "      <td>0.0000000000</td>\n", "      <td>0.7604069093</td>\n", "      <td>1</td>\n", "      <td>-57.4911221333</td>\n", "      <td>28.0752848978</td>\n", "      <td>754.1563864081</td>\n", "      <td>12.0</td>\n", "      <td>0.0000000000</td>\n", "      <td>-0.0000000000</td>\n", "      <td>0.0000000000</td>\n", "      <td>0.0000000000</td>\n", "      <td>-0.0000000000</td>\n", "      <td>0.0000000000</td>\n", "      <td>0.0000000000</td>\n", "      <td>0.0000000000</td>\n", "      <td>0.0000000000</td>\n", "      <td>112.7359359875</td>\n", "      <td>143.0809908267</td>\n", "      <td>19.7306556945</td>\n", "      <td>19.7306556945</td>\n", "      <td>90.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>38</th>\n", "      <td>260229.785638</td>\n", "      <td>11</td>\n", "      <td>0</td>\n", "      <td>0.0000000000</td>\n", "      <td>0.5000000000</td>\n", "      <td>0.5000000000</td>\n", "      <td>0.0000000000</td>\n", "      <td>3d c++</td>\n", "      <td>160.0000000000</td>\n", "      <td>120.0000000000</td>\n", "      <td>0.0000000000</td>\n", "      <td>0.0000000000</td>\n", "      <td>90.0000000000</td>\n", "      <td>0.0000000000</td>\n", "      <td>0.7604069093</td>\n", "      <td>1</td>\n", "      <td>-57.4911221333</td>\n", "      <td>28.0752848978</td>\n", "      <td>754.1563864081</td>\n", "      <td>12.0</td>\n", "      <td>0.0000000000</td>\n", "      <td>-0.0000000000</td>\n", "      <td>0.0000000000</td>\n", "      <td>0.0000000000</td>\n", "      <td>-0.0000000000</td>\n", "      <td>0.0000000000</td>\n", "      <td>0.0000000000</td>\n", "      <td>0.0000000000</td>\n", "      <td>0.0000000000</td>\n", "      <td>112.7359359875</td>\n", "      <td>143.0809908267</td>\n", "      <td>19.7306556945</td>\n", "      <td>19.7306556945</td>\n", "      <td>90.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>39</th>\n", "      <td>260229.794038</td>\n", "      <td>12</td>\n", "      <td>0</td>\n", "      <td>0.0000000000</td>\n", "      <td>0.7405343533</td>\n", "      <td>0.2577756246</td>\n", "      <td>15.5832605455</td>\n", "      <td>3d c++</td>\n", "      <td>236.9709930419</td>\n", "      <td>178.1338500976</td>\n", "      <td>11.1331882543</td>\n", "      <td>15.5832605455</td>\n", "      <td>-22.7709503174</td>\n", "      <td>2.0000000000</td>\n", "      <td>0.7604069093</td>\n", "      <td>1</td>\n", "      <td>-57.4911221333</td>\n", "      <td>28.0752848978</td>\n", "      <td>754.1563864081</td>\n", "      <td>12.0</td>\n", "      <td>9.9714732818</td>\n", "      <td>7.5240373295</td>\n", "      <td>80.2826677375</td>\n", "      <td>0.5540003370</td>\n", "      <td>-0.3278195814</td>\n", "      <td>-0.7652567860</td>\n", "      <td>1.0000000000</td>\n", "      <td>1.2368016340</td>\n", "      <td>-0.9441829908</td>\n", "      <td>112.7359359875</td>\n", "      <td>143.0809908267</td>\n", "      <td>19.7306556945</td>\n", "      <td>19.7306556945</td>\n", "      <td>90.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>40</th>\n", "      <td>260229.802438</td>\n", "      <td>12</td>\n", "      <td>0</td>\n", "      <td>0.0000000000</td>\n", "      <td>0.5177763462</td>\n", "      <td>0.4011275609</td>\n", "      <td>43.1504592945</td>\n", "      <td>3d c++</td>\n", "      <td>165.6884307862</td>\n", "      <td>143.7293853759</td>\n", "      <td>33.1232109108</td>\n", "      <td>43.1504592945</td>\n", "      <td>78.8464889526</td>\n", "      <td>2.0000000000</td>\n", "      <td>0.7604069093</td>\n", "      <td>1</td>\n", "      <td>-57.4911221333</td>\n", "      <td>28.0752848978</td>\n", "      <td>754.1563864081</td>\n", "      <td>12.0</td>\n", "      <td>0.2668195564</td>\n", "      <td>1.1161106207</td>\n", "      <td>28.7431845020</td>\n", "      <td>0.1170007704</td>\n", "      <td>0.5994522232</td>\n", "      <td>-0.7918130157</td>\n", "      <td>1.0000000000</td>\n", "      <td>2.2136128903</td>\n", "      <td>-1.4240947407</td>\n", "      <td>112.7359359875</td>\n", "      <td>143.0809908267</td>\n", "      <td>19.7306556945</td>\n", "      <td>19.7306556945</td>\n", "      <td>90.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>41</th>\n", "      <td>260229.810838</td>\n", "      <td>12</td>\n", "      <td>0</td>\n", "      <td>0.0000000000</td>\n", "      <td>0.5209532738</td>\n", "      <td>0.4008720398</td>\n", "      <td>43.9954833997</td>\n", "      <td>3d c++</td>\n", "      <td>166.7050476086</td>\n", "      <td>143.7907104489</td>\n", "      <td>33.3101577768</td>\n", "      <td>43.9954833997</td>\n", "      <td>76.6512680052</td>\n", "      <td>2.0000000000</td>\n", "      <td>0.7604069093</td>\n", "      <td>1</td>\n", "      <td>-57.4911221333</td>\n", "      <td>28.0752848978</td>\n", "      <td>754.1563864081</td>\n", "      <td>12.0</td>\n", "      <td>0.3086910270</td>\n", "      <td>1.0980720894</td>\n", "      <td>28.1916372308</td>\n", "      <td>0.1426966822</td>\n", "      <td>0.6065537816</td>\n", "      <td>-0.7821318092</td>\n", "      <td>1.0000000000</td>\n", "      <td>2.2225150786</td>\n", "      <td>-1.3903353368</td>\n", "      <td>112.7359359875</td>\n", "      <td>143.0809908267</td>\n", "      <td>19.7306556945</td>\n", "      <td>19.7306556945</td>\n", "      <td>90.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>42</th>\n", "      <td>260229.819238</td>\n", "      <td>12</td>\n", "      <td>0</td>\n", "      <td>0.0000000000</td>\n", "      <td>0.5188312531</td>\n", "      <td>0.3997919718</td>\n", "      <td>44.0171737680</td>\n", "      <td>3d c++</td>\n", "      <td>166.0260009762</td>\n", "      <td>144.0499267578</td>\n", "      <td>33.7969284064</td>\n", "      <td>44.0171737680</td>\n", "      <td>80.0602951050</td>\n", "      <td>2.0000000000</td>\n", "      <td>0.7604069093</td>\n", "      <td>1</td>\n", "      <td>-57.4911221333</td>\n", "      <td>28.0752848978</td>\n", "      <td>754.1563864081</td>\n", "      <td>12.0</td>\n", "      <td>0.2766422774</td>\n", "      <td>1.1094082553</td>\n", "      <td>28.1775444564</td>\n", "      <td>0.1032420116</td>\n", "      <td>0.6012982750</td>\n", "      <td>-0.7923266192</td>\n", "      <td>1.0000000000</td>\n", "      <td>2.2159212688</td>\n", "      <td>-1.4412240200</td>\n", "      <td>112.7359359875</td>\n", "      <td>143.0809908267</td>\n", "      <td>19.7306556945</td>\n", "      <td>19.7306556945</td>\n", "      <td>90.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>43</th>\n", "      <td>260229.827638</td>\n", "      <td>13</td>\n", "      <td>0</td>\n", "      <td>0.0000000000</td>\n", "      <td>0.5205333710</td>\n", "      <td>0.4000488281</td>\n", "      <td>44.3857269282</td>\n", "      <td>3d c++</td>\n", "      <td>166.5706787116</td>\n", "      <td>143.9882812499</td>\n", "      <td>33.8191375729</td>\n", "      <td>44.3857269282</td>\n", "      <td>81.2568206786</td>\n", "      <td>2.0000000000</td>\n", "      <td>0.7604069093</td>\n", "      <td>1</td>\n", "      <td>-57.4911221333</td>\n", "      <td>28.0752848978</td>\n", "      <td>754.1563864081</td>\n", "      <td>12.0</td>\n", "      <td>0.2985516780</td>\n", "      <td>1.0978396204</td>\n", "      <td>27.9440211218</td>\n", "      <td>0.0905435582</td>\n", "      <td>0.6106279662</td>\n", "      <td>-0.7867244441</td>\n", "      <td>1.0000000000</td>\n", "      <td>2.2276496447</td>\n", "      <td>-1.4562111757</td>\n", "      <td>112.7359359875</td>\n", "      <td>143.0809908267</td>\n", "      <td>19.7306556945</td>\n", "      <td>19.7306556945</td>\n", "      <td>90.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>44</th>\n", "      <td>260229.836038</td>\n", "      <td>13</td>\n", "      <td>0</td>\n", "      <td>0.0000000000</td>\n", "      <td>0.5213183403</td>\n", "      <td>0.3998810450</td>\n", "      <td>44.2527732692</td>\n", "      <td>3d c++</td>\n", "      <td>166.8218688960</td>\n", "      <td>144.0285491945</td>\n", "      <td>33.5592651248</td>\n", "      <td>44.2527732692</td>\n", "      <td>80.1550445557</td>\n", "      <td>2.0000000000</td>\n", "      <td>0.7604069093</td>\n", "      <td>1</td>\n", "      <td>-57.4911221333</td>\n", "      <td>28.0752848978</td>\n", "      <td>754.1563864081</td>\n", "      <td>12.0</td>\n", "      <td>0.3111271045</td>\n", "      <td>1.1028404699</td>\n", "      <td>28.0279810815</td>\n", "      <td>0.1032640577</td>\n", "      <td>0.6128273231</td>\n", "      <td>-0.7834406209</td>\n", "      <td>1.0000000000</td>\n", "      <td>2.2304298831</td>\n", "      <td>-1.4397433860</td>\n", "      <td>112.7359359875</td>\n", "      <td>143.0809908267</td>\n", "      <td>19.7306556945</td>\n", "      <td>19.7306556945</td>\n", "      <td>90.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>45</th>\n", "      <td>260229.844438</td>\n", "      <td>13</td>\n", "      <td>0</td>\n", "      <td>0.0168686972</td>\n", "      <td>0.3471217195</td>\n", "      <td>0.4125695418</td>\n", "      <td>19.2677671182</td>\n", "      <td>3d c++</td>\n", "      <td>111.0789502366</td>\n", "      <td>140.9833099569</td>\n", "      <td>18.4677038804</td>\n", "      <td>19.2677671182</td>\n", "      <td>53.2346877920</td>\n", "      <td>22.9977136875</td>\n", "      <td>0.7604069093</td>\n", "      <td>1</td>\n", "      <td>-57.4911221333</td>\n", "      <td>28.0752848978</td>\n", "      <td>754.1563864081</td>\n", "      <td>12.0</td>\n", "      <td>-58.6088237859</td>\n", "      <td>25.0890330332</td>\n", "      <td>742.5877632049</td>\n", "      <td>-0.0931418044</td>\n", "      <td>-0.2488543220</td>\n", "      <td>-0.9640519336</td>\n", "      <td>11.4988568438</td>\n", "      <td>1.3192991423</td>\n", "      <td>-1.6671123079</td>\n", "      <td>112.7359359875</td>\n", "      <td>143.0809908267</td>\n", "      <td>19.7306556945</td>\n", "      <td>19.7306556945</td>\n", "      <td>90.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>46</th>\n", "      <td>260229.852838</td>\n", "      <td>13</td>\n", "      <td>0</td>\n", "      <td>0.0000000000</td>\n", "      <td>0.5201590538</td>\n", "      <td>0.3993202845</td>\n", "      <td>43.9419555685</td>\n", "      <td>3d c++</td>\n", "      <td>166.4508972171</td>\n", "      <td>144.1631317138</td>\n", "      <td>33.8862075821</td>\n", "      <td>43.9419555685</td>\n", "      <td>78.6627273559</td>\n", "      <td>2.0000000000</td>\n", "      <td>0.7604069093</td>\n", "      <td>1</td>\n", "      <td>-57.4911221333</td>\n", "      <td>28.0752848978</td>\n", "      <td>754.1563864081</td>\n", "      <td>12.0</td>\n", "      <td>0.2968484576</td>\n", "      <td>1.1162768847</td>\n", "      <td>28.2256340241</td>\n", "      <td>0.1172412858</td>\n", "      <td>0.5942104143</td>\n", "      <td>-0.7957188350</td>\n", "      <td>1.0000000000</td>\n", "      <td>2.2070799254</td>\n", "      <td>-1.4245087665</td>\n", "      <td>112.7359359875</td>\n", "      <td>143.0809908267</td>\n", "      <td>19.7306556945</td>\n", "      <td>19.7306556945</td>\n", "      <td>90.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>47</th>\n", "      <td>260229.861238</td>\n", "      <td>14</td>\n", "      <td>0</td>\n", "      <td>0.0667007762</td>\n", "      <td>0.3467183599</td>\n", "      <td>0.4102105384</td>\n", "      <td>19.3976171409</td>\n", "      <td>3d c++</td>\n", "      <td>110.9498751821</td>\n", "      <td>141.5494707947</td>\n", "      <td>18.7775176505</td>\n", "      <td>19.3976171409</td>\n", "      <td>43.5195261105</td>\n", "      <td>23.1564683107</td>\n", "      <td>0.7604069093</td>\n", "      <td>1</td>\n", "      <td>-57.4911221333</td>\n", "      <td>28.0752848978</td>\n", "      <td>754.1563864081</td>\n", "      <td>12.0</td>\n", "      <td>-58.7552148995</td>\n", "      <td>25.7712841201</td>\n", "      <td>742.4476866555</td>\n", "      <td>-0.1053410639</td>\n", "      <td>-0.1920000648</td>\n", "      <td>-0.9757249794</td>\n", "      <td>11.5782341554</td>\n", "      <td>1.3775966033</td>\n", "      <td>-1.6783416167</td>\n", "      <td>112.7359359875</td>\n", "      <td>143.0809908267</td>\n", "      <td>19.7306556945</td>\n", "      <td>19.7306556945</td>\n", "      <td>90.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>48</th>\n", "      <td>260229.869638</td>\n", "      <td>14</td>\n", "      <td>0</td>\n", "      <td>0.0899701805</td>\n", "      <td>0.3398915777</td>\n", "      <td>0.4189423455</td>\n", "      <td>19.2670055810</td>\n", "      <td>3d c++</td>\n", "      <td>108.7653048741</td>\n", "      <td>139.4538370839</td>\n", "      <td>16.0386424360</td>\n", "      <td>19.2670055810</td>\n", "      <td>43.1177904887</td>\n", "      <td>23.0450855179</td>\n", "      <td>0.7604069093</td>\n", "      <td>1</td>\n", "      <td>-57.4911221333</td>\n", "      <td>28.0752848978</td>\n", "      <td>754.1563864081</td>\n", "      <td>12.0</td>\n", "      <td>-61.5222618226</td>\n", "      <td>23.2797839317</td>\n", "      <td>743.9214910354</td>\n", "      <td>-0.3359283074</td>\n", "      <td>-0.3996250805</td>\n", "      <td>-0.8529079477</td>\n", "      <td>11.5225427589</td>\n", "      <td>1.1596885149</td>\n", "      <td>-1.9460004326</td>\n", "      <td>112.7359359875</td>\n", "      <td>143.0809908267</td>\n", "      <td>19.7306556945</td>\n", "      <td>19.7306556945</td>\n", "      <td>90.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>49</th>\n", "      <td>260229.878038</td>\n", "      <td>14</td>\n", "      <td>0</td>\n", "      <td>0.0364256728</td>\n", "      <td>0.3376279978</td>\n", "      <td>0.4267960018</td>\n", "      <td>20.7432664868</td>\n", "      <td>3d c++</td>\n", "      <td>108.0409593092</td>\n", "      <td>137.5689595684</td>\n", "      <td>13.8690249558</td>\n", "      <td>20.7432664868</td>\n", "      <td>49.9647071742</td>\n", "      <td>24.8680139066</td>\n", "      <td>0.7604069093</td>\n", "      <td>1</td>\n", "      <td>-57.4911221333</td>\n", "      <td>28.0752848978</td>\n", "      <td>754.1563864081</td>\n", "      <td>12.0</td>\n", "      <td>-62.5576233282</td>\n", "      <td>21.0481966873</td>\n", "      <td>745.8527367933</td>\n", "      <td>-0.4222084329</td>\n", "      <td>-0.5855906842</td>\n", "      <td>-0.6919708012</td>\n", "      <td>12.4340069533</td>\n", "      <td>0.9451877669</td>\n", "      <td>-2.1186482332</td>\n", "      <td>112.7359359875</td>\n", "      <td>143.0809908267</td>\n", "      <td>19.7306556945</td>\n", "      <td>19.7306556945</td>\n", "      <td>90.0</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["    world_timestamp  world_index  eye_id    confidence    norm_pos_x  \\\n", "0     260229.466438            2       0  0.0000000000  0.5000000000   \n", "1     260229.474838            2       0  0.0000000000  0.5000000000   \n", "2     260229.483238            2       0  0.0000000000  0.5000000000   \n", "3     260229.491638            3       0  0.0000000000  0.5000000000   \n", "4     260229.500038            3       0  0.0000000000  0.5000000000   \n", "5     260229.508438            3       0  0.0000000000  0.5000000000   \n", "6     260229.516838            3       0  0.0000000000  0.5000000000   \n", "7     260229.525238            4       0  0.0000000000  0.5000000000   \n", "8     260229.533638            4       0  0.0000000000  0.5000000000   \n", "9     260229.542038            4       0  0.0000000000  0.5000000000   \n", "10    260229.550438            4       0  0.0000000000  0.5000000000   \n", "11    260229.558838            5       0  0.0000000000  0.5000000000   \n", "12    260229.567238            5       0  0.0000000000  0.5000000000   \n", "13    260229.575638            5       0  0.0000000000  0.5000000000   \n", "14    260229.584038            5       0  0.0000000000  0.5000000000   \n", "15    260229.592438            6       0  0.0000000000  0.5000000000   \n", "16    260229.600838            6       0  0.0000000000  0.5000000000   \n", "17    260229.609238            6       0  0.0000000000  0.5000000000   \n", "18    260229.617638            6       0  0.0000000000  0.5000000000   \n", "19    260229.626038            7       0  0.0000000000  0.5000000000   \n", "20    260229.634438            7       0  0.0000000000  0.5000000000   \n", "21    260229.642838            7       0  0.0000000000  0.5000000000   \n", "22    260229.651238            7       0  0.0000000000  0.5000000000   \n", "23    260229.659638            8       0  0.0000000000  0.5000000000   \n", "24    260229.668038            8       0  0.0000000000  0.5000000000   \n", "25    260229.676438            8       0  0.0000000000  0.5000000000   \n", "26    260229.684838            8       0  0.0000000000  0.5000000000   \n", "27    260229.693238            9       0  0.0000000000  0.5000000000   \n", "28    260229.701638            9       0  0.0000000000  0.5000000000   \n", "29    260229.710038            9       0  0.9962267124  0.3613892703   \n", "30    260229.718438            9       0  0.8299052680  0.3611930158   \n", "31    260229.726838           10       0  0.8438979385  0.3610203835   \n", "32    260229.735238           10       0  0.8565511767  0.3610999126   \n", "33    260229.743638           10       0  0.9143075032  0.3649516768   \n", "34    260229.752038           10       0  0.0000000000  0.5000000000   \n", "35    260229.760438           11       0  0.0000000000  0.4130360603   \n", "36    260229.768838           11       0  0.0000000000  0.4426527023   \n", "37    260229.777238           11       0  0.0000000000  0.5000000000   \n", "38    260229.785638           11       0  0.0000000000  0.5000000000   \n", "39    260229.794038           12       0  0.0000000000  0.7405343533   \n", "40    260229.802438           12       0  0.0000000000  0.5177763462   \n", "41    260229.810838           12       0  0.0000000000  0.5209532738   \n", "42    260229.819238           12       0  0.0000000000  0.5188312531   \n", "43    260229.827638           13       0  0.0000000000  0.5205333710   \n", "44    260229.836038           13       0  0.0000000000  0.5213183403   \n", "45    260229.844438           13       0  0.0168686972  0.3471217195   \n", "46    260229.852838           13       0  0.0000000000  0.5201590538   \n", "47    260229.861238           14       0  0.0667007762  0.3467183599   \n", "48    260229.869638           14       0  0.0899701805  0.3398915777   \n", "49    260229.878038           14       0  0.0364256728  0.3376279978   \n", "\n", "      norm_pos_y       diameter  method  ellipse_center_x  ellipse_center_y  \\\n", "0   0.5000000000   0.0000000000  3d c++    160.0000000000    120.0000000000   \n", "1   0.5000000000   0.0000000000  3d c++    160.0000000000    120.0000000000   \n", "2   0.5000000000   0.0000000000  3d c++    160.0000000000    120.0000000000   \n", "3   0.5000000000   0.0000000000  3d c++    160.0000000000    120.0000000000   \n", "4   0.5000000000   0.0000000000  3d c++    160.0000000000    120.0000000000   \n", "5   0.5000000000   0.0000000000  3d c++    160.0000000000    120.0000000000   \n", "6   0.5000000000   0.0000000000  3d c++    160.0000000000    120.0000000000   \n", "7   0.5000000000   0.0000000000  3d c++    160.0000000000    120.0000000000   \n", "8   0.5000000000   0.0000000000  3d c++    160.0000000000    120.0000000000   \n", "9   0.5000000000   0.0000000000  3d c++    160.0000000000    120.0000000000   \n", "10  0.5000000000   0.0000000000  3d c++    160.0000000000    120.0000000000   \n", "11  0.5000000000   0.0000000000  3d c++    160.0000000000    120.0000000000   \n", "12  0.5000000000   0.0000000000  3d c++    160.0000000000    120.0000000000   \n", "13  0.5000000000   0.0000000000  3d c++    160.0000000000    120.0000000000   \n", "14  0.5000000000   0.0000000000  3d c++    160.0000000000    120.0000000000   \n", "15  0.5000000000   0.0000000000  3d c++    160.0000000000    120.0000000000   \n", "16  0.5000000000   0.0000000000  3d c++    160.0000000000    120.0000000000   \n", "17  0.5000000000   0.0000000000  3d c++    160.0000000000    120.0000000000   \n", "18  0.5000000000   0.0000000000  3d c++    160.0000000000    120.0000000000   \n", "19  0.5000000000   0.0000000000  3d c++    160.0000000000    120.0000000000   \n", "20  0.5000000000   0.0000000000  3d c++    160.0000000000    120.0000000000   \n", "21  0.5000000000   0.0000000000  3d c++    160.0000000000    120.0000000000   \n", "22  0.5000000000   0.0000000000  3d c++    160.0000000000    120.0000000000   \n", "23  0.5000000000   0.0000000000  3d c++    160.0000000000    120.0000000000   \n", "24  0.5000000000   0.0000000000  3d c++    160.0000000000    120.0000000000   \n", "25  0.5000000000   0.0000000000  3d c++    160.0000000000    120.0000000000   \n", "26  0.5000000000   0.0000000000  3d c++    160.0000000000    120.0000000000   \n", "27  0.5000000000   0.0000000000  3d c++    160.0000000000    120.0000000000   \n", "28  0.5000000000   0.0000000000  3d c++    160.0000000000    120.0000000000   \n", "29  0.4233949946  44.7212072101  3d c++    115.6445664819    138.3852012984   \n", "30  0.4239705428  44.5313281117  3d c++    115.5817650543    138.2470697285   \n", "31  0.4239182024  44.7566062783  3d c++    115.5265227056    138.2596314344   \n", "32  0.4236166220  44.8551897086  3d c++    115.5519720429    138.3320107175   \n", "33  0.4250905258  45.0386065650  3d c++    116.7845365732    137.9782737996   \n", "34  0.5000000000   0.0000000000  3d c++    160.0000000000    120.0000000000   \n", "35  0.4173611959  48.4634704620  3d c++    132.1715393067    139.8333129883   \n", "36  0.4091581980  45.7661209020  3d c++    141.6488647462    141.8020324706   \n", "37  0.5000000000   0.0000000000  3d c++    160.0000000000    120.0000000000   \n", "38  0.5000000000   0.0000000000  3d c++    160.0000000000    120.0000000000   \n", "39  0.2577756246  15.5832605455  3d c++    236.9709930419    178.1338500976   \n", "40  0.4011275609  43.1504592945  3d c++    165.6884307862    143.7293853759   \n", "41  0.4008720398  43.9954833997  3d c++    166.7050476086    143.7907104489   \n", "42  0.3997919718  44.0171737680  3d c++    166.0260009762    144.0499267578   \n", "43  0.4000488281  44.3857269282  3d c++    166.5706787116    143.9882812499   \n", "44  0.3998810450  44.2527732692  3d c++    166.8218688960    144.0285491945   \n", "45  0.4125695418  19.2677671182  3d c++    111.0789502366    140.9833099569   \n", "46  0.3993202845  43.9419555685  3d c++    166.4508972171    144.1631317138   \n", "47  0.4102105384  19.3976171409  3d c++    110.9498751821    141.5494707947   \n", "48  0.4189423455  19.2670055810  3d c++    108.7653048741    139.4538370839   \n", "49  0.4267960018  20.7432664868  3d c++    108.0409593092    137.5689595684   \n", "\n", "    ellipse_axis_a  ellipse_axis_b  ellipse_angle    diameter_3d  \\\n", "0     0.0000000000    0.0000000000  90.0000000000   0.0000000000   \n", "1     0.0000000000    0.0000000000  90.0000000000   0.0000000000   \n", "2     0.0000000000    0.0000000000  90.0000000000   0.0000000000   \n", "3     0.0000000000    0.0000000000  90.0000000000   0.0000000000   \n", "4     0.0000000000    0.0000000000  90.0000000000   0.0000000000   \n", "5     0.0000000000    0.0000000000  90.0000000000   0.0000000000   \n", "6     0.0000000000    0.0000000000  90.0000000000   0.0000000000   \n", "7     0.0000000000    0.0000000000  90.0000000000   0.0000000000   \n", "8     0.0000000000    0.0000000000  90.0000000000   0.0000000000   \n", "9     0.0000000000    0.0000000000  90.0000000000   0.0000000000   \n", "10    0.0000000000    0.0000000000  90.0000000000   0.0000000000   \n", "11    0.0000000000    0.0000000000  90.0000000000   0.0000000000   \n", "12    0.0000000000    0.0000000000  90.0000000000   0.0000000000   \n", "13    0.0000000000    0.0000000000  90.0000000000   0.0000000000   \n", "14    0.0000000000    0.0000000000  90.0000000000   0.0000000000   \n", "15    0.0000000000    0.0000000000  90.0000000000   0.0000000000   \n", "16    0.0000000000    0.0000000000  90.0000000000   0.0000000000   \n", "17    0.0000000000    0.0000000000  90.0000000000   0.0000000000   \n", "18    0.0000000000    0.0000000000  90.0000000000   0.0000000000   \n", "19    0.0000000000    0.0000000000  90.0000000000   0.0000000000   \n", "20    0.0000000000    0.0000000000  90.0000000000   0.0000000000   \n", "21    0.0000000000    0.0000000000  90.0000000000   0.0000000000   \n", "22    0.0000000000    0.0000000000  90.0000000000   0.0000000000   \n", "23    0.0000000000    0.0000000000  90.0000000000   0.0000000000   \n", "24    0.0000000000    0.0000000000  90.0000000000   0.0000000000   \n", "25    0.0000000000    0.0000000000  90.0000000000   0.0000000000   \n", "26    0.0000000000    0.0000000000  90.0000000000   0.0000000000   \n", "27    0.0000000000    0.0000000000  90.0000000000   0.0000000000   \n", "28    0.0000000000    0.0000000000  90.0000000000   0.0000000000   \n", "29   35.7816664419   44.7212072101 -58.3010590344  53.6716426016   \n", "30   35.3374596090   44.5313281117 -59.5798599856  53.4450390977   \n", "31   35.6371738448   44.7566062783 -60.0013811033  53.7114407917   \n", "32   35.8854549196   44.8551897086 -59.4020243396  53.8285587004   \n", "33   32.0955593110   45.0386065650 -51.5893427367  54.1499054568   \n", "34    0.0000000000    0.0000000000  90.0000000000   0.0000000000   \n", "35   34.7094726584   48.4634704620 -77.8656997681   2.0000000000   \n", "36   34.5704193050   45.7661209020 -78.3986282349   2.0000000000   \n", "37    0.0000000000    0.0000000000  90.0000000000   0.0000000000   \n", "38    0.0000000000    0.0000000000  90.0000000000   0.0000000000   \n", "39   11.1331882543   15.5832605455 -22.7709503174   2.0000000000   \n", "40   33.1232109108   43.1504592945  78.8464889526   2.0000000000   \n", "41   33.3101577768   43.9954833997  76.6512680052   2.0000000000   \n", "42   33.7969284064   44.0171737680  80.0602951050   2.0000000000   \n", "43   33.8191375729   44.3857269282  81.2568206786   2.0000000000   \n", "44   33.5592651248   44.2527732692  80.1550445557   2.0000000000   \n", "45   18.4677038804   19.2677671182  53.2346877920  22.9977136875   \n", "46   33.8862075821   43.9419555685  78.6627273559   2.0000000000   \n", "47   18.7775176505   19.3976171409  43.5195261105  23.1564683107   \n", "48   16.0386424360   19.2670055810  43.1177904887  23.0450855179   \n", "49   13.8690249558   20.7432664868  49.9647071742  24.8680139066   \n", "\n", "    model_confidence  model_id  sphere_center_x  sphere_center_y  \\\n", "0       0.0000000000         1     0.0000000000    -0.0000000000   \n", "1       0.0000000000         1   -57.4911221333    28.0752848978   \n", "2       0.0000000000         1   -57.4911221333    28.0752848978   \n", "3       0.0000000000         1   -57.4911221333    28.0752848978   \n", "4       0.0000000000         1   -57.4911221333    28.0752848978   \n", "5       0.0000000000         1   -57.4911221333    28.0752848978   \n", "6       0.0000000000         1   -57.4911221333    28.0752848978   \n", "7       0.0000000000         1   -57.4911221333    28.0752848978   \n", "8       0.0000000000         1   -57.4911221333    28.0752848978   \n", "9       0.0000000000         1   -57.4911221333    28.0752848978   \n", "10      0.0000000000         1   -57.4911221333    28.0752848978   \n", "11      0.0000000000         1   -57.4911221333    28.0752848978   \n", "12      0.0000000000         1   -57.4911221333    28.0752848978   \n", "13      0.0000000000         1   -57.4911221333    28.0752848978   \n", "14      0.0000000000         1   -57.4911221333    28.0752848978   \n", "15      0.0000000000         1   -57.4911221333    28.0752848978   \n", "16      0.0000000000         1   -57.4911221333    28.0752848978   \n", "17      0.0000000000         1   -57.4911221333    28.0752848978   \n", "18      0.0000000000         1   -57.4911221333    28.0752848978   \n", "19      0.0000000000         1   -57.4911221333    28.0752848978   \n", "20      0.0000000000         1   -57.4911221333    28.0752848978   \n", "21      0.0000000000         1   -57.4911221333    28.0752848978   \n", "22      0.0000000000         1   -57.4911221333    28.0752848978   \n", "23      0.0000000000         1   -57.4911221333    28.0752848978   \n", "24      0.0000000000         1   -57.4911221333    28.0752848978   \n", "25      0.0000000000         1   -57.4911221333    28.0752848978   \n", "26      0.0000000000         1   -57.4911221333    28.0752848978   \n", "27      0.0000000000         1   -57.4911221333    28.0752848978   \n", "28      0.0000000000         1   -57.4911221333    28.0752848978   \n", "29      0.9184168476         1   -57.4911221333    28.0752848978   \n", "30      0.9203868279         1   -57.4911221333    28.0752848978   \n", "31      0.9222307744         1   -57.4911221333    28.0752848978   \n", "32      0.9207269834         1   -57.4911221333    28.0752848978   \n", "33      0.7604069093         1   -57.4911221333    28.0752848978   \n", "34      0.7604069093         1   -57.4911221333    28.0752848978   \n", "35      0.7604069093         1   -57.4911221333    28.0752848978   \n", "36      0.7604069093         1   -57.4911221333    28.0752848978   \n", "37      0.7604069093         1   -57.4911221333    28.0752848978   \n", "38      0.7604069093         1   -57.4911221333    28.0752848978   \n", "39      0.7604069093         1   -57.4911221333    28.0752848978   \n", "40      0.7604069093         1   -57.4911221333    28.0752848978   \n", "41      0.7604069093         1   -57.4911221333    28.0752848978   \n", "42      0.7604069093         1   -57.4911221333    28.0752848978   \n", "43      0.7604069093         1   -57.4911221333    28.0752848978   \n", "44      0.7604069093         1   -57.4911221333    28.0752848978   \n", "45      0.7604069093         1   -57.4911221333    28.0752848978   \n", "46      0.7604069093         1   -57.4911221333    28.0752848978   \n", "47      0.7604069093         1   -57.4911221333    28.0752848978   \n", "48      0.7604069093         1   -57.4911221333    28.0752848978   \n", "49      0.7604069093         1   -57.4911221333    28.0752848978   \n", "\n", "    sphere_center_z  sphere_radius  circle_3d_center_x  circle_3d_center_y  \\\n", "0      0.0000000000            0.0        0.0000000000       -0.0000000000   \n", "1    754.1563864081           12.0        0.0000000000       -0.0000000000   \n", "2    754.1563864081           12.0        0.0000000000       -0.0000000000   \n", "3    754.1563864081           12.0        0.0000000000       -0.0000000000   \n", "4    754.1563864081           12.0        0.0000000000       -0.0000000000   \n", "5    754.1563864081           12.0        0.0000000000       -0.0000000000   \n", "6    754.1563864081           12.0        0.0000000000       -0.0000000000   \n", "7    754.1563864081           12.0        0.0000000000       -0.0000000000   \n", "8    754.1563864081           12.0        0.0000000000       -0.0000000000   \n", "9    754.1563864081           12.0        0.0000000000       -0.0000000000   \n", "10   754.1563864081           12.0        0.0000000000       -0.0000000000   \n", "11   754.1563864081           12.0        0.0000000000       -0.0000000000   \n", "12   754.1563864081           12.0        0.0000000000       -0.0000000000   \n", "13   754.1563864081           12.0        0.0000000000       -0.0000000000   \n", "14   754.1563864081           12.0        0.0000000000       -0.0000000000   \n", "15   754.1563864081           12.0        0.0000000000       -0.0000000000   \n", "16   754.1563864081           12.0        0.0000000000       -0.0000000000   \n", "17   754.1563864081           12.0        0.0000000000       -0.0000000000   \n", "18   754.1563864081           12.0        0.0000000000       -0.0000000000   \n", "19   754.1563864081           12.0        0.0000000000       -0.0000000000   \n", "20   754.1563864081           12.0        0.0000000000       -0.0000000000   \n", "21   754.1563864081           12.0        0.0000000000       -0.0000000000   \n", "22   754.1563864081           12.0        0.0000000000       -0.0000000000   \n", "23   754.1563864081           12.0        0.0000000000       -0.0000000000   \n", "24   754.1563864081           12.0        0.0000000000       -0.0000000000   \n", "25   754.1563864081           12.0        0.0000000000       -0.0000000000   \n", "26   754.1563864081           12.0        0.0000000000       -0.0000000000   \n", "27   754.1563864081           12.0        0.0000000000       -0.0000000000   \n", "28   754.1563864081           12.0        0.0000000000       -0.0000000000   \n", "29   754.1563864081           12.0      -52.9970305314       21.6905188053   \n", "30   754.1563864081           12.0      -53.0874560204       21.5231101238   \n", "31   754.1563864081           12.0      -53.1512918313       21.5320564245   \n", "32   754.1563864081           12.0      -53.1129993628       21.6194582831   \n", "33   754.1563864081           12.0      -51.6563074178       21.2509759705   \n", "34   754.1563864081           12.0        0.0000000000       -0.0000000000   \n", "35   754.1563864081           12.0       -1.1439377632        0.7998330313   \n", "36   754.1563864081           12.0       -0.7977320594        0.9347457440   \n", "37   754.1563864081           12.0        0.0000000000       -0.0000000000   \n", "38   754.1563864081           12.0        0.0000000000       -0.0000000000   \n", "39   754.1563864081           12.0        9.9714732818        7.5240373295   \n", "40   754.1563864081           12.0        0.2668195564        1.1161106207   \n", "41   754.1563864081           12.0        0.3086910270        1.0980720894   \n", "42   754.1563864081           12.0        0.2766422774        1.1094082553   \n", "43   754.1563864081           12.0        0.2985516780        1.0978396204   \n", "44   754.1563864081           12.0        0.3111271045        1.1028404699   \n", "45   754.1563864081           12.0      -58.6088237859       25.0890330332   \n", "46   754.1563864081           12.0        0.2968484576        1.1162768847   \n", "47   754.1563864081           12.0      -58.7552148995       25.7712841201   \n", "48   754.1563864081           12.0      -61.5222618226       23.2797839317   \n", "49   754.1563864081           12.0      -62.5576233282       21.0481966873   \n", "\n", "    circle_3d_center_z  circle_3d_normal_x  circle_3d_normal_y  \\\n", "0         0.0000000000        0.0000000000       -0.0000000000   \n", "1         0.0000000000        0.0000000000       -0.0000000000   \n", "2         0.0000000000        0.0000000000       -0.0000000000   \n", "3         0.0000000000        0.0000000000       -0.0000000000   \n", "4         0.0000000000        0.0000000000       -0.0000000000   \n", "5         0.0000000000        0.0000000000       -0.0000000000   \n", "6         0.0000000000        0.0000000000       -0.0000000000   \n", "7         0.0000000000        0.0000000000       -0.0000000000   \n", "8         0.0000000000        0.0000000000       -0.0000000000   \n", "9         0.0000000000        0.0000000000       -0.0000000000   \n", "10        0.0000000000        0.0000000000       -0.0000000000   \n", "11        0.0000000000        0.0000000000       -0.0000000000   \n", "12        0.0000000000        0.0000000000       -0.0000000000   \n", "13        0.0000000000        0.0000000000       -0.0000000000   \n", "14        0.0000000000        0.0000000000       -0.0000000000   \n", "15        0.0000000000        0.0000000000       -0.0000000000   \n", "16        0.0000000000        0.0000000000       -0.0000000000   \n", "17        0.0000000000        0.0000000000       -0.0000000000   \n", "18        0.0000000000        0.0000000000       -0.0000000000   \n", "19        0.0000000000        0.0000000000       -0.0000000000   \n", "20        0.0000000000        0.0000000000       -0.0000000000   \n", "21        0.0000000000        0.0000000000       -0.0000000000   \n", "22        0.0000000000        0.0000000000       -0.0000000000   \n", "23        0.0000000000        0.0000000000       -0.0000000000   \n", "24        0.0000000000        0.0000000000       -0.0000000000   \n", "25        0.0000000000        0.0000000000       -0.0000000000   \n", "26        0.0000000000        0.0000000000       -0.0000000000   \n", "27        0.0000000000        0.0000000000       -0.0000000000   \n", "28        0.0000000000        0.0000000000       -0.0000000000   \n", "29      745.0438728899        0.3745076335       -0.5320638410   \n", "30      745.1188684690        0.3669721761       -0.5460145645   \n", "31      745.0815842562        0.3616525252       -0.5452690394   \n", "32      745.0374860507        0.3648435642       -0.5379855512   \n", "33      746.1949955183        0.4862345596       -0.5686924106   \n", "34        0.0000000000        0.0000000000       -0.0000000000   \n", "35       25.6141647827        0.1796871084       -0.7042083705   \n", "36       27.1094271159        0.1547767800       -0.6679820371   \n", "37        0.0000000000        0.0000000000       -0.0000000000   \n", "38        0.0000000000        0.0000000000       -0.0000000000   \n", "39       80.2826677375        0.5540003370       -0.3278195814   \n", "40       28.7431845020        0.1170007704        0.5994522232   \n", "41       28.1916372308        0.1426966822        0.6065537816   \n", "42       28.1775444564        0.1032420116        0.6012982750   \n", "43       27.9440211218        0.0905435582        0.6106279662   \n", "44       28.0279810815        0.1032640577        0.6128273231   \n", "45      742.5877632049       -0.0931418044       -0.2488543220   \n", "46       28.2256340241        0.1172412858        0.5942104143   \n", "47      742.4476866555       -0.1053410639       -0.1920000648   \n", "48      743.9214910354       -0.3359283074       -0.3996250805   \n", "49      745.8527367933       -0.4222084329       -0.5855906842   \n", "\n", "    circle_3d_normal_z  circle_3d_radius         theta           phi  \\\n", "0         0.0000000000      0.0000000000  0.0000000000  0.0000000000   \n", "1         0.0000000000      0.0000000000  0.0000000000  0.0000000000   \n", "2         0.0000000000      0.0000000000  0.0000000000  0.0000000000   \n", "3         0.0000000000      0.0000000000  0.0000000000  0.0000000000   \n", "4         0.0000000000      0.0000000000  0.0000000000  0.0000000000   \n", "5         0.0000000000      0.0000000000  0.0000000000  0.0000000000   \n", "6         0.0000000000      0.0000000000  0.0000000000  0.0000000000   \n", "7         0.0000000000      0.0000000000  0.0000000000  0.0000000000   \n", "8         0.0000000000      0.0000000000  0.0000000000  0.0000000000   \n", "9         0.0000000000      0.0000000000  0.0000000000  0.0000000000   \n", "10        0.0000000000      0.0000000000  0.0000000000  0.0000000000   \n", "11        0.0000000000      0.0000000000  0.0000000000  0.0000000000   \n", "12        0.0000000000      0.0000000000  0.0000000000  0.0000000000   \n", "13        0.0000000000      0.0000000000  0.0000000000  0.0000000000   \n", "14        0.0000000000      0.0000000000  0.0000000000  0.0000000000   \n", "15        0.0000000000      0.0000000000  0.0000000000  0.0000000000   \n", "16        0.0000000000      0.0000000000  0.0000000000  0.0000000000   \n", "17        0.0000000000      0.0000000000  0.0000000000  0.0000000000   \n", "18        0.0000000000      0.0000000000  0.0000000000  0.0000000000   \n", "19        0.0000000000      0.0000000000  0.0000000000  0.0000000000   \n", "20        0.0000000000      0.0000000000  0.0000000000  0.0000000000   \n", "21        0.0000000000      0.0000000000  0.0000000000  0.0000000000   \n", "22        0.0000000000      0.0000000000  0.0000000000  0.0000000000   \n", "23        0.0000000000      0.0000000000  0.0000000000  0.0000000000   \n", "24        0.0000000000      0.0000000000  0.0000000000  0.0000000000   \n", "25        0.0000000000      0.0000000000  0.0000000000  0.0000000000   \n", "26        0.0000000000      0.0000000000  0.0000000000  0.0000000000   \n", "27        0.0000000000      0.0000000000  0.0000000000  0.0000000000   \n", "28        0.0000000000      0.0000000000  0.0000000000  0.0000000000   \n", "29       -0.7593761265     26.8358213008  1.0097601236 -1.1126211604   \n", "30       -0.7531264949     26.7225195488  0.9931966676 -1.1173885292   \n", "31       -0.7562335127     26.8557203959  0.9940862961 -1.1247170071   \n", "32       -0.7599083631     26.9142793502  1.0027507940 -1.1231827187   \n", "33       -0.6634492408     27.0749527284  0.9658810240 -0.9383365874   \n", "34        0.0000000000      0.0000000000  0.0000000000  0.0000000000   \n", "35       -0.6868792572      1.0000000000  0.7894887799 -1.3149308530   \n", "36       -0.7279039405      1.0000000000  0.8393025201 -1.3612831823   \n", "37        0.0000000000      0.0000000000  0.0000000000  0.0000000000   \n", "38        0.0000000000      0.0000000000  0.0000000000  0.0000000000   \n", "39       -0.7652567860      1.0000000000  1.2368016340 -0.9441829908   \n", "40       -0.7918130157      1.0000000000  2.2136128903 -1.4240947407   \n", "41       -0.7821318092      1.0000000000  2.2225150786 -1.3903353368   \n", "42       -0.7923266192      1.0000000000  2.2159212688 -1.4412240200   \n", "43       -0.7867244441      1.0000000000  2.2276496447 -1.4562111757   \n", "44       -0.7834406209      1.0000000000  2.2304298831 -1.4397433860   \n", "45       -0.9640519336     11.4988568438  1.3192991423 -1.6671123079   \n", "46       -0.7957188350      1.0000000000  2.2070799254 -1.4245087665   \n", "47       -0.9757249794     11.5782341554  1.3775966033 -1.6783416167   \n", "48       -0.8529079477     11.5225427589  1.1596885149 -1.9460004326   \n", "49       -0.6919708012     12.4340069533  0.9451877669 -2.1186482332   \n", "\n", "    projected_sphere_center_x  projected_sphere_center_y  \\\n", "0                0.0000000000               0.0000000000   \n", "1              112.7359359875             143.0809908267   \n", "2              112.7359359875             143.0809908267   \n", "3              112.7359359875             143.0809908267   \n", "4              112.7359359875             143.0809908267   \n", "5              112.7359359875             143.0809908267   \n", "6              112.7359359875             143.0809908267   \n", "7              112.7359359875             143.0809908267   \n", "8              112.7359359875             143.0809908267   \n", "9              112.7359359875             143.0809908267   \n", "10             112.7359359875             143.0809908267   \n", "11             112.7359359875             143.0809908267   \n", "12             112.7359359875             143.0809908267   \n", "13             112.7359359875             143.0809908267   \n", "14             112.7359359875             143.0809908267   \n", "15             112.7359359875             143.0809908267   \n", "16             112.7359359875             143.0809908267   \n", "17             112.7359359875             143.0809908267   \n", "18             112.7359359875             143.0809908267   \n", "19             112.7359359875             143.0809908267   \n", "20             112.7359359875             143.0809908267   \n", "21             112.7359359875             143.0809908267   \n", "22             112.7359359875             143.0809908267   \n", "23             112.7359359875             143.0809908267   \n", "24             112.7359359875             143.0809908267   \n", "25             112.7359359875             143.0809908267   \n", "26             112.7359359875             143.0809908267   \n", "27             112.7359359875             143.0809908267   \n", "28             112.7359359875             143.0809908267   \n", "29             112.7359359875             143.0809908267   \n", "30             112.7359359875             143.0809908267   \n", "31             112.7359359875             143.0809908267   \n", "32             112.7359359875             143.0809908267   \n", "33             112.7359359875             143.0809908267   \n", "34             112.7359359875             143.0809908267   \n", "35             112.7359359875             143.0809908267   \n", "36             112.7359359875             143.0809908267   \n", "37             112.7359359875             143.0809908267   \n", "38             112.7359359875             143.0809908267   \n", "39             112.7359359875             143.0809908267   \n", "40             112.7359359875             143.0809908267   \n", "41             112.7359359875             143.0809908267   \n", "42             112.7359359875             143.0809908267   \n", "43             112.7359359875             143.0809908267   \n", "44             112.7359359875             143.0809908267   \n", "45             112.7359359875             143.0809908267   \n", "46             112.7359359875             143.0809908267   \n", "47             112.7359359875             143.0809908267   \n", "48             112.7359359875             143.0809908267   \n", "49             112.7359359875             143.0809908267   \n", "\n", "    projected_sphere_axis_a  projected_sphere_axis_b  projected_sphere_angle  \n", "0              0.0000000000             0.0000000000                    90.0  \n", "1             19.7306556945            19.7306556945                    90.0  \n", "2             19.7306556945            19.7306556945                    90.0  \n", "3             19.7306556945            19.7306556945                    90.0  \n", "4             19.7306556945            19.7306556945                    90.0  \n", "5             19.7306556945            19.7306556945                    90.0  \n", "6             19.7306556945            19.7306556945                    90.0  \n", "7             19.7306556945            19.7306556945                    90.0  \n", "8             19.7306556945            19.7306556945                    90.0  \n", "9             19.7306556945            19.7306556945                    90.0  \n", "10            19.7306556945            19.7306556945                    90.0  \n", "11            19.7306556945            19.7306556945                    90.0  \n", "12            19.7306556945            19.7306556945                    90.0  \n", "13            19.7306556945            19.7306556945                    90.0  \n", "14            19.7306556945            19.7306556945                    90.0  \n", "15            19.7306556945            19.7306556945                    90.0  \n", "16            19.7306556945            19.7306556945                    90.0  \n", "17            19.7306556945            19.7306556945                    90.0  \n", "18            19.7306556945            19.7306556945                    90.0  \n", "19            19.7306556945            19.7306556945                    90.0  \n", "20            19.7306556945            19.7306556945                    90.0  \n", "21            19.7306556945            19.7306556945                    90.0  \n", "22            19.7306556945            19.7306556945                    90.0  \n", "23            19.7306556945            19.7306556945                    90.0  \n", "24            19.7306556945            19.7306556945                    90.0  \n", "25            19.7306556945            19.7306556945                    90.0  \n", "26            19.7306556945            19.7306556945                    90.0  \n", "27            19.7306556945            19.7306556945                    90.0  \n", "28            19.7306556945            19.7306556945                    90.0  \n", "29            19.7306556945            19.7306556945                    90.0  \n", "30            19.7306556945            19.7306556945                    90.0  \n", "31            19.7306556945            19.7306556945                    90.0  \n", "32            19.7306556945            19.7306556945                    90.0  \n", "33            19.7306556945            19.7306556945                    90.0  \n", "34            19.7306556945            19.7306556945                    90.0  \n", "35            19.7306556945            19.7306556945                    90.0  \n", "36            19.7306556945            19.7306556945                    90.0  \n", "37            19.7306556945            19.7306556945                    90.0  \n", "38            19.7306556945            19.7306556945                    90.0  \n", "39            19.7306556945            19.7306556945                    90.0  \n", "40            19.7306556945            19.7306556945                    90.0  \n", "41            19.7306556945            19.7306556945                    90.0  \n", "42            19.7306556945            19.7306556945                    90.0  \n", "43            19.7306556945            19.7306556945                    90.0  \n", "44            19.7306556945            19.7306556945                    90.0  \n", "45            19.7306556945            19.7306556945                    90.0  \n", "46            19.7306556945            19.7306556945                    90.0  \n", "47            19.7306556945            19.7306556945                    90.0  \n", "48            19.7306556945            19.7306556945                    90.0  \n", "49            19.7306556945            19.7306556945                    90.0  "]}, "execution_count": 14, "metadata": {}, "output_type": "execute_result"}], "source": ["df2_trimmed.head(50)"]}, {"cell_type": "code", "execution_count": 15, "id": "7c61c20c", "metadata": {}, "outputs": [], "source": ["columns = ['world_index', 'diameter']\n", "df2_trimmed = df2_trimmed[columns]"]}, {"cell_type": "code", "execution_count": 23, "id": "ee6989df", "metadata": {"scrolled": true}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>world_index</th>\n", "      <th>diameter</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>27</th>\n", "      <td>9</td>\n", "      <td>0.0000000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>28</th>\n", "      <td>9</td>\n", "      <td>0.0000000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>29</th>\n", "      <td>9</td>\n", "      <td>44.7212072101</td>\n", "    </tr>\n", "    <tr>\n", "      <th>30</th>\n", "      <td>9</td>\n", "      <td>44.5313281117</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["    world_index       diameter\n", "27            9   0.0000000000\n", "28            9   0.0000000000\n", "29            9  44.7212072101\n", "30            9  44.5313281117"]}, "execution_count": 23, "metadata": {}, "output_type": "execute_result"}], "source": ["df2_trimmed[df2_trimmed['world_index'] == 9]"]}, {"cell_type": "code", "execution_count": 18, "id": "2a6ef165", "metadata": {"collapsed": true}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["      world_index       diameter\n", "0               2   0.0000000000\n", "1               3   0.0000000000\n", "2               4   0.0000000000\n", "3               5   0.0000000000\n", "4               6   0.0000000000\n", "5               7   0.0000000000\n", "6               8   0.0000000000\n", "7               9  22.3131338305\n", "8              10  33.6626006380\n", "9              11  23.5573978410\n", "10             12  36.6865942519\n", "11             13  37.9620557210\n", "12             14  25.8993804075\n", "13             15  23.9813361956\n", "14             16  31.0969727311\n", "15             17  23.2405886844\n", "16             18  23.6695215168\n", "17             19  22.9213589605\n", "18             20  29.5277400649\n", "19             21  21.8019695546\n", "20             22  13.5065879163\n", "21             23  21.0258201482\n", "22             24  21.0028377769\n", "23             25  29.7560926555\n", "24             26  20.9957441150\n", "25             27  29.9646939229\n", "26             28  38.3151915501\n", "27             29  29.3853308442\n", "28             30  29.8989684646\n", "29             31  39.4820054423\n", "30             32   3.9773979737\n", "31             33  36.9727668735\n", "32             34  28.6757778156\n", "33             35  29.3257524109\n", "34             36  29.1274066881\n", "35             37  28.9802776910\n", "36             38  28.5524770275\n", "37             39  39.4996976109\n", "38             40  28.9420614971\n", "39             41  40.0903882843\n", "40             42  25.6424493784\n", "41             43  41.5614595508\n", "42             44  49.8717431999\n", "43             45  50.0061483319\n", "44             46  50.0532960878\n", "45             47  50.2502021792\n", "46             48  50.3266000228\n", "47             49  47.1848208093\n", "48             50  46.7851255602\n", "49             52  48.1492237097\n", "50             53  48.1658978978\n", "51             54  48.3059050747\n", "52             55  48.3569230114\n", "53             56  48.0265422093\n", "54             57  47.6894291364\n", "55             58  46.9143939958\n", "56             59  46.1165332592\n", "57             60  45.6353963960\n", "58             61  45.4345845234\n", "59             62  46.1093029315\n", "60             63  45.9321888239\n", "61             64  45.7669271526\n", "62             65  46.0392308474\n", "63             66  46.3332628373\n", "64             67  46.4375680425\n", "65             68  46.6337449259\n", "66             69  47.0988472014\n", "67             70  47.1755191835\n", "68             71  47.4451815386\n", "69             72  47.7457162728\n", "70             73  47.9771854734\n", "71             74  48.4276893103\n", "72             75  48.8698111255\n", "73             76  48.9358355887\n", "74             77  49.4672682901\n", "75             78  49.6590243333\n", "76             79  48.9246768289\n", "77             80  50.0683571848\n", "78             81  50.1588811783\n", "79             82  51.1772933353\n", "80             83  51.2319792421\n", "81             84  51.4814977160\n", "82             85  51.4910019377\n", "83             86  51.8207697415\n", "84             87  51.6086710881\n", "85             88  51.7318724350\n", "86             89  51.7953088569\n", "87             90  51.6260672561\n", "88             91  51.3808173419\n", "89             92  50.9706318363\n", "90             94  50.4939753909\n", "91             95  49.9362027707\n", "92             96  49.5854450916\n", "93             97  49.1338669765\n", "94             98  48.2973277279\n", "95             99  48.5588494245\n", "96            100  48.2918276006\n", "97            101  48.1463925536\n", "98            102  47.9457133409\n", "99            103  47.8632282839\n", "100           104  47.7906677817\n", "101           105  47.9126760503\n", "102           106  48.2681758503\n", "103           107  48.3699838433\n", "104           108  48.3405266239\n", "105           109  48.9911256913\n", "106           110  47.9111713187\n", "107           111  47.5151917956\n", "108           112  48.2678365270\n", "109           113  48.7372113071\n", "110           114  49.3150799632\n", "111           115  50.2612257659\n", "112           116  50.3030690102\n", "113           117  49.6170393226\n", "114           118  50.0568897222\n", "115           119  49.7557826216\n", "116           120  48.8060458176\n", "117           121  49.1837006274\n", "118           122  49.5572549190\n", "119           124  49.0832623408\n", "120           125  48.3207466609\n", "121           126  46.7280451006\n", "122           127  45.3106108223\n", "123           128  43.7674326706\n", "124           129  42.8218266411\n", "125           130  42.0665577245\n", "126           131  41.5719280685\n", "127           132  41.0286511529\n", "128           133  40.8728764601\n", "129           134  40.6954766271\n", "130           135  40.5350835590\n", "131           136  40.8127620405\n", "132           137  42.1937552048\n", "133           138  41.6506297464\n", "134           139  42.1294076982\n", "135           140  42.4973652054\n", "136           141  43.0626677203\n", "137           142  43.6518464680\n", "138           143  43.9765847603\n", "139           144  44.4405754776\n", "140           145  45.0231548619\n", "141           146  45.6499858083\n", "142           147  44.9953181480\n", "143           148  43.9417654134\n", "144           149  44.3727162514\n", "145           150  45.3899160231\n", "146           151  45.0369188995\n", "147           152  45.1144055715\n", "148           153  44.9359503963\n", "149           154  45.0209064238\n", "150           155  44.5371191249\n", "151           156  44.3009341449\n", "152           157  44.2311095022\n", "153           158  44.9340618267\n", "154           159  44.8681049551\n", "155           160  44.4067391467\n", "156           161  44.0385300280\n", "157           162  44.4433197967\n", "158           163  44.3851701020\n", "159           164  44.7840628318\n", "160           165  44.8931272040\n", "161           166  44.8930316737\n", "162           167  44.7582321329\n", "163           168  44.2247942779\n", "164           169  44.1626776722\n", "165           170  43.7003756495\n", "166           171  43.5248535107\n", "167           172  44.3857172684\n", "168           173  42.3452075011\n", "169           174  42.0163732633\n", "170           175  41.7730705473\n", "171           176  41.3771695799\n", "172           177  41.3818006690\n", "173           178  41.2228399647\n", "174           179  41.0735784444\n", "175           180  40.6860666504\n", "176           181  40.7062510922\n", "177           182  40.7406245692\n", "178           183  40.4045441186\n", "179           184  40.6185565518\n", "180           185  40.3331852598\n", "181           186  40.1585678621\n", "182           187  40.2539971101\n", "183           188  39.9291384620\n", "184           189  39.5446776354\n", "185           190  39.3726273124\n", "186           191  40.4019926438\n", "187           192  41.3144534555\n", "188           193  40.6776956436\n", "189           194  38.1642582286\n", "190           195  38.4356709615\n", "191           196  38.5214494746\n", "192           197  38.6923514701\n", "193           198  39.0647521492\n", "194           199  39.6184079994\n", "195           200  39.5401381809\n", "196           201  39.5122254324\n", "197           202  40.1041037258\n", "198           203  39.9306534138\n", "199           204  40.1247547676\n", "200           205  39.8256545978\n", "201           206  39.3283097458\n", "202           207  39.7239619622\n", "203           208  40.3758241080\n", "204           209  40.2504416605\n", "205           210  39.8285042452\n", "206           211  40.1197200968\n", "207           212  40.3460858739\n", "208           213  40.9372869320\n", "209           214  40.9196423020\n", "210           215  40.9517175805\n", "211           216  41.2751743440\n", "212           217  41.0798186618\n", "213           218  41.5213489162\n", "214           219  41.4237761254\n", "215           220  41.3892510414\n", "216           221  41.2757763540\n", "217           222  41.0027090683\n", "218           223  40.9100084735\n", "219           224  41.0081438784\n", "220           225  40.8491074320\n", "221           226  40.8496515710\n", "222           227  40.9765449774\n", "223           228  40.8495530493\n", "224           229  39.2355547615\n", "225           230  39.3147159831\n", "226           231  39.7130360032\n", "227           232  40.1284914836\n", "228           233  40.3516055167\n", "229           234  40.6474087342\n", "230           235  40.7115519094\n", "231           236  41.0497697074\n", "232           237  41.2206551658\n", "233           238  41.3304345918\n", "234           239  41.2372898466\n", "235           240  41.3865830814\n", "236           241  41.1964695522\n", "237           242  41.2793795737\n", "238           243  41.3347162156\n", "239           244  41.3858615575\n", "240           245  42.0340217711\n", "241           246  42.4414583513\n", "242           247  43.8727712280\n", "243           248  44.0620019861\n", "244           249  44.4223648338\n", "245           250  44.8922025157\n", "246           251  43.7597132588\n", "247           252  45.0365391001\n", "248           253  44.9683681855\n", "249           255  43.8886507660\n", "250           256  45.2091317378\n", "251           257  45.2843263723\n", "252           258  45.3852725611\n", "253           259  46.0678242690\n", "254           260  45.9469479178\n", "255           261  45.8640936609\n", "256           262  46.0236967036\n", "257           263  46.3625278510\n", "258           264  46.6160676985\n", "259           265  47.1161993705\n", "260           266  47.3308600735\n", "261           267  47.8041985284\n", "262           268  48.1853461500\n", "263           269  48.1352785285\n", "264           270  48.3663752680\n", "265           271  48.4849266521\n", "266           272  48.4030337091\n", "267           273  47.8698828659\n", "268           274  46.5910773957\n", "269           275  46.7871292251\n", "270           276  47.1213755493\n", "271           277  47.2327039740\n", "272           278  47.8634758422\n", "273           279  47.9173992394\n", "274           280  48.2319410996\n", "275           281  48.3743623568\n", "276           282  48.1402548661\n", "277           283  47.4087597630\n", "278           284  47.1597252576\n", "279           285  46.9482238902\n", "280           286  47.4343673521\n", "281           287  47.1471208240\n", "282           288  46.8548611486\n", "283           289  46.5825622190\n", "284           290  46.5653476580\n", "285           291  46.5990434094\n", "286           292  46.4172615278\n", "287           293  46.7465282224\n", "288           294  46.6615350537\n", "289           295  46.9203865168\n", "290           296  47.0283901378\n", "291           297  46.4065878444\n", "292           298  46.8164988413\n", "293           299  47.0588508494\n", "294           300  46.6673853851\n", "295           301  46.4611741442\n", "296           302  46.5712230558\n", "297           303  46.2627205714\n", "298           304  45.9853732422\n", "299           305  45.7857411300\n", "300           306  45.6493653567\n", "301           307  45.0842751597\n", "302           308  45.3992115102\n", "303           309  45.3594545720\n", "304           310  44.9974629835\n", "305           311  44.9730478460\n", "306           312  45.0188431465\n", "307           313  44.8798658849\n", "308           314  45.0368731398\n", "309           315  44.9819115043\n", "310           316  44.8928076243\n", "311           317  44.7157396593\n", "312           318  44.7031391051\n", "313           319  44.4134995675\n", "314           320  43.8672608825\n", "315           321  43.7722386096\n", "316           322  43.6550136580\n", "317           323  43.3559096077\n", "318           324  43.0615187542\n", "319           325  42.8812944578\n", "320           326  42.5762800360\n", "321           327  42.1431811739\n", "322           328  42.0644160730\n", "323           329  42.0415572350\n", "324           330  41.9340231573\n", "325           331  41.7691114804\n", "326           332  41.9885354678\n", "327           333  42.3253710568\n", "328           334  42.8080043591\n", "329           335  43.2373715366\n", "330           336  42.4826846579\n", "331           337  42.3320228898\n", "332           338  42.8890958155\n", "333           339  43.0127737592\n", "334           340  42.9329874233\n", "335           341  42.7437721026\n", "336           342  42.7699666432\n", "337           343  42.3033203659\n", "338           344  42.6365507855\n", "339           345  42.2152638722\n", "340           346  41.9027216945\n", "341           347  41.7258480683\n", "342           348  41.5356724424\n", "343           349  41.5221338940\n", "344           350  41.4212359266\n", "345           351  41.3241769417\n", "346           352  41.5321535334\n", "347           353  41.5290253395\n", "348           354  41.0086816035\n", "349           355  41.0587104566\n", "350           356  41.0285566677\n", "351           357  40.9382593261\n", "352           358  40.5504882979\n", "353           359  40.8011851145\n", "354           360  40.6734657775\n", "355           361  40.4575708406\n", "356           362  39.1357675876\n", "357           363  38.6686645401\n", "358           364  38.0948491970\n", "359           365  37.9422594503\n", "360           366  37.9018825507\n", "361           367  37.8100721881\n", "362           368  38.6627167190\n", "363           369  40.2788200220\n", "364           370  38.1787978098\n", "365           371  37.9582550961\n", "366           372  37.9942420097\n", "367           373  38.0655769297\n", "368           374  38.5837382805\n", "369           375  39.4726179736\n", "370           376  39.2735857899\n", "371           377  39.1771520840\n", "372           378  39.2413140876\n", "373           379  39.5165571718\n", "374           380  39.7390375295\n", "375           381  40.0710875041\n", "376           382  40.4072538341\n", "377           383  39.7443320614\n", "378           384  41.1139977469\n", "379           385  41.8447309748\n", "380           386  42.3639443211\n", "381           387  42.7508956774\n", "382           388  43.2942626760\n", "383           389  43.5290403193\n", "384           390  43.8809399400\n", "385           391  44.3124204679\n", "386           392  44.5963421134\n", "387           393  44.9174640409\n", "388           394  44.9802399340\n", "389           395  45.4266808420\n", "390           396  45.6119042631\n", "391           397  45.8972110607\n", "392           398  46.2711777156\n", "393           399  46.4262381379\n", "394           400  46.6646844893\n", "395           401  46.6935818971\n", "396           402  46.7692130120\n", "397           403  46.8606916938\n", "398           404  47.0716597769\n", "399           405  46.9719202238\n", "400           406  47.0447933064\n", "401           407  47.0344481516\n", "402           408  46.2266366900\n", "403           409  45.1174570403\n", "404           410  45.0410863211\n", "405           411  45.3794832526\n", "406           413  45.9365234413\n", "407           414  46.0026891771\n", "408           415  45.9906293799\n", "409           416  45.7451717121\n", "410           417  45.2051858498\n", "411           418  44.6237773266\n", "412           419  43.9313863389\n", "413           420  43.5898586093\n", "414           421  43.1692927328\n", "415           422  42.8106680165\n", "416           423  42.8266505120\n", "417           424  42.1487528276\n", "418           425  41.4554518761\n", "419           426  41.7810235633\n", "420           427  41.9270450119\n", "421           428  42.0800151530\n", "422           429  42.2984262132\n", "423           430  43.6116785455\n", "424           431  44.3248198504\n", "425           432  43.2771031418\n", "426           433  43.0895020078\n", "427           434  42.9800465859\n", "428           435  42.9026418785\n", "429           436  42.7405623960\n", "430           437  42.7417991369\n", "431           438  42.9804869873\n", "432           439  43.1716496510\n", "433           440  43.3537948331\n", "434           441  43.5082205736\n", "435           442  43.7362635026\n", "436           443  44.1137931044\n", "437           444  44.3081722403\n", "438           445  44.9455880460\n", "439           446  44.5345576667\n", "440           447  46.0107721092\n", "441           448  46.2690429783\n", "442           449  47.2095839395\n", "443           450  47.2896600265\n", "444           451  45.6560350451\n", "445           452  44.4587900021\n", "446           453  44.7789067852\n", "447           454  45.4170204076\n", "448           455  45.6850649452\n", "449           456  45.7736136083\n", "450           457  45.9738861924\n", "451           458  45.7853595404\n", "452           459  45.8287741225\n", "453           460  46.2129090067\n", "454           461  46.1007640373\n", "455           462  45.8579685829\n", "456           463  45.7135448689\n", "457           464  47.4156922665\n", "458           465  45.9515219143\n", "459           466  45.0446738357\n", "460           467  44.6827014841\n", "461           468  44.3461325653\n", "462           469  43.9796134400\n", "463           470  43.7022602487\n", "464           471  43.3384656476\n", "465           472  42.8998702262\n", "466           473  42.6646434608\n", "467           474  42.5021454294\n", "468           475  42.5489147504\n", "469           476  42.3437952415\n", "470           477  42.2072662849\n", "471           478  42.1364838678\n", "472           479  41.8950297226\n", "473           480  41.9965250548\n", "474           481  41.9418757936\n", "475           482  42.1778105673\n", "476           483  42.4025673823\n", "477           484  42.6871976861\n", "478           485  42.1489461981\n", "479           486  42.8297808766\n", "480           487  42.7404852280\n", "481           488  42.8018186127\n", "482           489  42.7627136966\n", "483           490  42.5115106926\n", "484           491  41.8026770279\n", "485           492  41.5591988267\n", "486           493  41.1575750990\n", "487           494  40.6513965440\n", "488           495  40.6367458455\n", "489           496  41.8872415744\n", "490           497  42.8210300763\n", "491           498  41.6729168077\n", "492           499  41.8298328835\n", "493           500  42.1081492992\n", "494           501  42.4294512048\n", "495           502  42.7453295165\n", "496           503  43.0287754831\n", "497           504  43.1855714649\n", "498           505  43.5021615940\n", "499           506  43.6622110803\n", "500           507  43.7563449000\n", "501           508  43.9423012530\n", "502           509  44.2381070849\n", "503           510  44.2041649453\n", "504           511  44.5990798426\n", "505           512  44.8848733128\n", "506           513  45.1468914517\n", "507           514  45.3667005296\n", "508           515  45.6644015430\n", "509           516  45.9624170949\n", "510           517  46.4057362783\n", "511           518  46.4794883265\n", "512           519  46.4502902921\n", "513           520  46.9140157877\n", "514           521  47.5237677414\n", "515           522  46.8319687426\n", "516           523  46.5098349540\n", "517           524  46.6333521086\n", "518           525  46.6801264451\n", "519           526  46.9067648264\n", "520           527  47.1307129783\n", "521           528  46.9915701693\n", "522           529  45.7631161571\n", "523           530  46.0697762809\n", "524           531  47.2964028823\n", "525           532  47.7163960775\n", "526           533  47.8522379378\n", "527           534  48.1580823904\n", "528           535  48.2497094632\n", "529           536  48.4134939301\n", "530           537  48.1545305232\n", "531           538  47.8807835784\n", "532           539  47.1878377264\n", "533           540  46.9200306004\n", "534           541  46.3254060192\n", "535           542  46.0454780926\n", "536           543  45.3874173399\n", "537           544  43.6003446637\n", "538           545  43.4613018869\n", "539           546  43.3468587129\n", "540           547  43.2722316869\n", "541           548  42.9773269463\n", "542           549  42.7820447747\n", "543           550  42.5926360850\n", "544           551  42.4102092677\n", "545           552  43.4488471538\n", "546           553  43.5682698211\n", "547           554  43.4058431099\n", "548           555  43.8618021837\n", "549           556  44.3271748215\n", "550           557  44.1709679358\n", "551           558  44.7058894591\n", "552           559  44.6617805823\n", "553           560  44.3380420058\n", "554           561  44.3113422689\n", "555           562  44.1584457381\n", "556           563  44.5310122977\n", "557           564  44.9209099048\n", "558           565  45.0595136669\n", "559           566  45.0776629115\n", "560           567  44.5439632307\n", "561           568  43.6048045958\n", "562           569  42.8513678667\n", "563           570  42.8227476584\n", "564           571  44.3716350730\n", "565           572  43.2860496508\n", "566           573  43.3378398634\n", "567           574  43.3478009938\n", "568           575  43.3183306359\n", "569           576  43.6781244859\n", "570           577  43.6327837929\n", "571           578  41.6080437431\n", "572           579  41.6865928814\n", "573           580  42.4587426262\n", "574           581  42.2580385688\n", "575           582  42.5715005025\n", "576           583  42.7550940368\n", "577           584  42.2174071950\n", "578           585  42.2972911536\n", "579           586  43.0022954314\n", "580           587  43.3913959106\n", "581           588  43.4674450008\n", "582           589  43.7644389502\n", "583           590  43.7991586002\n", "584           591  43.7697545740\n", "585           592  43.5782374278\n", "586           593  43.1727289462\n", "587           594  42.7737989484\n", "588           595  42.4861971706\n", "589           596  42.4382628321\n", "590           597  42.6506613321\n", "591           598  42.3887569684\n", "592           599  42.6204967212\n", "593           600  42.6490909466\n", "594           601  42.6862392471\n", "595           602  42.4746423364\n", "596           603  42.5506875420\n", "597           604  41.9567518760\n", "598           605  41.8740501021\n", "599           606  41.6758187655\n", "600           607  41.3068232839\n", "601           608  41.0926102870\n", "602           609  40.4040557987\n", "603           610  39.6217000456\n", "604           611  39.6930483443\n", "605           612  39.5470401267\n", "606           613  39.4730170279\n", "607           614  39.5049828668\n", "608           615  39.3878088552\n", "609           616  39.4628156263\n", "610           617  39.5286145769\n", "611           618  39.3122736625\n", "612           619  39.4505583107\n", "613           620  39.1470064538\n", "614           621  39.3475907021\n", "615           622  39.4189645427\n", "616           623  39.3517128549\n", "617           624  38.9575986703\n", "618           625  39.8008772312\n", "619           626  39.8144335506\n", "620           627  39.3488816906\n", "621           628  39.2641946045\n", "622           629  39.6023965395\n", "623           630  39.8170603746\n", "624           631  39.6877790759\n", "625           632  39.4815323269\n", "626           633  39.5810311306\n", "627           634  39.9881407530\n", "628           635  39.8747937243\n", "629           636  39.8630628449\n", "630           637  40.0170928634\n", "631           638  40.0087012070\n", "632           639  39.1420456167\n", "633           640  39.7677440071\n", "634           641  39.7374174311\n", "635           642  39.6805442072\n", "636           643  39.6978114701\n", "637           644  39.3960568770\n", "638           645  38.8659708269\n", "639           646  39.0134466327\n", "640           647  38.9749138791\n", "641           648  38.6837067939\n", "642           649  38.7508926174\n", "643           650  38.8608401044\n", "644           651  38.8363118639\n", "645           652  38.9073646523\n", "646           653  39.1425697318\n", "647           654  39.4144734349\n", "648           655  39.7235915462\n", "649           656  40.1940407879\n", "650           657  40.2376686882\n", "651           658  40.3936335901\n", "652           659  40.6495996604\n", "653           660  40.6010209578\n", "654           661  40.5840425099\n", "655           662  40.3766020216\n", "656           663  39.7648163720\n", "657           664  39.3478751026\n", "658           665  39.7349637401\n", "659           666  39.8697165499\n", "660           667  39.8360972440\n", "661           668  40.0059192083\n", "662           669  40.1996753708\n", "663           670  40.2946994470\n", "664           671  40.1372916774\n", "665           672  40.4133123824\n", "666           673  40.1060705725\n", "667           674  40.1746366631\n", "668           675  39.7530544803\n", "669           676  39.4422070384\n", "670           677  39.5940690439\n", "671           678  40.0577190251\n", "672           679  40.2136200087\n", "673           680  40.5848818156\n", "674           681  40.6561808553\n", "675           682  40.7902098885\n", "676           683  40.6651732184\n", "677           684  40.7104434780\n", "678           685  40.8379700939\n", "679           686  40.7223425566\n", "680           687  40.8764071892\n", "681           688  41.0174893751\n", "682           689  40.9411970577\n", "683           690  40.9700637870\n", "684           691  40.8539283764\n", "685           692  40.7507836284\n", "686           693  40.6690398599\n", "687           694  40.7844299598\n", "688           695  40.8205326194\n", "689           696  40.9673401157\n", "690           697  40.9384237648\n", "691           698  40.6434132233\n", "692           699  41.0345799257\n", "693           700  41.1507317593\n", "694           701  41.0455348483\n", "695           702  40.9310055456\n", "696           703  40.8574632508\n", "697           704  41.3571516218\n", "698           705  41.0717444494\n", "699           706  41.0592031995\n", "700           707  41.0534112499\n", "701           708  40.9414242915\n", "702           709  41.0305496396\n", "703           710  40.8460559703\n", "704           711  40.7624537855\n", "705           712  40.8226554852\n", "706           713  41.0096132834\n", "707           714  40.6465297326\n", "708           715  40.5192055570\n", "709           716  40.8039310133\n", "710           717  40.8410980066\n", "711           718  40.8120552643\n", "712           719  40.9266083203\n", "713           720  40.7115330296\n", "714           721  40.4680269404\n", "715           722  40.1614747053\n", "716           723  39.8175673122\n", "717           724  39.5881478444\n", "718           725  39.2965832354\n", "719           726  39.2317121636\n", "720           727  39.2083878267\n", "721           728  40.3364049996\n", "722           729  39.7846887249\n", "723           730  39.7487463514\n", "724           731  40.2415017341\n", "725           732  40.3580988444\n", "726           733  40.2720610193\n", "727           734  40.1772585332\n", "728           735  40.3187323214\n", "729           736  40.2979150657\n", "730           737  40.5884139041\n", "731           738  40.6914081699\n", "732           739  40.8858917265\n", "733           740  41.0250298768\n", "734           741  41.1489779079\n", "735           742  41.0888699227\n", "736           743  41.1776850370\n", "737           744  40.7888246797\n", "738           745  40.8675399174\n", "739           746  40.7928459458\n", "740           747  40.7235452258\n", "741           748  40.6301387150\n", "742           749  39.9197039896\n", "743           750  40.1303371713\n", "744           751  39.7658843298\n", "745           752  39.5043036912\n", "746           753  39.2161491700\n", "747           754  39.1794787225\n", "748           755  39.1126787129\n", "749           756  38.5890738110\n", "750           757  38.4686620331\n", "751           758  38.2990334413\n", "752           759  38.4638039400\n", "753           760  38.7464423936\n", "754           761  38.7413702540\n", "755           762  38.8020452245\n", "756           763  38.8126873359\n", "757           764  38.7684703720\n", "758           765  38.8284079636\n", "759           766  38.8141433682\n", "760           767  38.6336992194\n", "761           768  38.6260000268\n", "762           769  38.8274864391\n", "763           770  39.1538176945\n", "764           771  38.1196569502\n", "765           772  38.2342768514\n", "766           773  38.5361580568\n", "767           774  38.7382500349\n", "768           775  38.8696331312\n", "769           776  39.0154971826\n", "770           777  38.6681994784\n", "771           778  38.5588419757\n", "772           779  38.3484896666\n", "773           780  38.4356431693\n", "774           781  38.4865288356\n", "775           782  38.5805312304\n", "776           783  39.2116477744\n", "777           784  38.7120554416\n", "778           785  38.5472181121\n", "779           786  38.6238235219\n", "780           787  38.6230125310\n", "781           788  38.8230157351\n", "782           789  38.7568611274\n", "783           790  38.7450651634\n", "784           791  38.7004337959\n", "785           792  38.7301347620\n", "786           793  38.7575056196\n", "787           794  38.8007165202\n", "788           795  38.9087968672\n", "789           796  38.9205294160\n", "790           797  38.8136174449\n", "791           798  38.4165628408\n", "792           799  38.4787523609\n", "793           800  38.6787417686\n", "794           801  38.3897234504\n", "795           802  38.3921072026\n", "796           803  38.2090021565\n", "797           804  38.0881717382\n", "798           805  38.0821482266\n", "799           806  37.9145119934\n", "800           807  37.7526813933\n", "801           808  37.9280239823\n", "802           809  37.8851306310\n", "803           810  37.9197605736\n", "804           811  37.8691130692\n", "805           812  37.8956886705\n", "806           813  38.5802915730\n", "807           814  38.2881241708\n", "808           815  38.2372067994\n", "809           816  38.3612783297\n", "810           817  38.4611193230\n", "811           818  38.3347396516\n", "812           819  38.4390656113\n", "813           820  38.6135891105\n", "814           821  38.7639344167\n", "815           822  38.8568571798\n", "816           823  38.8797006295\n", "817           824  38.6468807286\n", "818           825  38.8458742462\n", "819           826  39.1332055560\n", "820           827  39.1011919222\n", "821           828  38.8131356661\n", "822           830  38.5381848666\n", "823           831  38.3580320319\n", "824           832  38.2446797359\n", "825           833  37.5242764739\n", "826           834  37.2509308569\n", "827           835  37.1664378504\n", "828           836  37.0525765327\n", "829           837  37.1994204374\n", "830           838  38.0030016167\n", "831           839  39.4089244824\n", "832           840  39.8206131793\n", "833           841  40.1091014267\n", "834           842  40.1982793590\n", "835           843  40.2286957030\n", "836           844  40.4660493710\n", "837           845  40.8297546752\n", "838           846  41.5501652430\n", "839           847  42.1187981330\n", "840           848  42.0316744764\n", "841           849  42.3133484733\n", "842           850  42.5095039841\n", "843           851  43.0495018554\n", "844           852  41.1944544356\n", "845           853  41.8816842326\n", "846           854  42.5978345399\n", "847           855  42.6446205587\n", "848           856  42.7387186147\n", "849           857  42.7991157907\n", "850           858  43.0085323865\n", "851           859  43.5516028030\n", "852           860  44.7699057683\n", "853           861  44.4469587739\n", "854           862  44.5701913666\n", "855           863  44.7532172830\n", "856           864  44.7538637548\n", "857           865  44.5828791950\n", "858           866  44.7873123777\n", "859           867  44.0163430474\n", "860           868  42.4398371506\n", "861           869  42.5052522124\n", "862           870  43.1848019558\n", "863           871  43.6944112459\n", "864           872  44.2567993584\n", "865           873  44.3102792465\n", "866           874  44.0312983589\n", "867           875  44.0424612022\n", "868           876  42.9614483559\n", "869           877  42.8829779908\n", "870           878  42.4326143067\n", "871           879  41.9595240210\n", "872           880  40.7098048294\n", "873           881  40.2908606895\n", "874           882  40.1549505784\n", "875           883  40.3962041022\n", "876           884  41.5412171698\n", "877           885  41.1718628217\n", "878           886  41.1125126330\n", "879           887  41.6291354616\n", "880           888  42.7389079155\n", "881           889  42.4333577561\n", "882           890  42.3845034128\n", "883           891  42.6660158113\n", "884           892  43.1278328924\n", "885           893  43.5910981691\n", "886           894  43.1398631680\n", "887           895  41.5377424836\n", "888           897  42.3766556239\n", "889           898  43.0636183790\n", "890           899  43.5312083679\n", "891           900  43.8417304786\n", "892           901  44.1749392317\n", "893           902  44.2871576051\n", "894           903  44.1799346529\n", "895           904  44.2028140257\n", "896           905  44.0772419961\n", "897           906  43.8708555471\n", "898           907  43.7267557812\n", "899           908  43.5477507950\n", "900           909  43.2384630344\n", "901           910  43.5061544861\n", "902           911  43.3035223872\n", "903           912  43.2764235538\n", "904           913  43.4003237779\n", "905           914  43.6100365770\n", "906           915  43.4889922005\n", "907           916  43.7614306455\n", "908           917  44.1242826372\n", "909           918  44.9601014453\n", "910           919  44.8876292865\n", "911           920  45.6384254659\n", "912           921  45.6061261864\n", "913           922  46.0176567813\n", "914           923  44.7217756143\n", "915           924  44.5942103300\n", "916           925  44.6630664042\n", "917           926  44.7131980698\n", "918           927  44.5709784699\n", "919           928  44.3255225622\n", "920           929  44.5224701074\n", "921           930  44.5111027204\n", "922           931  44.7317141838\n", "923           932  45.0438445997\n", "924           933  45.2413112148\n", "925           934  44.0594993048\n", "926           935  43.5639833708\n", "927           936  43.9507436292\n", "928           937  44.4925782373\n", "929           938  44.4552992032\n", "930           939  44.6511638160\n", "931           940  44.6864253124\n", "932           941  44.6810927477\n", "933           942  44.3388018133\n", "934           943  44.0447009744\n", "935           944  43.4527359660\n", "936           945  42.9670900609\n", "937           946  42.5742208886\n", "938           947  42.3990743006\n", "939           948  42.4676413947\n", "940           949  42.3673304253\n", "941           950  42.4863581063\n", "942           951  42.5127279602\n", "943           952  42.4588573355\n", "944           953  42.2216492434\n", "945           954  42.1300777871\n", "946           955  42.1818698557\n", "947           956  42.3247730003\n", "948           957  41.6713029204\n", "949           958  41.6226639917\n", "950           959  41.5602912266\n", "951           960  41.6915278583\n", "952           961  41.6679360389\n", "953           962  41.6394576853\n", "954           963  41.6357741032\n", "955           964  41.5193424621\n", "956           965  41.3905131705\n", "957           966  41.2017050345\n", "958           967  40.8808540759\n", "959           968  40.9009792889\n", "960           969  40.8039205757\n", "961           970  40.8273344502\n", "962           971  42.2052042004\n", "963           972  42.3958337754\n", "964           973  42.3666971499\n", "965           974  42.5574484611\n", "966           975  42.7194804598\n", "967           976  42.8745442368\n", "968           977  43.1689920507\n", "969           978  43.4056305921\n", "970           979  42.3317374736\n", "971           980  42.4076876730\n", "972           981  42.4272418580\n", "973           982  42.7679140113\n", "974           983  42.8884928615\n", "975           984  43.2409166672\n", "976           985  43.6488958364\n", "977           986  43.9899565320\n", "978           987  44.4582802020\n", "979           988  44.7571114565\n", "980           989  44.8269257496\n", "981           990  43.3394905532\n", "982           991  44.6479258580\n", "983           992  45.2111484214\n", "984           993  45.7386242147\n", "985           994  46.4702368710\n", "986           995  47.1523033592\n", "987           996  47.4479118653\n", "988           997  46.4603312499\n", "989           998  46.0500268245\n", "990           999  45.8699274275\n", "991          1000  45.4868266740\n", "992          1001  45.1273393369\n", "993          1002  44.8161243864\n", "994          1003  44.5916174221\n", "995          1004  43.7677636444\n", "996          1005  43.2521514190\n", "997          1006  42.8071119334\n", "998          1007  42.0540213940\n", "999          1008  41.9979856702\n", "1000         1009  41.4785389929\n", "1001         1010  41.0321945994\n", "1002         1011  40.8456239543\n", "1003         1012  40.6291688710\n", "1004         1013  40.4238769895\n", "1005         1014  40.4315608369\n", "1006         1015  40.4244101915\n", "1007         1016  40.4814833530\n", "1008         1017  40.5784312567\n", "1009         1018  40.9173594756\n", "1010         1019  40.8872855191\n", "1011         1020  41.3037177864\n", "1012         1021  41.3664125538\n", "1013         1022  41.6606982116\n", "1014         1023  41.7085550256\n", "1015         1024  41.5560884406\n", "1016         1025  41.4041570724\n", "1017         1026  41.2983856349\n", "1018         1027  41.2066692842\n", "1019         1028  41.1010290441\n", "1020         1029  41.1031493729\n", "1021         1030  41.0683833370\n", "1022         1031  41.5754857096\n", "1023         1032  40.7957480791\n", "1024         1033  40.5069992247\n", "1025         1034  39.9418887267\n", "1026         1035  40.3784718895\n", "1027         1036  39.9985677290\n", "1028         1037  39.2272014768\n", "1029         1038  38.9407210421\n", "1030         1039  38.5809304425\n", "1031         1040  38.1441785036\n", "1032         1041  37.8522272470\n", "1033         1042  37.6583458922\n", "1034         1043  37.5884884331\n", "1035         1044  37.6435653526\n", "1036         1045  37.6323750179\n", "1037         1046  37.7586243891\n", "1038         1047  38.0662138658\n", "1039         1048  38.3241315359\n", "1040         1049  38.6160184543\n", "1041         1050  38.8504203864\n", "1042         1051  39.1691197241\n", "1043         1052  39.6951559429\n", "1044         1053  39.9821638834\n", "1045         1054  41.2136291205\n", "1046         1055  41.2437969713\n", "1047         1056  41.3320488443\n", "1048         1057  41.8684509720\n", "1049         1058  41.7796354574\n", "1050         1059  41.9258060917\n", "1051         1060  41.2881567560\n", "1052         1061  41.3251528726\n", "1053         1062  41.5401259058\n", "1054         1063  41.6660274865\n", "1055         1064  41.8798158013\n", "1056         1065  42.2326859422\n", "1057         1066  40.7784429503\n", "1058         1067  40.9761759850\n", "1059         1068  41.5123618895\n", "1060         1069  42.0031596436\n", "1061         1070  41.8791600611\n", "1062         1071  42.0835090837\n", "1063         1072  42.2795107557\n", "1064         1073  41.8102374928\n", "1065         1074  41.8503613983\n", "1066         1075  41.6989359713\n", "1067         1076  41.8466831231\n", "1068         1077  41.3357264204\n", "1069         1078  41.1404812558\n", "1070         1079  41.1865789549\n", "1071         1080  41.0949126273\n", "1072         1081  40.7596310487\n", "1073         1082  41.0373799223\n", "1074         1083  41.3460951450\n", "1075         1084  41.3504417664\n", "1076         1085  41.1327837095\n", "1077         1086  41.2289704109\n", "1078         1087  41.4302249323\n", "1079         1088  41.1463436562\n", "1080         1089  41.6403837670\n", "1081         1090  41.4307652837\n", "1082         1091  41.7420956337\n", "1083         1092  41.8236275579\n", "1084         1093  41.6870633479\n", "1085         1094  41.2754709944\n", "1086         1095  41.5767492946\n", "1087         1096  41.9335674680\n", "1088         1097  41.8059108982\n", "1089         1098  41.4605986639\n", "1090         1099  41.3064325409\n", "1091         1100  41.4560955382\n", "1092         1101  41.2184413048\n", "1093         1102  41.1472686943\n", "1094         1103  40.9776600264\n", "1095         1104  40.8755371048\n", "1096         1105  40.7689105652\n", "1097         1106  40.3443066427\n", "1098         1107  40.2670996908\n", "1099         1108  40.3567640897\n", "1100         1109  40.2911997337\n", "1101         1110  40.3042383884\n", "1102         1111  40.3847064259\n", "1103         1112  40.5264855399\n", "1104         1113  40.4925618220\n", "1105         1114  40.6418878187\n", "1106         1115  40.8047903980\n", "1107         1116  41.3208614992\n", "1108         1117  39.1506364341\n", "1109         1118  38.9380371039\n", "1110         1119  38.9274546180\n", "1111         1120  38.8673351188\n", "1112         1121  38.7433373532\n", "1113         1122  38.4709746554\n", "1114         1123  38.0826038569\n", "1115         1124  37.7456299379\n", "1116         1125  37.3341168076\n", "1117         1126  37.2361772107\n", "1118         1127  36.9467772398\n", "1119         1128  36.7614470362\n", "1120         1129  37.1548439274\n", "1121         1130  37.6558510746\n", "1122         1131  37.3061932786\n", "1123         1132  37.7579491544\n", "1124         1134  38.0106794104\n", "1125         1135  38.8109372773\n", "1126         1136  39.4890539764\n", "1127         1137  39.7392763768\n", "1128         1138  39.8513560587\n", "1129         1139  39.9169826822\n", "1130         1140  40.2850235083\n", "1131         1141  41.5374682031\n", "1132         1142  41.5169840008\n", "1133         1143  41.6342846027\n", "1134         1144  42.0555150329\n", "1135         1145  42.1142146095\n", "1136         1146  42.5235043846\n", "1137         1147  42.7038698752\n", "1138         1148  42.9022515891\n", "1139         1149  43.1703178161\n", "1140         1150  44.3048357040\n", "1141         1151  43.0153482513\n", "1142         1152  42.8869252097\n", "1143         1153  43.1846979447\n", "1144         1154  43.2147241897\n", "1145         1155  43.4824222822\n", "1146         1156  43.8192989629\n", "1147         1157  43.9308140095\n", "1148         1158  44.2052769616\n", "1149         1159  44.1767282944\n", "1150         1160  44.4577907768\n", "1151         1161  44.4122550580\n", "1152         1162  44.6695949488\n", "1153         1163  44.0385110995\n", "1154         1164  45.4256740231\n", "1155         1165  45.6323661157\n", "1156         1166  45.4204497296\n", "1157         1167  45.8783324322\n", "1158         1168  45.8173407818\n", "1159         1169  46.0056228792\n", "1160         1170  46.4324153481\n", "1161         1171  46.0605642863\n", "1162         1172  45.9229265037\n", "1163         1173  45.9811625846\n", "1164         1174  45.9898685603\n", "1165         1175  46.0407214312\n", "1166         1176  46.2300029577\n", "1167         1177  46.5344932027\n", "1168         1178  46.8674913143\n", "1169         1179  46.2734944376\n", "1170         1180  45.0166662746\n", "1171         1181  45.1800717356\n", "1172         1182  45.6791615487\n", "1173         1183  46.2930883937\n", "1174         1184  46.2394570795\n", "1175         1185  46.1040898093\n", "1176         1186  45.7242484497\n", "1177         1187  45.4875220816\n", "1178         1188  45.1535084220\n", "1179         1189  44.5339029500\n", "1180         1190  43.8930027178\n", "1181         1191  42.9570736895\n", "1182         1192  42.3598673616\n", "1183         1193  42.2749390400\n", "1184         1194  42.3758391761\n", "1185         1195  41.2646645153\n", "1186         1196  41.3887717837\n", "1187         1197  41.0296007503\n", "1188         1198  40.7801900326\n", "1189         1199  40.0420761780\n", "1190         1200  42.8677820284\n", "1191         1201  40.8130954776\n", "1192         1202  41.3065178751\n", "1193         1203  40.3963968983\n", "1194         1204  40.1290043782\n", "1195         1205  40.1953647697\n", "1196         1206  40.0459481955\n", "1197         1207  40.3054147486\n", "1198         1208  40.4269613656\n", "1199         1209  40.8959228990\n", "1200         1210  41.3163189491\n", "1201         1211  40.1006211083\n", "1202         1212  39.9736611620\n", "1203         1213  40.1894927909\n", "1204         1214  40.5074106918\n", "1205         1215  40.9342924677\n", "1206         1216  41.2273021740\n", "1207         1217  41.4175034876\n", "1208         1218  41.8478378283\n", "1209         1219  42.3375727713\n", "1210         1220  41.5482214508\n", "1211         1221  41.4198753382\n", "1212         1223  42.3289629253\n", "1213         1224  42.8597980142\n", "1214         1225  43.3612970237\n", "1215         1226  43.9441690888\n", "1216         1227  44.1153588505\n", "1217         1228  43.8821199289\n", "1218         1229  43.5229649095\n", "1219         1230  43.0642013505\n", "1220         1231  42.1897335905\n", "1221         1232  40.4220999035\n", "1222         1233  40.1283244264\n", "1223         1234  39.5870407504\n", "1224         1235  39.2698284148\n", "1225         1236  38.8545224664\n", "1226         1237  39.5745330611\n", "1227         1238  39.1660888207\n", "1228         1239  38.7552718774\n", "1229         1240  38.9856618923\n", "1230         1241  41.2263771067\n", "1231         1242  41.6488763940\n", "1232         1243  41.2853965558\n", "1233         1244  41.4672424946\n", "1234         1245  41.7217945671\n", "1235         1246  42.0035181700\n", "1236         1248  42.9845648885\n", "1237         1249  42.7140436634\n", "1238         1250  43.4737094473\n", "1239         1251  43.6949235369\n", "1240         1252  43.9541647481\n", "1241         1253  43.8768605197\n", "1242         1254  44.5385703039\n", "1243         1255  44.5141915276\n", "1244         1256  44.4967646281\n", "1245         1257  42.8845035360\n", "1246         1258  43.6666298801\n", "1247         1259  44.1729286560\n", "1248         1260  44.5236375163\n", "1249         1261  44.7746169368\n", "1250         1262  44.8834473272\n", "1251         1263  44.8404794263\n", "1252         1264  43.8323569216\n", "1253         1265  43.0410190016\n", "1254         1266  42.4003049807\n", "1255         1267  42.6224342186\n", "1256         1268  42.4827235735\n", "1257         1269  43.2171364676\n", "1258         1270  43.1787378761\n", "1259         1271  43.7693688560\n", "1260         1272  44.1604433357\n", "1261         1273  44.1790165597\n", "1262         1274  43.6487135720\n", "1263         1275  43.2617310201\n", "1264         1276  43.2225434216\n", "1265         1277  42.6902320201\n", "1266         1278  42.5590497866\n", "1267         1279  42.2951759647\n", "1268         1280  42.4883567667\n", "1269         1281  42.7416513974\n", "1270         1282  42.6844776954\n", "1271         1283  42.5843443903\n", "1272         1284  42.1645767312\n", "1273         1285  42.5249676970\n", "1274         1286  42.4647608598\n", "1275         1287  42.5199131278\n", "1276         1288  42.7291220373\n", "1277         1289  42.5020873462\n", "1278         1290  42.5673385047\n", "1279         1291  42.2608248875\n", "1280         1292  41.8381388553\n", "1281         1293  41.7676313505\n", "1282         1294  41.7501739057\n", "1283         1295  42.1246250335\n", "1284         1296  42.9909184356\n", "1285         1297  41.5242999158\n", "1286         1298  41.4577636380\n", "1287         1299  41.5554278940\n", "1288         1300  41.7195883586\n", "1289         1301  41.8239295071\n", "1290         1302  41.8751633480\n", "1291         1303  42.0470736038\n", "1292         1304  43.4993834256\n", "1293         1305  42.5872049259\n", "1294         1306  42.4689771055\n", "1295         1309  42.8012318968\n", "1296         1310  42.9297764952\n", "1297         1311  43.0596817552\n", "1298         1312  43.2175992328\n", "1299         1313  43.6995803761\n", "1300         1314  43.8796333137\n", "1301         1315  44.0064968052\n", "1302         1317  43.8543412196\n", "1303         1318  44.5742323657\n", "1304         1319  44.8046003242\n", "1305         1320  44.8613727939\n", "1306         1321  44.6902925350\n", "1307         1322  44.9467039605\n", "1308         1323  45.0261720510\n", "1309         1324  45.2113082167\n", "1310         1325  45.3998909886\n", "1311         1326  45.5552625858\n", "1312         1327  45.6345992723\n", "1313         1328  45.7328028938\n", "1314         1329  46.1649608186\n", "1315         1330  45.7404898121\n", "1316         1331  47.0625335875\n", "1317         1332  47.1593264251\n", "1318         1333  47.4285975526\n", "1319         1334  47.4289656753\n", "1320         1335  47.4119987213\n", "1321         1336  47.7097109768\n", "1322         1337  46.4448988984\n", "1323         1338  46.4788339967\n", "1324         1339  46.0824281377\n", "1325         1340  45.9626863280\n", "1326         1341  45.8702377708\n", "1327         1342  45.6828929324\n", "1328         1343  45.4757483241\n", "1329         1344  45.9109322160\n", "1330         1345  46.2772094890\n", "1331         1346  46.4106259315\n", "1332         1347  46.0851161798\n", "1333         1348  46.3178185440\n", "1334         1349  45.9845950900\n", "1335         1350  45.6903843197\n", "1336         1351  45.4076217896\n", "1337         1352  45.1959818475\n", "1338         1353  44.7204764846\n", "1339         1354  44.4651854951\n", "1340         1355  44.1144658834\n", "1341         1356  44.0470575253\n", "1342         1357  43.9218263189\n", "1343         1358  43.5610151230\n", "1344         1359  44.5175755106\n", "1345         1360  44.5309643597\n", "1346         1361  43.9558938970\n", "1347         1362  42.9554843139\n", "1348         1363  42.9810328752\n", "1349         1364  42.8724208492\n", "1350         1365  42.7902588559\n", "1351         1366  42.8042114993\n", "1352         1367  42.7887961618\n", "1353         1368  42.4349818826\n", "1354         1369  42.7203439735\n", "1355         1370  42.6818244243\n", "1356         1371  42.6444043648\n", "1357         1372  42.7219947280\n", "1358         1373  43.0602033397\n", "1359         1374  43.3994766407\n", "1360         1375  43.8505957893\n", "1361         1376  44.1843362869\n", "1362         1378  45.2981667313\n", "1363         1379  43.8442691739\n", "1364         1380  44.5232953260\n", "1365         1381  44.3842626197\n", "1366         1382  44.7174572839\n", "1367         1383  45.4539441132\n", "1368         1384  45.5857510209\n", "1369         1385  46.0492321015\n", "1370         1386  46.3784757965\n", "1371         1387  44.8005151760\n", "1372         1388  44.7608587877\n", "1373         1389  44.6208138986\n", "1374         1390  44.7923969445\n", "1375         1391  44.1033854509\n", "1376         1392  43.7930438273\n", "1377         1393  43.3685051332\n", "1378         1394  42.9238971214\n", "1379         1395  42.5293253609\n", "1380         1396  41.8613716272\n", "1381         1397  41.1876287155\n", "1382         1398  40.8895853519\n", "1383         1399  40.9310564373\n", "1384         1400  40.7988836565\n", "1385         1401  40.8479089482\n", "1386         1402  40.8532217938\n", "1387         1404  40.3360392096\n", "1388         1405  40.0044243180\n", "1389         1406  39.7022256981\n", "1390         1407  39.6286913163\n", "1391         1408  39.4474686620\n", "1392         1409  39.5173148907\n", "1393         1410  39.6248713612\n", "1394         1411  39.6466856303\n", "1395         1412  39.6880859264\n", "1396         1413  39.9783175642\n", "1397         1414  39.9896283132\n", "1398         1415  39.9947129109\n", "1399         1416  39.6197224976\n", "1400         1417  39.6502953614\n", "1401         1418  39.1816040236\n", "1402         1419  39.1630898760\n", "1403         1420  38.8586738107\n", "1404         1421  38.8245467416\n", "1405         1422  38.8247848099\n", "1406         1423  38.8257775874\n", "1407         1424  38.5456111990\n", "1408         1425  38.2623753489\n", "1409         1426  38.2882401416\n", "1410         1427  38.2778784475\n", "1411         1428  38.1850307264\n", "1412         1429  38.5105588784\n", "1413         1430  38.4179047798\n", "1414         1431  38.6680578436\n", "1415         1432  38.6748053432\n", "1416         1433  39.0564765060\n", "1417         1434  39.1999285029\n", "1418         1435  39.5959390162\n", "1419         1436  39.8804172961\n", "1420         1437  40.1159989916\n", "1421         1438  40.2901762311\n", "1422         1439  40.1260408577\n", "1423         1440  39.9582128449\n", "1424         1441  40.1010257321\n", "1425         1442  39.9677813371\n", "1426         1443  39.4821716087\n", "1427         1444  39.5412293142\n", "1428         1445  39.1652143815\n", "1429         1446  39.1224516336\n", "1430         1447  39.0391747044\n", "1431         1448  39.1510605679\n", "1432         1449  39.3679675178\n", "1433         1450  39.3486238479\n", "1434         1451  39.1876292145\n", "1435         1452  39.2349261417\n", "1436         1453  39.4892033671\n", "1437         1454  39.6937814531\n", "1438         1455  39.5766346022\n", "1439         1456  39.7551770233\n", "1440         1457  39.7334750032\n", "1441         1458  39.4711828964\n", "1442         1459  39.8655990897\n", "1443         1460  40.3228494249\n", "1444         1461  40.7745683507\n", "1445         1462  40.8572599343\n", "1446         1463  41.0164309889\n", "1447         1464  40.9353200081\n", "1448         1465  40.9615965559\n", "1449         1466  41.2456344707\n", "1450         1467  41.4313113559\n", "1451         1468  41.5344181493\n", "1452         1469  41.5168559930\n", "1453         1470  41.3885076657\n", "1454         1471  40.5206677442\n", "1455         1472  40.1756458212\n", "1456         1473  40.7123371855\n", "1457         1474  40.9326024965\n", "1458         1475  40.9968678594\n", "1459         1477  41.6179256993\n", "1460         1478  41.8839014209\n", "1461         1479  42.1991811495\n", "1462         1480  42.1158739683\n", "1463         1481  41.6693669535\n", "1464         1482  41.4195094436\n", "1465         1483  41.1720738987\n", "1466         1484  41.0550816922\n", "1467         1485  41.4592121940\n", "1468         1486  40.9332920947\n", "1469         1487  40.8440051062\n", "1470         1488  40.7427402831\n", "1471         1489  40.8956802154\n", "1472         1490  40.8816381979\n", "1473         1491  40.9405267992\n", "1474         1492  41.0026008868\n", "1475         1493  40.8425050923\n", "1476         1494  41.1713604535\n", "1477         1495  41.1798195099\n", "1478         1496  41.3504864223\n", "1479         1497  41.4077023247\n", "1480         1498  41.5441058158\n", "1481         1499  41.6451597429\n", "1482         1500  41.5860193188\n", "1483         1501  41.6248183694\n", "1484         1502  41.3215346643\n", "1485         1503  40.9389249792\n", "1486         1504  40.9953497630\n", "1487         1505  40.7785852068\n", "1488         1506  40.2997356274\n", "1489         1507  39.3904919761\n", "1490         1508  38.9302149965\n", "1491         1509  38.3739535995\n", "1492         1510  38.2076549506\n", "1493         1511  39.1189509497\n", "1494         1512  38.8286518449\n", "1495         1513  39.0010576903\n", "1496         1514  38.6816260660\n", "1497         1515  38.8359876587\n", "1498         1516  39.9570931602\n", "1499         1517  39.7478201461\n", "1500         1518  39.8822789548\n", "1501         1519  40.0582625082\n", "1502         1520  40.1078334462\n", "1503         1521  40.2628071726\n", "1504         1522  40.3351991170\n", "1505         1523  40.4658739582\n", "1506         1524  39.9974249027\n", "1507         1525  39.7872848547\n", "1508         1526  39.6499539596\n", "1509         1527  40.4510802138\n", "1510         1528  40.6429295962\n", "1511         1529  41.4324168547\n", "1512         1530  41.4887544230\n", "1513         1531  41.7446599411\n", "1514         1532  41.9090454642\n", "1515         1533  41.9462964619\n", "1516         1534  41.5531147108\n", "1517         1535  42.0262226183\n", "1518         1536  41.1955711175\n", "1519         1537  41.1875288003\n", "1520         1538  41.3421284100\n", "1521         1539  41.2357975857\n", "1522         1540  41.0523556491\n", "1523         1541  41.0819933024\n", "1524         1542  40.9502679919\n", "1525         1543  40.8156318462\n", "1526         1544  40.4989839122\n", "1527         1545  40.4784603455\n", "1528         1546  40.3777212163\n", "1529         1547  40.4655962771\n", "1530         1548  40.6019764295\n", "1531         1549  41.7244750260\n", "1532         1550  40.4820746870\n", "1533         1551  39.8782799630\n", "1534         1552  39.9335434577\n", "1535         1553  40.0557755455\n", "1536         1554  40.1933368509\n", "1537         1555  40.2720536105\n", "1538         1556  40.2719494121\n", "1539         1557  40.3154762685\n", "1540         1558  40.4752701027\n", "1541         1559  40.7082033492\n", "1542         1560  40.9678905240\n", "1543         1561  41.1642760330\n", "1544         1562  41.4630500759\n", "1545         1563  41.6706663334\n", "1546         1564  41.9446774768\n", "1547         1565  42.3537675604\n", "1548         1566  42.7918555430\n", "1549         1567  43.1101270716\n", "1550         1568  42.8727885402\n", "1551         1569  41.2825127423\n", "1552         1570  43.1119897830\n", "1553         1571  43.3817285422\n", "1554         1572  43.7062480706\n", "1555         1573  44.0066159916\n", "1556         1574  44.0805446246\n", "1557         1575  45.3232556179\n", "1558         1576  42.5491248398\n", "1559         1577  42.7200911731\n", "1560         1578  42.5764105498\n", "1561         1580  42.7033023410\n", "1562         1581  42.7065085524\n", "1563         1582  42.6122320928\n", "1564         1583  42.5304142849\n", "1565         1584  42.7939088334\n", "1566         1585  42.9014635925\n", "1567         1586  42.7905333304\n", "1568         1587  42.7376756796\n", "1569         1588  42.3403716450\n", "1570         1589  42.1449846847\n", "1571         1590  43.4363628305\n", "1572         1591  42.2760908069\n", "1573         1592  41.8279502651\n", "1574         1593  41.7768313886\n", "1575         1594  41.8259096734\n", "1576         1595  41.7694288062\n", "1577         1596  42.1837322474\n", "1578         1597  41.8507423270\n", "1579         1598  42.1464271360\n", "1580         1599  42.4698936182\n", "1581         1600  42.5959782183\n", "1582         1601  42.8936423969\n", "1583         1602  43.2213762848\n", "1584         1603  43.4475742506\n", "1585         1604  43.6531412116\n", "1586         1605  44.0301103768\n", "1587         1606  44.2277323148\n", "1588         1607  44.0227317509\n", "1589         1608  42.9699567216\n", "1590         1609  43.0652954745\n", "1591         1610  43.5314532686\n", "1592         1611  43.3584818315\n", "1593         1612  43.0821934065\n", "1594         1613  42.0248067404\n", "1595         1614  41.1176057617\n", "1596         1615  40.5162168058\n", "1597         1616  40.2421273714\n", "1598         1617  39.9249212154\n", "1599         1618  39.7752397578\n", "1600         1619  39.5658362490\n", "1601         1620  39.3512572105\n", "1602         1621  39.3132585249\n", "1603         1622  39.2918100502\n", "1604         1623  39.0986014235\n", "1605         1624  39.0940021966\n", "1606         1625  39.0399033013\n", "1607         1626  39.0175236097\n", "1608         1627  38.9407590106\n", "1609         1628  39.3076981942\n", "1610         1629  39.2643008701\n", "1611         1630  39.5017050284\n", "1612         1631  39.7016004292\n", "1613         1632  39.8808449017\n", "1614         1633  40.0223149661\n", "1615         1634  40.2200523690\n", "1616         1635  40.6467733062\n", "1617         1636  40.7041301021\n", "1618         1637  40.4175768564\n", "1619         1638  40.4299702940\n", "1620         1639  40.5021055923\n", "1621         1640  40.2276561307\n", "1622         1641  40.2625411953\n", "1623         1642  40.4346431854\n", "1624         1643  40.3640646895\n", "1625         1644  40.6244946802\n", "1626         1645  41.7004566312\n", "1627         1646  41.5628559496\n", "1628         1647  41.1433003944\n", "1629         1648  41.1349913648\n", "1630         1649  41.0153028973\n", "1631         1650  41.0810218198\n", "1632         1651  41.2530245711\n", "1633         1652  41.4223602685\n", "1634         1653  41.1744550058\n", "1635         1654  40.8140059767\n", "1636         1655  40.7513597452\n", "1637         1656  40.7764635352\n", "1638         1657  41.3223963532\n", "1639         1658  41.8814302613\n", "1640         1659  42.2474837872\n", "1641         1660  42.4296411052\n", "1642         1661  42.5133991292\n", "1643         1662  43.0857003262\n", "1644         1663  43.3621338018\n", "1645         1664  43.5362837834\n", "1646         1665  43.6302234596\n", "1647         1666  43.8083291994\n", "1648         1667  44.0356951577\n", "1649         1668  43.9684387043\n", "1650         1669  44.0499641563\n", "1651         1670  44.0746213251\n", "1652         1672  44.3674892358\n", "1653         1673  44.9633572907\n", "1654         1674  45.3610687840\n", "1655         1675  44.3469675973\n", "1656         1676  45.4537317778\n", "1657         1677  45.3198549837\n", "1658         1678  44.7326316473\n", "1659         1679  44.8948318390\n", "1660         1680  45.3457673817\n", "1661         1681  45.4166972003\n", "1662         1682  44.6579344594\n", "1663         1683  45.0359013566\n", "1664         1684  44.7643041071\n", "1665         1685  44.5391424845\n", "1666         1686  43.9063620870\n", "1667         1687  44.3143324796\n", "1668         1688  43.7107689668\n", "1669         1689  43.6554232585\n", "1670         1690  44.2358651846\n", "1671         1691  44.6422323763\n", "1672         1692  44.7434307949\n", "1673         1693  45.0132251164\n", "1674         1694  45.2961295906\n", "1675         1695  45.2197218612\n", "1676         1696  43.8757284701\n", "1677         1697  43.6686855729\n", "1678         1698  43.9333386766\n", "1679         1699  43.4884679561\n", "1680         1700  43.4650357396\n", "1681         1701  43.0518403095\n", "1682         1702  42.5994045229\n", "1683         1703  41.8686037372\n", "1684         1704  41.3509230439\n", "1685         1705  40.8699830894\n", "1686         1706  40.7154728767\n", "1687         1707  41.2108672333\n", "1688         1708  39.9815361958\n", "1689         1709  39.4207622377\n", "1690         1710  39.7445570675\n", "1691         1711  38.9875476993\n", "1692         1712  39.0706916930\n", "1693         1713  39.0763399480\n", "1694         1714  39.0344966135\n", "1695         1715  39.1102381592\n", "1696         1716  39.1215095369\n", "1697         1717  39.2558643755\n", "1698         1718  39.5602844289\n", "1699         1719  39.4544114801\n", "1700         1720  39.9415240467\n", "1701         1721  39.9974149247\n", "1702         1722  40.2533915754\n", "1703         1723  40.1407336834\n", "1704         1724  40.0645843619\n", "1705         1725  40.1470931632\n", "1706         1726  39.7608927541\n", "1707         1727  39.2239648396\n", "1708         1728  40.3890287922\n", "1709         1729  40.7933209497\n", "1710         1730  40.9856586565\n", "1711         1731  40.6642288650\n", "1712         1732  40.3792648259\n", "1713         1733  40.2885581966\n", "1714         1734  40.5303129154\n", "1715         1735  41.0271353864\n", "1716         1736  40.7531970046\n", "1717         1737  40.4012574520\n", "1718         1738  40.6730461158\n", "1719         1739  40.6275345269\n", "1720         1740  40.8432606279\n", "1721         1741  41.0157924911\n", "1722         1742  41.3711765341\n", "1723         1743  42.0267018025\n", "1724         1744  41.2795363895\n", "1725         1745  42.3007822680\n", "1726         1746  42.4982943236\n", "1727         1747  42.9091615428\n", "1728         1748  42.8658075648\n", "1729         1749  42.9820508131\n", "1730         1750  43.0871601599\n", "1731         1751  43.1283270264\n", "1732         1752  42.7422506559\n", "1733         1753  41.7293873151\n", "1734         1754  42.5672690959\n", "1735         1755  42.3171768817\n", "1736         1756  42.3309846671\n", "1737         1757  42.7245173450\n", "1738         1758  44.1221683009\n", "1739         1759  43.7137088082\n", "1740         1760  43.8537600026\n", "1741         1761  43.6019364310\n", "1742         1762  43.7786891922\n", "1743         1763  43.8143361453\n", "1744         1764  43.9031177298\n", "1745         1765  44.0260076735\n", "1746         1766  44.0023278928\n", "1747         1767  43.6791099534\n", "1748         1768  43.3043241184\n", "1749         1769  42.5934136016\n", "1750         1770  43.3802959367\n", "1751         1771  43.4604326616\n", "1752         1772  43.7277117786\n", "1753         1773  43.8988165020\n", "1754         1774  43.7828272569\n", "1755         1775  43.8873682318\n", "1756         1776  43.3697023516\n", "1757         1777  43.2639280539\n", "1758         1778  43.0894684033\n", "1759         1779  42.8307596963\n", "1760         1780  42.1695509162\n", "1761         1781  41.9745468791\n", "1762         1782  41.8160258437\n", "1763         1783  41.6588837994\n", "1764         1784  41.5720711913\n", "1765         1785  41.3551201368\n", "1766         1786  41.1831860334\n", "1767         1787  41.1473260878\n", "1768         1788  41.0538626181\n", "1769         1789  41.3747813842\n", "1770         1790  41.2405552792\n", "1771         1791  41.4228116825\n", "1772         1792  41.4510572396\n", "1773         1793  41.5394543941\n", "1774         1794  41.3744184575\n", "1775         1795  41.8844934318\n", "1776         1796  41.9297561338\n", "1777         1797  41.8674783649\n", "1778         1798  41.7490713527\n", "1779         1799  41.6428714349\n", "1780         1800  41.6377709293\n", "1781         1801  41.4778883899\n", "1782         1802  41.1680822169\n", "1783         1803  41.8882269383\n", "1784         1804  41.3949932168\n", "1785         1805  41.1735776917\n", "1786         1806  41.3631774677\n", "1787         1807  41.5131004433\n", "1788         1808  41.3553723899\n", "1789         1809  41.6282337770\n", "1790         1810  41.4156760412\n", "1791         1811  41.0908307395\n", "1792         1812  41.0366666995\n", "1793         1813  41.1121760477\n", "1794         1814  41.2790223784\n", "1795         1815  41.4217404070\n", "1796         1816  41.2362243718\n", "1797         1817  41.2201427210\n", "1798         1818  41.0044406375\n", "1799         1819  40.6349589162\n", "1800         1820  40.3368788562\n", "1801         1821  39.9671077973\n", "1802         1822  39.8553497664\n", "1803         1823  39.7519716091\n", "1804         1824  39.6021786434\n", "1805         1825  39.5812428718\n", "1806         1826  39.3371378999\n", "1807         1827  39.7279251310\n", "1808         1828  39.5928596955\n", "1809         1829  39.6254483458\n", "1810         1830  39.5916828518\n", "1811         1831  39.8556960308\n", "1812         1832  39.8163732686\n", "1813         1833  39.7576653674\n", "1814         1834  39.6738187841\n", "1815         1835  39.4826395330\n", "1816         1836  39.4288604671\n", "1817         1837  39.3198057856\n", "1818         1838  39.1320116791\n", "1819         1839  38.9740065004\n", "1820         1840  38.8084223063\n", "1821         1841  39.7698607316\n", "1822         1842  39.1875307393\n", "1823         1843  38.7791563583\n", "1824         1844  38.7899663696\n", "1825         1845  38.5735472182\n", "1826         1846  38.6903473238\n", "1827         1847  38.7056128157\n", "1828         1848  38.8716730266\n", "1829         1849  38.7539247798\n", "1830         1850  38.7659551201\n", "1831         1851  38.8769897002\n", "1832         1852  39.0214969298\n", "1833         1853  39.0015695990\n", "1834         1854  38.9425307288\n", "1835         1855  38.9511425953\n", "1836         1856  38.4311933821\n", "1837         1857  38.5100464271\n", "1838         1858  38.4166690057\n", "1839         1859  38.2457388990\n", "1840         1860  38.3226551819\n", "1841         1861  38.3233156971\n", "1842         1862  38.1874058907\n", "1843         1863  38.1074791814\n", "1844         1864  38.0208014440\n", "1845         1865  38.0850824235\n", "1846         1866  38.0815757504\n", "1847         1867  38.1780365824\n", "1848         1868  38.4094575892\n", "1849         1869  38.4100731544\n", "1850         1870  38.3726666312\n", "1851         1871  38.3958092300\n", "1852         1872  38.1938621935\n", "1853         1873  38.1453754351\n", "1854         1874  38.2366946116\n", "1855         1875  38.3477936133\n", "1856         1876  38.0642049210\n", "1857         1877  38.0617721344\n", "1858         1878  38.1129241780\n", "1859         1879  38.2947209525\n", "1860         1880  38.4218746123\n", "1861         1881  38.4978819183\n", "1862         1882  38.7770892068\n", "1863         1883  38.7287097783\n", "1864         1884  39.0712185693\n", "1865         1885  39.2506618107\n", "1866         1886  39.5172278541\n", "1867         1887  39.4319432243\n", "1868         1888  39.4408793163\n", "1869         1889  39.3203004048\n", "1870         1890  39.1742332321\n", "1871         1891  39.1429162263\n", "1872         1892  38.8317110937\n", "1873         1893  38.5838280384\n", "1874         1894  38.6093449632\n", "1875         1895  38.1768184325\n", "1876         1896  38.1557264483\n", "1877         1897  37.8267893435\n", "1878         1898  37.8111272932\n", "1879         1899  37.8939274196\n", "1880         1900  37.6639275087\n", "1881         1901  37.3155070216\n", "1882         1902  36.8018707881\n", "1883         1903  37.0774269060\n", "1884         1904  37.0666921273\n", "1885         1905  37.1022861411\n", "1886         1906  36.9503858734\n", "1887         1907  37.0522645014\n", "1888         1908  37.0107677391\n", "1889         1909  37.2128028066\n", "1890         1910  37.2098362369\n", "1891         1911  37.0154454786\n", "1892         1912  36.7461324246\n", "1893         1913  36.9178297831\n", "1894         1914  37.2568020376\n", "1895         1915  37.1513333813\n", "1896         1916  36.9745275618\n", "1897         1917  37.5755230671\n", "1898         1918  37.8337557742\n", "1899         1919  37.7865995137\n", "1900         1920  37.7644245336\n", "1901         1921  37.9547459521\n", "1902         1922  37.7590044419\n", "1903         1923  37.9486055335\n", "1904         1924  37.7360986065\n", "1905         1925  37.7318269780\n", "1906         1926  38.0180690695\n", "1907         1927  38.1908942277\n", "1908         1928  38.6034895822\n", "1909         1929  38.8703637380\n", "1910         1930  38.8132121756\n", "1911         1931  39.0276377681\n", "1912         1932  39.1459548999\n", "1913         1933  39.3176476758\n", "1914         1934  39.4635695664\n", "1915         1935  39.5372642119\n", "1916         1936  39.1946396368\n", "1917         1937  39.3824107326\n", "1918         1938  39.3192610975\n", "1919         1939  38.1639226914\n", "1920         1940  38.4537472699\n", "1921         1941  38.9799501812\n", "1922         1942  39.2977990213\n", "1923         1943  39.8463927002\n", "1924         1944  40.0441035453\n", "1925         1945  40.5172796797\n", "1926         1946  40.1753015737\n", "1927         1947  40.3951660369\n", "1928         1948  40.6450519547\n", "1929         1950  40.3901861135\n", "1930         1951  40.2189027292\n", "1931         1952  40.2014209979\n", "1932         1953  40.2099481713\n", "1933         1954  40.4934055322\n", "1934         1955  40.5699701580\n", "1935         1956  40.6013746098\n", "1936         1957  40.5893983991\n", "1937         1958  40.4343094183\n", "1938         1959  40.4665367844\n", "1939         1960  40.7250418432\n", "1940         1961  40.5766391291\n", "1941         1962  41.0693501891\n", "1942         1963  40.8203691441\n", "1943         1964  40.8488652389\n", "1944         1965  40.7760869417\n", "1945         1966  40.8915995995\n", "1946         1967  41.5631398159\n", "1947         1968  41.3231493093\n", "1948         1969  41.1748647532\n", "1949         1970  41.1405926120\n", "1950         1971  41.2666017766\n", "1951         1972  41.1778896341\n", "1952         1973  41.2810589610\n", "1953         1974  41.5373172500\n", "1954         1975  41.4478464455\n", "1955         1976  41.7395255881\n", "1956         1977  41.0141866782\n", "1957         1978  41.7864939196\n", "1958         1979  40.4482816552\n", "1959         1980  40.9898204575\n", "1960         1981  41.1128267330\n", "1961         1982  40.4774001007\n", "1962         1983  41.4319127030\n", "1963         1984  42.1343872404\n", "1964         1985  42.2930466507\n", "1965         1986  42.3935913317\n", "1966         1987  42.0264876504\n", "1967         1988  41.7801687098\n", "1968         1989  41.1838371151\n", "1969         1990  39.9232172688\n", "1970         1991  39.2231780337\n", "1971         1992  39.4436264607\n", "1972         1993  39.1474293557\n", "1973         1994  39.0513629863\n", "1974         1995  38.6938030259\n", "1975         1996  38.3061760001\n", "1976         1997  37.8064583192\n", "1977         1998  38.4157909543\n", "1978         1999  38.1061756532\n", "1979         2000  38.1720646195\n", "1980         2001  38.1862867361\n", "1981         2002  38.3661613879\n", "1982         2003  38.6353652053\n", "1983         2004  38.6587221711\n", "1984         2005  39.2172269046\n", "1985         2006  39.4753321192\n", "1986         2007  39.4254714013\n", "1987         2008  39.4991387987\n", "1988         2009  39.6667487086\n", "1989         2010  39.8023555534\n", "1990         2011  39.6689837939\n", "1991         2012  38.8593371644\n", "1992         2013  38.7031509920\n", "1993         2014  38.7834561445\n", "1994         2015  38.6366958224\n", "1995         2016  40.5026254677\n", "1996         2017  39.5174851706\n", "1997         2018  40.0159302556\n", "1998         2019  39.2878672403\n", "1999         2020  39.7067544248\n", "2000         2021  40.1205855418\n", "2001         2022  40.0300770436\n", "2002         2023  40.1958969677\n", "2003         2024  39.9484981568\n", "2004         2025  40.0899131540\n", "2005         2026  40.2063883912\n", "2006         2027  40.3830205718\n", "2007         2028  40.5779197880\n", "2008         2029  40.5391035929\n", "2009         2030  42.3642900332\n", "2010         2031  41.5147521628\n", "2011         2032  42.7381174097\n", "2012         2033  41.6490245200\n", "2013         2034  42.2577099943\n", "2014         2035  44.5927409176\n", "2015         2036  42.7967496792\n", "2016         2037  45.4616295479\n", "2017         2038  42.9928941072\n", "2018         2039  42.1174990177\n", "2019         2040  43.3328527112\n", "2020         2041  44.9822231708\n", "2021         2042  45.1196620717\n", "2022         2043  45.8504745837\n", "2023         2045  45.2731983921\n", "2024         2046  44.9766532148\n", "2025         2047  44.5429367601\n", "2026         2048  44.0166429423\n", "2027         2049  43.4742551831\n", "2028         2050  42.7226205761\n", "2029         2051  42.1777640842\n", "2030         2052  41.3548107240\n", "2031         2053  40.8606026443\n", "2032         2054  40.2113244273\n", "2033         2055  39.6683344075\n", "2034         2056  39.3773959029\n", "2035         2057  39.2277056972\n", "2036         2058  39.0571046529\n", "2037         2059  38.6840705761\n", "2038         2060  38.9317357094\n", "2039         2061  38.8256235340\n", "2040         2062  38.9832159864\n", "2041         2063  39.0713323138\n", "2042         2064  39.1989521257\n", "2043         2065  39.3715929708\n", "2044         2066  39.7960657487\n", "2045         2067  40.8047380443\n", "2046         2068  40.8444347299\n", "2047         2069  41.0273142925\n", "2048         2070  41.4141944401\n", "2049         2071  41.3758623287\n", "2050         2072  41.6291966461\n", "2051         2073  41.7665066713\n", "2052         2074  41.5407032849\n", "2053         2075  41.1734254857\n", "2054         2076  40.9615834281\n", "2055         2077  40.9175401225\n", "2056         2078  40.6106061931\n", "2057         2079  40.7219919576\n", "2058         2080  41.1666464635\n", "2059         2081  41.0893670328\n", "2060         2082  41.4127999733\n", "2061         2083  41.7825867407\n", "2062         2084  41.8391894820\n", "2063         2085  42.1571844440\n", "2064         2086  42.4391078168\n", "2065         2087  42.7122915066\n", "2066         2088  42.8107088944\n", "2067         2089  42.9938964521\n", "2068         2090  43.1919975917\n", "2069         2091  43.0049172572\n", "2070         2092  43.4090465993\n", "2071         2093  43.8247030278\n", "2072         2094  43.8784835268\n", "2073         2095  43.9510181813\n", "2074         2096  43.6842081108\n", "2075         2097  44.2273506839\n", "2076         2098  44.4007393573\n", "2077         2099  44.5843226669\n", "2078         2100  44.4592473823\n", "2079         2101  44.5652432403\n", "2080         2102  44.2492879590\n", "2081         2103  44.0760464454\n", "2082         2104  43.9879135307\n", "2083         2105  44.7491149513\n", "2084         2106  42.5994001297\n", "2085         2107  42.3607504396\n", "2086         2108  42.3961913540\n", "2087         2109  42.6617003995\n", "2088         2110  42.4808282146\n", "2089         2111  42.6684084996\n", "2090         2112  42.6338473980\n", "2091         2113  42.5752350331\n", "2092         2114  42.2291284819\n", "2093         2115  42.2684292717\n", "2094         2116  41.5220254501\n", "2095         2117  40.9459303014\n", "2096         2118  40.4103441746\n", "2097         2119  40.0871999111\n", "2098         2120  39.8741750455\n", "2099         2121  39.5527575982\n", "2100         2122  39.1981505136\n", "2101         2123  39.1908951594\n", "2102         2124  39.2630687545\n", "2103         2125  39.1382181049\n", "2104         2126  39.0365852482\n", "2105         2127  39.2302496945\n", "2106         2128  39.2115977672\n", "2107         2129  39.3267655744\n", "2108         2130  39.1760371704\n", "2109         2131  39.4870040755\n", "2110         2132  39.5956998841\n", "2111         2133  39.3296060111\n", "2112         2134  39.4412863203\n", "2113         2135  39.6664335307\n", "2114         2136  39.6809339975\n", "2115         2137  39.6309220751\n", "2116         2138  39.7980231503\n", "2117         2139  39.6845945493\n", "2118         2140  39.6423370009\n", "2119         2141  39.5436862959\n", "2120         2142  39.4879764192\n", "2121         2144  39.2978181327\n", "2122         2145  39.0969411306\n", "2123         2146  38.8730541604\n", "2124         2147  38.1273434729\n", "2125         2148  38.3242772160\n", "2126         2149  37.9265173483\n", "2127         2150  37.9555567836\n", "2128         2151  37.8588902563\n", "2129         2152  37.6942129160\n", "2130         2153  37.8690036988\n", "2131         2154  38.4481573095\n", "2132         2155  38.7289235723\n", "2133         2156  40.5040145173\n", "2134         2157  40.4538963267\n", "2135         2158  41.0695281959\n", "2136         2159  40.9663963477\n", "2137         2160  41.0269385610\n", "2138         2161  40.9208002806\n", "2139         2162  40.1799683786\n", "2140         2163  40.9331373002\n", "2141         2164  41.3365220007\n", "2142         2165  41.2185198196\n", "2143         2166  41.3281683161\n", "2144         2167  41.9439798534\n", "2145         2168  41.8363691264\n", "2146         2169  42.9247000996\n", "2147         2170  43.0811686450\n", "2148         2171  43.3388719473\n", "2149         2172  43.7163251659\n", "2150         2173  41.9809527318\n", "2151         2174  42.2435238203\n", "2152         2175  42.7717779345\n", "2153         2176  43.0351794485\n", "2154         2177  43.0991370490\n", "2155         2178  43.2786741713\n", "2156         2179  43.3320486075\n", "2157         2180  43.7037573358\n", "2158         2181  44.1399402986\n", "2159         2182  44.1536847679\n", "2160         2183  43.0907843580\n", "2161         2184  44.1666268932\n", "2162         2185  44.8185220851\n", "2163         2186  45.0792666987\n", "2164         2187  45.1622693744\n", "2165         2188  45.2076637292\n", "2166         2189  45.2951210399\n", "2167         2190  44.8894468854\n", "2168         2191  44.6928542292\n", "2169         2192  44.1819376721\n", "2170         2193  43.7105118944\n", "2171         2194  43.2132470813\n", "2172         2195  42.4122649781\n", "2173         2196  42.1627265896\n", "2174         2197  41.4778481700\n", "2175         2198  40.6864610472\n", "2176         2199  40.7634680370\n", "2177         2200  40.2857265328\n", "2178         2201  40.2934844489\n", "2179         2202  39.0587983411\n", "2180         2203  38.9828447804\n", "2181         2204  39.6010583839\n", "2182         2205  39.5632275311\n", "2183         2206  40.2028183058\n", "2184         2207  40.4894914623\n", "2185         2208  40.3880463442\n", "2186         2209  40.3789815867\n", "2187         2210  40.4581243298\n", "2188         2211  40.5505038214\n", "2189         2212  40.2676493220\n", "2190         2213  40.1796627930\n", "2191         2214  40.1251229429\n", "2192         2215  40.1358283320\n", "2193         2216  40.2781080497\n", "2194         2217  40.6009791715\n", "2195         2218  40.6214845547\n", "2196         2219  40.6668024965\n", "2197         2220  40.8185634287\n", "2198         2221  41.1184580504\n", "2199         2222  41.3794417590\n", "2200         2223  41.3186907580\n", "2201         2224  41.5439377214\n", "2202         2225  42.1786494651\n", "2203         2226  41.9698356185\n", "2204         2227  41.7630789829\n", "2205         2228  41.6216961944\n", "2206         2229  41.6494804982\n", "2207         2230  41.9383102831\n", "2208         2231  42.3619881842\n", "2209         2232  42.0168504163\n", "2210         2233  41.6719048694\n", "2211         2234  41.5760418280\n", "2212         2235  41.5589744548\n", "2213         2236  41.6799239833\n", "2214         2237  41.8128557148\n", "2215         2238  42.8866697415\n", "2216         2239  42.9424857703\n", "2217         2240  43.2721619549\n", "2218         2241  43.5359900768\n", "2219         2242  43.9887483056\n", "2220         2243  44.2459651283\n", "2221         2244  44.6282950764\n", "2222         2245  44.8376684824\n", "2223         2246  45.2022481519\n", "2224         2247  45.5791691633\n", "2225         2248  45.6716852122\n", "2226         2249  45.8861000676\n", "2227         2250  45.9847659994\n", "2228         2251  46.1222773919\n", "2229         2252  47.0407196128\n", "2230         2254  44.5305069642\n", "2231         2255  44.3826333677\n", "2232         2256  44.6255942139\n", "2233         2257  44.8983513406\n", "2234         2258  45.2710818146\n", "2235         2259  45.3437939794\n", "2236         2260  45.4599051991\n", "2237         2261  45.2348963394\n", "2238         2262  44.7233907661\n", "2239         2263  43.4110923693\n", "2240         2264  42.6560060888\n", "2241         2265  42.3546328677\n", "2242         2266  41.8721926301\n", "2243         2267  41.7105546973\n", "2244         2268  41.4285211149\n", "2245         2269  40.9179945890\n", "2246         2270  40.4348700818\n", "2247         2271  40.1070318513\n", "2248         2272  39.8658856094\n", "2249         2273  40.2815898710\n", "2250         2274  40.3497889754\n", "2251         2275  40.2816400113\n", "2252         2276  40.4951430363\n", "2253         2277  40.6775915123\n", "2254         2278  41.5627730487\n", "2255         2279  42.0787535293\n", "2256         2280  42.5471791151\n", "2257         2281  42.2308487571\n", "2258         2282  42.2464219954\n", "2259         2283  42.2510109965\n", "2260         2284  42.2321605421\n", "2261         2285  42.3861448386\n", "2262         2286  42.6275887661\n", "2263         2287  41.9355866532\n", "2264         2288  41.0104227509\n", "2265         2289  40.1697626012\n", "2266         2290  40.7281583320\n", "2267         2291  40.8359857095\n", "2268         2292  41.3289563979\n", "2269         2293  41.5444214754\n", "2270         2294  41.7347949358\n", "2271         2295  41.8928307458\n", "2272         2296  41.9907813688\n", "2273         2297  41.9221299686\n", "2274         2298  41.9683447706\n", "2275         2299  41.8231435152\n", "2276         2300  40.8984911254\n", "2277         2301  41.4632472507\n", "2278         2302  41.5048849670\n", "2279         2303  41.3116119551\n", "2280         2304  41.6611814775\n", "2281         2305  41.1070753678\n", "2282         2306  40.5400790971\n", "2283         2307  40.2465863012\n", "2284         2308  40.0867337456\n", "2285         2309  39.3430352896\n", "2286         2310  38.8519146020\n", "2287         2311  38.8815393528\n", "2288         2312  38.7888765112\n", "2289         2313  38.5312856411\n", "2290         2314  38.6079851411\n", "2291         2315  38.8148525447\n", "2292         2316  38.6504573766\n", "2293         2317  38.5838442715\n", "2294         2318  38.5536894720\n", "2295         2319  38.6080468467\n", "2296         2320  39.7540531272\n", "2297         2321  40.2409288553\n", "2298         2322  40.5636833946\n", "2299         2323  41.4411179535\n", "2300         2324  41.6752001110\n", "2301         2325  41.6408889291\n", "2302         2326  42.4282205295\n", "2303         2327  42.3586441875\n", "2304         2328  42.8778577927\n", "2305         2329  42.2311394905\n", "2306         2330  42.3616709290\n", "2307         2331  42.0501945482\n", "2308         2332  42.2500383035\n", "2309         2333  42.4228869870\n", "2310         2334  42.4582049686\n", "2311         2335  41.9107221377\n", "2312         2336  41.4532070753\n", "2313         2337  41.8631918170\n", "2314         2338  42.3203506642\n", "2315         2339  42.6676115008\n", "2316         2340  42.9752638015\n", "2317         2341  43.4034105571\n", "2318         2342  43.6217199351\n", "2319         2343  43.6753773616\n", "2320         2344  43.4579211990\n", "2321         2345  43.5752104206\n", "2322         2346  43.3440778961\n", "2323         2347  43.0269453084\n", "2324         2348  43.0117932041\n", "2325         2349  42.6489805486\n", "2326         2350  42.4612508273\n", "2327         2351  42.4231915894\n", "2328         2352  42.3335633889\n", "2329         2353  42.0905226483\n", "2330         2354  42.0958936006\n", "2331         2355  42.1078958446\n", "2332         2356  41.8627126551\n", "2333         2357  41.6559097106\n", "2334         2358  41.4198058379\n", "2335         2359  41.1204526596\n", "2336         2360  41.1998615056\n", "2337         2361  40.9825315018\n", "2338         2362  40.7339589350\n", "2339         2363  40.6993346498\n", "2340         2364  40.1274740039\n", "2341         2365  39.3983239173\n", "2342         2366  39.2275729547\n", "2343         2367  39.0973006389\n", "2344         2368  39.0337281472\n", "2345         2369  38.8231601331\n", "2346         2370  38.8443580664\n", "2347         2371  38.8761346715\n", "2348         2372  38.6971440521\n", "2349         2373  38.8678833112\n", "2350         2374  38.7555276392\n", "2351         2375  38.8414577842\n", "2352         2376  38.8087703063\n", "2353         2377  38.5637255352\n", "2354         2378  38.9705100587\n", "2355         2379  38.1715568325\n", "2356         2380  38.6116210183\n", "2357         2381  38.3883351573\n", "2358         2382  38.0267268342\n", "2359         2383  38.0011446511\n", "2360         2384  37.7528901769\n", "2361         2385  37.5284553273\n", "2362         2386  37.4804038995\n", "2363         2388  37.6832942538\n", "2364         2389  37.8983444148\n", "2365         2390  37.8937369637\n", "2366         2391  37.9620028673\n", "2367         2392  37.9178497362\n", "2368         2393  38.2592280899\n", "2369         2394  39.1178649084\n", "2370         2395  39.1931748215\n", "2371         2396  39.5153358187\n", "2372         2397  39.8352547055\n", "2373         2398  40.1101483085\n", "2374         2399  40.4658391560\n", "2375         2400  40.4605512100\n", "2376         2401  40.1899597272\n", "2377         2402  40.5271995995\n", "2378         2403  40.1559391342\n", "2379         2404  40.2062563492\n", "2380         2405  40.2351445341\n", "2381         2406  40.4629164543\n", "2382         2407  41.0521283589\n", "2383         2408  40.8756807986\n", "2384         2409  41.1593266102\n", "2385         2410  41.7205256018\n", "2386         2411  41.8715429760\n", "2387         2412  42.1438836397\n", "2388         2413  40.8583563651\n", "2389         2414  41.6371214212\n", "2390         2415  42.9457964651\n", "2391         2416  43.2602788654\n", "2392         2417  43.2816680204\n", "2393         2418  43.5704510281\n", "2394         2419  43.8367624084\n", "2395         2420  43.8484748407\n", "2396         2421  44.0459881879\n", "2397         2422  44.3395610331\n", "2398         2423  44.2347110735\n", "2399         2424  43.5522060037\n", "2400         2425  43.8903467307\n", "2401         2426  44.8102196125\n", "2402         2427  44.6647049172\n", "2403         2428  44.9085322130\n", "2404         2429  45.1509752206\n", "2405         2430  45.4250460902\n", "2406         2431  45.2701685853\n", "2407         2432  45.2078476048\n", "2408         2433  45.0588461399\n", "2409         2434  44.6195489368\n", "2410         2435  45.1933757910\n", "2411         2436  43.1327188879\n", "2412         2437  43.3788021711\n", "2413         2438  43.5638713187\n", "2414         2439  43.8233471493\n", "2415         2440  44.0803045656\n", "2416         2441  44.2380340741\n", "2417         2442  44.4884604599\n", "2418         2443  44.9213027111\n", "2419         2444  44.0614768460\n", "2420         2445  43.6098668784\n", "2421         2446  42.6349167232\n", "2422         2447  42.2770219718\n", "2423         2448  41.2514764021\n", "2424         2449  40.7526083473\n", "2425         2450  41.2467682383\n", "2426         2451  41.6126191852\n", "2427         2452  43.4710425354\n", "2428         2453  41.3138651748\n", "2429         2454  40.7202713991\n", "2430         2455  40.9543844436\n", "2431         2456  40.8258875258\n", "2432         2457  40.8854214548\n", "2433         2458  40.9992445507\n", "2434         2459  40.8158702199\n", "2435         2460  40.7834398096\n", "2436         2461  40.9537364855\n", "2437         2462  40.7858777464\n", "2438         2463  41.1201348216\n", "2439         2464  41.2566958419\n", "2440         2465  41.6630538586\n", "2441         2466  41.5695252689\n", "2442         2467  41.8913214463\n", "2443         2468  41.7312297425\n", "2444         2469  41.2333020618\n", "2445         2470  42.4650079092\n", "2446         2471  43.2455491913\n", "2447         2472  43.5525768496\n", "2448         2473  43.6298085235\n", "2449         2474  42.2677296692\n", "2450         2475  42.4573384395\n", "2451         2476  42.7696037826\n", "2452         2477  42.4047440168\n", "2453         2478  41.9809955909\n", "2454         2479  41.5050334892\n", "2455         2480  41.1835239428\n", "2456         2481  40.7290164886\n", "2457         2482  40.1465273954\n", "2458         2483  40.7282062726\n", "2459         2484  40.8035948960\n", "2460         2485  40.3501730961\n", "2461         2486  40.4711017202\n", "2462         2487  40.4985558834\n", "2463         2488  40.7461030380\n", "2464         2489  41.1008240869\n", "2465         2490  41.5188663744\n", "2466         2491  41.6480696317\n", "2467         2492  42.4325545396\n", "2468         2493  42.8396194557\n", "2469         2494  42.9869644804\n", "2470         2495  43.3853004072\n", "2471         2496  43.5397066301\n", "2472         2497  43.8345696550\n", "2473         2498  44.1320014090\n", "2474         2499  44.3267123152\n", "2475         2500  44.6509736079\n", "2476         2501  44.7727747758\n", "2477         2502  44.9734140612\n", "2478         2503  45.0788001075\n", "2479         2504  45.2516819474\n", "2480         2505  45.5208834053\n", "2481         2506  45.6055358414\n", "2482         2507  46.0583564216\n", "2483         2508  46.0068742831\n", "2484         2509  46.0995024891\n", "2485         2510  46.0792308243\n", "2486         2511  46.1932080116\n", "2487         2512  46.0101647096\n", "2488         2513  46.5068901893\n", "2489         2514  46.2873031697\n", "2490         2515  46.5554867914\n", "2491         2516  47.0802688611\n", "2492         2517  46.9142577351\n", "2493         2518  47.6681308574\n", "2494         2519  47.9649573399\n", "2495         2520  46.6374943681\n", "2496         2521  46.2771527105\n", "2497         2522  46.3022974631\n", "2498         2523  46.3761371593\n", "2499         2524  46.5093485113\n", "2500         2525  46.7564141324\n", "2501         2526  47.3379259710\n", "2502         2527  46.4072488417\n", "2503         2528  46.0161502482\n", "2504         2529  45.7999757138\n", "2505         2530  45.8778111066\n", "2506         2531  45.8676498907\n", "2507         2532  46.0947253599\n", "2508         2533  46.1843800246\n", "2509         2534  46.0357800650\n", "2510         2535  46.0170165613\n", "2511         2536  45.9667507248\n", "2512         2537  46.1674814613\n", "2513         2538  46.2461854100\n", "2514         2539  46.3595979138\n", "2515         2540  46.6081062190\n", "2516         2541  46.7218603109\n", "2517         2542  46.3080035240\n", "2518         2543  44.8757499380\n", "2519         2544  45.0220644825\n", "2520         2545  45.5004767493\n", "2521         2546  45.7615461913\n", "2522         2547  45.8670015704\n", "2523         2548  45.4203015123\n", "2524         2549  44.9233673338\n", "2525         2550  43.8481574560\n", "2526         2551  42.5692002359\n", "2527         2552  41.9314401659\n", "2528         2553  41.1348035298\n", "2529         2554  41.1239312024\n", "2530         2555  40.8605444026\n", "2531         2556  40.8148823203\n", "2532         2557  40.6446871972\n", "2533         2558  40.6655675333\n", "2534         2559  40.3602423661\n", "2535         2561  41.9364669664\n", "2536         2562  41.5527901829\n", "2537         2563  40.3847746535\n", "2538         2564  40.5281073933\n", "2539         2565  40.4379865379\n", "2540         2566  40.3242711153\n", "2541         2567  40.1829293512\n", "2542         2568  40.3911063474\n", "2543         2569  39.8954660798\n", "2544         2570  39.8832207179\n", "2545         2571  40.1633210170\n", "2546         2572  40.0360332783\n", "2547         2573  40.1083247756\n", "2548         2574  40.0676992770\n", "2549         2575  40.3007622086\n", "2550         2576  40.3210676957\n", "2551         2577  40.5748526084\n", "2552         2578  40.5102124150\n", "2553         2579  40.5317580170\n", "2554         2580  40.7553474456\n", "2555         2581  40.6582047427\n", "2556         2582  40.5754975602\n", "2557         2583  40.4764028538\n", "2558         2584  40.3966845977\n", "2559         2585  40.0125661483\n", "2560         2586  39.7955846834\n", "2561         2587  39.7476924590\n", "2562         2588  39.2271495897\n", "2563         2589  39.0442174872\n", "2564         2590  39.0610998056\n", "2565         2591  38.9072366038\n", "2566         2592  39.9930363291\n", "2567         2593  39.9975831045\n", "2568         2594  39.8354587250\n", "2569         2595  40.1112895065\n", "2570         2596  40.4446852759\n", "2571         2597  40.6896884912\n", "2572         2598  40.9129810901\n", "2573         2599  41.0860463139\n", "2574         2600  41.6198725586\n", "2575         2601  41.9481383455\n", "2576         2602  41.9780566398\n", "2577         2603  42.1752960505\n", "2578         2604  42.3559807026\n", "2579         2605  42.7820098943\n", "2580         2606  42.8304559625\n", "2581         2607  43.4046699836\n", "2582         2608  43.6412379296\n", "2583         2609  43.8301167554\n", "2584         2610  44.2184480038\n", "2585         2611  44.5198025083\n", "2586         2612  44.5829092342\n", "2587         2613  43.8887132869\n", "2588         2614  43.0798639894\n", "2589         2615  43.3121209366\n", "2590         2616  43.6304821697\n", "2591         2617  44.0676167313\n", "2592         2618  44.0111774432\n", "2593         2619  43.9307786464\n", "2594         2620  43.2563036216\n", "2595         2621  42.8343961426\n", "2596         2622  42.3963906915\n", "2597         2623  42.1050630271\n", "2598         2624  42.2303182874\n", "2599         2625  42.0170743680\n", "2600         2626  41.6781278581\n", "2601         2627  42.0213270243\n", "2602         2628  42.2668950304\n", "2603         2629  42.3821794561\n", "2604         2630  42.6592368067\n", "2605         2631  43.1753153476\n", "2606         2632  43.1140433879\n", "2607         2633  43.6302248019\n", "2608         2634  42.8960438299\n", "2609         2635  43.2439418549\n", "2610         2636  42.8224627968\n", "2611         2637  42.6800833787\n", "2612         2638  42.5976666778\n", "2613         2639  42.3398747995\n", "2614         2640  43.5744472579\n", "2615         2641  41.2971348141\n", "2616         2642  40.1970248243\n", "2617         2643  40.5189579953\n", "2618         2644  40.5334030099\n", "2619         2645  40.5890039277\n", "2620         2646  40.6065883857\n", "2621         2647  41.0223150105\n", "2622         2648  41.8318911081\n", "2623         2649  42.2617562718\n", "2624         2650  41.8368954466\n", "2625         2651  41.8450795093\n", "2626         2652  41.9591655618\n", "2627         2653  42.0215587540\n", "2628         2654  42.1468285241\n", "2629         2655  42.3199881347\n", "2630         2656  42.3460474471\n", "2631         2657  42.5123165653\n", "2632         2658  42.5895540482\n", "2633         2659  43.0607842257\n", "2634         2660  43.1461414475\n", "2635         2661  42.9867673012\n", "2636         2662  43.1951647486\n", "2637         2663  43.7262072452\n", "2638         2664  44.4615628100\n", "2639         2665  43.1196598287\n", "2640         2666  42.8206191654\n", "2641         2667  42.2348156328\n", "2642         2668  42.1577637858\n", "2643         2669  42.0357583360\n", "2644         2670  41.8435578082\n", "2645         2671  41.7529352778\n", "2646         2672  41.4744807895\n", "2647         2673  41.9022894699\n", "2648         2674  41.8368588138\n", "2649         2675  42.8942344742\n", "2650         2676  42.8585394759\n", "2651         2677  43.3174368356\n", "2652         2678  43.8859297640\n", "2653         2679  44.2837614486\n", "2654         2680  44.9395480433\n", "2655         2681  45.0737829273\n", "2656         2682  44.9129427304\n", "2657         2683  45.0123837402\n", "2658         2684  45.3373596690\n", "2659         2685  45.8396396956\n", "2660         2686  46.0335178374\n", "2661         2687  45.9255143699\n", "2662         2688  46.4025855552\n", "2663         2689  46.6217146724\n", "2664         2690  46.5754144364\n", "2665         2691  46.6910554736\n", "2666         2692  46.6473121727\n", "2667         2693  46.3728971496\n", "2668         2694  46.5266907469\n", "2669         2695  46.3238662450\n", "2670         2696  45.2483750156\n", "2671         2697  45.9066600720\n", "2672         2698  45.6570362832\n", "2673         2699  47.5126739878\n", "2674         2700  47.8151939376\n", "2675         2701  47.8907750670\n", "2676         2702  47.8548287408\n", "2677         2703  47.7486674220\n", "2678         2704  47.4962353506\n", "2679         2705  46.9072389421\n", "2680         2706  46.4899462750\n", "2681         2707  46.6992042656\n", "2682         2708  46.4685565789\n", "2683         2709  46.6156317762\n", "2684         2710  46.7004998526\n", "2685         2711  46.9192698725\n", "2686         2712  47.3365395049\n", "2687         2713  47.3745987018\n", "2688         2714  47.7562813347\n", "2689         2715  47.5160313257\n", "2690         2716  47.5379869202\n", "2691         2717  47.4310969648\n", "2692         2718  47.6083707814\n", "2693         2719  48.0516679891\n", "2694         2720  48.2655714908\n", "2695         2721  48.0737909375\n", "2696         2722  47.8694264936\n", "2697         2723  47.8498531828\n", "2698         2724  47.5300774663\n", "2699         2725  46.6869097516\n", "2700         2726  46.7701257265\n", "2701         2727  45.4106955894\n", "2702         2728  44.0999621698\n", "2703         2729  43.6728712217\n", "2704         2730  43.3507163428\n", "2705         2731  43.3419791690\n", "2706         2732  43.3446612571\n", "2707         2733  43.4617824138\n", "2708         2734  43.4821322275\n", "2709         2735  43.8131578204\n", "2710         2736  43.9143689966\n", "2711         2737  44.0515387458\n", "2712         2738  44.1631428866\n", "2713         2739  44.3743863705\n", "2714         2740  44.9479998454\n", "2715         2741  45.2260984418\n", "2716         2742  45.5144272718\n", "2717         2743  45.8152191471\n", "2718         2744  46.0984908885\n", "2719         2745  46.3234853409\n", "2720         2746  46.5154511718\n", "2721         2747  46.7690496514\n", "2722         2748  46.4956828266\n", "2723         2749  45.9959112221\n", "2724         2750  46.4233375738\n", "2725         2751  45.4259828451\n", "2726         2752  45.4577962740\n", "2727         2753  45.6790937740\n", "2728         2754  45.8426812988\n", "2729         2755  46.0472001494\n", "2730         2756  46.1603598584\n", "2731         2757  46.4474252087\n", "2732         2758  46.8288913881\n", "2733         2759  46.6243973662\n", "2734         2760  46.7026285083\n", "2735         2761  46.4985362903\n", "2736         2762  46.9548933967\n", "2737         2763  46.6105600350\n", "2738         2764  45.9470039624\n", "2739         2765  46.3935307029\n", "2740         2766  46.5383629688\n", "2741         2767  46.3648024070\n", "2742         2768  46.6472429795\n", "2743         2769  46.1342871471\n", "2744         2770  44.8351402750\n", "2745         2771  44.6693075367\n", "2746         2772  44.3510127114\n", "2747         2774  43.2797184885\n", "2748         2775  42.8964205918\n", "2749         2776  42.5821252511\n", "2750         2777  42.4107098518\n", "2751         2778  41.6469705870\n", "2752         2779  41.2273719620\n", "2753         2780  41.0576852606\n", "2754         2781  39.8446265966\n", "2755         2782  40.3094858095\n", "2756         2783  41.8504170893\n", "2757         2784  39.6489803085\n", "2758         2785  38.5470286754\n", "2759         2786  38.5683479116\n", "2760         2787  38.7487461637\n", "2761         2788  38.9948525345\n", "2762         2789  39.4459311910\n", "2763         2790  39.5683067542\n", "2764         2791  39.8269054796\n", "2765         2792  39.9196037776\n", "2766         2793  39.8685153940\n", "2767         2794  40.1330458257\n", "2768         2795  40.2308330059\n", "2769         2796  40.6315523698\n", "2770         2797  40.9787291513\n", "2771         2798  41.2752820599\n", "2772         2799  41.6487187907\n", "2773         2800  42.3068798936\n", "2774         2801  42.6746255324\n", "2775         2802  42.9605192294\n", "2776         2803  44.3619799377\n", "2777         2804  41.8894726248\n", "2778         2805  42.5153120256\n", "2779         2806  42.8441838661\n", "2780         2807  43.2593870494\n", "2781         2808  43.3022832641\n", "2782         2809  43.3758335903\n", "2783         2810  43.3575988545\n", "2784         2811  43.1923586350\n", "2785         2812  42.7866529492\n", "2786         2813  42.3205271834\n", "2787         2814  42.0371258695\n", "2788         2815  41.4224830314\n", "2789         2816  41.1951287633\n", "2790         2817  40.7992568451\n", "2791         2818  40.6145913180\n", "2792         2819  40.7210593642\n", "2793         2820  40.9165951420\n", "2794         2821  39.6316096065\n", "2795         2822  39.4596193425\n", "2796         2823  39.8311365103\n", "2797         2824  40.1419682496\n", "2798         2825  40.6683462252\n", "2799         2826  40.9178247909\n", "2800         2827  40.9990179021\n", "2801         2828  43.0780152410\n", "2802         2829  42.3382925184\n", "2803         2830  42.2899178613\n", "2804         2831  42.1380792811\n", "2805         2832  42.1592690267\n", "2806         2833  42.1610628265\n", "2807         2834  42.3037093385\n", "2808         2835  42.6476446077\n", "2809         2836  42.5188481246\n", "2810         2837  42.4275517245\n", "2811         2838  42.4105686344\n", "2812         2839  43.1074956296\n", "2813         2840  43.8191834203\n", "2814         2841  44.0734051886\n", "2815         2842  44.5836891571\n", "2816         2843  44.7775427610\n", "2817         2844  45.3736264513\n", "2818         2845  45.9223746662\n", "2819         2846  46.2303005569\n", "2820         2847  46.4459283343\n", "2821         2848  46.0641966699\n", "2822         2849  44.3437754358\n", "2823         2850  45.2892459617\n", "2824         2851  45.7179211120\n", "2825         2852  46.4372255464\n", "2826         2853  46.7791648735\n", "2827         2854  47.2446371920\n", "2828         2855  47.5494873332\n", "2829         2856  47.2115035166\n", "2830         2857  46.3829978030\n", "2831         2858  46.1225281699\n", "2832         2859  45.6553008265\n", "2833         2860  45.6433925731\n", "2834         2861  44.9685646841\n", "2835         2862  44.1166937134\n", "2836         2863  45.5275771149\n", "2837         2864  44.3229371863\n", "2838         2865  43.6216470110\n", "2839         2866  43.6441931008\n", "2840         2867  43.6539037949\n", "2841         2868  45.0352449577\n", "2842         2869  46.2570493929\n", "2843         2870  44.7801248505\n", "2844         2871  44.3893385057\n", "2845         2872  44.3097673048\n", "2846         2873  44.4282401184\n", "2847         2874  44.2828566677\n", "2848         2875  44.4236669634\n", "2849         2876  44.0502129786\n", "2850         2877  44.2399492253\n", "2851         2878  44.3243704134\n", "2852         2879  44.4513284003\n", "2853         2880  44.6447183341\n", "2854         2881  43.9974111234\n", "2855         2882  42.9394932375\n", "2856         2883  43.5637355099\n", "2857         2884  43.9622191592\n", "2858         2885  44.1532806439\n", "2859         2886  44.4217498158\n", "2860         2887  44.3905931089\n", "2861         2888  44.0333753505\n", "2862         2889  43.2675541197\n", "2863         2890  42.6329060916\n", "2864         2891  41.9808975668\n", "2865         2892  41.3923062780\n", "2866         2893  40.9673121775\n", "2867         2894  40.6652685572\n", "2868         2895  40.2343559969\n", "2869         2896  39.9369040214\n", "2870         2897  39.6605017758\n", "2871         2898  39.4620494998\n", "2872         2899  38.9154040506\n", "2873         2900  38.6284049326\n", "2874         2901  38.6073541067\n", "2875         2902  38.7627059356\n", "2876         2903  38.8099605503\n", "2877         2904  38.8240362057\n", "2878         2905  38.5754544213\n", "2879         2906  38.9122780421\n", "2880         2907  39.2650930654\n", "2881         2908  39.4112722253\n", "2882         2909  39.3194938744\n", "2883         2910  39.4253731670\n", "2884         2911  39.5325367820\n", "2885         2912  40.0431718548\n", "2886         2913  40.2385513352\n", "2887         2914  40.2434500757\n", "2888         2915  39.9043275929\n", "2889         2916  39.9025292499\n", "2890         2917  39.7534509468\n", "2891         2918  39.6788896087\n", "2892         2919  39.3783199582\n", "2893         2920  39.2821195623\n", "2894         2921  38.5115980527\n", "2895         2922  39.4681274957\n", "2896         2923  39.4895145499\n", "2897         2924  38.7284512382\n", "2898         2925  38.7841506070\n", "2899         2926  38.8938708247\n", "2900         2927  39.0314281986\n", "2901         2928  39.0323978512\n", "2902         2929  38.6215948388\n", "2903         2930  38.5797885214\n", "2904         2931  38.2613715673\n", "2905         2932  38.1220026358\n", "2906         2933  38.3437056766\n", "2907         2934  38.6245087657\n", "2908         2935  38.5353007867\n", "2909         2936  38.7147455268\n", "2910         2937  38.7684463081\n", "2911         2938  38.7524650324\n", "2912         2939  38.9846951342\n", "2913         2940  41.0619902797\n", "2914         2941  40.5650291173\n", "2915         2942  39.8239042558\n", "2916         2943  39.6891835526\n", "2917         2944  39.7273502564\n", "2918         2945  39.8845387992\n", "2919         2946  39.9427235723\n", "2920         2947  40.2352056793\n", "2921         2948  40.1106192697\n", "2922         2949  39.0401836578\n", "2923         2950  38.7663220432\n", "2924         2951  39.1675191073\n", "2925         2952  39.0026592796\n", "2926         2953  39.2413393614\n", "2927         2954  39.1488980070\n", "2928         2955  39.5357129887\n", "2929         2956  39.4736314507\n", "2930         2957  39.4799489468\n", "2931         2958  39.7126447676\n", "2932         2959  40.1051362030\n", "2933         2960  40.3048684678\n", "2934         2961  39.5443634815\n", "2935         2962  39.8856984566\n", "2936         2963  39.9391690449\n", "2937         2964  39.9416817692\n", "2938         2965  40.0901884667\n", "2939         2966  40.4698534997\n", "2940         2967  39.0074951158\n", "2941         2968  39.7172577400\n", "2942         2969  39.1663142115\n", "2943         2970  39.9615799909\n", "2944         2971  39.7036607139\n", "2945         2972  39.8985967675\n", "2946         2973  39.9047649935\n", "2947         2974  40.1329681282\n", "2948         2975  39.7722255810\n", "2949         2976  39.8999128666\n", "2950         2977  40.0486601398\n", "2951         2978  40.2139991503\n", "2952         2979  40.3879958963\n", "2953         2980  40.6632351612\n", "2954         2981  40.9412427739\n", "2955         2982  41.2711708939\n", "2956         2983  43.1433672153\n", "2957         2984  40.9956182194\n", "2958         2985  41.4713679316\n", "2959         2986  41.9997639067\n", "2960         2987  42.3083061075\n", "2961         2988  42.6805771161\n", "2962         2989  42.7554632748\n", "2963         2990  43.0396085853\n", "2964         2991  43.6524557955\n", "2965         2992  43.7353435747\n", "2966         2993  43.5842169801\n", "2967         2994  43.4224099615\n", "2968         2995  43.0706714413\n", "2969         2996  43.1232515996\n", "2970         2997  43.1004514668\n", "2971         2998  43.5002613148\n", "2972         2999  43.9966840513\n", "2973         3000  44.0923604108\n", "2974         3001  43.7386678415\n", "2975         3002  43.4582071581\n", "2976         3003  42.9162826431\n", "2977         3004  43.4404884731\n", "2978         3005  42.5086913430\n", "2979         3006  42.4501343861\n", "2980         3007  42.3260993453\n", "2981         3008  42.0125979118\n", "2982         3009  41.8569588558\n", "2983         3010  41.8115935465\n", "2984         3011  41.6811781874\n", "2985         3013  41.4623848457\n", "2986         3014  40.8826335890\n", "2987         3015  42.3105479723\n", "2988         3016  41.4558408516\n", "2989         3017  41.5023934250\n", "2990         3018  40.3391638120\n", "2991         3019  39.9736655714\n", "2992         3020  39.8855580698\n", "2993         3021  39.6863210236\n", "2994         3022  39.5360591631\n", "2995         3023  39.6137090474\n", "2996         3024  39.7329031746\n", "2997         3025  39.6695525797\n", "2998         3026  40.2520316562\n", "2999         3027  40.5295454788\n", "3000         3028  40.1503772727\n", "3001         3029  39.8152636020\n", "3002         3030  40.0634826108\n", "3003         3031  40.4282164806\n", "3004         3032  40.7257959872\n", "3005         3033  41.0560017857\n", "3006         3034  41.4554950039\n", "3007         3035  41.5793507451\n", "3008         3036  40.4824365078\n", "3009         3037  40.0831091394\n", "3010         3038  40.7758817280\n", "3011         3039  41.2893980590\n", "3012         3040  41.5237172888\n", "3013         3041  41.7271862089\n", "3014         3042  41.5879473991\n", "3015         3043  41.2922983906\n", "3016         3044  41.3287146920\n", "3017         3045  41.0686707500\n", "3018         3046  41.0922221593\n", "3019         3047  40.5374567663\n", "3020         3048  40.3394227874\n", "3021         3049  39.8886401270\n", "3022         3050  39.6081071932\n", "3023         3051  39.4339535667\n", "3024         3052  39.2263424001\n", "3025         3053  39.2978200310\n", "3026         3054  39.1769734607\n", "3027         3055  39.2588883675\n", "3028         3056  39.3820679768\n", "3029         3057  40.1096575931\n", "3030         3058  40.4428600680\n", "3031         3059  40.5295250704\n", "3032         3060  41.2257875071\n", "3033         3061  43.2744913899\n", "3034         3062  42.7153268347\n", "3035         3063  42.9613229991\n", "3036         3064  43.2590261571\n", "3037         3065  44.0037090259\n", "3038         3066  43.8544534191\n", "3039         3067  43.8503916430\n", "3040         3068  44.6236254382\n", "3041         3069  43.6872508332\n", "3042         3070  41.9090466878\n", "3043         3071  41.4459248262\n", "3044         3072  41.8835357999\n", "3045         3073  41.8842393565\n", "3046         3074  42.2535217707\n", "3047         3075  42.4647815478\n", "3048         3076  42.6604828564\n", "3049         3077  42.5750144094\n", "3050         3078  42.4049902597\n", "3051         3079  42.1575692931\n", "3052         3080  41.9804919681\n", "3053         3081  41.2764703203\n", "3054         3082  40.7388266970\n", "3055         3083  40.5706528204\n", "3056         3084  40.5830349690\n", "3057         3085  40.8580806138\n", "3058         3086  40.5121908756\n", "3059         3087  40.9079299622\n", "3060         3088  40.6742542188\n", "3061         3089  40.2926008187\n", "3062         3090  40.0272761106\n", "3063         3091  39.7830840776\n", "3064         3092  42.1198371126\n", "3065         3093  41.0589803377\n", "3066         3094  39.9043454823\n", "3067         3095  39.6811158512\n", "3068         3096  39.4235969647\n", "3069         3097  39.6595165431\n", "3070         3098  39.7984770895\n", "3071         3099  39.6850735863\n", "3072         3100  39.6296649257\n", "3073         3101  39.6648003884\n", "3074         3102  40.0135231194\n", "3075         3103  40.1027758132\n", "3076         3104  40.1350031167\n", "3077         3105  40.4071792933\n", "3078         3106  40.6027292137\n", "3079         3107  41.0000171294\n", "3080         3108  41.1204582416\n", "3081         3109  41.6712461466\n", "3082         3110  41.8128694869\n", "3083         3111  41.8238293818\n", "3084         3112  41.4554908213\n", "3085         3113  41.0742266541\n", "3086         3114  40.5883590653\n", "3087         3115  40.4428101700\n", "3088         3116  40.3758935728\n", "3089         3117  40.1592978342\n", "3090         3118  39.6817392495\n", "3091         3119  39.7490301127\n", "3092         3120  39.4680372219\n", "3093         3121  39.4200760563\n", "3094         3122  39.3107552545\n", "3095         3123  38.9706508734\n", "3096         3124  38.7747700798\n", "3097         3125  39.4493490172\n", "3098         3126  39.6380825005\n", "3099         3127  39.5859105970\n", "3100         3128  39.9138988930\n", "3101         3129  39.9190448611\n", "3102         3130  40.1116190228\n", "3103         3131  40.8937444405\n", "3104         3132  41.1407151860\n", "3105         3133  41.0320067876\n", "3106         3134  40.8879064038\n", "3107         3135  40.9908305000\n", "3108         3136  41.0348480187\n", "3109         3137  41.1246752903\n", "3110         3138  41.3807671300\n", "3111         3139  41.3337958780\n", "3112         3140  41.5898172479\n", "3113         3141  41.5247465960\n", "3114         3142  41.6249059065\n", "3115         3143  41.6885508453\n", "3116         3144  41.7872024685\n", "3117         3145  41.3918272034\n", "3118         3146  40.3928806798\n", "3119         3147  40.8871608534\n", "3120         3148  41.4691966241\n", "3121         3149  41.4767310562\n", "3122         3150  41.8253495475\n", "3123         3151  41.9819836783\n", "3124         3152  42.2074257274\n", "3125         3153  41.5989331372\n", "3126         3154  41.0574596467\n", "3127         3155  41.0082168187\n", "3128         3156  40.7050055856\n", "3129         3157  40.6267554988\n", "3130         3158  40.4104811132\n", "3131         3159  40.4863698770\n", "3132         3160  40.3577216462\n", "3133         3161  40.1495929772\n", "3134         3162  39.6420282450\n", "3135         3163  39.2050646887\n", "3136         3164  38.9257270243\n", "3137         3165  39.0133224604\n", "3138         3166  38.5602202243\n", "3139         3167  38.4446351894\n", "3140         3168  38.1461991596\n", "3141         3169  39.7490107615\n", "3142         3170  40.6270957643\n", "3143         3171  40.6004560699\n", "3144         3172  40.7583056868\n", "3145         3173  40.8644952178\n", "3146         3174  41.3302295871\n", "3147         3175  41.5846191635\n", "3148         3176  41.7047131469\n", "3149         3177  42.0570954099\n", "3150         3178  42.1767123732\n", "3151         3179  42.1417260564\n", "3152         3180  42.3041426206\n", "3153         3181  41.9179607457\n", "3154         3182  40.8871779395\n", "3155         3183  40.1926016452\n", "3156         3184  40.6863529847\n", "3157         3185  41.0787186587\n", "3158         3186  41.3549079816\n", "3159         3187  41.6955733419\n", "3160         3188  41.9653287567\n", "3161         3189  42.0712221757\n", "3162         3190  41.9781301465\n", "3163         3191  41.9403281678\n", "3164         3192  41.7397525094\n", "3165         3193  41.6666802949\n", "3166         3194  41.1469760549\n", "3167         3195  41.0414531501\n", "3168         3196  40.5797782167\n", "3169         3197  40.5733901917\n", "3170         3198  40.0611662346\n", "3171         3199  40.1366281838\n", "3172         3200  39.9108459348\n", "3173         3201  39.7387184473\n", "3174         3202  39.6545053625\n", "3175         3203  39.4833188699\n", "3176         3204  39.4001523844\n", "3177         3205  39.4047869924\n", "3178         3206  39.3193823621\n", "3179         3207  39.1060791881\n", "3180         3208  39.2086584554\n", "3181         3209  39.2431823993\n", "3182         3210  39.1846123813\n", "3183         3211  38.9470025481\n", "3184         3212  38.9359317246\n", "3185         3213  38.9144860238\n", "3186         3214  38.5662057097\n", "3187         3215  38.8455445550\n", "3188         3216  38.4757228949\n", "3189         3217  38.7588078976\n", "3190         3218  38.9498697715\n", "3191         3219  39.1451283150\n", "3192         3220  39.2397308910\n", "3193         3221  39.6229637488\n", "3194         3222  40.2915972007\n", "3195         3223  41.2961412472\n", "3196         3224  41.8040282363\n", "3197         3225  41.8010152002\n", "3198         3226  41.9495029928\n", "3199         3227  41.6287600620\n", "3200         3228  40.9768627926\n", "3201         3229  40.0860553713\n", "3202         3230  40.6468135866\n", "3203         3231  41.0819119321\n", "3204         3232  41.3094472486\n", "3205         3233  41.5020184696\n", "3206         3234  41.7331330815\n", "3207         3235  41.2165150749\n", "3208         3236  40.4737454393\n", "3209         3237  40.0867532824\n", "3210         3238  40.2077996472\n", "3211         3239  40.8089018206\n", "3212         3240  41.6201707371\n", "3213         3241  41.5178834199\n", "3214         3242  41.7746953513\n", "3215         3243  41.6457049304\n", "3216         3244  41.4582169580\n", "3217         3245  41.5027823143\n", "3218         3246  41.0788468424\n", "3219         3247  40.6664774020\n", "3220         3248  39.4627045035\n", "3221         3249  40.8629387678\n", "3222         3250  39.8489456257\n", "3223         3251  38.6941089955\n", "3224         3252  38.2833186024\n", "3225         3253  38.5850804775\n", "3226         3254  38.5505617799\n", "3227         3256  38.5621389378\n", "3228         3257  38.4881338303\n", "3229         3258  38.4425814991\n", "3230         3259  38.7137567496\n", "3231         3260  38.7375080668\n", "3232         3261  38.6067022768\n", "3233         3262  38.3145865136\n", "3234         3263  38.2728205680\n", "3235         3264  38.0435813800\n", "3236         3265  37.7121431107\n", "3237         3266  37.6402113890\n", "3238         3267  37.8694306889\n", "3239         3268  37.9799828573\n", "3240         3269  37.8827428855\n", "3241         3270  37.9873124449\n", "3242         3271  38.1230244968\n", "3243         3272  38.3689569791\n", "3244         3273  38.6770716813\n", "3245         3274  38.7970256275\n", "3246         3275  38.7687527631\n", "3247         3276  38.8980318037\n", "3248         3277  39.1896133772\n", "3249         3278  39.1251533101\n", "3250         3279  39.3737607672\n", "3251         3280  39.4184287639\n", "3252         3281  39.4697722569\n", "3253         3282  39.3588149114\n", "3254         3283  39.4637727093\n", "3255         3284  39.6617538062\n", "3256         3285  39.7458083971\n", "3257         3286  39.6199963722\n", "3258         3287  39.6463661696\n", "3259         3288  39.9103559574\n", "3260         3289  39.6883491662\n", "3261         3290  39.9490740763\n", "3262         3291  39.9930801557\n", "3263         3292  40.0826668128\n", "3264         3293  40.1100371150\n", "3265         3294  40.6128706552\n", "3266         3295  40.5849270002\n", "3267         3296  40.5127439485\n", "3268         3297  40.6245535667\n", "3269         3298  40.6159383669\n", "3270         3299  40.7624056552\n", "3271         3300  40.8687058043\n", "3272         3301  41.0634250189\n", "3273         3302  41.0235682077\n", "3274         3303  41.1233502845\n", "3275         3304  42.4463588978\n", "3276         3305  42.1103695523\n", "3277         3306  42.1992844664\n", "3278         3307  42.2690442870\n", "3279         3308  42.0604789197\n", "3280         3309  42.4214475601\n", "3281         3310  43.1399936449\n", "3282         3311  42.7585349027\n", "3283         3312  42.4985765504\n", "3284         3313  42.6498832250\n", "3285         3314  42.3603365573\n", "3286         3315  42.4886837699\n", "3287         3316  42.7076009023\n", "3288         3317  42.4656552808\n", "3289         3318  42.4951186220\n", "3290         3319  42.9485941326\n", "3291         3320  43.0147557443\n", "3292         3321  43.3880425705\n", "3293         3322  43.3272484858\n", "3294         3323  43.7377586327\n", "3295         3324  43.9859281686\n", "3296         3325  44.3994475349\n", "3297         3326  44.6259297601\n", "3298         3327  44.9472803630\n", "3299         3328  45.0804049223\n", "3300         3329  45.1640723614\n", "3301         3330  45.5730688933\n", "3302         3331  45.8308954559\n", "3303         3332  45.4460989283\n", "3304         3333  43.9147923689\n", "3305         3334  43.8037221149\n", "3306         3335  43.9484059658\n", "3307         3336  44.0727695889\n", "3308         3337  43.7153767680\n", "3309         3338  43.4465736266\n", "3310         3339  42.9341531499\n", "3311         3340  42.5248173049\n", "3312         3341  41.9339113388\n", "3313         3342  41.4880131873\n", "3314         3343  41.1094564817\n", "3315         3344  40.7975333707\n", "3316         3345  40.7639217887\n", "3317         3346  40.6743839788\n", "3318         3347  40.6716440727\n", "3319         3348  41.0114349074\n", "3320         3349  41.4120588250\n", "3321         3350  42.2356411851\n", "3322         3351  42.0593514543\n", "3323         3352  42.2771821640\n", "3324         3353  42.2140778018\n", "3325         3354  42.1913083444\n", "3326         3355  42.1064374615\n", "3327         3356  41.9979178826\n", "3328         3357  41.9492106702\n", "3329         3358  41.6932382012\n", "3330         3359  41.8101819201\n", "3331         3360  42.2800890391\n", "3332         3361  41.4694359991\n", "3333         3362  41.5449796930\n", "3334         3363  41.7392228974\n", "3335         3364  42.2218223113\n", "3336         3365  41.8270849319\n", "3337         3366  41.8869717723\n", "3338         3367  42.3305063783\n", "3339         3368  42.4544581559\n", "3340         3369  43.0234121815\n", "3341         3370  43.3542681884\n", "3342         3371  43.3437532851\n", "3343         3372  43.3138292671\n", "3344         3373  43.3727690517\n", "3345         3374  43.1741938605\n", "3346         3375  42.9816564410\n", "3347         3376  42.9305384458\n", "3348         3377  42.8282846531\n", "3349         3378  43.0371375511\n", "3350         3379  43.0165254770\n", "3351         3380  42.9785735224\n", "3352         3381  42.7609056833\n", "3353         3382  42.5853980682\n", "3354         3383  42.1279939006\n", "3355         3384  42.1460676210\n", "3356         3385  42.0941587214\n", "3357         3386  42.0953673972\n", "3358         3387  42.0631873476\n", "3359         3388  41.1281023609\n", "3360         3389  41.1252828761\n", "3361         3390  40.6587434406\n", "3362         3391  39.5602625375\n", "3363         3392  39.8974705107\n", "3364         3393  40.7328014958\n", "3365         3394  42.1122706876\n", "3366         3395  39.1065832364\n", "3367         3396  38.3348576917\n", "3368         3397  38.4341963625\n", "3369         3398  38.4521127099\n", "3370         3399  38.7844877759\n", "3371         3400  38.7640942778\n", "3372         3401  39.0906505521\n", "3373         3402  39.4826865281\n", "3374         3403  38.6464386062\n", "3375         3404  38.1247491638\n", "3376         3405  38.8832045590\n", "3377         3406  38.9745344265\n", "3378         3407  39.3513935749\n", "3379         3408  39.8739505162\n", "3380         3409  40.3305336582\n", "3381         3410  40.7310570927\n", "3382         3411  41.1973062484\n", "3383         3412  40.4861931747\n", "3384         3413  42.1918330048\n", "3385         3414  42.0473828555\n", "3386         3415  42.0241558895\n", "3387         3416  41.9238948131\n", "3388         3417  42.1253809285\n", "3389         3418  42.3169529920\n", "3390         3419  42.2843698523\n", "3391         3420  42.4479109299\n", "3392         3421  42.5189318070\n", "3393         3422  42.7053367546\n", "3394         3423  42.7492385884\n", "3395         3424  43.1353110602\n", "3396         3425  45.1249757350\n", "3397         3426  42.8997406735\n", "3398         3427  41.6644303753\n", "3399         3428  42.2694562868\n", "3400         3429  42.9390817048\n", "3401         3430  43.2148553325\n", "3402         3431  43.4417048958\n", "3403         3432  43.2786891989\n", "3404         3433  42.8583222794\n", "3405         3434  42.7861682967\n", "3406         3435  42.6808386216\n", "3407         3436  42.5116118459\n", "3408         3437  42.3445545539\n", "3409         3438  41.9560551678\n", "3410         3439  41.8360274394\n", "3411         3440  41.6510167546\n", "3412         3441  41.1860884211\n", "3413         3442  41.1350385602\n", "3414         3443  40.8786612516\n", "3415         3444  40.9018716754\n", "3416         3445  40.6524781532\n", "3417         3446  40.3489505408\n", "3418         3447  40.3314446739\n", "3419         3448  40.4399536007\n", "3420         3449  40.5247981235\n", "3421         3450  40.3903522396\n", "3422         3451  40.6509533239\n", "3423         3452  40.6556080783\n", "3424         3453  40.8021578354\n", "3425         3454  40.7010753246\n", "3426         3455  40.8005455043\n", "3427         3456  40.3857686821\n", "3428         3457  40.1329134902\n", "3429         3458  39.9580053533\n", "3430         3459  39.9464333833\n", "3431         3460  39.5898898796\n", "3432         3461  39.4953777209\n", "3433         3462  39.3258465800\n", "3434         3463  39.0323047490\n", "3435         3464  38.6463621847\n", "3436         3465  38.7209228547\n", "3437         3466  39.1207160434\n", "3438         3467  39.3194558381\n", "3439         3468  38.9617631904\n", "3440         3469  38.9194685051\n", "3441         3470  39.1639224692\n", "3442         3471  39.4672838085\n", "3443         3472  39.4371921078\n", "3444         3473  39.8491181378\n", "3445         3474  40.0736967221\n", "3446         3475  40.0307627946\n", "3447         3476  40.2167938865\n", "3448         3477  39.8284987675\n", "3449         3478  39.9258890825\n", "3450         3479  40.0097945683\n", "3451         3480  40.5892661588\n", "3452         3481  40.3312965049\n", "3453         3482  40.4536198987\n", "3454         3483  40.1359739591\n", "3455         3484  40.1327071209\n", "3456         3485  39.9050202230\n", "3457         3486  39.5064099798\n", "3458         3487  39.3763531882\n", "3459         3488  39.0641956940\n", "3460         3489  38.8770382304\n", "3461         3490  38.8090627172\n", "3462         3491  39.0179664358\n", "3463         3492  39.1774598080\n", "3464         3493  39.4553319202\n", "3465         3494  39.5830186658\n", "3466         3495  39.7743089067\n", "3467         3496  39.8014275841\n", "3468         3497  39.9276933738\n", "3469         3498  40.0453803610\n", "3470         3499  40.0662676959\n", "3471         3500  40.1193923018\n", "3472         3501  40.0701799951\n", "3473         3502  39.8913041304\n", "3474         3503  39.5532797076\n", "3475         3504  39.3540592621\n", "3476         3505  39.2693263795\n", "3477         3506  38.9857781645\n", "3478         3507  40.0186457191\n", "3479         3508  38.7004048761\n", "3480         3509  38.3691052528\n", "3481         3510  38.4273417642\n", "3482         3511  38.4643123559\n", "3483         3512  39.3742642376\n", "3484         3513  38.8538616295\n", "3485         3514  38.9708864066\n", "3486         3515  39.1562965122\n", "3487         3516  39.2670340907\n", "3488         3517  39.5913680089\n", "3489         3518  39.7894767787\n", "3490         3519  40.4894592378\n", "3491         3520  41.2599709036\n", "3492         3522  41.8850083272\n", "3493         3523  42.4746676121\n", "3494         3524  42.5004433830\n", "3495         3525  42.8591233252\n", "3496         3526  43.0988265733\n", "3497         3527  43.5506583495\n", "3498         3528  43.7358840854\n", "3499         3529  43.8196567267\n", "3500         3530  44.2318898357\n", "3501         3531  44.3289217922\n", "3502         3532  44.3101744056\n", "3503         3533  44.6274145591\n", "3504         3534  44.7530723444\n", "3505         3535  44.8613168817\n", "3506         3536  45.0103620573\n", "3507         3537  45.3374602531\n", "3508         3538  45.4011113449\n", "3509         3539  44.7357269401\n", "3510         3540  44.0582441344\n", "3511         3541  44.8238419105\n", "3512         3542  45.2011931520\n", "3513         3543  45.2705034621\n", "3514         3544  45.2732004283\n", "3515         3545  45.1769476786\n", "3516         3546  44.8583787660\n", "3517         3547  44.2538816013\n", "3518         3548  43.2720164785\n", "3519         3549  43.0056927174\n", "3520         3550  42.2222864624\n", "3521         3551  41.5882702247\n", "3522         3552  40.9715853527\n", "3523         3553  40.9991420179\n", "3524         3554  40.9370875030\n", "3525         3555  40.8764130051\n", "3526         3556  40.6355957031\n", "3527         3557  40.4939804582\n", "3528         3558  40.2758987699\n", "3529         3559  40.3026972918\n", "3530         3560  40.1353893640\n", "3531         3561  40.9513902555\n", "3532         3562  41.0236003018\n", "3533         3563  40.8295814336\n", "3534         3564  40.8443541426\n", "3535         3565  40.9413257021\n", "3536         3566  41.0781596649\n", "3537         3567  40.8413094924\n", "3538         3568  40.6181827791\n", "3539         3569  40.2013616790\n", "3540         3570  39.6908314732\n", "3541         3571  39.3372918344\n", "3542         3572  39.0323799764\n", "3543         3573  39.9389105844\n", "3544         3574  40.6647365729\n", "3545         3575  39.2949352933\n", "3546         3576  39.4080623708\n", "3547         3577  39.7570185402\n", "3548         3578  39.6040877461\n", "3549         3579  40.0116830000\n", "3550         3580  40.0593853200\n", "3551         3581  40.1158885841\n", "3552         3582  40.4296238380\n", "3553         3583  40.6909504991\n", "3554         3584  40.8526885321\n", "3555         3585  41.1369959559\n", "3556         3586  41.7888956522\n", "3557         3587  42.3430018609\n", "3558         3588  42.9433311154\n", "3559         3589  43.2051972886\n", "3560         3590  43.3081902698\n", "3561         3591  43.5964518307\n", "3562         3592  43.7093021453\n", "3563         3593  44.0295567628\n", "3564         3594  43.5974351281\n", "3565         3595  42.1250022546\n", "3566         3596  43.7308202727\n", "3567         3597  43.0840323377\n", "3568         3598  43.4956639308\n", "3569         3599  43.6448783600\n", "3570         3600  43.9781404761\n", "3571         3601  44.0695803140\n", "3572         3602  44.2947332450\n", "3573         3603  44.0037830476\n", "3574         3604  43.7390053335\n", "3575         3605  43.5092899649\n", "3576         3606  43.1036307254\n", "3577         3607  43.4145958127\n", "3578         3608  42.4380363174\n", "3579         3609  41.7012566926\n", "3580         3610  41.2871546254\n", "3581         3611  41.4600346126\n", "3582         3612  41.4013250590\n", "3583         3613  41.6352470114\n", "3584         3614  41.9357358206\n", "3585         3615  41.6739625656\n", "3586         3616  41.9861624512\n", "3587         3617  41.3949566799\n", "3588         3618  40.9510088978\n", "3589         3619  40.8778811033\n", "3590         3620  40.9055513853\n", "3591         3621  40.9163186999\n", "3592         3622  41.1416752492\n", "3593         3623  41.4924107035\n", "3594         3624  41.9996619690\n", "3595         3625  42.2706118435\n", "3596         3626  42.6542299336\n", "3597         3627  42.8289332990\n", "3598         3628  43.2858620203\n", "3599         3629  42.4146299962\n", "3600         3630  42.2565896731\n", "3601         3631  43.0692147892\n", "3602         3632  43.2026619981\n", "3603         3633  42.9324934706\n", "3604         3634  42.4392025242\n", "3605         3635  41.9032281619\n", "3606         3636  41.3697159866\n", "3607         3637  40.9606675002\n", "3608         3638  40.6120547785\n", "3609         3639  40.2535689074\n", "3610         3640  39.7590665391\n", "3611         3641  39.7030867038\n", "3612         3642  39.6855888315\n", "3613         3643  39.3254336964\n", "3614         3644  41.2630129275\n", "3615         3645  41.2098523213\n", "3616         3646  40.5669737437\n", "3617         3647  40.3796817347\n", "3618         3648  40.6400200044\n", "3619         3649  40.6767794129\n", "3620         3650  40.8307106083\n", "3621         3651  40.9302863079\n", "3622         3652  41.1695551513\n", "3623         3653  41.1773341486\n", "3624         3654  41.4326272718\n", "3625         3655  41.4811740487\n", "3626         3656  41.6987991146\n", "3627         3657  41.8707865327\n", "3628         3658  42.0662773071\n", "3629         3659  42.2469453897\n", "3630         3660  42.6071346345\n", "3631         3661  42.8956770580\n", "3632         3662  43.0750614157\n", "3633         3663  43.1789235451\n", "3634         3664  43.5059334906\n", "3635         3665  43.0868234138\n", "3636         3666  41.9958398624\n", "3637         3667  42.3887296206\n", "3638         3668  42.9040020670\n", "3639         3669  43.3613908904\n", "3640         3670  43.7095635637\n", "3641         3671  43.9250949607\n", "3642         3672  43.5928872138\n", "3643         3673  43.3328663822\n", "3644         3674  43.0341764158\n", "3645         3675  42.1036493798\n", "3646         3676  41.5927797140\n", "3647         3677  40.2204600642\n", "3648         3678  39.8831765163\n", "3649         3679  39.9238703230\n", "3650         3680  39.9057956304\n", "3651         3681  40.0627647330\n", "3652         3682  40.0712381103\n", "3653         3683  39.8499236364\n", "3654         3684  39.5282343548\n", "3655         3685  39.3471135776\n", "3656         3686  38.9093396592\n", "3657         3687  38.8410923467\n", "3658         3688  38.9939618444\n", "3659         3689  39.0242224505\n", "3660         3690  40.0509369595\n", "3661         3691  40.2459698678\n", "3662         3692  40.6916709120\n", "3663         3693  41.6925573643\n", "3664         3694  41.3749209692\n", "3665         3695  41.9663237836\n", "3666         3696  41.9301476185\n", "3667         3697  42.0314752615\n", "3668         3698  44.4987840172\n", "3669         3699  43.9239401676\n", "3670         3700  44.1906096290\n", "3671         3701  44.5482806473\n", "3672         3702  43.9186544726\n", "3673         3703  43.6072412068\n", "3674         3704  43.0446776757\n", "3675         3705  43.1497082216\n", "3676         3706  43.3891693817\n", "3677         3707  43.6011128210\n", "3678         3708  43.4287980344\n", "3679         3709  43.4950826936\n", "3680         3710  43.6163156909\n", "3681         3711  43.4989617473\n", "3682         3712  43.5006595918\n", "3683         3713  43.3724842679\n", "3684         3714  43.0703029046\n", "3685         3715  42.7848293886\n", "3686         3716  42.5086546138\n", "3687         3717  42.0499896666\n", "3688         3718  41.6245441492\n", "3689         3719  41.5230887095\n", "3690         3720  41.1050716093\n", "3691         3721  40.9856335516\n", "3692         3722  40.8999715369\n", "3693         3723  40.9157690783\n", "3694         3724  40.5587314156\n", "3695         3725  40.4852289519\n", "3696         3726  40.6612399929\n", "3697         3727  40.8079541183\n", "3698         3728  40.9508996433\n", "3699         3729  40.8679731344\n", "3700         3730  40.8417020397\n", "3701         3731  41.1149165308\n", "3702         3732  41.3917767091\n", "3703         3733  41.3182640752\n", "3704         3734  42.0840099584\n", "3705         3735  41.4987526240\n", "3706         3736  41.3844894702\n", "3707         3737  41.6695636936\n", "3708         3738  41.6207659344\n", "3709         3739  41.4721479312\n", "3710         3740  41.9639811981\n", "3711         3741  41.5519490019\n", "3712         3742  41.1075866096\n", "3713         3743  41.2394137025\n", "3714         3744  41.0593513917\n", "3715         3745  41.0956973607\n", "3716         3746  41.2506989137\n", "3717         3747  41.3063754358\n", "3718         3748  41.5046099638\n", "3719         3749  41.7303929338\n", "3720         3750  41.7676720017\n", "3721         3751  41.8465164846\n", "3722         3752  41.4547440745\n", "3723         3753  40.5199999299\n", "3724         3754  41.0525249143\n", "3725         3755  41.7886721106\n", "3726         3756  41.9116975875\n", "3727         3757  41.7671746270\n", "3728         3758  42.1677829129\n", "3729         3759  42.2157843312\n", "3730         3760  41.9541158188\n", "3731         3761  41.3469644931\n", "3732         3762  41.8021785968\n", "3733         3763  41.4282150448\n", "3734         3764  41.0492994635\n", "3735         3765  40.8428891281\n", "3736         3766  41.1698007645\n", "3737         3767  41.4077552029\n", "3738         3768  41.5516660404\n", "3739         3769  41.7010991515\n", "3740         3770  41.8731086255\n", "3741         3771  41.8789305148\n", "3742         3772  41.8888494439\n", "3743         3773  41.3186705580\n", "3744         3774  40.3862161745\n", "3745         3775  40.6147708409\n", "3746         3776  40.6664778005\n", "3747         3777  40.4606586777\n", "3748         3778  40.1385342424\n", "3749         3779  39.8989096614\n", "3750         3780  39.5206909948\n", "3751         3781  38.8730898359\n", "3752         3782  38.4239073624\n", "3753         3783  38.1176374648\n", "3754         3784  38.1302207566\n", "3755         3785  38.0774691401\n", "3756         3786  37.9853005511\n", "3757         3787  38.0619881313\n", "3758         3788  38.0539684617\n", "3759         3789  37.9678746334\n", "3760         3790  38.6363780748\n", "3761         3791  38.9082708161\n", "3762         3792  38.6397400203\n", "3763         3793  38.5495711467\n", "3764         3794  38.5542178907\n", "3765         3795  38.8196069826\n", "3766         3796  38.7516940320\n", "3767         3797  38.6310826685\n", "3768         3798  38.4691829136\n", "3769         3799  38.7977137172\n", "3770         3801  38.7153564868\n", "3771         3802  39.0177623863\n", "3772         3803  38.6898201216\n", "3773         3804  38.2309651833\n", "3774         3805  38.2790289082\n", "3775         3806  38.3212420100\n", "3776         3807  38.1189839769\n", "3777         3808  38.2206040679\n", "3778         3809  38.2781430795\n", "3779         3810  38.4654181111\n", "3780         3811  38.8542831872\n", "3781         3812  37.8541630454\n", "3782         3813  37.7808231566\n", "3783         3814  38.6107399519\n", "3784         3815  38.8551169573\n", "3785         3816  38.9641592231\n", "3786         3817  39.5944807340\n", "3787         3818  39.9176366951\n", "3788         3819  40.4259795811\n", "3789         3820  40.5859523285\n", "3790         3821  40.7447667389\n", "3791         3822  40.8965835885\n", "3792         3823  40.5576395071\n", "3793         3824  41.2055667512\n", "3794         3825  42.0785600455\n", "3795         3826  41.9290902025\n", "3796         3827  42.1569612356\n", "3797         3828  42.4167943685\n", "3798         3829  42.4300932553\n", "3799         3830  42.4472094758\n", "3800         3831  42.3249151392\n", "3801         3832  42.1927989910\n", "3802         3833  42.1489520492\n", "3803         3834  42.1421693309\n", "3804         3835  41.9890578694\n", "3805         3836  42.0021973218\n", "3806         3837  41.9033436764\n", "3807         3838  41.9919979874\n", "3808         3839  42.2357131689\n", "3809         3840  42.4729336156\n", "3810         3841  42.7068299547\n", "3811         3842  42.7667191083\n", "3812         3843  42.7820787337\n", "3813         3844  42.6924228433\n", "3814         3845  42.7058830305\n", "3815         3846  43.2786511554\n", "3816         3847  43.1079126842\n", "3817         3848  43.3818856501\n", "3818         3849  43.9240588058\n", "3819         3850  43.8318170075\n", "3820         3851  44.1515330674\n", "3821         3852  44.3249739248\n", "3822         3853  43.0432002798\n", "3823         3854  42.4325001565\n", "3824         3855  42.5794689266\n", "3825         3856  43.2924798278\n", "3826         3857  43.7360482703\n", "3827         3858  44.0016749863\n", "3828         3859  44.0795651694\n", "3829         3860  43.9033169448\n", "3830         3861  43.7838293287\n", "3831         3862  43.4250782304\n", "3832         3863  42.9861754044\n", "3833         3864  42.6790299029\n", "3834         3865  42.0598926202\n", "3835         3866  41.6816073102\n", "3836         3867  41.2152268361\n", "3837         3868  40.8822656439\n", "3838         3869  40.3619516849\n", "3839         3870  40.1878774680\n", "3840         3871  40.7357157313\n", "3841         3872  40.8006095603\n", "3842         3873  40.1658133654\n", "3843         3874  39.6846697804\n", "3844         3875  39.5633547740\n", "3845         3876  40.0140392085\n", "3846         3877  40.1466591648\n", "3847         3878  40.3833036072\n", "3848         3879  40.3583386537\n", "3849         3880  40.7282785044\n", "3850         3881  41.2080549953\n", "3851         3882  40.6811466865\n", "3852         3883  40.8049910989\n", "3853         3884  40.5547493717\n", "3854         3885  40.5447172183\n", "3855         3886  41.0044797333\n", "3856         3887  40.6532825558\n", "3857         3888  39.6721256239\n", "3858         3889  39.7671157706\n", "3859         3890  39.8893607024\n", "3860         3891  40.1645369033\n", "3861         3892  40.1678071819\n", "3862         3893  40.2915623601\n", "3863         3894  40.6055370022\n", "3864         3895  40.7949966639\n", "3865         3896  40.8094263692\n", "3866         3897  40.9408287718\n", "3867         3898  40.5448138982\n", "3868         3899  40.4652458656\n", "3869         3900  40.2108646872\n", "3870         3901  40.1780560263\n", "3871         3902  39.9987750330\n", "3872         3903  40.1349083308\n", "3873         3904  39.3765509859\n", "3874         3905  39.8330162089\n", "3875         3906  39.9677838095\n", "3876         3907  38.3562852040\n", "3877         3908  38.7425747901\n", "3878         3909  38.8515175322\n", "3879         3910  38.7148499435\n", "3880         3911  38.7308177281\n", "3881         3912  38.6824843213\n", "3882         3913  38.4785675327\n", "3883         3914  38.2671889094\n", "3884         3915  38.4422450260\n", "3885         3916  38.4443651014\n", "3886         3917  38.2681695869\n", "3887         3918  38.0455069225\n", "3888         3919  37.9568781994\n", "3889         3920  37.8476824585\n", "3890         3921  37.5193419211\n", "3891         3922  37.5036184738\n", "3892         3923  37.6052316756\n", "3893         3924  37.6421791863\n", "3894         3925  37.5649688661\n", "3895         3926  38.0584709034\n", "3896         3927  38.1350899496\n", "3897         3928  38.1837515778\n", "3898         3929  38.2370943457\n", "3899         3930  38.2397370932\n", "3900         3931  38.3289339735\n", "3901         3932  38.3975966029\n", "3902         3933  38.4625857711\n", "3903         3934  38.4235119966\n", "3904         3935  38.5672958387\n", "3905         3936  38.6154678700\n", "3906         3937  40.1302631640\n", "3907         3938  39.4268813991\n", "3908         3939  39.2316393034\n", "3909         3940  39.2749852164\n", "3910         3941  40.2779335704\n", "3911         3942  39.5231345337\n", "3912         3943  39.3671292316\n", "3913         3944  39.3540960418\n", "3914         3945  39.3895455933\n", "3915         3946  39.4489828266\n", "3916         3947  39.5890550033\n", "3917         3948  39.8086419251\n", "3918         3949  39.9325990739\n", "3919         3950  40.2453060744\n", "3920         3951  40.3481303687\n", "3921         3952  40.6702376952\n", "3922         3953  40.6829664670\n", "3923         3954  41.1767575964\n", "3924         3955  41.3878735043\n", "3925         3956  40.0891566421\n", "3926         3957  41.4382206769\n", "3927         3958  41.9487861333\n", "3928         3959  42.1437490785\n", "3929         3960  42.4295524856\n", "3930         3961  42.3773491257\n", "3931         3962  42.6084618739\n", "3932         3963  42.8104326908\n", "3933         3964  42.6466725919\n", "3934         3965  42.3458921062\n", "3935         3966  41.7394421617\n", "3936         3967  41.0514473531\n", "3937         3968  41.0370720117\n", "3938         3969  41.0881198919\n", "3939         3970  41.9857544316\n", "3940         3971  41.6851104216\n", "3941         3972  41.5899692884\n", "3942         3973  41.7500271328\n", "3943         3974  41.7060041587\n", "3944         3975  41.9826451359\n", "3945         3976  42.1671313958\n", "3946         3977  42.4585136705\n", "3947         3978  42.5089186230\n", "3948         3979  42.5186288087\n", "3949         3980  42.7394602280\n", "3950         3981  42.8548946175\n", "3951         3982  42.8038976282\n", "3952         3983  42.1993423461\n", "3953         3984  42.1820853578\n", "3954         3985  42.5124230821\n", "3955         3986  42.6843368970\n", "3956         3987  43.1805717235\n", "3957         3988  43.5642036832\n", "3958         3989  43.6704670266\n", "3959         3990  43.5831718831\n", "3960         3991  43.4087389122\n", "3961         3992  43.2370617222\n", "3962         3993  42.7300579151\n", "3963         3994  42.7488750741\n", "3964         3995  42.3025443834\n", "3965         3996  41.8779337336\n", "3966         3997  41.5764129580\n", "3967         3998  41.2907822572\n", "3968         3999  41.1586415453\n", "3969         4000  41.1467823300\n", "3970         4001  40.8664412230\n", "3971         4002  40.5196044642\n", "3972         4003  39.9945295341\n", "3973         4004  41.7452364358\n", "3974         4005  40.7520274872\n", "3975         4006  40.4758295703\n", "3976         4007  40.5461830839\n", "3977         4008  40.6047866705\n", "3978         4009  40.6851375413\n", "3979         4010  40.8301240422\n", "3980         4011  40.9419187387\n", "3981         4012  40.5286081511\n", "3982         4013  40.4242843873\n", "3983         4014  40.2705619607\n", "3984         4015  40.3524124193\n", "3985         4016  40.5026337245\n", "3986         4017  42.1283659210\n", "3987         4018  41.2804478773\n", "3988         4019  41.0254329201\n", "3989         4020  41.4259049398\n", "3990         4021  42.0330350106\n", "3991         4022  42.8467857570\n", "3992         4023  42.9482681993\n", "3993         4024  43.3912361006\n", "3994         4025  44.2797952785\n", "3995         4026  44.3127619703\n", "3996         4027  42.3396526134\n", "3997         4028  42.0528965028\n", "3998         4029  42.3937915386\n", "3999         4030  43.2355265660\n", "4000         4031  43.3967431934\n", "4001         4032  43.6152475393\n", "4002         4033  43.7407952600\n", "4003         4034  43.8844756959\n", "4004         4035  44.0201907204\n", "4005         4036  43.2213012332\n", "4006         4037  42.6599753378\n", "4007         4038  42.6705325598\n", "4008         4039  42.1688564591\n", "4009         4040  41.4800010488\n", "4010         4041  41.0962867585\n", "4011         4042  40.6896110798\n", "4012         4043  40.3721921560\n", "4013         4044  39.8440957683\n", "4014         4045  39.5676777370\n", "4015         4046  39.6554136759\n", "4016         4047  39.4040017303\n", "4017         4048  39.4812727878\n", "4018         4049  39.4375873140\n", "4019         4050  39.7307481516\n", "4020         4051  39.6846365692\n", "4021         4053  40.1063216976\n", "4022         4054  39.9568821511\n", "4023         4055  40.2799352476\n", "4024         4056  40.3514569609\n", "4025         4057  40.4422032401\n", "4026         4058  40.7302367279\n", "4027         4059  40.5611878327\n", "4028         4060  40.8527966284\n", "4029         4061  41.0660715358\n", "4030         4062  41.4154840951\n", "4031         4063  41.2644753741\n", "4032         4064  41.3442119583\n", "4033         4065  40.9651873690\n", "4034         4066  40.7291844257\n", "4035         4067  40.4264149851\n", "4036         4068  40.1072466236\n", "4037         4069  39.6651526324\n", "4038         4070  39.7481865276\n", "4039         4071  39.5155963413\n", "4040         4072  39.9562741478\n", "4041         4073  40.8932918500\n", "4042         4074  40.8381255181\n", "4043         4075  40.3200046400\n", "4044         4076  40.5207390454\n", "4045         4077  40.3989921209\n", "4046         4078  40.6013942794\n", "4047         4079  41.5388799257\n", "4048         4080  41.3099310553\n", "4049         4081  41.3399522359\n", "4050         4082  41.4299229085\n", "4051         4083  41.6958113310\n", "4052         4084  41.8845109760\n", "4053         4085  42.2419724910\n", "4054         4086  42.3397570109\n", "4055         4087  42.8057401301\n", "4056         4088  43.5611067448\n", "4057         4089  44.0689294530\n", "4058         4090  44.3121318429\n", "4059         4091  44.3551984981\n", "4060         4092  45.0165810229\n", "4061         4093  45.2735446961\n", "4062         4094  45.3389248928\n", "4063         4095  45.5906595069\n", "4064         4096  44.4094800516\n", "4065         4097  43.7950028673\n", "4066         4098  44.5972530955\n", "4067         4099  44.9969228410\n", "4068         4100  45.5009231251\n", "4069         4101  45.7698701047\n", "4070         4102  45.9994387286\n", "4071         4103  46.4833397626\n", "4072         4104  46.4041634672\n", "4073         4105  46.4169177298\n", "4074         4106  45.7887367411\n", "4075         4107  45.3444900475\n", "4076         4108  44.8832137876\n", "4077         4109  43.9944253475\n", "4078         4110  43.9365711324\n", "4079         4111  43.8647943807\n", "4080         4112  43.9177282203\n", "4081         4113  43.4947608041\n", "4082         4114  42.9759512689\n", "4083         4115  42.2411058069\n", "4084         4116  41.7842048145\n", "4085         4117  41.4077837517\n", "4086         4118  41.1519812128\n", "4087         4119  40.9844385557\n", "4088         4120  40.7681225634\n", "4089         4121  40.8542757817\n", "4090         4122  40.3406075985\n", "4091         4123  40.5866349714\n", "4092         4124  41.0627019999\n", "4093         4125  41.3030315709\n", "4094         4126  41.6736634645\n", "4095         4127  41.5087351893\n", "4096         4128  41.8950798883\n", "4097         4129  41.6607009413\n", "4098         4130  42.1319716720\n", "4099         4131  42.3619210585\n", "4100         4132  42.5088456267\n", "4101         4133  42.5811552569\n", "4102         4134  42.3969045696\n", "4103         4135  42.7356950035\n", "4104         4136  42.0811501437\n", "4105         4137  41.5623906068\n", "4106         4138  41.3844873980\n", "4107         4139  41.7572243108\n", "4108         4140  42.2807600248\n", "4109         4141  41.9885573786\n", "4110         4142  41.8088078230\n", "4111         4143  41.2359892112\n", "4112         4144  40.5272424754\n", "4113         4145  40.3247590332\n", "4114         4146  40.2333305596\n", "4115         4147  40.4663451202\n", "4116         4148  40.7100491726\n", "4117         4149  40.3739387382\n", "4118         4150  40.1847491188\n", "4119         4151  40.5028198246\n", "4120         4152  41.0485070231\n", "4121         4153  41.3558861842\n", "4122         4154  41.4774027709\n", "4123         4155  41.6708123565\n", "4124         4156  41.8920219222\n", "4125         4157  42.1644446214\n", "4126         4158  42.0603319179\n", "4127         4159  42.7877260523\n", "4128         4160  42.7377844465\n", "4129         4161  42.9666688710\n", "4130         4162  43.1178518152\n", "4131         4163  43.0835465810\n", "4132         4164  43.0071460072\n", "4133         4165  42.7829026314\n", "4134         4166  42.7112907553\n", "4135         4167  42.2970020359\n", "4136         4168  42.2617073395\n", "4137         4169  42.1277415313\n", "4138         4170  42.1905923003\n", "4139         4171  42.4597581308\n", "4140         4172  42.5033870976\n", "4141         4173  42.8101740055\n", "4142         4174  43.0208462635\n", "4143         4175  43.3306731424\n", "4144         4176  43.6773695333\n", "4145         4177  43.6699066618\n", "4146         4178  43.9223604122\n", "4147         4179  45.6408384715\n", "4148         4180  45.5546777342\n", "4149         4181  45.8082110055\n", "4150         4182  46.0064984864\n", "4151         4183  46.0659985095\n", "4152         4184  46.1753104393\n", "4153         4185  45.5904773465\n", "4154         4186  44.8801816322\n", "4155         4187  45.0878802724\n", "4156         4188  45.0937883089\n", "4157         4189  44.9132165061\n", "4158         4190  45.0858937871\n", "4159         4191  44.9155496669\n", "4160         4192  45.0212681380\n", "4161         4193  44.4285607582\n", "4162         4194  42.8102095872\n", "4163         4195  41.7148136174\n", "4164         4197  42.1653449409\n", "4165         4198  42.0313099161\n", "4166         4199  42.2462987268\n", "4167         4200  42.4903753819\n", "4168         4201  42.4740961507\n", "4169         4202  42.2669561335\n", "4170         4203  41.8049045878\n", "4171         4204  41.3974163299\n", "4172         4205  41.0180867705\n", "4173         4206  40.6269821799\n", "4174         4207  40.7199225201\n", "4175         4208  40.1241699249\n", "4176         4209  39.8535464204\n", "4177         4210  39.6709204105\n", "4178         4211  39.0460280616\n", "4179         4212  38.7802388274\n", "4180         4213  38.3356521730\n", "4181         4214  38.2003559730\n", "4182         4215  39.0808511562\n", "4183         4216  39.4773439285\n", "4184         4217  39.5897439915\n", "4185         4218  39.8263087774\n", "4186         4219  40.1916224737\n", "4187         4220  40.2102344328\n", "4188         4221  40.6238012315\n", "4189         4222  41.2054817751\n", "4190         4223  41.4623062492\n", "4191         4224  41.1507743477\n", "4192         4225  41.3498153690\n", "4193         4226  42.9388281927\n", "4194         4227  42.6746199271\n", "4195         4228  42.9374420260\n", "4196         4229  42.9870494798\n", "4197         4230  43.0961827737\n", "4198         4231  43.5033807330\n", "4199         4232  42.8824221928\n", "4200         4233  43.1815939454\n", "4201         4234  42.9821888158\n", "4202         4235  43.0353421941\n", "4203         4236  43.1448617440\n", "4204         4237  43.2047904855\n", "4205         4238  43.3914850224\n", "4206         4239  43.7306510651\n", "4207         4240  43.9230236597\n", "4208         4241  43.2194118170\n", "4209         4242  43.2001136449\n", "4210         4243  43.5136669976\n", "4211         4244  43.7467283926\n", "4212         4245  43.9771529034\n", "4213         4246  44.0914396749\n", "4214         4247  44.3386521415\n", "4215         4248  44.5652068803\n", "4216         4249  44.8036651643\n", "4217         4250  44.8716307554\n", "4218         4251  45.2016567948\n", "4219         4252  44.2044803565\n", "4220         4253  44.0727583586\n", "4221         4254  44.4664153273\n", "4222         4255  44.5207927068\n", "4223         4256  44.9508060262\n", "4224         4257  44.9658111772\n", "4225         4258  45.0207175868\n", "4226         4259  45.3677284159\n", "4227         4260  45.3282619982\n", "4228         4261  44.9828785173\n", "4229         4262  44.4856947945\n", "4230         4263  43.8318957729\n", "4231         4264  44.1874315099\n", "4232         4265  43.5598097619\n", "4233         4266  42.9262137948\n", "4234         4267  42.9023400890\n", "4235         4268  42.5342238282\n", "4236         4269  42.5458957033\n", "4237         4270  42.3316868251\n", "4238         4271  41.8635715776\n", "4239         4272  41.5735433180\n", "4240         4273  41.5587458078\n", "4241         4274  41.1894964107\n", "4242         4275  40.9587171955\n", "4243         4276  40.9518755712\n", "4244         4277  41.2315853592\n", "4245         4278  42.5472972626\n", "4246         4279  42.1929276561\n", "4247         4280  41.7892396848\n", "4248         4281  41.8152462899\n", "4249         4282  42.0181223328\n", "4250         4283  42.2738765047\n", "4251         4284  42.3942349695\n", "4252         4285  42.4283259947\n", "4253         4286  42.8673411564\n", "4254         4287  41.9821941966\n", "4255         4288  42.1846450313\n", "4256         4289  42.6499328716\n", "4257         4290  42.9418350400\n", "4258         4291  43.4990709956\n", "4259         4292  43.7425646349\n", "4260         4293  43.7527296747\n", "4261         4294  43.9103974867\n", "4262         4295  44.0101622686\n", "4263         4296  44.0476261152\n", "4264         4297  44.1874983758\n", "4265         4298  44.1832804421\n", "4266         4299  44.3878231599\n", "4267         4300  44.3876932382\n", "4268         4301  44.0063986921\n", "4269         4302  43.2273272895\n", "4270         4303  43.1730569903\n", "4271         4304  43.5915992597\n", "4272         4305  44.3180178469\n", "4273         4306  44.8820212147\n", "4274         4307  44.4552543302\n", "4275         4308  44.9309675012\n", "4276         4309  44.4617780410\n", "4277         4310  44.4430210542\n", "4278         4311  43.1211373609\n", "4279         4312  42.3249005048\n", "4280         4313  41.3552451304\n", "4281         4314  40.9274831999\n", "4282         4315  40.4852538321\n", "4283         4316  40.4663484397\n", "4284         4317  40.2537107380\n", "4285         4318  39.8647190106\n", "4286         4319  39.8024069475\n", "4287         4320  39.7586795811\n", "4288         4321  39.9056182058\n", "4289         4322  39.8131803710\n", "4290         4323  40.2019676101\n", "4291         4324  39.7860383972\n", "4292         4325  40.1014124793\n", "4293         4326  40.2255241926\n", "4294         4327  40.1315935588\n", "4295         4328  40.0546591237\n", "4296         4329  40.3392534505\n", "4297         4330  40.4977280823\n", "4298         4331  40.1171018885\n", "4299         4332  40.4997496362\n", "4300         4333  40.9673511369\n", "4301         4334  40.9576706115\n", "4302         4335  41.0385643463\n", "4303         4336  41.0863748612\n", "4304         4337  41.3596750471\n", "4305         4338  42.4257940386\n", "4306         4339  43.3694113993\n", "4307         4340  43.3842826181\n", "4308         4341  43.2935243066\n", "4309         4342  43.5250754803\n", "4310         4343  43.7478584504\n", "4311         4344  43.9779536456\n", "4312         4345  44.0911225861\n", "4313         4346  44.4742161309\n", "4314         4347  44.8029042033\n", "4315         4348  44.4767543531\n", "4316         4349  45.2887195002\n", "4317         4350  45.4311639976\n", "4318         4351  45.6436497280\n", "4319         4352  45.7353516769\n", "4320         4353  46.0800033717\n", "4321         4354  46.0462213747\n", "4322         4355  46.3462517784\n", "4323         4356  46.7054340443\n", "4324         4357  47.0438579051\n", "4325         4358  45.8551779671\n", "4326         4359  45.6667557001\n", "4327         4360  45.8247387556\n", "4328         4361  45.8786866601\n", "4329         4362  45.8135602038\n", "4330         4363  45.6975519418\n", "4331         4364  45.6827465721\n", "4332         4365  45.4879400261\n", "4333         4366  43.8122496913\n", "4334         4367  44.0747776588\n", "4335         4368  43.9626301302\n", "4336         4369  43.6846654045\n", "4337         4370  43.7376275447\n", "4338         4371  43.7119046291\n", "4339         4372  43.6047292310\n", "4340         4373  43.6447168670\n", "4341         4374  43.5342289691\n", "4342         4375  43.6672651532\n", "4343         4376  43.5139582123\n", "4344         4377  43.5041425025\n", "4345         4378  43.5124992541\n", "4346         4379  43.4952851281\n", "4347         4380  43.5784407948\n", "4348         4381  42.8581953478\n", "4349         4382  42.6315917603\n", "4350         4383  42.7071301170\n", "4351         4384  42.6612174227\n", "4352         4385  42.6456789975\n", "4353         4386  42.8273097384\n", "4354         4387  42.7592854474\n", "4355         4388  42.5409433409\n", "4356         4389  42.2703518976\n", "4357         4390  42.1771356902\n", "4358         4391  42.0106662577\n", "4359         4392  41.6433090806\n", "4360         4393  41.9356583869\n", "4361         4394  42.2416152575\n", "4362         4395  42.6347931347\n", "4363         4396  42.3281205539\n", "4364         4397  42.8389653609\n", "4365         4398  41.8806040613\n", "4366         4399  41.5044671962\n", "4367         4400  41.5121923573\n", "4368         4401  41.4396500786\n", "4369         4402  41.4827813083\n", "4370         4403  42.0717679631\n", "4371         4404  41.0314791352\n", "4372         4405  38.9220935141\n", "4373         4406  39.1341286259\n", "4374         4407  39.2433001036\n", "4375         4408  39.3955740430\n", "4376         4409  39.5181806899\n", "4377         4410  39.6187959604\n", "4378         4411  39.7430738041\n", "4379         4412  39.8257457710\n", "4380         4413  40.0001657322\n", "4381         4414  39.7692481420\n", "4382         4415  39.5884879135\n", "4383         4416  39.4789157327\n", "4384         4417  39.2456545828\n", "4385         4418  38.8111321465\n", "4386         4419  38.6226403892\n", "4387         4420  38.3869000866\n", "4388         4421  38.1155856816\n", "4389         4422  38.0174113138\n", "4390         4423  37.9786374478\n", "4391         4424  38.1424746848\n", "4392         4425  38.3920377306\n", "4393         4426  38.2363738832\n", "4394         4427  38.4434094610\n", "4395         4428  38.3660813660\n", "4396         4429  38.5559907299\n", "4397         4430  39.0259119099\n", "4398         4431  39.6502600686\n", "4399         4432  39.5402129829\n", "4400         4433  39.4097643294\n", "4401         4434  39.7393609443\n", "4402         4435  39.9895965098\n", "4403         4436  40.0789352529\n", "4404         4437  40.1683705350\n", "4405         4438  40.2556070038\n", "4406         4439  40.3727793236\n", "4407         4440  40.5413207045\n", "4408         4441  40.9777817726\n", "4409         4442  40.8373346755\n", "4410         4443  41.3623873163\n", "4411         4444  41.5669352070\n", "4412         4445  41.7796288535\n", "4413         4446  42.0514227347\n", "4414         4447  43.4196134912\n", "4415         4448  42.9434054776\n", "4416         4449  42.9365443873\n", "4417         4450  43.2345249175\n", "4418         4451  43.3064889198\n", "4419         4452  43.6558601095\n", "4420         4453  43.6631840741\n", "4421         4454  43.9447044629\n", "4422         4455  44.9067377152\n", "4423         4456  42.9899427422\n", "4424         4457  43.0580906503\n", "4425         4458  43.4572759322\n", "4426         4459  43.9365498814\n", "4427         4460  44.1155272839\n", "4428         4461  44.2788556800\n", "4429         4462  44.5064933755\n", "4430         4463  44.2200225669\n", "4431         4464  44.0180519244\n", "4432         4465  43.2096004535\n", "4433         4466  42.7811247471\n", "4434         4467  42.2804532288\n", "4435         4468  41.5556709030\n", "4436         4470  42.1165227148\n", "4437         4471  42.7719676469\n", "4438         4472  41.3031031305\n", "4439         4473  40.9703265775\n", "4440         4474  40.9440336638\n", "4441         4475  41.0522736175\n", "4442         4476  41.7625060693\n", "4443         4477  42.7786454942\n", "4444         4478  42.4872508964\n", "4445         4479  42.6904989523\n", "4446         4480  43.1415379722\n", "4447         4481  43.5904479065\n", "4448         4482  43.8740362946\n", "4449         4483  44.3431220299\n", "4450         4484  44.8377255913\n", "4451         4485  45.2492680162\n", "4452         4486  45.5732727951\n", "4453         4487  45.9963142241\n", "4454         4488  46.2506190788\n", "4455         4489  46.5679724087\n", "4456         4490  47.0362795410\n", "4457         4491  47.1298899369\n", "4458         4492  47.3646577865\n", "4459         4493  47.6414748099\n", "4460         4494  47.5245144146\n", "4461         4495  47.4132007257\n", "4462         4496  46.0914394342\n", "4463         4497  45.3296542635\n", "4464         4498  44.8109958075\n", "4465         4499  45.3992129299\n", "4466         4500  45.2429595259\n", "4467         4501  45.5034405316\n", "4468         4502  45.7094674168\n", "4469         4503  45.8516759142\n", "4470         4504  45.1114052040\n", "4471         4505  44.6169700416\n", "4472         4506  43.9914744956\n", "4473         4507  43.8082652502\n", "4474         4508  43.6295032553\n", "4475         4509  43.2261040955\n", "4476         4510  43.3844817098\n", "4477         4511  42.7795802970\n", "4478         4512  42.3698970934\n", "4479         4513  41.8575460780\n", "4480         4514  41.9674592181\n", "4481         4515  42.0573182593\n", "4482         4516  41.4761285407\n", "4483         4517  41.4160737201\n", "4484         4518  42.4395621014\n", "4485         4519  42.1732417927\n", "4486         4520  41.7973237525\n", "4487         4521  41.4090262369\n", "4488         4522  41.6074201654\n", "4489         4523  42.3966802637\n", "4490         4524  42.7439309948\n", "4491         4525  43.2108520876\n", "4492         4526  42.9434696334\n", "4493         4527  42.4131354546\n"]}], "source": ["result = df2_trimmed.groupby('world_index')['diameter'].mean().reset_index()\n", "print(result)"]}, {"cell_type": "code", "execution_count": 30, "id": "0864d1fb", "metadata": {}, "outputs": [{"data": {"text/plain": ["25.899380407492473"]}, "execution_count": 30, "metadata": {}, "output_type": "execute_result"}], "source": ["result[\"diameter\"][12]"]}, {"cell_type": "code", "execution_count": 31, "id": "0b6a55eb", "metadata": {}, "outputs": [], "source": ["result.to_csv(\"left_eye_pupil_measures.csv\", index=False)"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.16"}}, "nbformat": 4, "nbformat_minor": 5}