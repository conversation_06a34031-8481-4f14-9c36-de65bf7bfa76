#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
实验配置文件
包含实验的各种设置参数
"""
# ==================== 题目选择配置 ====================
# 屏蔽的题目ID列表 - 这些题目不会被随机选中
BLOCKED_QUESTION_IDS = [
    127, 49, 179, 39, 88, 32, 81, 15, 59, 87, 167, 168, 115, 119, 178, 185, 57, 26, 97, 130, 56, 44, 51, 124, 149, 74, 6, 92, 152, 42, 13, 171, 33, 45, 144, 53, 35, 61, 27, 150, 137, 1, 9, 99, 104, 143, 82, 103, 85, 77, 71, 139, 18, 16, 114, 73, 20, 151, 94, 140, 30, 4, 111, 84, 184, 12, 132, 188, 165, 156, 79, 89, 129, 41, 8, 3, 134, 5, 17, 11, 126, 70, 68, 69, 176, 174, 164, 145, 122, 2, 34, 186, 173, 112, 159, 95, 160, 183, 128, 181, 91, 93, 146, 65, 155, 58, 19, 64, 189, 47, 76, 123, 29, 157, 175, 163, 54, 48, 23, 135, 62, 24, 177, 117, 148, 118, 90, 154, 106, 67, 78, 133, 80, 22, 113, 105, 60, 14, 46, 172, 98, 166, 55, 110, 100, 109, 153, 116, 50, 75
]
# ==================== 实验时间配置 ====================
# 各阶段持续时间（秒）
# TIMING_CONFIG = {
#     'fixation_baseline': 2.0,      # 注视基线时间
#     'question_display': 5.0,       # 问题显示时间
#     'answer_input': None,          # 答案输入时间（None表示无限制）
#     'curiosity_rating': 10.0,      # 好奇心评分时间
#     'pupil_baseline': 2.0,         # 瞳孔基线时间
#     'answer_display': 5.0,         # 答案显示时间
#     'pleasure_rating': None,       # 有趣度评分时间（None表示无限制）
#     'surprise_rating': None,       # 惊讶度评分时间（None表示无限制）
#     'gaze_duration_threshold': 1.0 # 注视持续时间阈值
# }

# ==================== 显示配置 ====================
# 屏幕显示设置
# DISPLAY_CONFIG = {
#     'fullscreen': True,            # 是否全屏显示
#     'screen_size': (1920, 1080),   # 屏幕分辨率
#     'background_color': 'black',   # 背景颜色
#     'text_color': 'white',         # 文字颜色
#     'font_size': 24,               # 字体大小
#     'font_name': 'Arial'           # 字体名称
# }

# ==================== EyeLink配置 ====================
# EyeLink眼动仪设置
# EYELINK_CONFIG = {
#     'use_eyelink': True,           # 是否使用EyeLink
#     'dummy_mode': False,           # 是否使用模拟模式
#     'calibration_type': 'HV9',     # 校准类型
#     'sampling_rate': 1000,         # 采样率
#     'file_prefix': 'exp',          # EDF文件前缀
#     'pupil_size_mode': 'DIAMETER'  # 瞳孔大小模式
# }

# ==================== 数据保存配置 ====================
# 数据保存设置
# DATA_CONFIG = {
#     'base_data_dir': 'data',       # 基础数据目录
#     'save_summary': True,          # 是否保存实验摘要
#     'save_csv': True,              # 是否保存CSV格式数据
#     'save_log': True,              # 是否保存日志文件
#     'auto_backup': False           # 是否自动备份
# }

# ==================== 评分配置 ====================
# 评分量表设置
# RATING_CONFIG = {
#     'curiosity_scale': (1, 5),     # 好奇心评分范围
#     'pleasure_scale': (1, 5),      # 有趣度评分范围
#     'surprise_scale': (1, 5),      # 惊讶度评分范围
#     'rating_keys': ['1', '2', '3', '4', '5'],  # 评分按键
#     'show_scale': True             # 是否显示评分量表
# }

# ==================== 实验流程配置 ====================
# 实验流程设置
# EXPERIMENT_CONFIG = {
#     'default_num_questions': 1,    # 默认题目数量
#     'max_questions': 300,          # 最大题目数量
#     'min_questions': 1,            # 最小题目数量
#     'allow_skip': False,           # 是否允许跳过题目
#     'randomize_order': True,       # 是否随机化题目顺序
#     'show_progress': True          # 是否显示进度
# }

# ==================== 调试配置 ====================
# 调试和测试设置
# DEBUG_CONFIG = {
#     'debug_mode': False,           # 是否开启调试模式
#     'verbose_logging': True,       # 是否详细记录日志
#     'test_mode': False,            # 是否测试模式
#     'skip_calibration': False      # 是否跳过校准
# }

# ==================== 辅助函数 ====================
def get_available_questions(all_questions):
    """
    获取可用的题目列表（排除被屏蔽的题目）
    
    Args:
        all_questions: 所有题目列表
        
    Returns:
        可用题目列表
    """
    available_questions = []
    blocked_count = 0
    
    for question in all_questions:
        if question.get('id') not in BLOCKED_QUESTION_IDS:
            available_questions.append(question)
        else:
            blocked_count += 1
    
    print(f"总题目数: {len(all_questions)}")
    print(f"屏蔽题目数: {blocked_count}")
    print(f"可用题目数: {len(available_questions)}")
    
    return available_questions

def update_blocked_questions(new_blocked_ids):
    """
    更新屏蔽题目列表
    
    Args:
        new_blocked_ids: 新的屏蔽题目ID列表
    """
    global BLOCKED_QUESTION_IDS
    BLOCKED_QUESTION_IDS = list(set(BLOCKED_QUESTION_IDS + new_blocked_ids))
    print(f"已更新屏蔽题目列表，当前屏蔽 {len(BLOCKED_QUESTION_IDS)} 道题目")

def clear_blocked_questions():
    """清空屏蔽题目列表"""
    global BLOCKED_QUESTION_IDS
    BLOCKED_QUESTION_IDS = []
    print("已清空屏蔽题目列表")

def show_blocked_questions():
    """显示当前屏蔽的题目ID"""
    print(f"当前屏蔽的题目ID: {BLOCKED_QUESTION_IDS}")
    return BLOCKED_QUESTION_IDS

if __name__ == "__main__":
    # 测试配置
    print("实验配置测试")
    print("=" * 50)
    
    print(f"屏蔽题目数量: {len(BLOCKED_QUESTION_IDS)}")
    print(f"屏蔽的题目ID: {BLOCKED_QUESTION_IDS}")
    
    print(f"\n时间配置: {TIMING_CONFIG}")
    print(f"显示配置: {DISPLAY_CONFIG}")
    print(f"实验配置: {EXPERIMENT_CONFIG}")
