{"cells": [{"cell_type": "markdown", "id": "c0ae4ed0", "metadata": {}, "source": ["# <center> Data Preprocessor </center>\n", "\n", "## Required Imports"]}, {"cell_type": "code", "execution_count": 6, "id": "1f10309e", "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import os\n", "import glob\n", "import warnings"]}, {"cell_type": "markdown", "id": "015ba7cb", "metadata": {}, "source": ["## Class Code\n", "This is the first quarter of the entire project. It is meant to preprocess the data that will be used for everything else. While it didn't necessarily needed to be well documented and modular, it is for the sake of readability and future iterations. "]}, {"cell_type": "code", "execution_count": 7, "id": "279c3d91", "metadata": {}, "outputs": [], "source": ["class Pypil_Dataframe_Processor:\n", "    \"\"\"\n", "    Purpose:\n", "    This class represents an automated approach to trimming and storing all of the\n", "    .csv files as newly named ones in a single folder that were given to me for the task\n", "    \n", "    It will strip the columns to only \"wold_index\" and \"diameter\" which are presumed\n", "    to be the timestamp and the diameter of the pupil features. If these don't exist\n", "    it will then drop those dataframes so we can view them later. \n", "    \n", "    ...\n", "    \n", "    Attributes:\n", "    directory : str\n", "        This is meant to be our working directory (default: C.W.D.)\n", "    \n", "    ...\n", "    \n", "    Methods:\n", "    list_directories(directory)\n", "        This is meant to make a list of all possible directories from this location.\n", "    process_csv_files(columns_to_keep, file_suffix, output_folder)\n", "        This will take all .csv files from the list given to it and read them,\n", "        remove all of their NaN/Nulls, trim it down to only the listed allowed\n", "        columns, then save it as a new dataframe wherever the class was ran.\n", "        E.g. If you change it's directory before running, it will save all of\n", "            the .csv files where the .py/.ipynb file is and not where the \n", "            directory is.\n", "        \n", "    \"\"\"\n", "    def __init__(self):\n", "        try:\n", "            import glob\n", "            import os\n", "            import warnings\n", "            import pandas as pd\n", "        except ImportError:\n", "            raise ImportError(\"You are missing a required library from this group: glob, os, pandas.\")\n", "        \n", "        # This will immediately run list_directories()\n", "        self.directory = self.list_directories(directory=os.getcwd()) \n", "        \n", "        # Initialize list to store skipped files if we want to view them\n", "        self.skipped_files = [] \n", "        \n", "    def list_directories(self, directory=os.getcwd(), max_dir=50):\n", "        \"\"\"\n", "        Purpose:\n", "        This will take in a directory, then it will use os.walk to find all\n", "        possible paths that can lead from here via folders and stores them in\n", "        a list.\n", "\n", "        ...\n", "\n", "        Parameters:\n", "        directory: str\n", "            This is the directory in which you want it to check from. It is \n", "            defaulted to checking the current working directory and is best \n", "            suited to working from there.\n", "            default: os.getcwd()\n", "        max_dir : int\n", "            This is the number of directories you can go into before the code will\n", "            break you out with an error.\n", "            default: 50\n", "        ...\n", "\n", "        Output:\n", "        This will output all potential folder locations in the form of strings\n", "        within a single list. NOTE: This can get problematically large if you\n", "        do this in a high enough folder location.\n", "        \"\"\"\n", "        \n", "        # We have to initialize the list first to store the directories\n", "        all_dir = []\n", "        \n", "        # We have to set this to 0 before we start, if this reaches 50 we break\n", "        num_dir_trav = 0\n", "\n", "        # os.walk will take us through every folder system in the directory\n", "        for root, dirs, files in os.walk(directory):\n", "            num_dir_trav += 1\n", "            \n", "            # As noted before, if we traverse too many directories, we'll break it\n", "            if num_dir_trav > max_dir:\n", "                warnings.warn(f\"Exceeded maximum number of directories traversed ({max_dir}). \"\n", "                              f\"Consider setting a lower limit.\", UserW<PERSON>ning)\n", "                break\n", "\n", "            # This is where we'll append everything\n", "            all_dir.append(root)\n", "            \n", "            # This is where we add the roots to everything\n", "            for dir_name in dirs:\n", "                all_dir.append(os.path.join(root, dir_name))\n", "        return all_dir\n", "    \n", "    def process_csv_files(self, columns_to_keep=[\"world_timestamp\", \"world_index\", \"diameter\"], \n", "                          file_suffix='_trimmed', output_folder='trimmed_files', \n", "                          additional_preprocessing=False):\n", "        \"\"\"\n", "        Purpose:\n", "        This method is meant to do very light pre-processing to the dataframes\n", "        that are fed into it. It will take in only a list of directories and \n", "        after that it will read through each one for the .csv files. Then it will\n", "        remove NaNs/Null values, trim it to the parameters of columns_to_keep \n", "        and then save it to the current working directory with an altered name.\n", "        ...\n", "        \n", "        Parameters:\n", "        columns_to_keep : list\n", "            This will be the list of columns within the dataframe that it will\n", "            save for the new version. As this is built for a certain project in\n", "            mind, it already has its default set to the columns that are wanted, \n", "            but this can easily be altered.\n", "            default: [\"world_index\", \"diameter\"]\n", "        file_suffix : str\n", "            The suffix to be added to the new file name. Added as a potential \n", "            option for you to change if the names get a little too long.\n", "            default: '_trimmed'\n", "        output_folder : str\n", "            The name of the folder where the new CSV files will be stored. Another\n", "            option for you to change if you wanted to store items differently.\n", "            default: 'trimmed_files'\n", "        ...\n", "        \n", "        Output:\n", "        There is no return statement for this method as it will create a large \n", "        number of new .csv files for the user.\n", "        \n", "        \"\"\"\n", "        # This is on the off chance you input a single dataframe into here\n", "        if isinstance(self.directory, list):\n", "            directories = self.directory\n", "        else:\n", "            directories = [self.directory]\n", "\n", "        # Sets our C.W.D. so we have it for later\n", "        save_dir = os.getcwd()\n", "        \n", "        # This is where we initialize the list of all skipped files\n", "        # It's also a set because I'm struggling to make it not repeat\n", "        self.skipped_files = set()\n", "\n", "        # Create output folder if it doesn't exist\n", "        output_folder_path = os.path.join(save_dir, output_folder)\n", "        os.makedirs(output_folder_path, exist_ok=True)\n", "\n", "        # I keep getting multiple of the same errors so I will make it a set\n", "        dec_err_file = set()\n", "        \n", "        # Now we'll take all of those directories and iterate through them here\n", "        for directory in directories:\n", "            # Get a list of all CSV files in the directory we are checking\n", "            csv_files = glob.glob(os.path.join(directory, \"*.csv\"))\n", "\n", "            # Now we'll do all of the preprocessing steps here\n", "            for csv_file in csv_files:\n", "                # This will try the utf encoding of the document, if it cannot do it then\n", "                # it will skip it\n", "                try:\n", "                    df = pd.read_csv(csv_file)\n", "\n", "                    # This is to prevent us from going through dataframes that don't\n", "                    # have the columns we want, if they don't have it we store it elsewhere\n", "                    if set(columns_to_keep).issubset(df.columns):\n", "                        # Drops NaN's and trims the columns in the DF\n", "                        df.dropna(inplace=True)\n", "                        df = df[columns_to_keep]\n", "                        \n", "                        # Additional preprocessing step: groupby \"world_index\" and average \"diameter\"\n", "                        if additional_preprocessing:\n", "                            df = df.groupby('world_index')['diameter'].mean().reset_index()\n", "\n", "                        # Extract the prefix from the directory name for ease of reading\n", "                        fold_name = os.path.basename(directory)\n", "                        prefix = fold_name.split(\"_\")[0].split(\"-\")[0]\n", "                        \n", "                        # Save trimmed DataFrame in the output folder we made earlier\n", "                        base = os.path.basename(csv_file)\n", "\n", "                        # Makes the new name and sets it as specified earlier\n", "                        new_file_name = f\"{prefix}_{os.path.splitext(base)[0]}{file_suffix}.csv\"\n", "\n", "                        # Sets the path to the folder to save it to\n", "                        new_file_path = os.path.join(output_folder_path, new_file_name)\n", "\n", "                        # Saves each one to that new folder\n", "                        df.to_csv(new_file_path, index=False)\n", "#                         print(f\"Processed DataFrame saved as {new_file_path}\")\n", "                    else:\n", "                        # Add skipped file to the list for us to view later\n", "                        self.skipped_files.add(os.path.basename(csv_file))\n", "                        file_name = os.path.basename(csv_file)\n", "#                         print(f\"\\nSkipped DataFrame due to missing columns: {file_name}\")\n", "                except UnicodeDecodeError:\n", "                    if csv_file not in dec_err_file:\n", "                        dec_err_file.add(csv_file)\n", "                        file_name = os.path.basename(csv_file)\n", "                        print(f\"\\nError reading file: {file_name}. \\nPlease try a different encoding.\")"]}, {"cell_type": "code", "execution_count": 14, "id": "caa84fdb", "metadata": {"scrolled": false}, "outputs": [], "source": ["# os.chdir(\"whatever your directory is\")"]}, {"cell_type": "code", "execution_count": 12, "id": "aa909f27", "metadata": {"scrolled": false}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "Error reading file: recording01.csv. \n", "Please try a different encoding.\n", "\n", "Error reading file: recording02.csv. \n", "Please try a different encoding.\n", "\n", "Error reading file: recording03.csv. \n", "Please try a different encoding.\n", "\n", "Error reading file: recording04.csv. \n", "Please try a different encoding.\n"]}], "source": ["processor = Pypil_Dataframe_Processor()\n", "\n", "# This is used to make the files without any additional preprocessing\n", "processor.process_csv_files()"]}, {"cell_type": "code", "execution_count": 13, "id": "3393f9b2", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "Error reading file: recording01.csv. \n", "Please try a different encoding.\n", "\n", "Error reading file: recording02.csv. \n", "Please try a different encoding.\n", "\n", "Error reading file: recording03.csv. \n", "Please try a different encoding.\n", "\n", "Error reading file: recording04.csv. \n", "Please try a different encoding.\n"]}], "source": ["# This is used to groupby world_index for each file so that there\n", "# is only one world_index per number\n", "processor.process_csv_files(output_folder=\"grouped_trimmed_folder\",\n", "                            additional_preprocessing=True)"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.16"}}, "nbformat": 4, "nbformat_minor": 5}