#!/usr/bin/env python3
# show_sentence.py  v2
# 用 ← → 控制前后句；可指定起始句序号
import re, sys, tkinter as tk
from typing import List

# ———— 1. 把文本按句号/问号/叹号/省略号拆句 ————
def sentences(text: str) -> List[str]:
    patt = re.compile(r'[^。！？!?…]+[。！？!?…]*', re.S)
    return [s.strip() for s in patt.findall(text) if s.strip()]

# ———— 2. 主程序 ————
def main(path: str, start_idx: int = 0):
    sents = sentences(open(path, encoding='utf-8').read())
    if not sents:
        raise ValueError('文件中没有检测到句子')

    # 起始索引校正
    idx = max(0, min(start_idx, len(sents) - 1))

    # —— Tkinter UI ——
    root = tk.Tk()
    root.attributes('-fullscreen', True)           # 全屏
    root.configure(bg='black')

    lbl = tk.Label(
        root, text='',
        font=('SimHei', 60),                       # 可改字体
        fg='white', bg='black',
        wraplength=root.winfo_screenwidth() - 160,
        justify='center'
    )
    lbl.pack(expand=True)

    def show():
        lbl.config(text=sents[idx])

    def next_sentence(_=None):
        nonlocal idx
        if idx < len(sents) - 1:
            idx += 1
            show()

    def prev_sentence(_=None):
        nonlocal idx
        if idx > 0:
            idx -= 1
            show()

    # 绑定方向键
    root.bind('<Right>', next_sentence)            # → 下一句
    root.bind('<Left>', prev_sentence)             # ← 上一句
    root.bind('<Escape>', lambda e: root.destroy())# Esc 退出

    show()      # 先显示起始句
    root.mainloop()

# ———— 3. 命令行接口 ————
if __name__ == '__main__':
    if len(sys.argv) not in (2, 3):
        print('用法: python show_sentence.py 文件.txt [起始句序号(从0开始)]')
        sys.exit(1)

    txt_path = sys.argv[1]
    start = int(sys.argv[2]) if len(sys.argv) == 3 else 0
    main(txt_path, start)
