import torch

print("torch:", torch.__version__)
print("built with CUDA:", torch.version.cuda)
print("CUDA available:", torch.cuda.is_available())

if torch.cuda.is_available():
    print("GPU count:", torch.cuda.device_count())
    for i in range(torch.cuda.device_count()):
        p = torch.cuda.get_device_properties(i)
        print(f"GPU[{i}]: {p.name}, CC={p.major}.{p.minor}, VRAM={p.total_memory/1024**3:.1f} GB")
    # 简单算一把，确保真的在 GPU 上跑
    x = torch.randn(4096, 4096, device="cuda")
    y = torch.randn(4096, 4096, device="cuda")
    torch.cuda.synchronize()
    import time; t0=time.time()
    z = x @ y
    torch.cuda.synchronize()
    print("GPU matmul OK, elapsed: %.3fs" % (time.time()-t0))
