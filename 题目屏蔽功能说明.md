# 题目屏蔽功能使用说明

## 功能概述

在好奇心瞳孔实验中，现在可以屏蔽特定的题目，使其不会在随机选题时被选中。这个功能特别适用于：
- 排除已经做过的题目
- 排除不适合当前实验的题目
- 控制实验材料的一致性

## 配置文件

### experiment_config.py
主要配置文件，包含所有实验设置，其中 `BLOCKED_QUESTION_IDS` 列表定义了要屏蔽的题目ID。

当前屏蔽的题目ID：
```python
BLOCKED_QUESTION_IDS = [
    25,   # "傻瓜"最早指的是哪种瓜?
    38,   # 棉花到底是不是花?
    31,   # 宫保鸡丁里"宫保"是个什么称呼?
    196,  # 四维 Yang-Mills 是否已证明存在质量缺口?
    148,  # 我们用什么来听声音?
    162,  # 我们用什么餐具来喝汤?
    14,   # 亚欧大陆以什么为分界线?
    47,   # 为什么是数"羊"来入睡呢?
    51,   # 哪个国家的国歌只有32个音节?
    170,  # 树叶通常是什么颜色?
    122,  # AB血型的人可以输A型血吗?
    94,   # 帕特农神庙位于哪个欧洲城市?
    46,   # 为什么油条都是两根一起炸?
    36,   # 蒸包子馒头时, 顶层和底层谁先熟?
    66,   # 西班牙的国花是什么?
]
```

## 使用方法

### 1. 配置管理工具（推荐）

运行配置管理工具：
```bash
python config_manager.py
```

提供以下功能：
- **显示当前屏蔽题目**：查看所有被屏蔽的题目
- **添加屏蔽题目**：输入题目ID来屏蔽新题目
- **移除屏蔽题目**：从屏蔽列表中移除题目
- **清空所有屏蔽题目**：清空整个屏蔽列表
- **搜索题目**：根据关键词搜索题目
- **显示统计信息**：查看屏蔽题目的统计数据
- **导出/导入屏蔽列表**：备份和恢复屏蔽配置

### 2. 直接修改配置文件

直接编辑 `experiment_config.py` 文件中的 `BLOCKED_QUESTION_IDS` 列表：

```python
# 添加新的屏蔽题目ID
BLOCKED_QUESTION_IDS = [25, 38, 31, 196, 新题目ID]

# 或者清空所有屏蔽题目
BLOCKED_QUESTION_IDS = []
```

### 3. 程序化管理

在Python代码中使用：

```python
from experiment_config import update_blocked_questions, clear_blocked_questions

# 添加屏蔽题目
update_blocked_questions([100, 101, 102])

# 清空屏蔽题目
clear_blocked_questions()
```

## 实验运行

运行实验时，屏蔽功能会自动生效：

```bash
python main_experiment.py
```

系统会：
1. 自动排除屏蔽的题目
2. 显示可用题目数量
3. 从可用题目中随机选择

## 验证功能

### 测试材料加载
```bash
python experiment_materials.py
```

### 查看当前配置
```bash
python -c "from config_manager import ConfigManager; cm = ConfigManager(); cm.show_statistics()"
```

## 注意事项

1. **题目ID验证**：系统会自动验证题目ID是否存在
2. **重复屏蔽**：重复添加相同ID不会产生问题
3. **数量检查**：如果屏蔽题目过多，系统会发出警告
4. **配置持久化**：修改会立即生效，但需要重新启动程序来加载新配置

## 统计信息

当前配置统计：
- 总题目数量：200道
- 屏蔽题目数量：15道
- 可用题目数量：185道
- 屏蔽比例：7.5%

## 故障排除

### 常见问题

1. **题目ID不存在**
   - 检查题目ID是否正确
   - 使用搜索功能查找正确的题目ID

2. **配置不生效**
   - 重新启动实验程序
   - 检查配置文件语法是否正确

3. **可用题目不足**
   - 减少屏蔽的题目数量
   - 或者增加实验所需的题目数量

### 调试命令

```bash
# 查看当前屏蔽题目
python -c "from experiment_config import show_blocked_questions; show_blocked_questions()"

# 测试随机选题
python -c "from experiment_materials import ExperimentMaterials; m = ExperimentMaterials(); print(m.get_random_questions(5))"
```

## 更新历史

- 2025-07-11：添加题目屏蔽功能
- 支持配置文件管理
- 添加配置管理工具
- 集成到主实验流程
