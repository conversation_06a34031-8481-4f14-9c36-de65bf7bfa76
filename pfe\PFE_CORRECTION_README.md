# PFE瞳孔前缩误差校正算法

基于Hayes & Petrov (2016) Behav Res论文实现的几何校正算法。

## 功能概述

PFE (Pupil Foreshortening Error) 是由于眼球相对摄像机转动时，瞳孔影像从圆形投影为椭圆形，导致表观面积按余弦定律缩小的现象。本算法通过几何模型计算校正系数来消除这种误差。

## 核心原理

1. **几何模型**: 计算相机轴与注视轴的夹角θ
2. **校正公式**: `校正后面积 = 原始面积 / cos θ` (论文公式2)
3. **数据类型**: EyeLink的pa_left是角面积，不是直径
4. **坐标转换**: 像素坐标转换为毫米物理坐标
5. **向量化计算**: 高效处理大量数据

## 使用方法

### 方法1: 直接使用校正函数

```python
from pfe.pfe_correction import apply_pfe_correction
import pandas as pd

# 准备数据 (包含注视坐标和瞳孔面积)
data = pd.DataFrame({
    'gx_left': [960, 480, 1440],    # 注视x坐标(像素)
    'gy_left': [540, 270, 810],     # 注视y坐标(像素)
    'pa_left': [2000, 2000, 2000],  # 左眼瞳孔面积
    'pa_right': [2000, 2000, 2000]  # 右眼瞳孔面积
})

# 应用PFE校正
corrected_data = apply_pfe_correction(
    samples=data,
    enable_correction=True,
    use_corneal_correction=False  # 可选：使用角膜像差修正
)
```

### 方法2: 结合预处理器使用

```python
from analysis.preprocess_edf import EyeDataPreprocessor

# 创建预处理器，启用PFE校正
preprocessor = EyeDataPreprocessor(
    enable_pfe_correction=True,
    pfe_correction_method='geometric'
)

# 应用校正
corrected_data = preprocessor.apply_pfe_correction(data)
```

### 方法3: 自定义参数

```python
# 自定义相机和屏幕位置
corrected_data = apply_pfe_correction(
    samples=data,
    camera_position={'x': 0, 'y': -100, 'z': 600},
    screen_position={'x': -240, 'y': 135, 'z': 700},
    screen_width_mm=480,
    screen_height_mm=270,
    use_corneal_correction=True
)
```

## 配置参数

### 默认几何参数 (config.py)

```python
CAMERA_POSITION = {
    'x': 17,    # 相机x坐标 (mm)
    'y': -115,  # 相机y坐标 (mm) 
    'z': 580    # 相机z坐标 (mm)
}

SCREEN_POSITION = {
    'x': -190,  # 屏幕左上角x坐标 (mm)
    'y': 160,   # 屏幕左上角y坐标 (mm)
    'z': 710    # 屏幕左上角z坐标 (mm)
}
```

### 可调参数

- `use_corneal_correction`: 是否使用角膜像差修正 (默认False)
- `screen_width_mm/screen_height_mm`: 屏幕物理尺寸
- `screen_width_pixels/screen_height_pixels`: 屏幕分辨率
- `camera_position/screen_position`: 自定义几何位置

## 校正效果

根据测试结果，PFE校正通常会：

- **平均校正系数**: 1.15-1.25 (增加15-25%)
- **校正范围**: 1.02-1.50 (边缘区域校正更大)
- **空间分布**: 屏幕边缘校正系数大于中心区域

**重要说明**: 校正系数较大是正常的，因为EyeLink输出的是角面积，使用公式2的面积校正方法。

## 文件结构

```
pfe/
├── pfe_correction.py          # 核心校正算法
├── config.py                  # 配置参数
├── example_usage.py           # 使用示例
├── PFE_CORRECTION_README.md   # 本文档
└── calibration_data/          # 标定数据目录
```

## 测试和验证

```bash
# 测试核心算法
conda activate et
python pfe/pfe_correction.py

# 测试集成功能
python test_pfe_correction.py

# 运行使用示例
python pfe/example_usage.py
```

## 注意事项

1. **坐标系统**: 以瞳孔中心为原点，x轴水平向右，y轴垂直向上，z轴指向屏幕后方
2. **数据要求**: 需要注视坐标(gx_left, gy_left)和瞳孔面积(pa_left, pa_right)
3. **单位转换**: 自动处理像素到毫米的坐标转换
4. **异常处理**: 自动跳过无效数据，限制极端校正系数
5. **性能优化**: 使用向量化计算，适合大数据集处理

## 参考文献

Hayes, T. R., & Petrov, A. A. (2016). Mapping and correcting the influence of gaze position on pupil size measurements. *Behavior Research Methods*, 48(2), 510-527.

## 更新历史

- 2025-08-01: 初始版本，实现基础几何校正算法
- 支持向量化计算和自定义参数
- 集成到现有预处理流程中
- **重要修正**: 修正了面积校正公式，从1/√(cos θ)改为1/cos θ，符合论文公式2
