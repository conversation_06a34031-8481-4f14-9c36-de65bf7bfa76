{"participant_id": "gyd20", "experiment_start": "2025-07-11T13:42:14.415144", "experiment_end": "2025-07-11T13:53:58.122235", "total_trials": 20, "timing_settings": {"fixation_baseline": 2.0, "question_display": 7.0, "answer_input": null, "curiosity_rating": 10.0, "pupil_baseline": 3.0, "answer_display": 8.0, "pleasure_rating": null, "surprise_rating": null, "gaze_duration_threshold": 1.0}, "trials": [{"trial_num": 1, "question_id": 18, "question": "哪种动物能拉出方形的粑粑?", "answer": "袋熊是地球上唯一能拉出方形便便的生物.", "start_time": "2025-07-11T13:42:14.415144", "participant_response": "?", "curiosity_rating": 4, "pleasure_rating": 4, "surprise_rating": 2, "timing_data": {"baseline_duration": 2.0155699253082275, "question_duration": 7.049795150756836, "input_duration": 4.017232418060303, "curiosity_rating_duration": 8.044684886932373, "pupil_baseline_duration": 3.0164191722869873, "answer_duration": 8.080315113067627, "pleasure_rating_duration": 4.302373886108398, "surprise_rating_duration": 3.2777304649353027}, "end_time": "2025-07-11T13:42:54.851108"}, {"trial_num": 2, "question_id": 70, "question": "东南亚政治和经济一体化组织的简称是什么?", "answer": "东盟 (ASEAN).", "start_time": "2025-07-11T13:42:54.864883", "participant_response": "?", "curiosity_rating": 3, "pleasure_rating": 3, "surprise_rating": 1, "timing_data": {"baseline_duration": 2.017530679702759, "question_duration": 7.0493059158325195, "input_duration": 3.5003602504730225, "curiosity_rating_duration": 2.8463735580444336, "pupil_baseline_duration": 3.0150811672210693, "answer_duration": 8.061521291732788, "pleasure_rating_duration": 3.7523083686828613, "surprise_rating_duration": 2.687215805053711}, "end_time": "2025-07-11T13:43:28.426223"}, {"trial_num": 3, "question_id": 9, "question": "为何牛肉可以吃三五七分熟, 猪肉却不可以?", "answer": "牛肉寄生虫和细菌相对较少, 而猪肉可能含有多种寄生虫和细菌, 不熟易致病.", "start_time": "2025-07-11T13:43:28.429667", "participant_response": "细菌多", "curiosity_rating": 3, "pleasure_rating": 2, "surprise_rating": 1, "timing_data": {"baseline_duration": 2.014650344848633, "question_duration": 7.033754348754883, "input_duration": 10.685203075408936, "curiosity_rating_duration": 3.216524362564087, "pupil_baseline_duration": 3.0104475021362305, "answer_duration": 8.088675260543823, "pleasure_rating_duration": 3.5337259769439697, "surprise_rating_duration": 2.827679395675659}, "end_time": "2025-07-11T13:44:09.478317"}, {"trial_num": 4, "question_id": 129, "question": "在巴比伦的世界七大奇迹之一是?", "answer": "空中花园.", "start_time": "2025-07-11T13:44:09.481816", "participant_response": "神庙", "curiosity_rating": 4, "pleasure_rating": 4, "surprise_rating": 1, "timing_data": {"baseline_duration": 2.0168609619140625, "question_duration": 7.049609184265137, "input_duration": 9.152625560760498, "curiosity_rating_duration": 2.4612114429473877, "pupil_baseline_duration": 3.0023956298828125, "answer_duration": 8.045264720916748, "pleasure_rating_duration": 3.8806138038635254, "surprise_rating_duration": 2.8408846855163574}, "end_time": "2025-07-11T13:44:48.569035"}, {"trial_num": 5, "question_id": 96, "question": "1937年在新泽西州莱克赫斯特爆炸的齐柏林飞艇叫什么名字?", "answer": "兴登堡号.", "start_time": "2025-07-11T13:44:48.573037", "participant_response": "？", "curiosity_rating": 4, "pleasure_rating": 2, "surprise_rating": 2, "timing_data": {"baseline_duration": 2.012855291366577, "question_duration": 7.081498146057129, "input_duration": 9.019586324691772, "curiosity_rating_duration": 5.084268808364868, "pupil_baseline_duration": 3.0090723037719727, "answer_duration": 8.051884651184082, "pleasure_rating_duration": 2.4552104473114014, "surprise_rating_duration": 2.2959702014923096}, "end_time": "2025-07-11T13:45:28.222017"}, {"trial_num": 6, "question_id": 120, "question": "大气中含量最高的气体?", "answer": "氮气.", "start_time": "2025-07-11T13:45:28.224043", "participant_response": "氮气", "curiosity_rating": 2, "pleasure_rating": 2, "surprise_rating": 1, "timing_data": {"baseline_duration": 2.0144641399383545, "question_duration": 7.049545049667358, "input_duration": 5.820211887359619, "curiosity_rating_duration": 4.546796083450317, "pupil_baseline_duration": 3.011908769607544, "answer_duration": 8.038113832473755, "pleasure_rating_duration": 2.145169258117676, "surprise_rating_duration": 1.546778678894043}, "end_time": "2025-07-11T13:46:03.035729"}, {"trial_num": 7, "question_id": 173, "question": "计算 Leech 格自同构群 Aut(Lambda_24) 的阶.", "answer": "Leech格的全自同构群Co0的阶为8315553613086720000.", "start_time": "2025-07-11T13:46:03.039710", "participant_response": "？", "curiosity_rating": 2, "pleasure_rating": 1, "surprise_rating": 1, "timing_data": {"baseline_duration": 2.0200698375701904, "question_duration": 7.047749996185303, "input_duration": 4.666516065597534, "curiosity_rating_duration": 2.1492671966552734, "pupil_baseline_duration": 3.013869524002075, "answer_duration": 8.07448124885559, "pleasure_rating_duration": 3.7225465774536133, "surprise_rating_duration": 1.8344361782073975}, "end_time": "2025-07-11T13:46:36.207630"}, {"trial_num": 8, "question_id": 149, "question": "中国的首都是哪里?", "answer": "北京", "start_time": "2025-07-11T13:46:36.211333", "participant_response": "北京", "curiosity_rating": 1, "pleasure_rating": 1, "surprise_rating": 1, "timing_data": {"baseline_duration": 2.0221376419067383, "question_duration": 7.045166730880737, "input_duration": 4.383634805679321, "curiosity_rating_duration": 2.162745714187622, "pupil_baseline_duration": 3.017712116241455, "answer_duration": 8.060774326324463, "pleasure_rating_duration": 2.3682401180267334, "surprise_rating_duration": 1.3015754222869873}, "end_time": "2025-07-11T13:47:07.211143"}, {"trial_num": 9, "question_id": 113, "question": "最初创作《唐璜》的诗人的姓氏是什么?", "answer": "拜伦.", "start_time": "2025-07-11T13:47:07.214960", "participant_response": "？", "curiosity_rating": 1, "pleasure_rating": 1, "surprise_rating": 3, "timing_data": {"baseline_duration": 2.016662120819092, "question_duration": 7.049335479736328, "input_duration": 2.302865982055664, "curiosity_rating_duration": 1.9756205081939697, "pupil_baseline_duration": 3.001422166824341, "answer_duration": 8.05162787437439, "pleasure_rating_duration": 1.7316205501556396, "surprise_rating_duration": 2.8938522338867188}, "end_time": "2025-07-11T13:47:36.876166"}, {"trial_num": 10, "question_id": 175, "question": "写出黎曼 zeta 函数在 s = -1 处的解析延拓值.", "answer": "根据黎曼zeta函数与伯努利数的关系式zeta(-n) = -B_(n+1)/(n+1), 当n=1时可得值为-1/12.", "start_time": "2025-07-11T13:47:36.880134", "participant_response": "？", "curiosity_rating": 3, "pleasure_rating": 3, "surprise_rating": 1, "timing_data": {"baseline_duration": 2.004606246948242, "question_duration": 7.065577507019043, "input_duration": 5.221060752868652, "curiosity_rating_duration": 2.1432383060455322, "pupil_baseline_duration": 3.0149967670440674, "answer_duration": 8.065059423446655, "pleasure_rating_duration": 2.538090944290161, "surprise_rating_duration": 3.1281626224517822}, "end_time": "2025-07-11T13:48:10.696230"}, {"trial_num": 11, "question_id": 44, "question": "海水里那么多盐是从哪儿来的?", "answer": "海水里的盐主要是酸性雨水溶解岩石中的矿物质, 经河流带入海洋, 且海洋无排盐机制, 盐分不断积累而来.", "start_time": "2025-07-11T13:48:10.699179", "participant_response": "？", "curiosity_rating": 4, "pleasure_rating": 4, "surprise_rating": 2, "timing_data": {"baseline_duration": 2.0039560794830322, "question_duration": 7.050215482711792, "input_duration": 4.383904218673706, "curiosity_rating_duration": 2.095745086669922, "pupil_baseline_duration": 3.0159666538238525, "answer_duration": 8.120596408843994, "pleasure_rating_duration": 3.481457471847534, "surprise_rating_duration": 2.0664126873016357}, "end_time": "2025-07-11T13:48:43.552699"}, {"trial_num": 12, "question_id": 135, "question": "冰是由什么液体冷冻而成的?", "answer": "水", "start_time": "2025-07-11T13:48:43.555694", "participant_response": "水", "curiosity_rating": 4, "pleasure_rating": 2, "surprise_rating": 1, "timing_data": {"baseline_duration": 2.01760196685791, "question_duration": 7.048023223876953, "input_duration": 4.486546516418457, "curiosity_rating_duration": 4.467915058135986, "pupil_baseline_duration": 3.0106003284454346, "answer_duration": 8.053518772125244, "pleasure_rating_duration": 5.7286083698272705, "surprise_rating_duration": 2.653093099594116}, "end_time": "2025-07-11T13:49:21.655560"}, {"trial_num": 13, "question_id": 29, "question": "老抽和生抽的\"抽\"是什么意思?", "answer": "指提取酱汁, 酱油酿造过程中, 第一次抽取出来的酱油叫\"头抽\", 还有\"二抽\"\"三抽\".", "start_time": "2025-07-11T13:49:21.660034", "participant_response": "?", "curiosity_rating": 3, "pleasure_rating": 3, "surprise_rating": 1, "timing_data": {"baseline_duration": 2.0141241550445557, "question_duration": 7.049599647521973, "input_duration": 4.184200763702393, "curiosity_rating_duration": 3.083616018295288, "pupil_baseline_duration": 3.009611129760742, "answer_duration": 8.080501556396484, "pleasure_rating_duration": 5.422608137130737, "surprise_rating_duration": 2.7300989627838135}, "end_time": "2025-07-11T13:49:57.873861"}, {"trial_num": 14, "question_id": 48, "question": "为什么\"一打面包\"是13个而不是12个?", "answer": "古时面包师傅为避免因面包重量不达标受罚, 顾客买一打面包时, 会多给一个, 所以\"一打面包\"是13个.", "start_time": "2025-07-11T13:49:57.877850", "participant_response": "?", "curiosity_rating": 5, "pleasure_rating": 5, "surprise_rating": 3, "timing_data": {"baseline_duration": 2.0169758796691895, "question_duration": 7.04984712600708, "input_duration": 3.7345879077911377, "curiosity_rating_duration": 3.166005849838257, "pupil_baseline_duration": 3.011082649230957, "answer_duration": 8.10307502746582, "pleasure_rating_duration": 3.3492159843444824, "surprise_rating_duration": 3.3666670322418213}, "end_time": "2025-07-11T13:50:32.313134"}, {"trial_num": 15, "question_id": 21, "question": "\"三长两短\"说的是哪三长哪两短?", "answer": "通常认为\"三长\"指棺材盖, 棺材底和棺材帮, \"两短\"指棺材的两头.", "start_time": "2025-07-11T13:50:32.317513", "participant_response": "?", "curiosity_rating": 5, "pleasure_rating": 5, "surprise_rating": 3, "timing_data": {"baseline_duration": 2.016477346420288, "question_duration": 7.032771587371826, "input_duration": 4.7845470905303955, "curiosity_rating_duration": 2.8970372676849365, "pupil_baseline_duration": 3.0156781673431396, "answer_duration": 8.09599232673645, "pleasure_rating_duration": 2.8684024810791016, "surprise_rating_duration": 1.933201551437378}, "end_time": "2025-07-11T13:51:05.595420"}, {"trial_num": 16, "question_id": 111, "question": "儒勒·凡尔纳小说《海底两万里》中的潜艇叫什么名字?", "answer": "\"鹦鹉螺\"号 (Nautilus).", "start_time": "2025-07-11T13:51:05.598933", "participant_response": "?", "curiosity_rating": 3, "pleasure_rating": 2, "surprise_rating": 1, "timing_data": {"baseline_duration": 2.0006706714630127, "question_duration": 7.04933762550354, "input_duration": 3.6831343173980713, "curiosity_rating_duration": 3.1480658054351807, "pupil_baseline_duration": 3.013857126235962, "answer_duration": 8.061506509780884, "pleasure_rating_duration": 4.3203325271606445, "surprise_rating_duration": 1.955162525177002}, "end_time": "2025-07-11T13:51:39.467334"}, {"trial_num": 17, "question_id": 39, "question": "羽绒服有鸭绒鹅绒, 为什么没有鸡绒?", "answer": "因为鸡属于陆禽, 绒羽少, 保暖性差, 且羽毛易沾杂质, 处理难度大, 尾脂腺不发达, 防水性差, 不符合羽绒标准.", "start_time": "2025-07-11T13:51:39.471272", "participant_response": "不保暖", "curiosity_rating": 2, "pleasure_rating": 2, "surprise_rating": 1, "timing_data": {"baseline_duration": 2.0156655311584473, "question_duration": 7.049217462539673, "input_duration": 8.54765772819519, "curiosity_rating_duration": 2.1841676235198975, "pupil_baseline_duration": 3.013754367828369, "answer_duration": 8.076976776123047, "pleasure_rating_duration": 2.788916826248169, "surprise_rating_duration": 2.1971187591552734}, "end_time": "2025-07-11T13:52:15.982620"}, {"trial_num": 18, "question_id": 164, "question": "糖是什么味道的?", "answer": "甜的", "start_time": "2025-07-11T13:52:15.985687", "participant_response": "甜", "curiosity_rating": 1, "pleasure_rating": 1, "surprise_rating": 1, "timing_data": {"baseline_duration": 2.003242015838623, "question_duration": 7.032524824142456, "input_duration": 6.83158016204834, "curiosity_rating_duration": 2.8840291500091553, "pupil_baseline_duration": 3.012603521347046, "answer_duration": 8.083467721939087, "pleasure_rating_duration": 2.320911407470703, "surprise_rating_duration": 1.663210153579712}, "end_time": "2025-07-11T13:52:50.454652"}, {"trial_num": 19, "question_id": 191, "question": "一个太阳质量的无自旋 Schwarzschild 黑洞的霍金温度约为多少?", "answer": "根据霍金辐射温度公式 T_H = (h-bar * c^3) / (8 * pi * G * M * k_B), 一个太阳质量黑洞的温度约为6. 17 x 10^-8 K.", "start_time": "2025-07-11T13:52:50.456378", "participant_response": "？", "curiosity_rating": 3, "pleasure_rating": 2, "surprise_rating": 1, "timing_data": {"baseline_duration": 2.0036871433258057, "question_duration": 7.065845251083374, "input_duration": 3.3523664474487305, "curiosity_rating_duration": 2.4457662105560303, "pupil_baseline_duration": 3.013556957244873, "answer_duration": 8.07145881652832, "pleasure_rating_duration": 3.843222141265869, "surprise_rating_duration": 1.8134150505065918}, "end_time": "2025-07-11T13:53:22.704369"}, {"trial_num": 20, "question_id": 55, "question": "世界上人口密度最高的国家是哪个?", "answer": "摩纳哥 - 摩纳哥是世界上人口密度最高的国家, 每平方公里约19497人, 国土面积仅2. 08平方公里, 是世界第二小的国家, 也是全球超级富豪的聚集地和避税天堂.", "start_time": "2025-07-11T13:53:22.708383", "participant_response": "？", "curiosity_rating": 4, "pleasure_rating": 4, "surprise_rating": 2, "timing_data": {"baseline_duration": 2.0052220821380615, "question_duration": 7.047987937927246, "input_duration": 4.235029458999634, "curiosity_rating_duration": 2.147299289703369, "pupil_baseline_duration": 3.0147337913513184, "answer_duration": 8.111109018325806, "pleasure_rating_duration": 6.185323476791382, "surprise_rating_duration": 2.031217098236084}, "end_time": "2025-07-11T13:53:58.122235"}]}