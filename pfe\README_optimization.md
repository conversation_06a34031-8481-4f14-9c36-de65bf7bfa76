# PFE瞳孔前缩误差矫正优化程序

## 功能说明

该程序用于对PFE（Pupil Foreshortening Error）瞳孔前缩误差进行矫正参数优化。基于Hayes & Petrov (2016)论文中的Nelder-Mead优化算法，通过标定数据优化几何模型参数，提高瞳孔大小测量的准确性。

## 使用方法

### 基本用法

```bash
# 激活环境并运行优化
conda activate et
python pfe/pfe_correction.py --calibration "标定文件路径.csv"
```

### 完整参数

```bash
python pfe/pfe_correction.py \
    --calibration "pfe/calibration_data/your_calibration.csv" \
    --output "pfe/custom_optimization" \
    --method optimized \
    --test
```

### 参数说明

- `--calibration, -c`: **必需** 标定数据文件路径（.csv格式）
- `--output, -o`: 可选，输出目录（默认: pfe/optimization_results）
- `--method, -m`: 可选，矫正方法（默认: optimized）
  - `geometric`: 基础几何模型矫正
  - `optimized`: 优化后的几何模型矫正
  - `interpolation`: 基于插值的矫正
  - `hybrid`: 混合矫正方法
- `--test, -t`: 可选，运行测试模式，验证矫正效果

## 输入文件格式

标定数据文件需要包含以下列：
- `screen_x`: 屏幕X坐标
- `screen_y`: 屏幕Y坐标  
- `pupil_left`: 左眼瞳孔大小
- `pupil_right`: 右眼瞳孔大小（可选）

## 输出文件

优化完成后会在指定目录生成JSON格式的结果文件，包含：

```json
{
  "success": true,
  "initial_parameters": {
    "Cx": 17.0, "Cy": -115.0, "Cz": 580.0,
    "Sx": -190.0, "Sy": 160.0, "Sz": 710.0
  },
  "optimized_parameters": {
    "Cx": 500.0, "Cy": -500.0, "Cz": 580.0,
    "Sx": 351.76, "Sy": 635.47, "Sz": 411.76
  },
  "initial_rmse": 272.69,
  "final_rmse": 184.96,
  "rmse_improvement_percent": 32.17,
  "baseline_pupil": 565.50,
  "n_calibration_points": 48,
  "optimization_iterations": 24,
  "optimization_function_evaluations": 204
}
```

## 使用示例

### 示例1：基本优化

```bash
conda activate et
python pfe/pfe_correction.py -c "pfe/calibration_data/test_calibration.csv"
```

### 示例2：指定输出目录并测试

```bash
conda activate et
python pfe/pfe_correction.py \
    -c "pfe/calibration_data/test_calibration.csv" \
    -o "pfe/my_optimization_results" \
    --test
```

### 示例3：使用不同的矫正方法

```bash
conda activate et
python pfe/pfe_correction.py \
    -c "pfe/calibration_data/test_calibration.csv" \
    -m interpolation
```

## 优化结果解读

- **RMSE改善**: 显示优化前后的均方根误差改善百分比
- **优化参数**: 包含相机位置(Cx,Cy,Cz)和屏幕位置(Sx,Sy,Sz)的优化值
- **迭代次数**: Nelder-Mead算法的迭代次数
- **函数评估**: 目标函数的评估次数

## 注意事项

1. 确保标定数据质量良好，至少需要36个有效标定点
2. 优化过程可能需要几秒到几分钟，取决于数据复杂度
3. 建议使用测试模式验证矫正效果
4. 优化结果文件可用于后续的瞳孔数据矫正

## 故障排除

- 如果优化失败，检查标定数据是否包含必要的列
- 如果RMSE改善很小，可能需要更多或更好质量的标定数据
- 如果程序报错，检查文件路径是否正确
