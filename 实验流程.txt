实验设计
被试先观看屏幕中央十字准星（5~7？），校准瞳孔基线。
然后屏幕呈现一个冷知识问题，呈现8s（有时间思考，又不会等的无聊）。（瞳孔数据收集从阅读完成到等待结束，这个时间段内混合了思考引发的瞳孔和好奇引发的瞳孔。）
然后屏幕上出现一个填空框，强制被试通过键盘给出自己的答案。(15s)（强制被试给答案是因为之前的研究得出这样可以普遍增加被试的好奇心。从第一次的问卷情况看，这种冷知识问答较难引发最强烈的好奇心5，先预测会可能有提升。）（一半的被试被要求给出答案；另外一半不被要求。因为在真实的教材或者网课中，作者提出问题之后会自己给出答案，通常读者不会专门停下来想一下，再接着看，而是懒得想直接看。这种场景还原真实的场景，希望能通过瞳孔分辨好奇程度。而强制给出答案是因为这样可以提升好奇心。）
屏幕出现问题要求被试对好奇心打分（1~5分）(2s)，问题是：“你有多想继续读下去，请给你的好奇程度打分”。被试通过键盘打分。
屏幕中间出现圆点，获取瞳孔基线（3s？）。
屏幕给出答案(10s)。（10s？自己调节速度？）。（预期在答案出现前1.5s开始上升，到答案展示完后2s回到基线）。（之前的好奇心主要是极短答案的阅读材料，这里我混合有长短答案，希望对于长答案可以分理出读完后的愉悦感）
屏幕出现问题要求被试对愉悦度打分（1~5分）(2s)，问题是：“你读完这个答案觉得它多有趣，是否让你觉得愉悦，有多强烈的“aha，原来是这样的感觉”。被试通过键盘打分。
加一个意外程度