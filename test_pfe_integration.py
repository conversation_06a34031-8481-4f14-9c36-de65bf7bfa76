#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
测试PFE集成功能
验证标定数据保存到pfe文件夹，分析器简化参数等功能
"""

import os
import sys

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from pfe.pfe_data_analyzer import analyze_pfe_calibration

def test_pfe_integration():
    """测试PFE集成功能"""
    print("🧪 测试PFE集成功能")
    print("=" * 50)
    
    # 1. 检查pfe/calibration_data目录是否存在
    calibration_dir = "pfe/calibration_data"
    if not os.path.exists(calibration_dir):
        print("❌ PFE标定数据目录不存在")
        return False
    
    print(f"✅ PFE标定数据目录存在: {calibration_dir}")
    
    # 2. 检查目录中是否有EDF和CSV文件
    files = os.listdir(calibration_dir)
    edf_files = [f for f in files if f.endswith('.edf')]
    csv_files = [f for f in files if f.endswith('.csv') and 'calibration' in f and 'analyzed' not in f]
    
    print(f"📁 目录内容:")
    for file in files:
        print(f"  - {file}")
    
    if not edf_files:
        print("❌ 未找到EDF文件")
        return False
    
    if not csv_files:
        print("❌ 未找到标定CSV文件")
        return False
    
    print(f"✅ 找到EDF文件: {edf_files}")
    print(f"✅ 找到标定CSV文件: {csv_files}")
    
    # 3. 测试简化的analyzer参数
    print("\n🔍 测试简化的analyzer参数...")
    try:
        result_file = analyze_pfe_calibration(calibration_dir)
        if result_file:
            print(f"✅ 分析成功，结果文件: {result_file}")
            
            # 检查结果文件是否存在
            if os.path.exists(result_file):
                print(f"✅ 结果文件确实存在")
                
                # 检查报告文件
                report_file = result_file.replace('.csv', '_report.txt')
                if os.path.exists(report_file):
                    print(f"✅ 报告文件也存在: {report_file}")
                else:
                    print(f"⚠️  报告文件不存在: {report_file}")
            else:
                print(f"❌ 结果文件不存在: {result_file}")
                return False
        else:
            print("❌ 分析失败")
            return False
    except Exception as e:
        print(f"❌ 分析过程出错: {e}")
        return False
    
    # 4. 验证数据结构
    print("\n📊 验证分析结果数据结构...")
    try:
        import pandas as pd
        df = pd.read_csv(result_file, encoding='utf-8-sig')
        
        required_columns = ['point_index', 'screen_x', 'screen_y', 'pupil_left', 'pupil_right']
        missing_columns = [col for col in required_columns if col not in df.columns]
        
        if missing_columns:
            print(f"❌ 缺少必要列: {missing_columns}")
            return False
        
        print(f"✅ 数据结构正确，包含 {len(df)} 行数据")
        print(f"✅ 包含所有必要列: {required_columns}")
        
        # 检查数据质量
        valid_left = df['pupil_left'].notna().sum()
        valid_right = df['pupil_right'].notna().sum()
        total_points = len(df)
        
        print(f"📈 数据质量:")
        print(f"  - 总标定点数: {total_points}")
        print(f"  - 有效左眼数据: {valid_left} ({valid_left/total_points:.1%})")
        print(f"  - 有效右眼数据: {valid_right} ({valid_right/total_points:.1%})")
        
        if valid_left > 0 and valid_right > 0:
            print(f"✅ 数据质量良好")
        else:
            print(f"⚠️  数据质量可能有问题")
        
    except Exception as e:
        print(f"❌ 验证数据结构失败: {e}")
        return False
    
    print("\n🎉 PFE集成测试完成!")
    print("✅ 所有功能正常工作:")
    print("  1. 标定数据正确保存到pfe/calibration_data目录")
    print("  2. 分析器简化参数功能正常")
    print("  3. 自动文件查找功能正常")
    print("  4. 数据分析和结果生成正常")
    
    return True

if __name__ == "__main__":
    success = test_pfe_integration()
    if success:
        print("\n🎯 测试通过!")
    else:
        print("\n❌ 测试失败!")
        sys.exit(1)
