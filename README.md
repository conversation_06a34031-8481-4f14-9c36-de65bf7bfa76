# 好奇心瞳孔冷知识实验

这是一个基于PsychoPy和EyeLink的眼动实验程序，用于研究好奇心与瞳孔反应的关系。

重要的api使用方法在目录："eyelink python example\Pylink api userguide.txt"以及"eyelink python example\Getting started with Python and Pylink.txt",如果你想调用任何和eyelink相关的函数，先上这个里面寻找参考

### 1. 统一数据管理
- **统一data目录**: 所有实验数据保存在`data/`文件夹下
- **标准化命名**: 使用`日期+时间+被试ID`格式，如`20241205_143022_participant001`
- **自动目录创建**: 程序自动创建数据目录结构

### 2. 增强的EyeLink集成
- **完整API支持**: 基于EyeLink Python API用户指南实现
- **详细事件记录**: 每个实验阶段都有精确的事件标记
- **自动EDF文件接收**: 实验结束后自动获取眼动数据文件
- **瞳孔数据优化**: 1000Hz采样率，专门优化瞳孔大小记录
- **智能错误处理**: 连接失败时自动切换到虚拟模式

### 3. 精确的事件标记系统
- **时间戳记录**: 每个事件都有精确的时间戳
- **事件计数器**: 自动编号所有事件
- **内容记录**: 记录题目内容、被试回答、评分等详细信息
- **阶段标识**: 清晰标识实验的每个阶段（基线、题目、评分等）

### 4. 🆕 改进的输入系统
- **中文输入支持**: 完全支持中文输入法，可连续输入多个汉字
- **无时间限制**: 答案输入不再有时间限制，按回车键确认
- **多层回退机制**: TextBox2 → Unicode事件处理 → 兼容模式
- **用户友好**: 清晰的操作提示和视觉反馈
- **🔧 调试修复**: 解决了TextBox2 refresh方法不存在的问题

### 5. 🆕 优化的默认设置
- **默认全屏显示**: 更适合正式实验环境
- **默认使用EyeLink**: 符合眼动实验的标准配置
- **默认1道题**: 便于快速测试和调试

## 实验设计

实验流程按照以下步骤进行：

1. **基线校准** (5-7秒) - 被试观看屏幕中央十字准星，校准瞳孔基线
2. **题目呈现** (8秒) - 屏幕呈现冷知识问题，被试阅读和思考
3. **答案输入** (无时间限制) - 被试通过键盘输入答案，支持中文输入，按回车确认
4. **好奇心评分** (10秒) - 被试对好奇程度打分 (1-5分)
5. **瞳孔基线** (3秒) - 屏幕中间出现圆点，获取瞳孔基线
6. **答案展示** (10秒) - 屏幕给出正确答案
7. **愉悦度评分** (10秒) - 被试对答案的有趣程度打分 (1-5分)
8. **意外程度评分** (10秒) - 被试对答案的意外程度打分 (1-3分)

## 文件结构

```
好奇心 瞳孔 冷知识实验/
├── main_experiment.py          # 主实验程序
├── experiment_flow.py          # 实验流程控制
├── experiment_materials.py     # 实验材料管理
├── experiment_display.py       # 实验界面显示
├── eyelink_manager.py          # EyeLink眼动仪管理
├── data_manager.py             # 数据记录和保存
├── check_dependencies.py       # 依赖检查工具
├── 实验材料库.txt              # 实验题目和答案
├── 实验流程.txt                # 实验设计说明
└── README.md                   # 说明文档
```

## 环境要求

### 必需依赖
- Python 3.7+
- 基本Python库：json, time, random, os, datetime

### 可选依赖（推荐安装）
- **PsychoPy** - 实验界面显示
- **PyLink** - EyeLink眼动仪接口
- **pandas** - 数据分析
- **numpy** - 数值计算

### 安装依赖

在conda eyetracking环境中安装：

```bash
# 激活环境
conda activate eyetracking

# 安装PsychoPy
conda install psychopy

# 安装EyeLink接口（如果使用EyeLink）
pip install pylink-platform

# 安装数据分析库
conda install pandas numpy
```

## 使用方法

### 1. 快速测试

首先运行测试模式验证所有模块：

```bash
python main_experiment.py --test
```

### 2. 运行完整实验

```bash
python main_experiment.py
```

程序会提示输入：
- 被试ID
- 是否使用EyeLink眼动仪（默认：是）
- 是否全屏显示（默认：是）
- 题目数量（默认：1道）

### 3. 仅测试材料加载

```bash
python experiment_materials.py
```

### 4. 仅测试显示功能

```bash
python experiment_display.py
```

### 5. 仅测试EyeLink功能

```bash
python eyelink_manager.py
```

### 6. 测试所有改进功能

```bash
python test_improvements.py
```

### 7. 测试输入功能改进

```bash
python test_input_improvements.py
```

### 8. 测试调试修复

```bash
python test_debug_fixes.py
```

### 9. EyeLink 校准与验证

```bash
python calibration_validation.py --id P001   # 真实模式
python calibration_validation.py --id test001 --dummy  # 无硬件测试
```

## 数据输出

实验完成后会在统一的data目录下保存数据，使用 **日期+时间+被试ID** 的命名格式：

```
data/
└── 20241205_143022_participant001/
    ├── experiment_summary.json     # 实验总结数据
    ├── experiment_data.csv         # CSV格式数据（便于分析）
    ├── experiment_log.txt          # 实验日志
    ├── selected_questions.json     # 本次实验选中的题目
    ├── participant001.edf          # EyeLink眼动数据文件
    ├── trial_1.json               # 试次1详细数据
    ├── trial_2.json               # 试次2详细数据
    └── trial_3.json               # 试次3详细数据
```

### EyeLink数据记录

程序会记录完整的眼动和瞳孔数据，包括：

- **瞳孔大小变化** - 1000Hz采样率记录瞳孔直径
- **眼动轨迹** - 注视点坐标、眼跳、注视等
- **详细事件标记** - 每个实验阶段都有精确的时间戳

#### 事件标记示例

```
EVENT_001 EXPERIMENT_START
EVENT_002 PARTICIPANT_ID participant001
EVENT_003 CALIBRATION_START
EVENT_004 CALIBRATION_END SUCCESS
EVENT_005 TRIAL_START 1
EVENT_006 QUESTION_TEXT 历史上"王府井"是哪个王府家的井？
EVENT_007 BASELINE_START FIXATION
EVENT_008 DISPLAY_FIXATION_CROSS
EVENT_009 BASELINE_END FIXATION
EVENT_010 QUESTION_DISPLAY_START
EVENT_011 QUESTION_CONTENT 历史上"王府井"是哪个王府家的井？
EVENT_012 QUESTION_DISPLAY_END
EVENT_013 INPUT_START
EVENT_014 DISPLAY_INPUT_BOX
EVENT_015 INPUT_END
EVENT_016 PARTICIPANT_RESPONSE 不知道
EVENT_017 RATING_START CURIOSITY
EVENT_018 DISPLAY_RATING_SCALE CURIOSITY
EVENT_019 RATING_END CURIOSITY
EVENT_020 RATING_VALUE CURIOSITY 4
EVENT_021 BASELINE_START PUPIL
EVENT_022 DISPLAY_FIXATION_CROSS
EVENT_023 BASELINE_END PUPIL
EVENT_024 ANSWER_DISPLAY_START
EVENT_025 ANSWER_CONTENT "王府井"实为"王爷府（群）旁的井"，而非哪位特定王爷的专属井。
EVENT_026 ANSWER_DISPLAY_END
EVENT_027 RATING_START PLEASURE
EVENT_028 RATING_END PLEASURE
EVENT_029 RATING_VALUE PLEASURE 3
EVENT_030 RATING_START SURPRISE
EVENT_031 RATING_END SURPRISE
EVENT_032 RATING_VALUE SURPRISE 2
EVENT_033 TRIAL_END 1
EVENT_034 EXPERIMENT_END
```

### 数据字段说明

每个试次包含以下数据：
- `trial_num`: 试次编号
- `question_id`: 题目ID
- `question`: 题目内容
- `answer`: 正确答案
- `participant_response`: 被试回答
- `curiosity_rating`: 好奇心评分 (1-5)
- `pleasure_rating`: 愉悦度评分 (1-5)
- `surprise_rating`: 意外程度评分 (1-3)
- `start_time`: 试次开始时间
- `end_time`: 试次结束时间
- `timing_data`: 各阶段用时详情

## 实验配置

### 时间参数

可在 `experiment_flow.py` 中的 `timing` 字典修改各阶段时间：

```python
self.timing = {
    'fixation_baseline': random.uniform(5, 7),  # 基线校准时间
    'question_display': 8.0,                   # 问题显示时间
    'answer_input': None,                      # 答案输入时间（无限制，按回车确认）
    'curiosity_rating': 10.0,                  # 好奇心评分时间
    'pupil_baseline': 3.0,                     # 瞳孔基线时间
    'answer_display': 10.0,                    # 答案显示时间
    'pleasure_rating': 10.0,                   # 愉悦度评分时间
    'surprise_rating': 10.0                    # 意外程度评分时间
}
```

### 输入系统配置

输入系统的相关设置在 `experiment_display.py` 中：

```python
# TextBox2配置（支持中文输入）
textbox = TextBox2(
    self.win,
    font='SimHei',          # 中文字体
    size=(800, 60),         # 输入框大小
    letterHeight=25,        # 字体大小
    color='black',          # 文字颜色
    fillColor='white',      # 背景颜色
    borderColor='gray',     # 边框颜色
    editable=True           # 可编辑
)
```

**输入操作说明**：
- 支持中文输入法
- 支持英文、数字、标点符号
- 按回车键确认输入
- 按ESC键取消输入
- 支持退格删除
- 无时间限制

### 评分标签

可在 `experiment_flow.py` 中修改评分选项：

```python
self.curiosity_labels = ["完全不想知道", "稍有兴趣", "可以知道一下", "比较感兴趣", "极其好奇"]
self.pleasure_labels = ["太无聊了", "有点无聊", "一般般", "有点意思", "太有趣了"]
self.surprise_labels = ["不意外", "有些意外", "很意外"]
```

## 故障排除

### 1. 模块导入失败

如果出现模块导入错误，请检查：
- 是否在正确的conda环境中
- 是否安装了必需的依赖库
- Python路径是否正确

### 2. PsychoPy相关错误

如果PsychoPy无法正常工作：
- 程序会自动切换到模拟模式
- 检查显卡驱动是否最新
- 尝试关闭其他占用显卡的程序

### 3. EyeLink连接问题

如果EyeLink无法连接：
- 检查网络连接和IP地址设置
- 确认EyeLink主机正常运行
- 程序会自动切换到虚拟模式继续实验

### 4. 数据保存问题

如果数据保存失败：
- 检查磁盘空间是否充足
- 确认有写入权限
- 查看实验日志了解详细错误信息

## 注意事项

1. **实验前准备**
   - 确保EyeLink眼动仪正常工作并已校准
   - 调整被试座椅和头托位置
   - 检查实验室光线条件

2. **实验过程中**
   - 提醒被试保持头部稳定
   - 注意观察眼动数据质量
   - 如有异常可按ESC键退出

3. **数据备份**
   - 实验完成后及时备份数据
   - 建议将数据复制到多个位置
   - 定期检查数据完整性

## 开发和定制

### 添加新题目

在 `实验材料库.txt` 文件中按格式添加新题目和答案。

### 修改实验流程

主要修改 `experiment_flow.py` 中的 `run_single_trial` 方法。

### 自定义界面

修改 `experiment_display.py` 中的显示方法来自定义界面样式。

### 扩展数据记录

在 `data_manager.py` 中添加新的数据字段和保存格式。

## 技术支持

如有问题请检查：
1. 实验日志文件中的错误信息
2. Python控制台的错误输出
3. EyeLink主机的状态信息

## 版本信息

- 版本：1.0
- 开发环境：Python 3.x + PsychoPy + EyeLink
- 兼容性：Windows 10/11, conda环境

## 🆕 PyTrack-NTU EDF 预处理示例

若只想快速把 EyeLink 采样的 *EDF* 文件转成“已过滤 + 已插值”后的瞳孔曲线，可直接使用脚本 `pytrack_preprocess.py`：

```bash
# 安装依赖（仅首次）
pip install PyTrack-NTU pandas matplotlib

# 运行示例
python pytrack_preprocess.py --edf data/20250709_125800_test_0709_1257/test_0709_1257.edf \
                            --out demo_clean
```

脚本完成的步骤：
1. `read_edf` 读取 EDF → 得到 trial-dict 列表。
2. `blink_detection` 自动找眨眼区段，并把瞳孔值置为 *NaN*。
3. `mean ± 3×std` 方式剔除异常点，再做线性插值。
4. 将首条试次保存为 `demo_clean_trial0_clean.csv`。
5. 输出对比图 `demo_clean_trial0_raw_clean.png`（红 = 原始，黑 = 清洗后）。

默认只处理第一条试次，可自行修改脚本循环全部。