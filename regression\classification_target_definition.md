# 分类目标定义

## 目标变量：兴趣程度分类

### 原始评分系统
基于连续阅读实验中的兴趣评分数据：
- **评分范围**: 1-5
- **评分含义**: 
  - 1: 非常不感兴趣
  - 2: 不感兴趣  
  - 3: 一般
  - 4: 感兴趣
  - 5: 非常感兴趣

### 分类策略设计

#### 策略1: 五分类任务 (原始评分)
```python
# 直接使用原始1-5评分
class_mapping = {
    1: 0,  # 非常不感兴趣
    2: 1,  # 不感兴趣
    3: 2,  # 一般
    4: 3,  # 感兴趣
    5: 4   # 非常感兴趣
}

n_classes = 5
class_names = ['非常不感兴趣', '不感兴趣', '一般', '感兴趣', '非常感兴趣']
```

**优势**: 保留完整的评分信息
**挑战**: 类别较多，可能存在类别不平衡问题

#### 策略2: 三分类任务 (推荐)
```python
# 将5个评分合并为3个类别
class_mapping = {
    1: 0,  # 低兴趣 (原评分1-2)
    2: 0,  
    3: 1,  # 中等兴趣 (原评分3)
    4: 2,  # 高兴趣 (原评分4-5)
    5: 2
}

n_classes = 3
class_names = ['低兴趣', '中等兴趣', '高兴趣']
```

**优势**: 
- 类别数量适中，便于分类
- 符合心理学上的三分法
- 减少类别不平衡问题

#### 策略3: 二分类任务 (基准)
```python
# 简化为高低兴趣二分类
class_mapping = {
    1: 0,  # 低兴趣 (原评分1-3)
    2: 0,
    3: 0,
    4: 1,  # 高兴趣 (原评分4-5)
    5: 1
}

n_classes = 2
class_names = ['低兴趣', '高兴趣']
```

**优势**: 
- 最简单的分类任务
- 适合作为基准模型
- 类别平衡相对容易

## 标签映射实现

### 编码函数
```python
def encode_interest_labels(ratings, strategy='ternary'):
    """
    将兴趣评分编码为分类标签
    
    Args:
        ratings: 原始评分列表 (1-5)
        strategy: 编码策略 ('binary', 'ternary', 'original')
    
    Returns:
        encoded_labels: 编码后的标签数组
        class_names: 类别名称列表
    """
    ratings = np.array(ratings)
    
    if strategy == 'binary':
        # 二分类: 1-3 -> 0, 4-5 -> 1
        labels = (ratings >= 4).astype(int)
        class_names = ['低兴趣', '高兴趣']
        
    elif strategy == 'ternary':
        # 三分类: 1-2 -> 0, 3 -> 1, 4-5 -> 2
        labels = np.zeros_like(ratings)
        labels[ratings == 3] = 1
        labels[ratings >= 4] = 2
        class_names = ['低兴趣', '中等兴趣', '高兴趣']
        
    elif strategy == 'original':
        # 五分类: 1-5 -> 0-4
        labels = ratings - 1
        class_names = ['非常不感兴趣', '不感兴趣', '一般', '感兴趣', '非常感兴趣']
        
    else:
        raise ValueError(f"未知的编码策略: {strategy}")
    
    return labels, class_names

# 使用示例
ratings = [1, 2, 3, 4, 5, 3, 4, 2, 5, 1]
labels, names = encode_interest_labels(ratings, strategy='ternary')
print(f"原始评分: {ratings}")
print(f"编码标签: {labels}")
print(f"类别名称: {names}")
```

### 标签分布分析
```python
def analyze_label_distribution(labels, class_names):
    """分析标签分布"""
    unique, counts = np.unique(labels, return_counts=True)
    
    print("标签分布分析:")
    print("-" * 30)
    for i, (label, count) in enumerate(zip(unique, counts)):
        percentage = count / len(labels) * 100
        print(f"{class_names[label]}: {count} ({percentage:.1f}%)")
    
    # 检查类别平衡
    min_count, max_count = counts.min(), counts.max()
    imbalance_ratio = max_count / min_count
    
    if imbalance_ratio > 2.0:
        print(f"\n⚠️ 类别不平衡 (比例: {imbalance_ratio:.1f}:1)")
        print("建议使用类别权重或重采样技术")
    else:
        print(f"\n✓ 类别相对平衡 (比例: {imbalance_ratio:.1f}:1)")
```

## 评估指标设计

### 多类分类指标
```python
from sklearn.metrics import accuracy_score, classification_report, confusion_matrix

def evaluate_classification(y_true, y_pred, class_names):
    """
    全面评估分类性能
    
    Args:
        y_true: 真实标签
        y_pred: 预测标签
        class_names: 类别名称
    
    Returns:
        evaluation_results: 评估结果字典
    """
    # 基本指标
    accuracy = accuracy_score(y_true, y_pred)
    
    # 详细报告
    report = classification_report(
        y_true, y_pred, 
        target_names=class_names,
        output_dict=True
    )
    
    # 混淆矩阵
    cm = confusion_matrix(y_true, y_pred)
    
    results = {
        'accuracy': accuracy,
        'classification_report': report,
        'confusion_matrix': cm,
        'per_class_accuracy': cm.diagonal() / cm.sum(axis=1)
    }
    
    return results
```

### 特定指标
```python
def compute_interest_metrics(y_true, y_pred, strategy='ternary'):
    """
    计算兴趣分类特定指标
    
    重点关注:
    - 高兴趣识别准确率 (对应用最重要)
    - 低兴趣识别准确率 (避免误判)
    - 整体平衡准确率
    """
    if strategy == 'binary':
        # 二分类: 关注高兴趣识别
        high_interest_precision = precision_score(y_true, y_pred, pos_label=1)
        high_interest_recall = recall_score(y_true, y_pred, pos_label=1)
        
    elif strategy == 'ternary':
        # 三分类: 关注各类别性能
        precision = precision_score(y_true, y_pred, average=None)
        recall = recall_score(y_true, y_pred, average=None)
        
    return {
        'precision_per_class': precision,
        'recall_per_class': recall,
        'balanced_accuracy': balanced_accuracy_score(y_true, y_pred)
    }
```

## 数据分割策略

### 时间序列交叉验证
```python
from sklearn.model_selection import TimeSeriesSplit

def create_time_series_splits(X, y, n_splits=5):
    """
    为时间序列数据创建交叉验证分割
    
    考虑时间顺序，避免数据泄露
    """
    tscv = TimeSeriesSplit(n_splits=n_splits)
    
    splits = []
    for train_idx, test_idx in tscv.split(X):
        splits.append((train_idx, test_idx))
    
    return splits
```

### 受试者分割
```python
def create_subject_splits(X, y, subject_ids, test_size=0.2):
    """
    按受试者分割数据，避免同一受试者的数据同时出现在训练和测试集
    
    Args:
        X: 特征数据
        y: 标签数据  
        subject_ids: 受试者ID列表
        test_size: 测试集比例
    
    Returns:
        train_idx, test_idx: 训练和测试索引
    """
    unique_subjects = np.unique(subject_ids)
    n_test_subjects = int(len(unique_subjects) * test_size)
    
    # 随机选择测试受试者
    test_subjects = np.random.choice(
        unique_subjects, 
        size=n_test_subjects, 
        replace=False
    )
    
    test_idx = np.isin(subject_ids, test_subjects)
    train_idx = ~test_idx
    
    return train_idx, test_idx
```

## 推荐的分类目标

### 主要目标: 三分类任务
- **类别**: 低兴趣、中等兴趣、高兴趣
- **编码**: 0, 1, 2
- **原因**: 
  - 平衡了信息保留和分类难度
  - 符合实际应用需求
  - 便于模型解释

### 辅助目标: 二分类任务
- **类别**: 低兴趣、高兴趣  
- **编码**: 0, 1
- **用途**: 基准模型和快速验证

### 扩展目标: 五分类任务
- **类别**: 原始1-5评分
- **编码**: 0, 1, 2, 3, 4
- **用途**: 完整信息利用和高精度需求

## 实施建议

1. **从简单开始**: 先实现二分类基准模型
2. **重点关注**: 三分类作为主要研究目标
3. **完整评估**: 最终在五分类上验证模型能力
4. **类别平衡**: 根据数据分布选择合适的处理策略
5. **交叉验证**: 使用受试者分割避免过拟合
