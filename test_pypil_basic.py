#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
测试PyPil基础功能
验证IPA和LHIPA类是否可以正常工作
"""

import sys
import os
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt

# 添加PyPil代码路径
pypil_path = os.path.join(os.getcwd(), 'Pupillometric-measures-of-cognitive-load-main', 'Maintained Code')
sys.path.insert(0, pypil_path)

print(f"添加PyPil路径: {pypil_path}")
print(f"当前工作目录: {os.getcwd()}")

try:
    # 导入PyPil类
    import glob
    import math
    import numpy as np
    import pandas as pd
    import pywt
    from matplotlib import pyplot as plt
    
    print("✅ 成功导入所有依赖包")
    
    # 复制PyPil类定义（从PyPil.ipynb中提取）
    class PyPil:
        """
        PyPil基类 - 瞳孔测量基础类
        """
        def __init__(self, pupil_data, wavelet="sym16", periodization="per", level = 2, specification="world_timestamp"):
            try:
                import numpy as np
                import math
                import pywt
            except ImportError as e:
                raise ImportError("A required library is not imported, verify that numpy, math, and pywt are imported.")
            
            self.pupil_data = pupil_data
            self.pupil_data_diam = list(pupil_data['diameter'])
            self.pupil_data_time_WI = list(pupil_data['world_index'])
            self.pupil_data_time_WT = list(pupil_data['world_timestamp'])
            self.wavelet = wavelet
            self.periodization = periodization
            self.level = level
            self.specification = specification
            
        def calculate_timestamps(self):
            """计算时间戳差值"""
            tt = self.pupil_data[self.specification].iloc[-1] - self.pupil_data[self.specification].iloc[0]
            return tt
            
        def compute_modmax(self, d):
            """计算模极大值"""
            m = [0.0] * len(d)
            for i in range(len(d)):
                m[i] = math.fabs(d[i])
                
            t = [0.0] * len(d)
            for i in range(len(d)):    
                ll = m[i-1] if i >= 1 else m[i]
                oo = m[i]
                rr = m[i+1] if i < len(d)-2 else m[i]
                
                if (ll <= oo and oo >= rr) and (ll < oo or oo > rr):
                    t[i] = math.sqrt(d[i]**2)
                else:
                    t[i] = 0.0
            return t

        def compute_threshold(self, detect, mode="hard"):
            """计算阈值"""
            thresh = np.std(detect) * math.sqrt(2.0*np.log2(len(detect)))
            cD2t = pywt.threshold(detect,thresh,mode)
            return cD2t
        
        def calculate_ipa(self, cD2t):
            """计算IPA值"""
            ctr = 0
            for i in range(len(cD2t)):
                if math.fabs(cD2t[i]) > 0: 
                    ctr += 1
            ipa = float(ctr)/ self.calculate_timestamps()
            return ipa

    class IPA(PyPil):
        """IPA子类 - 瞳孔活动指数"""
        def __init__(self, pupil_data, wavelet="sym16", periodization="per", level=2, specification="world_timestamp"):
            super().__init__(pupil_data, wavelet, periodization, level, specification)
            
            # 执行IPA计算
            cA2, cD2, cD1 = self.ipa_wavelet_decomposition()
            cA2[:], cD2[:], cD1[:] = self.ipa_normalize_coefficients(cA2, cD2, cD1, self.level)
            detect = self.compute_modmax(cD2[:])
            cD2t = self.compute_threshold(detect)
            self.ipa = self.calculate_ipa(cD2t)

        def ipa_wavelet_decomposition(self):
            """小波分解"""
            cA2, cD2, cD1 = pywt.wavedec(self.pupil_data_diam, self.wavelet, mode=self.periodization, level=self.level)
            return cA2, cD2, cD1 
            
        def ipa_normalize_coefficients(self, cA2, cD2, cD1, level):
            """系数归一化"""
            cA2[:] = [x / math.sqrt(2.0 * level) for x in cA2] 
            cD2[:] = [x / math.sqrt(2.0 * level) for x in cD2]
            cD1[:] = [x / math.sqrt(1.0 * level) for x in cD1] 
            return cA2[:], cD2[:], cD1[:]

    class LHIPA(PyPil):
        """LHIPA子类 - 低/高频瞳孔活动指数"""
        def __init__(self, pupil_data, wavelet="sym16", periodization="per", level=2, specification="world_timestamp"):
            super().__init__(pupil_data, wavelet, periodization, level, specification)
            
            # 执行LHIPA计算
            self.max_level = self.lhipa_wavelet_decomposition(self.pupil_data_diam)
            self.hif, self.lof = 1, int(self.max_level/2)
            cD_H, cD_L  = self.lhipa_normalize_coefficients(self.pupil_data_diam, self.max_level)
            cD_LH = self.lhipa_ratio(cD_H[:], cD_L[:])
            cD_LHm = self.compute_modmax(cD_LH)
            cD_LHt = self.compute_threshold(cD_LHm, mode="less")
            self.lhipa = self.calculate_ipa(cD_LHt)
            
        def lhipa_wavelet_decomposition(self, d):
            """LHIPA小波分解"""
            w = pywt.Wavelet('sym16')
            self.max_level = pywt.dwt_max_level(len(d),filter_len=w.dec_len)
            return self.max_level
            
        def lhipa_normalize_coefficients(self, d, max_level):
            """LHIPA系数归一化"""
            cD_H = pywt.downcoef('d',self.pupil_data_diam, 'sym16', 'per', level=self.hif)
            cD_L = pywt.downcoef('d',self.pupil_data_diam, 'sym16', 'per', level=self.lof)
            cD_H[:] = [x / math.sqrt(2**self.hif) for x in cD_H]
            cD_L[:] = [x / math.sqrt(2**self.lof) for x in cD_L]
            return cD_H[:], cD_L[:]   
        
        def lhipa_ratio(self, cD_H, cD_L):
            """计算低/高频比率"""
            cD_LH = cD_L.copy()
            for i in range(len(cD_L)):
                den = cD_H[((2**self.lof)//(2**self.hif))*i]
                if den != 0:
                    cD_LH[i] = cD_L[i] / den
                else: 
                    cD_LH[i] = cD_L[i] / 0.00000000001
            return cD_LH

    print("✅ 成功定义PyPil、IPA、LHIPA类")

    # 创建测试数据
    def create_test_data(n_points=1000):
        """创建测试用的瞳孔数据"""
        # 生成模拟的瞳孔直径数据
        time_points = np.linspace(0, 10, n_points)  # 10秒的数据
        
        # 基础瞳孔大小 + 噪声 + 一些周期性变化
        base_diameter = 4000
        noise = np.random.normal(0, 50, n_points)
        periodic = 100 * np.sin(2 * np.pi * 0.5 * time_points)  # 0.5Hz的周期变化
        
        diameter = base_diameter + noise + periodic
        
        # 创建DataFrame
        test_data = pd.DataFrame({
            'diameter': diameter,
            'world_index': range(n_points),
            'world_timestamp': time_points
        })
        
        return test_data

    # 测试IPA和LHIPA计算
    print("\n🔄 创建测试数据...")
    test_data = create_test_data(1000)
    print(f"测试数据形状: {test_data.shape}")
    print(f"瞳孔直径范围: {test_data['diameter'].min():.1f} - {test_data['diameter'].max():.1f}")

    print("\n🔄 测试IPA计算...")
    ipa_calculator = IPA(test_data)
    ipa_value = ipa_calculator.ipa
    print(f"✅ IPA计算成功: {ipa_value:.6f}")

    print("\n🔄 测试LHIPA计算...")
    lhipa_calculator = LHIPA(test_data)
    lhipa_value = lhipa_calculator.lhipa
    print(f"✅ LHIPA计算成功: {lhipa_value:.6f}")

    # 显示结果
    print("\n" + "="*50)
    print("📊 测试结果总结:")
    print("="*50)
    print(f"数据点数: {len(test_data)}")
    print(f"时间范围: {test_data['world_timestamp'].min():.1f} - {test_data['world_timestamp'].max():.1f} 秒")
    print(f"IPA值: {ipa_value:.6f}")
    print(f"LHIPA值: {lhipa_value:.6f}")
    print("="*50)
    
    # 绘制测试数据
    plt.figure(figsize=(12, 8))
    
    plt.subplot(2, 1, 1)
    plt.plot(test_data['world_timestamp'], test_data['diameter'], 'b-', alpha=0.7, linewidth=1)
    plt.title('测试瞳孔数据')
    plt.xlabel('时间 (秒)')
    plt.ylabel('瞳孔直径 (像素)')
    plt.grid(True, alpha=0.3)
    
    plt.subplot(2, 1, 2)
    plt.bar(['IPA', 'LHIPA'], [ipa_value, lhipa_value], color=['skyblue', 'lightcoral'])
    plt.title('IPA和LHIPA计算结果')
    plt.ylabel('指数值')
    plt.grid(True, alpha=0.3)
    
    plt.tight_layout()
    plt.savefig('pypil_test_results.png', dpi=300, bbox_inches='tight')
    print(f"✅ 测试结果图已保存: pypil_test_results.png")
    
    print("\n🎉 PyPil基础功能测试完成！所有功能正常工作。")

except ImportError as e:
    print(f"❌ 导入错误: {e}")
    print("请确保已安装所有必需的依赖包")
except Exception as e:
    print(f"❌ 测试过程中出现错误: {e}")
    import traceback
    traceback.print_exc()
