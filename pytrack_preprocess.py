#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
pytrack_preprocess.py
=====================
基于 **PyTrack-NTU** 的最小完整示例脚本，功能包括：
1. 读取 EyeLink 生成的 EDF 文件（`etDataReader.read_edf`）。
2. 自动眨眼检测，将眨眼区段替换为 NaN 并插值修复。
3. 使用均值 ± N×STD 的方式剔除异常瞳孔值并插值修复。
4. 保存清洗后的瞳孔数据为 CSV。
5. 对比绘制清洗前后曲线，并保存 PNG 图。

用法（Windows PowerShell）：
```
python pytrack_preprocess.py --edf C:\data\demo.edf --out demo_prefix
```
脚本会生成：
* `demo_prefix_trial0_raw_clean.png`  —— 第一条试次的对比图
* `demo_prefix_trial0_clean.csv`      —— 清洗后数据

作者: curiosity_pupil 项目
"""

import argparse
import os
import sys
from typing import Tuple

import numpy as np
import pandas as pd
import matplotlib.pyplot as plt

try:
    from PyTrack.etDataReader import read_edf, blink_detection
except ImportError:
    print("❌ 未找到 PyTrack-NTU，请先运行: pip install PyTrack-NTU")
    sys.exit(1)


def _interpolate_nan(series: pd.Series) -> pd.Series:
    """简单线性插值并前后向填充，确保首尾无缺失。"""
    return series.interpolate(method="linear").fillna(method="bfill").fillna(method="ffill")


def _clean_pupil(pupil: np.ndarray, ts: np.ndarray, Sblk, Eblk,
                 std_thresh: float = 3.0) -> Tuple[np.ndarray, np.ndarray]:
    """按眨眼信息和标准差阈值清理瞳孔数据并插值。

    参数
    ----
    pupil: shape = (n,) 或 (n,2)
    ts:    时间戳，与 pupil 长度一致
    Sblk/Eblk: blink_detection 的返回值
    std_thresh: pupil > mean ± std_thresh*std 视为异常
    """
    # 若为双眼，取双眼平均
    if pupil.ndim == 2:
        pupil_val = np.nanmean(pupil, axis=1)
    else:
        pupil_val = pupil.copy()

    pupil_series = pd.Series(pupil_val)

    # 1) 眨眼区段置 NaN
    blink_mask = np.zeros_like(pupil_val, dtype=bool)
    for st, et, _ in Eblk:
        blink_mask[(ts >= st) & (ts <= et)] = True
    pupil_series[blink_mask] = np.nan

    # 2) 异常值置 NaN
    mean_val = pupil_series.mean(skipna=True)
    std_val = pupil_series.std(skipna=True)
    outlier_mask = (pupil_series < mean_val - std_thresh * std_val) | \
                   (pupil_series > mean_val + std_thresh * std_val)
    pupil_series[outlier_mask] = np.nan

    # 3) 插值
    pupil_clean = _interpolate_nan(pupil_series)

    return pupil_val, pupil_clean.to_numpy()


def preprocess_single_trial(trial: dict, trial_idx: int, out_prefix: str,
                            std_thresh: float):
    """清理并保存单条试次。"""
    ts = trial['time']
    pupil = trial['size']          # shape (n,) or (n,2)
    # 眨眼检测
    Sblk, Eblk = blink_detection(trial['x'], trial['y'], ts, missing=0.0, minlen=10)

    raw, clean = _clean_pupil(pupil, ts, Sblk, Eblk, std_thresh)

    # 保存 CSV
    df = pd.DataFrame({
        'time_ms': ts,
        'pupil_raw': raw,
        'pupil_clean': clean
    })
    csv_path = f"{out_prefix}_trial{trial_idx}_clean.csv"
    df.to_csv(csv_path, index=False)
    print(f"✓ 已保存清洗后数据: {csv_path}")

    # 绘图对比
    fig, ax = plt.subplots(figsize=(12, 6), dpi=150)
    ax.plot(ts, raw, 'r-', alpha=0.4, label='Raw')
    ax.plot(ts, clean, 'k-', linewidth=1.2, label='Clean')
    ax.set_xlabel('Time (ms)')
    ax.set_ylabel('Pupil size (EyeLink units)')
    ax.set_title(f'Trial {trial_idx} pupil (raw vs clean)')
    ax.legend()
    ax.grid(alpha=0.3)

    png_path = f"{out_prefix}_trial{trial_idx}_raw_clean.png"
    fig.tight_layout()
    fig.savefig(png_path)
    plt.close(fig)
    print(f"✓ 已保存对比图: {png_path}")


def main():
    parser = argparse.ArgumentParser(description="PyTrack-NTU EDF 预处理示例")
    parser.add_argument('--edf', required=True, help='EDF 文件路径')
    parser.add_argument('--start', default='TRIAL_START', help='trial 开始标记')
    parser.add_argument('--stop', default='TRIAL_END', help='trial 结束标记')
    parser.add_argument('--eye', default='B', choices=['L', 'R', 'B'], help='读取哪只眼')
    parser.add_argument('--std', type=float, default=3.0, help='异常值剔除倍数 (mean±k*std)')
    parser.add_argument('--out', default='output', help='输出文件名前缀')
    args = parser.parse_args()

    if not os.path.isfile(args.edf):
        print(f"❌ EDF 文件不存在: {args.edf}")
        sys.exit(1)

    print("→ 正在读取 EDF… 这可能需要几秒钟")
    trials = read_edf(args.edf, start=args.start, stop=args.stop, eye=args.eye)
    if not trials:
        print("❌ 未读取到任何试次，检查 start/stop 字符串是否正确")
        sys.exit(1)
    print(f"✓ 读取完成，共 {len(trials)} 条试次")

    # 这里仅示范处理第一条试次，可自行循环全部
    preprocess_single_trial(trials[0], 0, args.out, args.std)


if __name__ == '__main__':
    main() 