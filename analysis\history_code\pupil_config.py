#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
瞳孔曲线绘制工具配置文件
修改此文件中的参数来自定义分析设置
"""

# ==================== 文件路径设置 ====================
# EDF文件路径（相对于项目根目录）
EDF_FILE_PATH = "data/20250711_112343_test_0711_1123/test_0711_1123.edf"

# 输出目录
OUTPUT_DIR = "analysis/figures/pupil_curves"

# ==================== 分析范围设置 ====================
# 要分析的试次
# 选项：
#   [1] - 只分析试次1
#   [1, 2] - 分析试次1和2
#   "all" - 分析所有试次
TRIALS_TO_ANALYZE = [1]

# 要分析的数据段
# 格式: [(开始消息, 结束消息, 段名称), ...]
# 常用消息标记：
#   TRIAL_START, TRIAL_END
#   BASELINE_START, BASELINE_END
#   QUESTION_DISPLAY_START, QUESTION_DISPLAY_END
#   INPUT_START, INPUT_END
#   RATING_START, RATING_END
#   ANSWER_DISPLAY_START, ANSWER_DISPLAY_END
DATA_SEGMENTS = [
    ("QUESTION_DISPLAY_START", "QUESTION_DISPLAY_END", "问题显示"),
    ("ANSWER_DISPLAY_START", "ANSWER_DISPLAY_END", "答案显示"),
    # 可以添加更多段：
    # ("BASELINE_START", "BASELINE_END", "基线测量"),
    # ("RATING_START", "RATING_END", "评分阶段"),
]

# ==================== 数据处理选项 ====================
# 缺失数据处理方式
# 选项：
#   "interpolate" - 插值填补缺失值
#   "keep_missing" - 保留缺失值
MISSING_DATA_HANDLING = "interpolate"

# 插值方法（当MISSING_DATA_HANDLING="interpolate"时使用）
# 选项：
#   "linear" - 线性插值
#   "cubic" - 三次样条插值
#   "nearest" - 最近邻插值
INTERPOLATION_METHOD = "linear"

# 数据过滤参数
MIN_PUPIL_SIZE = 1.0    # 最小瞳孔直径（毫米）
MAX_PUPIL_SIZE = 10.0   # 最大瞳孔直径（毫米）

# ==================== 模拟数据选项 ====================
# 当真实瞳孔数据不可用时的处理
USE_SIMULATED_DATA = True       # 是否生成模拟数据用于演示
SIMULATION_NOISE_LEVEL = 0.1    # 模拟数据噪声水平（0.0-1.0）

# ==================== 绘图参数 ====================
# 图形尺寸和质量
FIGURE_SIZE = (12, 8)   # 图形尺寸（宽, 高）英寸
DPI = 300               # 图形分辨率

# 保存格式
# 选项：
#   "png" - PNG格式（推荐）
#   "pdf" - PDF格式
#   "svg" - SVG格式
SAVE_FORMAT = "png"

# 时间轴设置
# 时间单位
# 选项：
#   "ms" - 毫秒
#   "s" - 秒
TIME_UNIT = "ms"

# 时间参考点
# 选项：
#   True - 相对于段开始时间（推荐）
#   False - 绝对时间戳
RELATIVE_TIME = True

# ==================== 高级选项 ====================
# 基线窗口（毫秒）- 用于未来的基线校正功能
BASELINE_WINDOW = 200

# 图形样式
PLOT_STYLE = {
    'left_eye_color': 'blue',
    'right_eye_color': 'red', 
    'both_eyes_color': 'black',
    'line_width': 1.5,
    'both_eyes_line_width': 2,
    'alpha': 0.7,
    'grid_alpha': 0.3
}

# 字体设置
FONT_CONFIG = {
    'title_size': 14,
    'label_size': 12,
    'legend_size': 10,
    'stats_size': 9
}

# ==================== 预设配置 ====================
# 快速配置选项，取消注释使用

# 配置1：完整实验流程分析
# DATA_SEGMENTS = [
#     ("BASELINE_START", "BASELINE_END", "基线测量"),
#     ("QUESTION_DISPLAY_START", "QUESTION_DISPLAY_END", "问题显示"),
#     ("INPUT_START", "INPUT_END", "答案输入"),
#     ("RATING_START", "RATING_END", "好奇心评分"),
#     ("ANSWER_DISPLAY_START", "ANSWER_DISPLAY_END", "答案显示"),
# ]

# 配置2：只分析关键阶段
# DATA_SEGMENTS = [
#     ("QUESTION_DISPLAY_START", "QUESTION_DISPLAY_END", "问题显示"),
#     ("ANSWER_DISPLAY_START", "ANSWER_DISPLAY_END", "答案显示"),
# ]

# 配置3：长时间段分析
# DATA_SEGMENTS = [
#     ("TRIAL_START", "INPUT_END", "问题到输入"),
#     ("ANSWER_DISPLAY_START", "TRIAL_END", "答案到结束"),
# ]

# 配置4：高质量输出
# FIGURE_SIZE = (16, 10)
# DPI = 600
# SAVE_FORMAT = "pdf"

# 配置5：快速预览
# FIGURE_SIZE = (8, 6)
# DPI = 150
# SAVE_FORMAT = "png"
