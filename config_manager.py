#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
实验配置管理工具
用于管理屏蔽题目列表和其他实验配置
"""

import json
import os
from typing import List, Dict
from experiment_materials import ExperimentMaterials
import experiment_config

class ConfigManager:
    """配置管理器"""
    
    def __init__(self):
        """初始化配置管理器"""
        self.materials = ExperimentMaterials()
        self.config_file = "experiment_config.py"
    
    def show_current_blocked_questions(self):
        """显示当前屏蔽的题目"""
        print("\n当前屏蔽题目列表:")
        print("=" * 50)
        
        blocked_info = self.materials.get_blocked_questions_info()
        
        if not blocked_info['blocked_questions']:
            print("当前没有屏蔽任何题目")
            return
        
        print(f"总题目数: {blocked_info['total_questions']}")
        print(f"屏蔽题目数: {blocked_info['blocked_count']}")
        print(f"可用题目数: {blocked_info['available_count']}")
        print()
        
        for i, blocked in enumerate(blocked_info['blocked_questions'], 1):
            print(f"{i:2d}. ID {blocked['id']:3d}: {blocked['question']}")
    
    def add_blocked_questions(self, question_ids: List[int]):
        """添加屏蔽题目"""
        # 验证题目ID是否存在
        valid_ids = []
        invalid_ids = []
        
        all_question_ids = [qa['id'] for qa in self.materials.question_answer_pairs]
        
        for qid in question_ids:
            if qid in all_question_ids:
                valid_ids.append(qid)
            else:
                invalid_ids.append(qid)
        
        if invalid_ids:
            print(f"警告：以下题目ID不存在: {invalid_ids}")
        
        if valid_ids:
            # 更新配置
            experiment_config.update_blocked_questions(valid_ids)
            print(f"已添加 {len(valid_ids)} 道题目到屏蔽列表: {valid_ids}")
        else:
            print("没有有效的题目ID可以添加")
    
    def remove_blocked_questions(self, question_ids: List[int]):
        """移除屏蔽题目"""
        current_blocked = experiment_config.BLOCKED_QUESTION_IDS.copy()
        
        removed_ids = []
        not_blocked_ids = []
        
        for qid in question_ids:
            if qid in current_blocked:
                current_blocked.remove(qid)
                removed_ids.append(qid)
            else:
                not_blocked_ids.append(qid)
        
        if not_blocked_ids:
            print(f"警告：以下题目ID未被屏蔽: {not_blocked_ids}")
        
        if removed_ids:
            experiment_config.BLOCKED_QUESTION_IDS = current_blocked
            print(f"已从屏蔽列表移除 {len(removed_ids)} 道题目: {removed_ids}")
        else:
            print("没有题目被移除")
    
    def clear_all_blocked_questions(self):
        """清空所有屏蔽题目"""
        count = len(experiment_config.BLOCKED_QUESTION_IDS)
        experiment_config.clear_blocked_questions()
        print(f"已清空所有屏蔽题目 (共 {count} 道)")
    
    def search_questions(self, keyword: str):
        """搜索题目"""
        print(f"\n搜索包含关键词 '{keyword}' 的题目:")
        print("=" * 50)
        
        found_questions = []
        for qa in self.materials.question_answer_pairs:
            if keyword.lower() in qa['question'].lower():
                found_questions.append(qa)
        
        if not found_questions:
            print("未找到匹配的题目")
            return
        
        print(f"找到 {len(found_questions)} 道匹配的题目:")
        for i, qa in enumerate(found_questions, 1):
            blocked_status = " (已屏蔽)" if qa['id'] in experiment_config.BLOCKED_QUESTION_IDS else ""
            print(f"{i:2d}. ID {qa['id']:3d}: {qa['question']}{blocked_status}")
    
    def show_statistics(self):
        """显示统计信息"""
        print("\n实验配置统计:")
        print("=" * 50)
        
        blocked_info = self.materials.get_blocked_questions_info()
        
        print(f"总题目数量: {blocked_info['total_questions']}")
        print(f"屏蔽题目数量: {blocked_info['blocked_count']}")
        print(f"可用题目数量: {blocked_info['available_count']}")
        print(f"屏蔽比例: {blocked_info['blocked_count']/blocked_info['total_questions']*100:.1f}%")
        
        # 显示配置信息
        print(f"\n当前配置:")
        print(f"默认题目数量: {experiment_config.EXPERIMENT_CONFIG['default_num_questions']}")
        print(f"最大题目数量: {experiment_config.EXPERIMENT_CONFIG['max_questions']}")
        print(f"是否随机化顺序: {experiment_config.EXPERIMENT_CONFIG['randomize_order']}")
    
    def export_blocked_questions(self, filename: str = "blocked_questions.json"):
        """导出屏蔽题目列表"""
        blocked_info = self.materials.get_blocked_questions_info()
        
        export_data = {
            'export_time': str(datetime.now()),
            'blocked_ids': blocked_info['blocked_ids'],
            'blocked_count': blocked_info['blocked_count'],
            'blocked_questions': blocked_info['blocked_questions']
        }
        
        try:
            with open(filename, 'w', encoding='utf-8') as f:
                json.dump(export_data, f, ensure_ascii=False, indent=2)
            print(f"已导出屏蔽题目列表到 {filename}")
        except Exception as e:
            print(f"导出失败: {e}")
    
    def import_blocked_questions(self, filename: str = "blocked_questions.json"):
        """导入屏蔽题目列表"""
        try:
            with open(filename, 'r', encoding='utf-8') as f:
                import_data = json.load(f)
            
            if 'blocked_ids' in import_data:
                self.add_blocked_questions(import_data['blocked_ids'])
                print(f"已从 {filename} 导入屏蔽题目列表")
            else:
                print("文件格式不正确")
        except FileNotFoundError:
            print(f"文件 {filename} 不存在")
        except Exception as e:
            print(f"导入失败: {e}")

def main():
    """主菜单"""
    config_manager = ConfigManager()
    
    while True:
        print("\n实验配置管理工具")
        print("=" * 50)
        print("1. 显示当前屏蔽题目")
        print("2. 添加屏蔽题目")
        print("3. 移除屏蔽题目")
        print("4. 清空所有屏蔽题目")
        print("5. 搜索题目")
        print("6. 显示统计信息")
        print("7. 导出屏蔽题目列表")
        print("8. 导入屏蔽题目列表")
        print("0. 退出")
        
        choice = input("\n请选择操作 (0-8): ").strip()
        
        if choice == '0':
            print("退出配置管理工具")
            break
        elif choice == '1':
            config_manager.show_current_blocked_questions()
        elif choice == '2':
            ids_input = input("请输入要屏蔽的题目ID (用逗号分隔): ").strip()
            try:
                ids = [int(x.strip()) for x in ids_input.split(',') if x.strip()]
                config_manager.add_blocked_questions(ids)
            except ValueError:
                print("输入格式错误，请输入数字ID")
        elif choice == '3':
            ids_input = input("请输入要移除的题目ID (用逗号分隔): ").strip()
            try:
                ids = [int(x.strip()) for x in ids_input.split(',') if x.strip()]
                config_manager.remove_blocked_questions(ids)
            except ValueError:
                print("输入格式错误，请输入数字ID")
        elif choice == '4':
            confirm = input("确认清空所有屏蔽题目? (y/N): ").strip().lower()
            if confirm == 'y':
                config_manager.clear_all_blocked_questions()
        elif choice == '5':
            keyword = input("请输入搜索关键词: ").strip()
            if keyword:
                config_manager.search_questions(keyword)
        elif choice == '6':
            config_manager.show_statistics()
        elif choice == '7':
            filename = input("请输入导出文件名 (默认: blocked_questions.json): ").strip()
            if not filename:
                filename = "blocked_questions.json"
            config_manager.export_blocked_questions(filename)
        elif choice == '8':
            filename = input("请输入导入文件名 (默认: blocked_questions.json): ").strip()
            if not filename:
                filename = "blocked_questions.json"
            config_manager.import_blocked_questions(filename)
        else:
            print("无效选择，请重新输入")

if __name__ == "__main__":
    from datetime import datetime
    main()
