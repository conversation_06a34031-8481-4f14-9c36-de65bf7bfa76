#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
测试坐标字段修改
验证从px_left, py_left, px_right, py_right到gx_left, gy_left, gx_right, gy_right的修改是否正确
"""

import pandas as pd
import numpy as np
import sys
import os

def test_pfe_correction():
    """测试PFE校正模块"""
    print("测试PFE校正模块...")
    
    try:
        # 添加pfe目录到路径
        pfe_path = os.path.join(os.path.dirname(__file__), 'pfe')
        if pfe_path not in sys.path:
            sys.path.insert(0, pfe_path)
        
        import pfe_correction
        
        # 创建测试数据（使用新的gx_left, gy_left字段）
        test_data = pd.DataFrame({
            'gx_left': [960, 480, 1440, 960, 960],
            'gy_left': [540, 270, 810, 540, 540],
            'pa_left': [2000, 2000, 2000, 2000, 2000],
            'pa_right': [2000, 2000, 2000, 2000, 2000]
        })
        
        print(f"✓ 成功创建测试数据，包含 {len(test_data)} 个样本")
        print(f"  列名: {list(test_data.columns)}")
        
        # 测试数据验证（检查必要的列是否存在）
        required_cols = ['gx_left', 'gy_left']
        missing_cols = [col for col in required_cols if col not in test_data.columns]
        is_valid = len(missing_cols) == 0
        print(f"✓ 数据验证结果: {'通过' if is_valid else '失败'}")
        
        # 应用PFE校正
        corrected_data = pfe_correction.apply_pfe_correction(
            samples=test_data,
            enable_correction=True
        )
        
        print(f"✓ PFE校正完成")
        print(f"  校正前瞳孔面积范围: {test_data['pa_left'].min():.1f} - {test_data['pa_left'].max():.1f}")
        print(f"  校正后瞳孔面积范围: {corrected_data['pa_left'].min():.1f} - {corrected_data['pa_left'].max():.1f}")
        
        return True
        
    except Exception as e:
        print(f"✗ PFE校正测试失败: {e}")
        return False

def test_preprocess_edf():
    """测试预处理模块"""
    print("\n测试预处理模块...")
    
    try:
        from analysis.preprocess_edf import EyeDataPreprocessor
        
        # 创建预处理器
        preprocessor = EyeDataPreprocessor(
            enable_pfe_correction=True,
            smooth_window=5
        )
        
        print(f"✓ 成功创建预处理器")
        print(f"  PFE校正: {'启用' if preprocessor.enable_pfe_correction else '禁用'}")
        
        return True
        
    except Exception as e:
        print(f"✗ 预处理模块测试失败: {e}")
        return False

def test_coordinate_fields():
    """测试坐标字段"""
    print("\n测试坐标字段...")
    
    # 创建包含新坐标字段的数据
    sample_data = pd.DataFrame({
        'time': [1000, 1001, 1002, 1003, 1004],
        'gx_left': [960, 965, 970, 975, 980],
        'gy_left': [540, 545, 550, 555, 560],
        'gx_right': [960, 965, 970, 975, 980],
        'gy_right': [540, 545, 550, 555, 560],
        'pa_left': [2000, 2010, 2020, 2030, 2040],
        'pa_right': [2000, 2010, 2020, 2030, 2040]
    })
    
    print(f"✓ 创建测试数据成功")
    print(f"  数据形状: {sample_data.shape}")
    print(f"  包含字段: {list(sample_data.columns)}")
    
    # 检查是否包含正确的坐标字段
    required_fields = ['gx_left', 'gy_left', 'gx_right', 'gy_right']
    missing_fields = [field for field in required_fields if field not in sample_data.columns]
    
    if missing_fields:
        print(f"✗ 缺少必要字段: {missing_fields}")
        return False
    else:
        print(f"✓ 所有必要的坐标字段都存在")
    
    # 检查是否还有旧的坐标字段
    old_fields = ['px_left', 'py_left', 'px_right', 'py_right']
    old_fields_present = [field for field in old_fields if field in sample_data.columns]
    
    if old_fields_present:
        print(f"⚠ 发现旧的坐标字段: {old_fields_present}")
    else:
        print(f"✓ 没有发现旧的坐标字段")
    
    return True

def main():
    """主测试函数"""
    print("=" * 60)
    print("坐标字段修改测试")
    print("=" * 60)
    print("测试从px_left, py_left, px_right, py_right")
    print("修改为gx_left, gy_left, gx_right, gy_right")
    print("=" * 60)
    
    test_results = []
    
    # 测试坐标字段
    test_results.append(test_coordinate_fields())
    
    # 测试PFE校正模块
    test_results.append(test_pfe_correction())
    
    # 测试预处理模块
    test_results.append(test_preprocess_edf())
    
    # 总结测试结果
    print("\n" + "=" * 60)
    print("测试结果总结")
    print("=" * 60)
    
    passed_tests = sum(test_results)
    total_tests = len(test_results)
    
    print(f"通过测试: {passed_tests}/{total_tests}")
    
    if passed_tests == total_tests:
        print("✓ 所有测试通过！坐标字段修改成功。")
        print("\n修改总结:")
        print("- px_left → gx_left (左眼X坐标)")
        print("- py_left → gy_left (左眼Y坐标)")
        print("- px_right → gx_right (右眼X坐标)")
        print("- py_right → gy_right (右眼Y坐标)")
        print("\n说明:")
        print("- P字段代表原始未校正的数据")
        print("- G字段代表经过EyeLink校正后的数据")
        print("- 使用G字段可以获得更准确的注视位置信息")
    else:
        print("✗ 部分测试失败，请检查修改。")
    
    print("=" * 60)

if __name__ == "__main__":
    main()
