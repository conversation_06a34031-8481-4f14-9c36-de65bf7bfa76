#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
检查摄像头可用性
用于验证系统中的摄像头是否可以被OpenCV正确识别和使用
"""

import cv2
import time

def check_camera_availability():
    """检查摄像头可用性"""
    print("=== 检查摄像头可用性 ===")
    
    # 检查OpenCV是否可用
    try:
        print(f"OpenCV版本: {cv2.__version__}")
    except Exception as e:
        print(f"❌ OpenCV不可用: {e}")
        return False
    
    # 尝试检测多个摄像头索引
    available_cameras = []
    for i in range(5):  # 检查索引0-4
        print(f"检查摄像头索引 {i}...")
        # cap = cv2.VideoCapture(i)
        # cap = cv2.VideoCapture('video="EMEET"', cv2.CAP_DSHOW)
        # cap = cv2.VideoCapture(r'video=EMEET SmartCam C960 AF', cv2.CAP_FFMPEG)  

        from cv2_enumerate_cameras import enumerate_cameras
        candidates = enumerate_cameras(cv2.CAP_DSHOW)   # 也可换成 CAP_MSMF
        for cam in candidates:
            print(cam.index, cam.name)                  # 0  EMEET SmartCam C960 AF
                                                # 1  Intel(R) AVStream Camera
        emeet = next(c for c in candidates if 'EMEET' in c.name)
        cap = cv2.VideoCapture(emeet.index, emeet.backend)
        
        if cap.isOpened():
            # 尝试读取一帧
            ret, frame = cap.read()
            if ret and frame is not None:
                height, width = frame.shape[:2]
                print(f"✅ 摄像头 {i} 可用: {width}x{height}")
                available_cameras.append(i)
            else:
                print(f"❌ 摄像头 {i} 无法读取帧")
        else:
            print(f"❌ 摄像头 {i} 无法打开")
        
        cap.release()
    
    if not available_cameras:
        print("\n❌ 未找到可用的摄像头")
        return False
    
    print(f"\n✅ 找到 {len(available_cameras)} 个可用摄像头: {available_cameras}")
    return available_cameras

def test_camera_recording(camera_index=2):
    """测试摄像头录像功能"""
    print(f"\n=== 测试摄像头 {camera_index} 录像功能 ===")
    
    # 初始化摄像头
    # cap = cv2.VideoCapture(camera_index)
    cap = cv2.VideoCapture(r"video=EMEET SmartCam C960 AF", cv2.CAP_DSHOW)
    # from cv2_enumerate_cameras import enumerate_cameras
    # candidates = enumerate_cameras(cv2.CAP_DSHOW)   # 也可换成 CAP_MSMF
    # for cam in candidates:
    #     print(cam.index, cam.name)                 
                                            
    # emeet = next(c for c in candidates if 'EMEET' in c.name)
    # cap = cv2.VideoCapture(emeet.index, emeet.backend)
    # print("emeet.index: ", emeet.index, "backend: ", emeet.backend)
    
    if not cap.isOpened():
        print(f"❌ 无法打开摄像头 {camera_index}")
        return False
    
    # 设置摄像头参数
    cap.set(cv2.CAP_PROP_FRAME_WIDTH, 1920)
    cap.set(cv2.CAP_PROP_FRAME_HEIGHT, 1080)
    cap.set(cv2.CAP_PROP_FPS, 30)
    
    # 获取实际参数
    width = int(cap.get(cv2.CAP_PROP_FRAME_WIDTH))
    height = int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
    fps = cap.get(cv2.CAP_PROP_FPS)
    
    print(f"摄像头参数: {width}x{height} @ {fps}fps")
    
    # 设置视频编码器，使用H.264编码
    fourcc = cv2.VideoWriter_fourcc(*'avc1')
    out = cv2.VideoWriter('test_camera_output.mp4', fourcc, fps, (width, height))
    
    if not out.isOpened():
        print("❌ 无法创建视频文件")
        cap.release()
        return False
    
    print("开始录像5秒...")
    start_time = time.time()
    frame_count = 0
    
    while time.time() - start_time < 5:  # 录像5秒
        ret, frame = cap.read()
        if ret:
            # 确保帧尺寸正确
            if frame.shape[:2] != (height, width):
                frame = cv2.resize(frame, (width, height))
            
            out.write(frame)
            frame_count += 1
            
            # 显示进度
            elapsed = time.time() - start_time
            if int(elapsed) != int(elapsed - 0.1):  # 每秒显示一次
                print(f"  录像中... {elapsed:.1f}s ({frame_count} 帧)")
        else:
            print("❌ 无法读取帧")
            break
    
    # 释放资源
    cap.release()
    out.release()
    
    # 检查输出文件
    import os
    if os.path.exists('test_camera_output.mp4'):
        file_size = os.path.getsize('test_camera_output.mp4')
        print(f"✅ 录像完成: test_camera_output.mp4 ({file_size} bytes, {frame_count} 帧)")
        return True
    else:
        print("❌ 视频文件未生成")
        return False

def show_camera_info(camera_index=2):
    """显示摄像头详细信息"""
    print(f"\n=== 摄像头 {camera_index} 详细信息 ===")
    
    cap = cv2.VideoCapture(camera_index)
    
    if not cap.isOpened():
        print(f"❌ 无法打开摄像头 {camera_index}")
        return
    
    # 获取摄像头属性
    properties = {
        'CAP_PROP_FRAME_WIDTH': cv2.CAP_PROP_FRAME_WIDTH,
        'CAP_PROP_FRAME_HEIGHT': cv2.CAP_PROP_FRAME_HEIGHT,
        'CAP_PROP_FPS': cv2.CAP_PROP_FPS,
        'CAP_PROP_FOURCC': cv2.CAP_PROP_FOURCC,
        'CAP_PROP_BRIGHTNESS': cv2.CAP_PROP_BRIGHTNESS,
        'CAP_PROP_CONTRAST': cv2.CAP_PROP_CONTRAST,
        'CAP_PROP_SATURATION': cv2.CAP_PROP_SATURATION,
        'CAP_PROP_HUE': cv2.CAP_PROP_HUE,
        'CAP_PROP_GAIN': cv2.CAP_PROP_GAIN,
        'CAP_PROP_EXPOSURE': cv2.CAP_PROP_EXPOSURE,
    }
    
    for prop_name, prop_id in properties.items():
        try:
            value = cap.get(prop_id)
            print(f"{prop_name}: {value}")
        except:
            print(f"{prop_name}: 不支持")
    
    cap.release()

if __name__ == "__main__":
    print("摄像头检查工具")
    print("=" * 50)
    
    # 检查摄像头可用性
    available_cameras = check_camera_availability()
    if available_cameras:
        # 使用摄像头2进行测试
        camera_index = 2
        
        # 显示摄像头信息
        show_camera_info(2)
        
        # 测试录像功能
        success = test_camera_recording(1)
        
        print("\n" + "=" * 50)
        if success:
            print("🎉 摄像头测试成功！")
            print("摄像头可以正常用于录像")
        else:
            print("❌ 摄像头测试失败")
            print("请检查摄像头驱动或尝试其他摄像头")
    else:
        print("\n❌ 未找到可用摄像头")
        print("请确保：")
        print("1. 摄像头已正确连接")
        print("2. 摄像头驱动已安装")
        print("3. 摄像头未被其他程序占用")
        print("4. 摄像头支持UVC标准")
    
    print("\n使用说明：")
    print("- 如果测试成功，可以在EyeLink Manager中使用摄像头录像功能")
    print("- 视频文件将保存为MP4格式，使用H.264编码")
    print("- 录像分辨率为1080p (1920x1080)，帧率为30fps")
