# 基于AEON库HIVECOTE2算法的眼动瞳孔数据分类研究详细计划

## 阶段1：环境准备与数据理解 (预计2-3天)

### 1.1 环境搭建
- [ ] 安装aeon库及其依赖
  - 创建专用虚拟环境
  - 安装aeon[all_extras]包含所有依赖
  - 验证安装成功
- [ ] 测试aeon基本功能
  - 运行官方示例代码
  - 测试HIVECOTE2算法
  - 验证数据格式兼容性

### 1.2 数据格式分析
- [ ] 分析现有眼动数据格式
  - 检查DataFrame结构和列名
  - 确认时间序列长度和采样率(1000Hz)
  - 分析瞳孔数据、坐标数据、事件数据
- [ ] 理解aeon数据要求
  - 3D numpy数组格式：(n_cases, n_channels, n_timepoints)
  - 标签格式要求
  - 多变量时间序列处理方式

### 1.3 目标变量定义
- [ ] 确定分类目标
  - 定义"有趣程度"的分类标准
  - 确定分类类别数量（如：低、中、高兴趣）
  - 建立标签映射关系

## 阶段2：数据预处理模块开发 (预计3-4天)

### 2.1 数据转换模块
- [ ] 开发DataFrame到aeon格式转换器
  - 提取瞳孔直径时间序列
  - 提取眼动坐标(x,y)时间序列
  - 处理事件标记和注释
  - 实现3D数组格式转换

### 2.2 特征工程模块
- [ ] 瞳孔数据特征提取
  - 瞳孔直径变化率
  - 瞳孔直径标准化
  - 眨眼检测和插值
- [ ] 眼动数据特征提取
  - 注视点坐标序列
  - 眼跳检测和特征
  - 注视时长统计
- [ ] 多通道数据整合
  - 左右眼数据融合
  - 多特征通道组合
  - 时间对齐处理

### 2.3 数据质量控制
- [ ] 缺失值处理策略
- [ ] 异常值检测和处理
- [ ] 数据标准化和归一化
- [ ] 时间窗口分割策略

## 阶段3：算法实现与测试 (预计4-5天)

### 3.1 HIVECOTE2算法配置
- [ ] 研究HIVECOTE2参数设置
  - time_limit_in_minutes参数调优
  - 各子算法参数配置
  - 多变量数据适配
- [ ] 实现算法包装器
  - 数据预处理集成
  - 模型训练接口
  - 预测结果后处理

### 3.2 多算法对比框架
- [ ] 实现多种分类算法
  - RocketClassifier（快速基准）
  - DrCIFClassifier（区间特征）
  - LITETimeClassifier（深度学习）
  - KNeighborsTimeSeriesClassifier（距离基础）
- [ ] 统一评估接口
  - 交叉验证框架
  - 性能指标计算
  - 结果可视化

### 3.3 参数优化模块
- [ ] 网格搜索实现
- [ ] 贝叶斯优化集成
- [ ] 自动化参数调优
- [ ] 最优参数保存和加载

## 阶段4：模块化架构设计 (预计2-3天)

### 4.1 核心模块设计
- [ ] 数据加载器模块
  - 支持多种数据源
  - 批量数据处理
  - 数据缓存机制
- [ ] 特征提取器模块
  - 可配置特征组合
  - 特征选择算法
  - 特征重要性分析
- [ ] 分类器管理模块
  - 算法注册机制
  - 模型保存和加载
  - 版本控制支持

### 4.2 配置管理系统
- [ ] 参数配置文件
- [ ] 实验配置管理
- [ ] 结果追踪系统
- [ ] 日志记录机制

### 4.3 扩展性设计
- [ ] 插件式特征添加
- [ ] 新算法集成接口
- [ ] 自定义评估指标
- [ ] 结果导出格式

## 阶段5：实验验证与优化 (预计3-4天)

### 5.1 基准实验
- [ ] 单一特征分类实验
  - 仅瞳孔数据
  - 仅眼动坐标
  - 仅事件特征
- [ ] 多特征融合实验
- [ ] 算法性能对比

### 5.2 模型评估
- [ ] 分类准确率分析
- [ ] 混淆矩阵可视化
- [ ] ROC曲线和AUC计算
- [ ] 特征重要性分析
- [ ] 计算时间性能测试

### 5.3 结果分析与优化
- [ ] 错误案例分析
- [ ] 参数敏感性分析
- [ ] 模型解释性研究
- [ ] 性能瓶颈识别

## 阶段6：文档与测试 (预计2天)

### 6.1 代码测试
- [ ] 单元测试编写
- [ ] 集成测试验证
- [ ] 性能测试基准
- [ ] 错误处理测试

### 6.2 使用文档
- [ ] API文档生成
- [ ] 使用示例编写
- [ ] 配置说明文档
- [ ] 故障排除指南

## 技术要点总结

### 关键技术挑战
1. 眼动数据的时间序列特征提取
2. 多通道数据的有效融合
3. 不平衡数据的处理策略
4. 算法参数的自动优化

### 预期输出
1. 完整的数据预处理管道
2. 多算法分类比较框架
3. 可扩展的模块化代码架构
4. 详细的实验结果分析报告

### 成功指标
1. 分类准确率 > 80%
2. 代码模块化程度高，易于扩展
3. 算法运行时间合理
4. 结果具有良好的解释性

## 下一步行动
1. 首先安装和测试aeon库
2. 分析现有眼动数据格式
3. 开发数据转换模块
4. 逐步实现各个功能模块
