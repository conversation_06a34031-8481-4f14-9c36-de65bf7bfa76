{"participant_id": "yxy", "experiment_start": "2025-07-11T17:41:16.543919", "experiment_end": "2025-07-11T17:48:08.549552", "total_trials": 5, "timing_settings": {"fixation_baseline": 2.0, "question_display": 7.0, "answer_input": null, "curiosity_rating": 10.0, "pupil_baseline": 3.0, "answer_display": 8.0, "pleasure_rating": null, "surprise_rating": null, "gaze_duration_threshold": 1.0}, "trials": [{"trial_num": 1, "question_id": 181, "question": "写出 E8 Weyl 群的阶.", "answer": "E8的Weyl群的阶可通过对根系反射进行计数得到, 其值为696729600.", "start_time": "2025-07-11T17:41:16.543919", "participant_response": "4", "curiosity_rating": 3, "pleasure_rating": 1, "surprise_rating": 1, "timing_data": {"baseline_duration": 0.00764775276184082, "question_duration": 7.0551769733428955, "input_duration": 10.015690803527832, "curiosity_rating_duration": 15.203781366348267, "pupil_baseline_duration": 3.015170097351074, "answer_duration": 8.094271659851074, "pleasure_rating_duration": 57.75448656082153, "surprise_rating_duration": 52.253613233566284}, "end_time": "2025-07-11T17:43:50.573883"}, {"trial_num": 2, "question_id": 150, "question": "哪种动物被称为\"百兽之王\"?", "answer": "老虎", "start_time": "2025-07-11T17:43:50.578881", "participant_response": "老虎", "curiosity_rating": 1, "pleasure_rating": 1, "surprise_rating": 1, "timing_data": {"baseline_duration": 2.005199670791626, "question_duration": 7.051238775253296, "input_duration": 15.105955600738525, "curiosity_rating_duration": 50.214207887649536, "pupil_baseline_duration": 3.013897180557251, "answer_duration": 8.048075437545776, "pleasure_rating_duration": 11.986929893493652, "surprise_rating_duration": 3.3790621757507324}, "end_time": "2025-07-11T17:45:32.017222"}, {"trial_num": 3, "question_id": 98, "question": "\"空中花园\"位于哪个古城?", "answer": "巴比伦.", "start_time": "2025-07-11T17:45:32.020218", "participant_response": "巴比伦", "curiosity_rating": 1, "pleasure_rating": 1, "surprise_rating": 1, "timing_data": {"baseline_duration": 2.0056474208831787, "question_duration": 7.048556089401245, "input_duration": 8.27020812034607, "curiosity_rating_duration": 3.837211847305298, "pupil_baseline_duration": 3.0068247318267822, "answer_duration": 8.046114206314087, "pleasure_rating_duration": 4.469738960266113, "surprise_rating_duration": 4.381914854049683}, "end_time": "2025-07-11T17:46:13.719457"}, {"trial_num": 4, "question_id": 137, "question": "1 + 1 等于几?", "answer": "2", "start_time": "2025-07-11T17:46:13.722862", "participant_response": "2", "curiosity_rating": 1, "pleasure_rating": 1, "surprise_rating": 1, "timing_data": {"baseline_duration": 2.0058982372283936, "question_duration": 7.048599004745483, "input_duration": 2.067775011062622, "curiosity_rating_duration": 2.5887458324432373, "pupil_baseline_duration": 3.006329298019409, "answer_duration": 8.051019191741943, "pleasure_rating_duration": 3.235717296600342, "surprise_rating_duration": 2.675640821456909}, "end_time": "2025-07-11T17:46:45.040311"}, {"trial_num": 5, "question_id": 186, "question": "写出 E8 晶格最小向量的平方长度.", "answer": "由于E8格是偶自伴的, 其最短非零向量的模长的平方必须为2.", "start_time": "2025-07-11T17:46:45.044278", "participant_response": "67", "curiosity_rating": 4, "pleasure_rating": 1, "surprise_rating": 1, "timing_data": {"baseline_duration": 2.0031886100769043, "question_duration": 7.049387216567993, "input_duration": 9.350528240203857, "curiosity_rating_duration": 2.1016900539398193, "pupil_baseline_duration": 3.0123918056488037, "answer_duration": 8.081860065460205, "pleasure_rating_duration": 49.96832633018494, "surprise_rating_duration": 1.301945686340332}, "end_time": "2025-07-11T17:48:08.549552"}]}