2025-07-21 12:14:25,471 - INFO - 搜索所有video子目录: C:/Users/<USER>/Desktop/curiosity_pupil/data
2025-07-21 12:14:25,473 - INFO - 找到 5 个video目录
2025-07-21 12:14:25,473 - INFO - 处理video目录: C:\Users\<USER>\Desktop\curiosity_pupil\data\20250718_140919_dulu\video
2025-07-21 12:14:25,473 - INFO -   找到 19 个AVI文件
2025-07-21 12:14:25,480 - INFO -   处理进度: 1/19 - dulu_facial_expression_2025-07-18_14-10-19.avi
2025-07-21 12:14:25,481 - INFO - 开始转换: dulu_facial_expression_2025-07-18_14-10-19.avi
2025-07-21 12:17:08,238 - INFO - 转换成功: dulu_facial_expression_2025-07-18_14-10-19.avi
2025-07-21 12:17:08,238 - INFO -   原始大小: 450.8 MB
2025-07-21 12:17:08,238 - INFO -   转换后大小: 80.2 MB
2025-07-21 12:17:08,238 - INFO -   压缩率: 82.2%
2025-07-21 12:17:08,238 - INFO -   处理进度: 2/19 - dulu_facial_expression_2025-07-18_14-11-09.avi
2025-07-21 12:17:08,238 - INFO - 开始转换: dulu_facial_expression_2025-07-18_14-11-09.avi
2025-07-21 12:19:00,885 - INFO - 转换成功: dulu_facial_expression_2025-07-18_14-11-09.avi
2025-07-21 12:19:00,885 - INFO -   原始大小: 333.5 MB
2025-07-21 12:19:00,885 - INFO -   转换后大小: 59.8 MB
2025-07-21 12:19:00,885 - INFO -   压缩率: 82.1%
2025-07-21 12:19:00,885 - INFO -   处理进度: 3/19 - dulu_facial_expression_2025-07-18_14-11-46.avi
2025-07-21 12:19:00,885 - INFO - 开始转换: dulu_facial_expression_2025-07-18_14-11-46.avi
2025-07-21 12:20:42,507 - INFO - 转换成功: dulu_facial_expression_2025-07-18_14-11-46.avi
2025-07-21 12:20:42,507 - INFO -   原始大小: 299.4 MB
2025-07-21 12:20:42,507 - INFO -   转换后大小: 55.5 MB
2025-07-21 12:20:42,507 - INFO -   压缩率: 81.4%
2025-07-21 12:20:42,507 - INFO -   处理进度: 4/19 - dulu_facial_expression_2025-07-18_14-12-18.avi
2025-07-21 12:20:42,507 - INFO - 开始转换: dulu_facial_expression_2025-07-18_14-12-18.avi
2025-07-21 12:22:16,738 - INFO - 转换成功: dulu_facial_expression_2025-07-18_14-12-18.avi
2025-07-21 12:22:16,738 - INFO -   原始大小: 271.7 MB
2025-07-21 12:22:16,738 - INFO -   转换后大小: 44.3 MB
2025-07-21 12:22:16,738 - INFO -   压缩率: 83.7%
2025-07-21 12:22:16,738 - INFO -   处理进度: 5/19 - dulu_facial_expression_2025-07-18_14-12-51.avi
2025-07-21 12:22:16,738 - INFO - 开始转换: dulu_facial_expression_2025-07-18_14-12-51.avi
2025-07-21 12:23:39,508 - INFO - 转换成功: dulu_facial_expression_2025-07-18_14-12-51.avi
2025-07-21 12:23:39,508 - INFO -   原始大小: 242.9 MB
2025-07-21 12:23:39,508 - INFO -   转换后大小: 40.5 MB
2025-07-21 12:23:39,508 - INFO -   压缩率: 83.3%
2025-07-21 12:23:39,508 - INFO -   处理进度: 6/19 - dulu_facial_expression_2025-07-18_14-13-20.avi
2025-07-21 12:23:39,508 - INFO - 开始转换: dulu_facial_expression_2025-07-18_14-13-20.avi
2025-07-21 12:25:08,455 - INFO - 转换成功: dulu_facial_expression_2025-07-18_14-13-20.avi
2025-07-21 12:25:08,455 - INFO -   原始大小: 259.8 MB
2025-07-21 12:25:08,455 - INFO -   转换后大小: 44.3 MB
2025-07-21 12:25:08,455 - INFO -   压缩率: 83.0%
2025-07-21 12:25:08,455 - INFO -   处理进度: 7/19 - dulu_facial_expression_2025-07-18_14-13-53.avi
2025-07-21 12:25:08,455 - INFO - 开始转换: dulu_facial_expression_2025-07-18_14-13-53.avi
2025-07-21 12:26:45,056 - INFO - 转换成功: dulu_facial_expression_2025-07-18_14-13-53.avi
2025-07-21 12:26:45,056 - INFO -   原始大小: 281.6 MB
2025-07-21 12:26:45,056 - INFO -   转换后大小: 49.3 MB
2025-07-21 12:26:45,056 - INFO -   压缩率: 82.5%
2025-07-21 12:26:45,071 - INFO -   处理进度: 8/19 - dulu_facial_expression_2025-07-18_14-14-27.avi
2025-07-21 12:26:45,071 - INFO - 开始转换: dulu_facial_expression_2025-07-18_14-14-27.avi
2025-07-21 12:28:44,172 - INFO - 转换成功: dulu_facial_expression_2025-07-18_14-14-27.avi
2025-07-21 12:28:44,172 - INFO -   原始大小: 347.9 MB
2025-07-21 12:28:44,172 - INFO -   转换后大小: 61.9 MB
2025-07-21 12:28:44,172 - INFO -   压缩率: 82.2%
2025-07-21 12:28:44,172 - INFO -   处理进度: 9/19 - dulu_facial_expression_2025-07-18_14-15-08.avi
2025-07-21 12:28:44,172 - INFO - 开始转换: dulu_facial_expression_2025-07-18_14-15-08.avi
2025-07-21 12:30:29,005 - INFO - 转换成功: dulu_facial_expression_2025-07-18_14-15-08.avi
2025-07-21 12:30:29,005 - INFO -   原始大小: 290.7 MB
2025-07-21 12:30:29,005 - INFO -   转换后大小: 52.4 MB
2025-07-21 12:30:29,005 - INFO -   压缩率: 82.0%
2025-07-21 12:30:29,005 - INFO -   处理进度: 10/19 - dulu_facial_expression_2025-07-18_14-15-41.avi
2025-07-21 12:30:29,005 - INFO - 开始转换: dulu_facial_expression_2025-07-18_14-15-41.avi
2025-07-21 12:31:53,017 - INFO - 转换成功: dulu_facial_expression_2025-07-18_14-15-41.avi
2025-07-21 12:31:53,017 - INFO -   原始大小: 237.9 MB
2025-07-21 12:31:53,017 - INFO -   转换后大小: 35.4 MB
2025-07-21 12:31:53,017 - INFO -   压缩率: 85.1%
2025-07-21 12:31:53,017 - INFO -   处理进度: 11/19 - dulu_facial_expression_2025-07-18_14-16-13.avi
2025-07-21 12:31:53,017 - INFO - 开始转换: dulu_facial_expression_2025-07-18_14-16-13.avi
2025-07-21 12:34:02,294 - INFO - 转换成功: dulu_facial_expression_2025-07-18_14-16-13.avi
2025-07-21 12:34:02,294 - INFO -   原始大小: 371.9 MB
2025-07-21 12:34:02,294 - INFO -   转换后大小: 57.3 MB
2025-07-21 12:34:02,294 - INFO -   压缩率: 84.6%
2025-07-21 12:34:02,294 - INFO -   处理进度: 12/19 - dulu_facial_expression_2025-07-18_14-17-03.avi
2025-07-21 12:34:02,294 - INFO - 开始转换: dulu_facial_expression_2025-07-18_14-17-03.avi
2025-07-21 12:35:32,111 - INFO - 转换成功: dulu_facial_expression_2025-07-18_14-17-03.avi
2025-07-21 12:35:32,111 - INFO -   原始大小: 251.8 MB
2025-07-21 12:35:32,111 - INFO -   转换后大小: 39.4 MB
2025-07-21 12:35:32,111 - INFO -   压缩率: 84.4%
2025-07-21 12:35:32,111 - INFO -   处理进度: 13/19 - dulu_facial_expression_2025-07-18_14-17-36.avi
2025-07-21 12:35:32,111 - INFO - 开始转换: dulu_facial_expression_2025-07-18_14-17-36.avi
2025-07-21 12:37:08,191 - INFO - 转换成功: dulu_facial_expression_2025-07-18_14-17-36.avi
2025-07-21 12:37:08,191 - INFO -   原始大小: 250.6 MB
2025-07-21 12:37:08,191 - INFO -   转换后大小: 39.8 MB
2025-07-21 12:37:08,191 - INFO -   压缩率: 84.1%
2025-07-21 12:37:08,191 - INFO -   处理进度: 14/19 - dulu_facial_expression_2025-07-18_14-18-08.avi
2025-07-21 12:37:08,191 - INFO - 开始转换: dulu_facial_expression_2025-07-18_14-18-08.avi
2025-07-21 12:38:44,495 - INFO - 转换成功: dulu_facial_expression_2025-07-18_14-18-08.avi
2025-07-21 12:38:44,495 - INFO -   原始大小: 235.0 MB
2025-07-21 12:38:44,495 - INFO -   转换后大小: 37.5 MB
2025-07-21 12:38:44,495 - INFO -   压缩率: 84.1%
2025-07-21 12:38:44,495 - INFO -   处理进度: 15/19 - dulu_facial_expression_2025-07-18_14-18-39.avi
2025-07-21 12:38:44,495 - INFO - 开始转换: dulu_facial_expression_2025-07-18_14-18-39.avi
2025-07-21 12:40:12,304 - INFO - 转换成功: dulu_facial_expression_2025-07-18_14-18-39.avi
2025-07-21 12:40:12,304 - INFO -   原始大小: 220.1 MB
2025-07-21 12:40:12,304 - INFO -   转换后大小: 35.1 MB
2025-07-21 12:40:12,304 - INFO -   压缩率: 84.1%
2025-07-21 12:40:12,304 - INFO -   处理进度: 16/19 - dulu_facial_expression_2025-07-18_14-19-07.avi
2025-07-21 12:40:12,304 - INFO - 开始转换: dulu_facial_expression_2025-07-18_14-19-07.avi
2025-07-21 12:41:54,437 - INFO - 转换成功: dulu_facial_expression_2025-07-18_14-19-07.avi
2025-07-21 12:41:54,437 - INFO -   原始大小: 237.9 MB
2025-07-21 12:41:54,437 - INFO -   转换后大小: 39.8 MB
2025-07-21 12:41:54,437 - INFO -   压缩率: 83.3%
2025-07-21 12:41:54,446 - INFO -   处理进度: 17/19 - dulu_facial_expression_2025-07-18_14-19-38.avi
2025-07-21 12:41:54,447 - INFO - 开始转换: dulu_facial_expression_2025-07-18_14-19-38.avi
2025-07-21 12:43:29,870 - INFO - 转换成功: dulu_facial_expression_2025-07-18_14-19-38.avi
2025-07-21 12:43:29,870 - INFO -   原始大小: 231.8 MB
2025-07-21 12:43:29,870 - INFO -   转换后大小: 38.6 MB
2025-07-21 12:43:29,870 - INFO -   压缩率: 83.4%
2025-07-21 12:43:29,870 - INFO -   处理进度: 18/19 - dulu_facial_expression_2025-07-18_14-20-07.avi
2025-07-21 12:43:29,870 - INFO - 开始转换: dulu_facial_expression_2025-07-18_14-20-07.avi
2025-07-21 12:45:04,805 - INFO - 转换成功: dulu_facial_expression_2025-07-18_14-20-07.avi
2025-07-21 12:45:04,805 - INFO -   原始大小: 247.6 MB
2025-07-21 12:45:04,805 - INFO -   转换后大小: 40.8 MB
2025-07-21 12:45:04,805 - INFO -   压缩率: 83.5%
2025-07-21 12:45:04,805 - INFO -   处理进度: 19/19 - dulu_facial_expression_2025-07-18_14-20-38.avi
2025-07-21 12:45:04,805 - INFO - 开始转换: dulu_facial_expression_2025-07-18_14-20-38.avi
2025-07-21 12:46:28,578 - INFO - 转换成功: dulu_facial_expression_2025-07-18_14-20-38.avi
2025-07-21 12:46:28,578 - INFO -   原始大小: 231.9 MB
2025-07-21 12:46:28,578 - INFO -   转换后大小: 38.5 MB
2025-07-21 12:46:28,578 - INFO -   压缩率: 83.4%
2025-07-21 12:46:28,578 - INFO -   该目录转换完成: 19/19 个文件
2025-07-21 12:46:28,586 - INFO - 处理video目录: C:\Users\<USER>\Desktop\curiosity_pupil\data\20250718_142425_dulu\video
2025-07-21 12:46:28,586 - INFO -   找到 30 个AVI文件
2025-07-21 12:46:28,586 - INFO -   处理进度: 1/30 - dulu_facial_expression_2025-07-18_14-25-24.avi
2025-07-21 12:46:28,586 - INFO - 开始转换: dulu_facial_expression_2025-07-18_14-25-24.avi
2025-07-21 12:47:51,282 - INFO - 转换成功: dulu_facial_expression_2025-07-18_14-25-24.avi
2025-07-21 12:47:51,282 - INFO -   原始大小: 218.7 MB
2025-07-21 12:47:51,282 - INFO -   转换后大小: 25.4 MB
2025-07-21 12:47:51,282 - INFO -   压缩率: 88.4%
2025-07-21 12:47:51,282 - INFO -   处理进度: 2/30 - dulu_facial_expression_2025-07-18_14-26-02.avi
2025-07-21 12:47:51,282 - INFO - 开始转换: dulu_facial_expression_2025-07-18_14-26-02.avi
2025-07-21 12:49:50,992 - INFO - 转换成功: dulu_facial_expression_2025-07-18_14-26-02.avi
2025-07-21 12:49:50,992 - INFO -   原始大小: 318.6 MB
2025-07-21 12:49:50,992 - INFO -   转换后大小: 38.3 MB
2025-07-21 12:49:50,992 - INFO -   压缩率: 88.0%
2025-07-21 12:49:50,992 - INFO -   处理进度: 3/30 - dulu_facial_expression_2025-07-18_14-26-56.avi
2025-07-21 12:49:50,992 - INFO - 开始转换: dulu_facial_expression_2025-07-18_14-26-56.avi
2025-07-21 12:50:58,777 - INFO - 转换成功: dulu_facial_expression_2025-07-18_14-26-56.avi
2025-07-21 12:50:58,777 - INFO -   原始大小: 178.1 MB
2025-07-21 12:50:58,777 - INFO -   转换后大小: 22.6 MB
2025-07-21 12:50:58,777 - INFO -   压缩率: 87.3%
2025-07-21 12:50:58,777 - INFO -   处理进度: 4/30 - dulu_facial_expression_2025-07-18_14-27-25.avi
2025-07-21 12:50:58,777 - INFO - 开始转换: dulu_facial_expression_2025-07-18_14-27-25.avi
2025-07-21 12:52:14,053 - INFO - 转换成功: dulu_facial_expression_2025-07-18_14-27-25.avi
2025-07-21 12:52:14,053 - INFO -   原始大小: 208.1 MB
2025-07-21 12:52:14,053 - INFO -   转换后大小: 26.9 MB
2025-07-21 12:52:14,053 - INFO -   压缩率: 87.1%
2025-07-21 12:52:14,053 - INFO -   处理进度: 5/30 - dulu_facial_expression_2025-07-18_14-27-58.avi
2025-07-21 12:52:14,053 - INFO - 开始转换: dulu_facial_expression_2025-07-18_14-27-58.avi
2025-07-21 12:53:24,610 - INFO - 转换成功: dulu_facial_expression_2025-07-18_14-27-58.avi
2025-07-21 12:53:24,610 - INFO -   原始大小: 195.9 MB
2025-07-21 12:53:24,610 - INFO -   转换后大小: 26.2 MB
2025-07-21 12:53:24,610 - INFO -   压缩率: 86.6%
2025-07-21 12:53:24,610 - INFO -   处理进度: 6/30 - dulu_facial_expression_2025-07-18_14-28-29.avi
2025-07-21 12:53:24,610 - INFO - 开始转换: dulu_facial_expression_2025-07-18_14-28-29.avi
2025-07-21 12:54:43,288 - INFO - 转换成功: dulu_facial_expression_2025-07-18_14-28-29.avi
2025-07-21 12:54:43,288 - INFO -   原始大小: 185.7 MB
2025-07-21 12:54:43,288 - INFO -   转换后大小: 25.2 MB
2025-07-21 12:54:43,288 - INFO -   压缩率: 86.4%
2025-07-21 12:54:43,290 - INFO -   处理进度: 7/30 - dulu_facial_expression_2025-07-18_14-28-57.avi
2025-07-21 12:54:43,291 - INFO - 开始转换: dulu_facial_expression_2025-07-18_14-28-57.avi
2025-07-21 12:55:59,581 - INFO - 转换成功: dulu_facial_expression_2025-07-18_14-28-57.avi
2025-07-21 12:55:59,581 - INFO -   原始大小: 177.5 MB
2025-07-21 12:55:59,581 - INFO -   转换后大小: 24.8 MB
2025-07-21 12:55:59,581 - INFO -   压缩率: 86.0%
2025-07-21 12:55:59,596 - INFO -   处理进度: 8/30 - dulu_facial_expression_2025-07-18_14-29-24.avi
2025-07-21 12:55:59,596 - INFO - 开始转换: dulu_facial_expression_2025-07-18_14-29-24.avi
2025-07-21 12:57:10,789 - ERROR - 转换失败: dulu_facial_expression_2025-07-18_14-29-24.avi
2025-07-21 12:57:10,789 - ERROR - 错误信息: ffmpeg version 7.1.1 Copyright (c) 2000-2025 the FFmpeg developers
  built with clang version 20.1.6
  configuration: --prefix=/d/bld/ffmpeg_1748704128577/_h_env/Library --cc=clang.exe --cxx=clang++.exe --nm=llvm-nm --ar=llvm-ar --disable-doc --enable-openssl --enable-demuxer=dash --enable-hardcoded-tables --enable-libfreetype --enable-libharfbuzz --enable-libfontconfig --enable-libopenh264 --enable-libdav1d --ld=lld-link --target-os=win64 --enable-cross-compile --toolchain=msvc --host-cc=clang.exe --extra-libs=ucrt.lib --extra-libs=vcruntime.lib --extra-libs=oldnames.lib --strip=llvm-strip --disable-stripping --host-extralibs= --disable-libopenvino --enable-gpl --enable-libx264 --enable-libx265 --enable-libmp3lame --enable-libaom --enable-libsvtav1 --enable-libxml2 --enable-pic --enable-shared --disable-static --enable-version3 --enable-zlib --enable-libvorbis --enable-libopus --enable-librsvg --enable-ffplay --pkg-config=/d/bld/ffmpeg_1748704128577/_build_env/Library/bin/pkg-config
  libavutil      59. 39.100 / 59. 39.100
  libavcodec     61. 19.101 / 61. 19.101
  libavformat    61.  7.100 / 61.  7.100
  libavdevice    61.  3.100 / 61.  3.100
  libavfilter    10.  4.100 / 10.  4.100
  libswscale      8.  3.100 /  8.  3.100
  libswresample   5.  3.100 /  5.  3.100
  libpostproc    58.  3.100 / 58.  3.100
Input #0, avi, from 'C:\Users\<USER>\Desktop\curiosity_pupil\data\20250718_142425_dulu\video\dulu_facial_expression_2025-07-18_14-29-24.avi':
  Metadata:
    software        : Lavf61.7.100
  Duration: 00:00:27.23, start: 0.000000, bitrate: 64772 kb/s
  Stream #0:0: Video: mjpeg (Baseline) (MJPG / 0x47504A4D), yuvj420p(pc, bt470bg/unknown/unknown), 1920x1080, 64837 kb/s, 30 fps, 30 tbr, 30 tbn
[out#0/mp4 @ 000002A19BB1ADC0] Codec AVOption b:a (set bitrate (in bits/s)) has not been used for any stream. The most likely reason is either wrong type (e.g. a video option with no video streams) or that it is a private option of some decoder which was not actually used for any stream.
Stream mapping:
  Stream #0:0 -> #0:0 (mjpeg (native) -> hevc (libx265))
Press [q] to stop, [?] for help
x265 [info]: HEVC encoder version 3.5+1-f0c1022b6
x265 [info]: build info [Windows][MSVC 1916][64 bit] 8bit+10bit+12bit
x265 [info]: using cpu capabilities: MMX2 SSE2Fast LZCNT SSSE3 SSE4.2 AVX FMA3 BMI2 AVX2
x265 [info]: Main profile, Level-4 (Main tier)
x265 [info]: Thread pool created using 8 threads
x265 [info]: Slices                              : 1
x265 [info]: frame threads / pool features       : 3 / wpp(17 rows)
x265 [info]: Coding QT: max CU size, min CU size : 64 / 8
x265 [info]: Residual QT: max TU size, max depth : 32 / 1 inter / 1 intra
x265 [info]: ME / range / subpel / merge         : hex / 57 / 2 / 3
x265 [info]: Keyframe min / max / scenecut / bias  : 25 / 250 / 40 / 5.00 
x265 [info]: Lookahead / bframes / badapt        : 20 / 4 / 2
x265 [info]: b-pyramid / weightp / weightb       : 1 / 1 / 0
x265 [info]: References / ref-limit  cu / depth  : 3 / off / on
x265 [info]: AQ: mode / str / qg-size / cu-tree  : 2 / 1.0 / 32 / 1
x265 [info]: Rate Control / qCompress            : CRF-23.0 / 0.60
x265 [info]: tools: rd=3 psy-rd=2.00 early-skip rskip mode=1 signhide tmvp
x265 [info]: tools: b-intra strong-intra-smoothing lslices=6 deblock sao
Output #0, mp4, to 'C:\Users\<USER>\Desktop\curiosity_pupil\data\20250718_142425_dulu\video_mp4\dulu_facial_expression_2025-07-18_14-29-24.mp4':
  Metadata:
    software        : Lavf61.7.100
    encoder         : Lavf61.7.100
  Stream #0:0: Video: hevc (hev1 / 0x31766568), yuvj420p(pc, bt470bg/unknown/unknown, progressive), 1920x1080, q=2-31, 30 fps, 15360 tbn
      Metadata:
        encoder         : Lavc61.19.101 libx265
      Side data:
        cpb: bitrate max/min/avg: 0/0/0 buffer size: 0 vbv_delay: N/A
frame=    0 fps=0.0 q=0.0 size=       0KiB time=N/A bitrate=N/A speed=N/A    
frame=    0 fps=0.0 q=0.0 size=       0KiB time=N/A bitrate=N/A speed=N/A    
frame=    3 fps=2.0 q=28.1 size=       0KiB time=00:00:00.03 bitrate=  10.6kbits/s speed=0.0218x    
frame=    8 fps=3.9 q=25.8 size=     256KiB time=00:00:00.20 bitrate=10487.5kbits/s speed=0.0985x    
frame=   12 fps=4.7 q=29.2 size=     512KiB time=00:00:00.33 bitrate=12584.0kbits/s speed=0.132x    
frame=   16 fps=5.3 q=29.2 size=     512KiB time=00:00:00.46 bitrate=8988.5kbits/s speed=0.154x    
frame=   21 fps=5.9 q=29.2 size=     768KiB time=00:00:00.63 bitrate=9934.4kbits/s speed=0.179x    
frame=   25 fps=6.2 q=29.2 size=    1024KiB time=00:00:00.76 bitrate=10942.1kbits/s speed=0.19x    
frame=   30 fps=6.6 q=28.1 size=    1024KiB time=00:00:00.93 bitrate=8988.2kbits/s speed=0.205x    
frame=   35 fps=6.9 q=27.9 size=    1280KiB time=00:00:01.10 bitrate=9532.8kbits/s speed=0.218x    
frame=   41 fps=7.4 q=29.2 size=    1536KiB time=00:00:01.30 bitrate=9679.4kbits/s speed=0.234x    
frame=   47 fps=7.8 q=29.2 size=    1792KiB time=00:00:01.50 bitrate=9786.9kbits/s speed=0.248x    
frame=   52 fps=7.9 q=29.2 size=    1792KiB time=00:00:01.66 bitrate=8808.2kbits/s speed=0.254x    
frame=   57 fps=8.1 q=29.2 size=    2048KiB time=00:00:01.83 bitrate=9151.4kbits/s speed=0.26x    
frame=   63 fps=8.3 q=25.7 size=    2304KiB time=00:00:02.03 bitrate=9282.7kbits/s speed=0.269x    
frame=   68 fps=8.4 q=25.7 size=    2560KiB time=00:00:02.20 bitrate=9532.7kbits/s speed=0.273x    
frame=   73 fps=8.5 q=25.7 size=    2560KiB time=00:00:02.36 bitrate=8861.4kbits/s speed=0.277x    
frame=   78 fps=8.6 q=25.8 size=    2816KiB time=00:00:02.53 bitrate=9106.2kbits/s speed=0.28x    
frame=   83 fps=8.7 q=25.8 size=    2816KiB time=00:00:02.70 bitrate=8544.1kbits/s speed=0.282x    
frame=   88 fps=8.7 q=25.8 size=    3072KiB time=00:00:02.86 bitrate=8778.9kbits/s speed=0.285x    
frame=   92 fps=8.7 q=29.2 size=    3328KiB time=00:00:03.00 bitrate=9087.8kbits/s speed=0.284x    
frame=   97 fps=8.8 q=29.2 size=    3328KiB time=00:00:03.16 bitrate=8609.5kbits/s speed=0.286x    
frame=  102 fps=8.8 q=29.2 size=    3584KiB time=00:00:03.33 bitrate=8808.1kbits/s speed=0.288x    
frame=  107 fps=8.9 q=29.2 size=    3840KiB time=00:00:03.50 bitrate=8987.9kbits/s speed=0.29x    
frame=  111 fps=8.8 q=29.2 size=    3840KiB time=00:00:03.63 bitrate=8658.1kbits/s speed=0.289x    
frame=  117 fps=8.9 q=29.2 size=    4096KiB time=00:00:03.83 bitrate=8753.4kbits/s speed=0.293x    
frame=  119 fps=8.8 q=27.4 size=    4352KiB time=00:00:03.90 bitrate=9141.5kbits/s speed=0.287x    
frame=  125 fps=8.9 q=29.2 size=    4352KiB time=00:00:04.10 bitrate=8695.6kbits/s speed=0.291x    
frame=  129 fps=8.8 q=29.2 size=    4608KiB time=00:00:04.23 bitrate=8917.1kbits/s speed=0.29x    
frame=  136 fps=9.0 q=27.1 size=    4864KiB time=00:00:04.46 bitrate=8920.8kbits/s speed=0.296x    
frame=  138 fps=8.9 q=28.1 size=    4864KiB time=00:00:04.53 bitrate=8789.6kbits/s speed=0.291x    
frame=  144 fps=8.9 q=29.2 size=    5120KiB time=00:00:04.73 bitrate=8861.3kbits/s speed=0.294x    
frame=  148 fps=8.9 q=28.1 size=    5376KiB time=00:00:04.86 bitrate=9049.4kbits/s speed=0.293x    
frame=  153 fps=8.9 q=28.1 size=    5632KiB time=00:00:05.03 bitrate=9166.4kbits/s speed=0.294x    
frame=  159 fps=9.0 q=28.1 size=    5632KiB time=00:00:05.23 bitrate=8816.1kbits/s speed=0.297x    
frame=  164 fps=9.1 q=28.1 size=    5888KiB time=00:00:05.40 bitrate=8932.4kbits/s speed=0.298x    
frame=  168 fps=9.0 q=25.7 size=    5888KiB time=00:00:05.53 bitrate=8717.1kbits/s speed=0.297x    
frame=  173 fps=9.1 q=24.3 size=    6144KiB time=00:00:05.70 bitrate=8830.2kbits/s speed=0.298x    
frame=  178 fps=9.1 q=27.8 size=    6400KiB time=00:00:05.86 bitrate=8936.8kbits/s speed=0.299x    
frame=  183 fps=9.1 q=29.2 size=    6656KiB time=00:00:06.03 bitrate=9037.5kbits/s speed= 0.3x    
frame=  187 fps=9.1 q=28.1 size=    6656KiB time=00:00:06.16 bitrate=8842.1kbits/s speed=0.299x    
frame=  192 fps=9.1 q=28.1 size=    6912KiB time=00:00:06.33 bitrate=8940.5kbits/s speed= 0.3x    
frame=  196 fps=9.1 q=25.8 size=    6912KiB time=00:00:06.46 bitrate=8756.2kbits/s speed=0.299x    
frame=  201 fps=9.1 q=25.8 size=    7168KiB time=00:00:06.63 bitrate=8852.4kbits/s speed= 0.3x    
frame=  206 fps=9.1 q=25.8 size=    7424KiB time=00:00:06.80 bitrate=8943.8kbits/s speed=0.301x    
frame=  210 fps=9.1 q=29.2 size=    7424KiB time=00:00:06.93 bitrate=8771.8kbits/s speed= 0.3x    
frame=  215 fps=9.1 q=29.2 size=    7680KiB time=00:00:07.10 bitrate=8861.3kbits/s speed= 0.3x    
frame=  220 fps=9.1 q=29.2 size=    7936KiB time=00:00:07.26 bitrate=8946.6kbits/s speed=0.301x    
frame=  225 fps=9.1 q=29.2 size=    8192KiB time=00:00:07.43 bitrate=9028.1kbits/s speed=0.302x    
frame=  228 fps=9.1 q=29.2 size=    8192KiB time=00:00:07.53 bitrate=8908.3kbits/s speed= 0.3x    
frame=  234 fps=9.1 q=29.2 size=    8448KiB time=00:00:07.73 bitrate=8949.1kbits/s speed=0.302x    
frame=  240 fps=9.2 q=29.2 size=    8704KiB time=00:00:07.93 bitrate=8987.8kbits/s speed=0.303x    
frame=  240 fps=9.0 q=29.2 size=    8704KiB time=00:00:07.93 bitrate=8987.8kbits/s speed=0.298x    
frame=  243 fps=9.0 q=29.2 size=    8704KiB time=00:00:08.03 bitrate=8876.0kbits/s speed=0.296x    
frame=  246 fps=8.9 q=27.6 size=    8960KiB time=00:00:08.13 bitrate=9024.7kbits/s speed=0.294x    
frame=  249 fps=8.8 q=24.3 size=    8960KiB time=00:00:08.23 bitrate=8915.1kbits/s speed=0.292x    
frame=  255 fps=8.9 q=29.2 size=    9472KiB time=00:00:08.43 bitrate=9201.0kbits/s speed=0.294x    
frame=  258 fps=8.8 q=28.1 size=    9472KiB time=00:00:08.53 bitrate=9093.2kbits/s speed=0.293x    
frame=  263 fps=8.9 q=28.1 size=    9728KiB time=00:00:08.70 bitrate=9160.0kbits/s speed=0.293x    
frame=  266 fps=8.8 q=29.2 size=    9728KiB time=00:00:08.80 bitrate=9055.9kbits/s speed=0.292x    
frame=  269 fps=8.8 q=29.1 size=    9984KiB time=00:00:08.90 bitrate=9189.8kbits/s speed=0.29x    
frame=  274 fps=8.8 q=29.2 size=   10240KiB time=00:00:09.06 bitrate=9252.2kbits/s speed=0.291x    
frame=  278 fps=8.8 q=29.2 size=   10496KiB time=00:00:09.20 bitrate=9346.0kbits/s speed=0.291x    
frame=  282 fps=8.8 q=31.0 size=   10496KiB time=00:00:09.33 bitrate=9212.5kbits/s speed=0.29x    
frame=  288 fps=8.8 q=29.2 size=   10752KiB time=00:00:09.53 bitrate=9239.2kbits/s speed=0.292x    
frame=  294 fps=8.9 q=29.3 size=   11008KiB time=00:00:09.73 bitrate=9264.9kbits/s speed=0.293x    
frame=  297 fps=8.8 q=29.2 size=   11008KiB time=00:00:09.83 bitrate=9170.6kbits/s speed=0.292x    
frame=  303 fps=8.9 q=29.2 size=   11264KiB time=00:00:10.03 bitrate=9196.8kbits/s speed=0.294x    
frame=  305 fps=8.8 q=28.1 size=   11520KiB time=00:00:10.10 bitrate=9343.8kbits/s speed=0.291x    
frame=  310 fps=8.8 q=28.1 size=   11520KiB time=00:00:10.26 bitrate=9192.1kbits/s speed=0.292x    
frame=  315 fps=8.8 q=28.1 size=   11776KiB time=00:00:10.43 bitrate=9246.3kbits/s speed=0.292x    
frame=  320 fps=8.8 q=28.1 size=   12032KiB time=00:00:10.60 bitrate=9298.7kbits/s speed=0.293x    
frame=  324 fps=8.8 q=25.7 size=   12032KiB time=00:00:10.73 bitrate=9183.2kbits/s speed=0.292x    
frame=  328 fps=8.8 q=29.2 size=   12288KiB time=00:00:10.86 bitrate=9263.5kbits/s speed=0.292x    
frame=  334 fps=8.9 q=26.8 size=   12544KiB time=00:00:11.06 bitrate=9285.6kbits/s speed=0.294x    
frame=  339 fps=8.9 q=29.2 size=   12544KiB time=00:00:11.23 bitrate=9147.8kbits/s speed=0.294x    
frame=  342 fps=8.8 q=29.2 size=   12800KiB time=00:00:11.33 bitrate=9252.2kbits/s speed=0.293x    
frame=  348 fps=8.9 q=29.2 size=   13056KiB time=00:00:11.53 bitrate=9273.6kbits/s speed=0.294x    
frame=  351 fps=8.8 q=28.1 size=   13056KiB time=00:00:11.63 bitrate=9193.8kbits/s speed=0.293x    
frame=  357 fps=8.9 q=31.0 size=   13312KiB time=00:00:11.83 bitrate=9215.7kbits/s speed=0.294x    
frame=  362 fps=8.9 q=29.3 size=   13568KiB time=00:00:12.00 bitrate=9262.5kbits/s speed=0.295x    
frame=  366 fps=8.9 q=28.1 size=   13568KiB time=00:00:12.13 bitrate=9160.7kbits/s speed=0.294x    
frame=  372 fps=8.9 q=29.2 size=   13824KiB time=00:00:12.33 bitrate=9182.2kbits/s speed=0.296x    
frame=  376 fps=8.9 q=28.1 size=   14080KiB time=00:00:12.46 bitrate=9252.2kbits/s speed=0.295x    
frame=  381 fps=8.9 q=28.1 size=   14336KiB time=00:00:12.63 bitrate=9296.1kbits/s speed=0.296x    
frame=  385 fps=8.9 q=27.3 size=   14336KiB time=00:00:12.76 bitrate=9199.0kbits/s speed=0.295x    
frame=  387 fps=8.8 q=28.1 size=   14592KiB time=00:00:12.83 bitrate=9314.7kbits/s speed=0.293x    
frame=  391 fps=8.8 q=25.8 size=   14592KiB time=00:00:12.96 bitrate=9218.9kbits/s speed=0.293x    
frame=  395 fps=8.8 q=29.2 size=   14848KiB time=00:00:13.10 bitrate=9285.1kbits/s speed=0.293x    
frame=  399 fps=8.8 q=29.2 size=   14848KiB time=00:00:13.23 bitrate=9191.6kbits/s speed=0.292x    
frame=  405 fps=8.9 q=29.2 size=   15104KiB time=00:00:13.43 bitrate=9210.8kbits/s speed=0.294x    
frame=  410 fps=8.9 q=25.8 size=   15360KiB time=00:00:13.60 bitrate=9252.2kbits/s speed=0.294x    
frame=  413 fps=8.8 q=29.2 size=   15360KiB time=00:00:13.70 bitrate=9184.6kbits/s speed=0.293x    
frame=  419 fps=8.9 q=29.2 size=   15616KiB time=00:00:13.90 bitrate=9203.4kbits/s speed=0.294x    
frame=  423 fps=8.9 q=29.2 size=   15872KiB time=00:00:14.03 bitrate=9265.4kbits/s speed=0.294x    
frame=  429 fps=8.9 q=29.2 size=   16128KiB time=00:00:14.23 bitrate=9282.5kbits/s speed=0.295x    
frame=  434 fps=8.9 q=29.2 size=   16128KiB time=00:00:14.40 bitrate=9175.1kbits/s speed=0.295x    
frame=  438 fps=8.9 q=29.2 size=   16384KiB time=00:00:14.53 bitrate=9235.2kbits/s speed=0.295x    
frame=  444 fps=8.9 q=29.2 size=   16640KiB time=00:00:14.73 bitrate=9252.2kbits/s speed=0.296x    
frame=  449 fps=8.9 q=29.3 size=   16640KiB time=00:00:14.90 bitrate=9148.7kbits/s speed=0.296x    
frame=  454 fps=8.9 q=30.2 size=   16896KiB time=00:00:15.06 bitrate=9186.7kbits/s speed=0.297x    
frame=  459 fps=9.0 q=29.2 size=   17152KiB time=00:00:15.20 bitrate=9244.0kbits/s speed=0.296x    
frame=  462 fps=8.9 q=31.0 size=   17152KiB time=00:00:15.33 bitrate=9163.7kbits/s speed=0.296x    
frame=  469 fps=9.0 q=29.2 size=   17408KiB time=00:00:15.56 bitrate=9161.0kbits/s speed=0.298x    
frame=  474 fps=9.0 q=29.2 size=   17664KiB time=00:00:15.73 bitrate=9197.3kbits/s speed=0.298x    
frame=  479 fps=9.0 q=29.2 size=   17664KiB time=00:00:15.90 bitrate=9100.9kbits/s speed=0.298x    
frame=  483 fps=9.0 q=29.2 size=   17920KiB time=00:00:16.03 bitrate=9156.0kbits/s speed=0.298x    
frame=  489 fps=9.0 q=26.1 size=   18176KiB time=00:00:16.23 bitrate=9172.4kbits/s speed=0.299x    
frame=  493 fps=9.0 q=29.9 size=   18432KiB time=00:00:16.36 bitrate=9225.8kbits/s speed=0.299x    
frame=  495 fps=8.9 q=28.1 size=   18432KiB time=00:00:16.43 bitrate=9188.4kbits/s speed=0.297x    
frame=  500 fps=9.0 q=29.2 size=   18688KiB time=00:00:16.60 bitrate=9222.4kbits/s speed=0.297x    
frame=  503 fps=8.9 q=28.1 size=   18944KiB time=00:00:16.70 bitrate=9292.8kbits/s speed=0.297x    
frame=  507 fps=8.9 q=31.0 size=   18944KiB time=00:00:16.83 bitrate=9219.2kbits/s speed=0.296x    
frame=  513 fps=8.9 q=29.2 size=   19200KiB time=00:00:17.03 bitrate=9234.1kbits/s speed=0.297x    
frame=  519 fps=9.0 q=29.2 size=   19456KiB time=00:00:17.23 bitrate=9248.6kbits/s speed=0.298x    
frame=  524 fps=9.0 q=29.2 size=   19712KiB time=00:00:17.40 bitrate=9280.5kbits/s speed=0.298x    
frame=  529 fps=9.0 q=30.7 size=   19968KiB time=00:00:17.56 bitrate=9311.9kbits/s speed=0.299x    
frame=  533 fps=9.0 q=29.2 size=   19968KiB time=00:00:17.70 bitrate=9241.7kbits/s speed=0.298x    
frame=  537 fps=9.0 q=29.2 size=   20224KiB time=00:00:17.83 bitrate=9290.2kbits/s speed=0.298x    
frame=  543 fps=9.0 q=29.2 size=   20224KiB time=00:00:18.03 bitrate=9187.2kbits/s speed=0.299x    
frame=  545 fps=9.0 q=28.1 size=   20480KiB time=00:00:18.10 bitrate=9269.2kbits/s speed=0.298x    
frame=  551 fps=9.0 q=29.2 size=   20736KiB time=00:00:18.30 bitrate=9282.5kbits/s speed=0.298x    
frame=  558 fps=9.0 q=29.2 size=   20992KiB time=00:00:18.53 bitrate=9278.8kbits/s speed= 0.3x    
frame=  562 fps=9.0 q=29.2 size=   20992KiB time=00:00:18.66 bitrate=9212.5kbits/s speed=0.299x    
frame=  566 fps=9.0 q=29.2 size=   21248KiB time=00:00:18.80 bitrate=9258.7kbits/s speed=0.299x    
frame=  570 fps=9.0 q=28.1 size=   21504KiB time=00:00:18.93 bitrate=9304.3kbits/s speed=0.299x    
frame=  576 fps=9.0 q=30.9 size=   21504KiB time=00:00:19.13 bitrate=9207.0kbits/s speed= 0.3x    
frame=  580 fps=9.0 q=28.1 size=   21760KiB time=00:00:19.26 bitrate=9252.2kbits/s speed=0.299x    
frame=  585 fps=9.0 q=28.1 size=   22016KiB time=00:00:19.43 bitrate=9280.7kbits/s speed= 0.3x    
frame=  589 fps=9.0 q=25.7 size=   22016KiB time=00:00:19.56 bitrate=9217.5kbits/s speed=0.299x    
[vost#0:0/libx265 @ 000002A19BB7DB40] Error submitting a packet to the muxer: No space left on device
    Last message repeated 1 times
[out#0/mp4 @ 000002A19BB1ADC0] Error muxing a packet
[out#0/mp4 @ 000002A19BB1ADC0] Task finished with error code: -28 (No space left on device)
[out#0/mp4 @ 000002A19BB1ADC0] Terminating thread with return code -28 (No space left on device)
[out#0/mp4 @ 000002A19BB1ADC0] Error writing trailer: No space left on device
[out#0/mp4 @ 000002A19BB1ADC0] Error closing file: No space left on device
[out#0/mp4 @ 000002A19BB1ADC0] video:22280KiB audio:0KiB subtitle:0KiB other streams:0KiB global headers:2KiB muxing overhead: unknown
frame=  590 fps=8.3 q=29.3 Lsize=   22016KiB time=00:00:19.60 bitrate=9201.8kbits/s speed=0.277x    
x265 [info]: frame I:      3, Avg QP:24.41  kb/s: 39835.92
x265 [info]: frame P:    138, Avg QP:25.73  kb/s: 19183.57
x265 [info]: frame B:    506, Avg QP:28.96  kb/s: 6341.53 
x265 [info]: Weighted P-Frames: Y:0.0% UV:0.0%
x265 [info]: consecutive B-frames: 5.7% 1.4% 2.8% 8.5% 81.6% 

encoded 647 frames in 70.85s (9.13 fps), 9235.94 kb/s, Avg QP:28.25
Conversion failed!

2025-07-21 12:57:10,802 - INFO -   处理进度: 9/30 - dulu_facial_expression_2025-07-18_14-29-54.avi
2025-07-21 12:57:10,802 - INFO - 开始转换: dulu_facial_expression_2025-07-18_14-29-54.avi
2025-07-21 12:57:18,173 - ERROR - 转换失败: dulu_facial_expression_2025-07-18_14-29-54.avi
2025-07-21 12:57:18,173 - ERROR - 错误信息: ffmpeg version 7.1.1 Copyright (c) 2000-2025 the FFmpeg developers
  built with clang version 20.1.6
  configuration: --prefix=/d/bld/ffmpeg_1748704128577/_h_env/Library --cc=clang.exe --cxx=clang++.exe --nm=llvm-nm --ar=llvm-ar --disable-doc --enable-openssl --enable-demuxer=dash --enable-hardcoded-tables --enable-libfreetype --enable-libharfbuzz --enable-libfontconfig --enable-libopenh264 --enable-libdav1d --ld=lld-link --target-os=win64 --enable-cross-compile --toolchain=msvc --host-cc=clang.exe --extra-libs=ucrt.lib --extra-libs=vcruntime.lib --extra-libs=oldnames.lib --strip=llvm-strip --disable-stripping --host-extralibs= --disable-libopenvino --enable-gpl --enable-libx264 --enable-libx265 --enable-libmp3lame --enable-libaom --enable-libsvtav1 --enable-libxml2 --enable-pic --enable-shared --disable-static --enable-version3 --enable-zlib --enable-libvorbis --enable-libopus --enable-librsvg --enable-ffplay --pkg-config=/d/bld/ffmpeg_1748704128577/_build_env/Library/bin/pkg-config
  libavutil      59. 39.100 / 59. 39.100
  libavcodec     61. 19.101 / 61. 19.101
  libavformat    61.  7.100 / 61.  7.100
  libavdevice    61.  3.100 / 61.  3.100
  libavfilter    10.  4.100 / 10.  4.100
  libswscale      8.  3.100 /  8.  3.100
  libswresample   5.  3.100 /  5.  3.100
  libpostproc    58.  3.100 / 58.  3.100
Input #0, avi, from 'C:\Users\<USER>\Desktop\curiosity_pupil\data\20250718_142425_dulu\video\dulu_facial_expression_2025-07-18_14-29-54.avi':
  Metadata:
    software        : Lavf61.7.100
  Duration: 00:00:24.07, start: 0.000000, bitrate: 66423 kb/s
  Stream #0:0: Video: mjpeg (Baseline) (MJPG / 0x47504A4D), yuvj420p(pc, bt470bg/unknown/unknown), 1920x1080, 66500 kb/s, 30 fps, 30 tbr, 30 tbn
[out#0/mp4 @ 00000181A8177100] Codec AVOption b:a (set bitrate (in bits/s)) has not been used for any stream. The most likely reason is either wrong type (e.g. a video option with no video streams) or that it is a private option of some decoder which was not actually used for any stream.
Stream mapping:
  Stream #0:0 -> #0:0 (mjpeg (native) -> hevc (libx265))
Press [q] to stop, [?] for help
x265 [info]: HEVC encoder version 3.5+1-f0c1022b6
x265 [info]: build info [Windows][MSVC 1916][64 bit] 8bit+10bit+12bit
x265 [info]: using cpu capabilities: MMX2 SSE2Fast LZCNT SSSE3 SSE4.2 AVX FMA3 BMI2 AVX2
x265 [info]: Main profile, Level-4 (Main tier)
x265 [info]: Thread pool created using 8 threads
x265 [info]: Slices                              : 1
x265 [info]: frame threads / pool features       : 3 / wpp(17 rows)
x265 [info]: Coding QT: max CU size, min CU size : 64 / 8
x265 [info]: Residual QT: max TU size, max depth : 32 / 1 inter / 1 intra
x265 [info]: ME / range / subpel / merge         : hex / 57 / 2 / 3
x265 [info]: Keyframe min / max / scenecut / bias  : 25 / 250 / 40 / 5.00 
x265 [info]: Lookahead / bframes / badapt        : 20 / 4 / 2
x265 [info]: b-pyramid / weightp / weightb       : 1 / 1 / 0
x265 [info]: References / ref-limit  cu / depth  : 3 / off / on
x265 [info]: AQ: mode / str / qg-size / cu-tree  : 2 / 1.0 / 32 / 1
x265 [info]: Rate Control / qCompress            : CRF-23.0 / 0.60
x265 [info]: tools: rd=3 psy-rd=2.00 early-skip rskip mode=1 signhide tmvp
x265 [info]: tools: b-intra strong-intra-smoothing lslices=6 deblock sao
Output #0, mp4, to 'C:\Users\<USER>\Desktop\curiosity_pupil\data\20250718_142425_dulu\video_mp4\dulu_facial_expression_2025-07-18_14-29-54.mp4':
  Metadata:
    software        : Lavf61.7.100
    encoder         : Lavf61.7.100
  Stream #0:0: Video: hevc (hev1 / 0x31766568), yuvj420p(pc, bt470bg/unknown/unknown, progressive), 1920x1080, q=2-31, 30 fps, 15360 tbn
      Metadata:
        encoder         : Lavc61.19.101 libx265
      Side data:
        cpb: bitrate max/min/avg: 0/0/0 buffer size: 0 vbv_delay: N/A
frame=    0 fps=0.0 q=0.0 size=       0KiB time=N/A bitrate=N/A speed=N/A    
frame=    0 fps=0.0 q=0.0 size=       0KiB time=N/A bitrate=N/A speed=N/A    
[vost#0:0/libx265 @ 00000181A81B6540] Error submitting a packet to the muxer: No space left on device
    Last message repeated 1 times
[out#0/mp4 @ 00000181A8177100] Error muxing a packet
[out#0/mp4 @ 00000181A8177100] Task finished with error code: -28 (No space left on device)
[out#0/mp4 @ 00000181A8177100] Terminating thread with return code -28 (No space left on device)
[out#0/mp4 @ 00000181A8177100] Error writing trailer: No space left on device
[out#0/mp4 @ 00000181A8177100] Error closing file: No space left on device
[out#0/mp4 @ 00000181A8177100] video:273KiB audio:0KiB subtitle:0KiB other streams:0KiB global headers:2KiB muxing overhead: unknown
frame=    3 fps=0.4 q=29.3 Lsize=       0KiB time=00:00:00.03 bitrate=  10.6kbits/s speed=0.0047x    
x265 [info]: frame I:      1, Avg QP:24.89  kb/s: 28581.60
x265 [info]: frame P:     12, Avg QP:25.91  kb/s: 19925.22
x265 [info]: frame B:     47, Avg QP:29.00  kb/s: 6791.74 
x265 [info]: Weighted P-Frames: Y:0.0% UV:0.0%
x265 [info]: consecutive B-frames: 7.7% 0.0% 0.0% 7.7% 84.6% 

encoded 60 frames in 7.08s (8.48 fps), 9781.60 kb/s, Avg QP:28.31
Conversion failed!

2025-07-21 12:57:18,179 - INFO -   处理进度: 10/30 - dulu_facial_expression_2025-07-18_14-30-21.avi
2025-07-21 12:57:18,179 - INFO - 开始转换: dulu_facial_expression_2025-07-18_14-30-21.avi
2025-07-21 12:57:25,895 - ERROR - 转换失败: dulu_facial_expression_2025-07-18_14-30-21.avi
2025-07-21 12:57:25,895 - ERROR - 错误信息: ffmpeg version 7.1.1 Copyright (c) 2000-2025 the FFmpeg developers
  built with clang version 20.1.6
  configuration: --prefix=/d/bld/ffmpeg_1748704128577/_h_env/Library --cc=clang.exe --cxx=clang++.exe --nm=llvm-nm --ar=llvm-ar --disable-doc --enable-openssl --enable-demuxer=dash --enable-hardcoded-tables --enable-libfreetype --enable-libharfbuzz --enable-libfontconfig --enable-libopenh264 --enable-libdav1d --ld=lld-link --target-os=win64 --enable-cross-compile --toolchain=msvc --host-cc=clang.exe --extra-libs=ucrt.lib --extra-libs=vcruntime.lib --extra-libs=oldnames.lib --strip=llvm-strip --disable-stripping --host-extralibs= --disable-libopenvino --enable-gpl --enable-libx264 --enable-libx265 --enable-libmp3lame --enable-libaom --enable-libsvtav1 --enable-libxml2 --enable-pic --enable-shared --disable-static --enable-version3 --enable-zlib --enable-libvorbis --enable-libopus --enable-librsvg --enable-ffplay --pkg-config=/d/bld/ffmpeg_1748704128577/_build_env/Library/bin/pkg-config
  libavutil      59. 39.100 / 59. 39.100
  libavcodec     61. 19.101 / 61. 19.101
  libavformat    61.  7.100 / 61.  7.100
  libavdevice    61.  3.100 / 61.  3.100
  libavfilter    10.  4.100 / 10.  4.100
  libswscale      8.  3.100 /  8.  3.100
  libswresample   5.  3.100 /  5.  3.100
  libpostproc    58.  3.100 / 58.  3.100
Input #0, avi, from 'C:\Users\<USER>\Desktop\curiosity_pupil\data\20250718_142425_dulu\video\dulu_facial_expression_2025-07-18_14-30-21.avi':
  Metadata:
    software        : Lavf61.7.100
  Duration: 00:00:26.27, start: 0.000000, bitrate: 67990 kb/s
  Stream #0:0: Video: mjpeg (Baseline) (MJPG / 0x47504A4D), yuvj420p(pc, bt470bg/unknown/unknown), 1920x1080, 68062 kb/s, 30 fps, 30 tbr, 30 tbn
[out#0/mp4 @ 0000026904FE0C40] Codec AVOption b:a (set bitrate (in bits/s)) has not been used for any stream. The most likely reason is either wrong type (e.g. a video option with no video streams) or that it is a private option of some decoder which was not actually used for any stream.
Stream mapping:
  Stream #0:0 -> #0:0 (mjpeg (native) -> hevc (libx265))
Press [q] to stop, [?] for help
x265 [info]: HEVC encoder version 3.5+1-f0c1022b6
x265 [info]: build info [Windows][MSVC 1916][64 bit] 8bit+10bit+12bit
x265 [info]: using cpu capabilities: MMX2 SSE2Fast LZCNT SSSE3 SSE4.2 AVX FMA3 BMI2 AVX2
x265 [info]: Main profile, Level-4 (Main tier)
x265 [info]: Thread pool created using 8 threads
x265 [info]: Slices                              : 1
x265 [info]: frame threads / pool features       : 3 / wpp(17 rows)
x265 [info]: Coding QT: max CU size, min CU size : 64 / 8
x265 [info]: Residual QT: max TU size, max depth : 32 / 1 inter / 1 intra
x265 [info]: ME / range / subpel / merge         : hex / 57 / 2 / 3
x265 [info]: Keyframe min / max / scenecut / bias  : 25 / 250 / 40 / 5.00 
x265 [info]: Lookahead / bframes / badapt        : 20 / 4 / 2
x265 [info]: b-pyramid / weightp / weightb       : 1 / 1 / 0
x265 [info]: References / ref-limit  cu / depth  : 3 / off / on
x265 [info]: AQ: mode / str / qg-size / cu-tree  : 2 / 1.0 / 32 / 1
x265 [info]: Rate Control / qCompress            : CRF-23.0 / 0.60
x265 [info]: tools: rd=3 psy-rd=2.00 early-skip rskip mode=1 signhide tmvp
x265 [info]: tools: b-intra strong-intra-smoothing lslices=6 deblock sao
Output #0, mp4, to 'C:\Users\<USER>\Desktop\curiosity_pupil\data\20250718_142425_dulu\video_mp4\dulu_facial_expression_2025-07-18_14-30-21.mp4':
  Metadata:
    software        : Lavf61.7.100
    encoder         : Lavf61.7.100
  Stream #0:0: Video: hevc (hev1 / 0x31766568), yuvj420p(pc, bt470bg/unknown/unknown, progressive), 1920x1080, q=2-31, 30 fps, 15360 tbn
      Metadata:
        encoder         : Lavc61.19.101 libx265
      Side data:
        cpb: bitrate max/min/avg: 0/0/0 buffer size: 0 vbv_delay: N/A
frame=    0 fps=0.0 q=0.0 size=       0KiB time=N/A bitrate=N/A speed=N/A    
frame=    0 fps=0.0 q=0.0 size=       0KiB time=N/A bitrate=N/A speed=N/A    
frame=    1 fps=0.6 q=25.0 size=       0KiB time=-00:00:00.03 bitrate=N/A speed=N/A    
[vost#0:0/libx265 @ 0000026905064CC0] Error submitting a packet to the muxer: No space left on device
    Last message repeated 1 times
[out#0/mp4 @ 0000026904FE0C40] Error muxing a packet
[out#0/mp4 @ 0000026904FE0C40] Task finished with error code: -28 (No space left on device)
[out#0/mp4 @ 0000026904FE0C40] Terminating thread with return code -28 (No space left on device)
[out#0/mp4 @ 0000026904FE0C40] Error writing trailer: No space left on device
[out#0/mp4 @ 0000026904FE0C40] Error closing file: No space left on device
[out#0/mp4 @ 0000026904FE0C40] video:282KiB audio:0KiB subtitle:0KiB other streams:0KiB global headers:2KiB muxing overhead: unknown
frame=    3 fps=0.4 q=30.6 Lsize=       0KiB time=00:00:00.03 bitrate=  10.6kbits/s speed=0.00448x    
x265 [info]: frame I:      1, Avg QP:25.04  kb/s: 29341.68
x265 [info]: frame P:     12, Avg QP:25.79  kb/s: 21390.90
x265 [info]: frame B:     47, Avg QP:29.07  kb/s: 6858.58 
x265 [info]: Weighted P-Frames: Y:0.0% UV:0.0%
x265 [info]: consecutive B-frames: 7.7% 0.0% 0.0% 7.7% 84.6% 

encoded 60 frames in 7.43s (8.08 fps), 10139.76 kb/s, Avg QP:28.35
Conversion failed!

2025-07-21 12:57:25,907 - INFO -   处理进度: 11/30 - dulu_facial_expression_2025-07-18_14-30-51.avi
2025-07-21 12:57:25,907 - INFO - 开始转换: dulu_facial_expression_2025-07-18_14-30-51.avi
2025-07-21 12:57:33,677 - ERROR - 转换失败: dulu_facial_expression_2025-07-18_14-30-51.avi
2025-07-21 12:57:33,677 - ERROR - 错误信息: ffmpeg version 7.1.1 Copyright (c) 2000-2025 the FFmpeg developers
  built with clang version 20.1.6
  configuration: --prefix=/d/bld/ffmpeg_1748704128577/_h_env/Library --cc=clang.exe --cxx=clang++.exe --nm=llvm-nm --ar=llvm-ar --disable-doc --enable-openssl --enable-demuxer=dash --enable-hardcoded-tables --enable-libfreetype --enable-libharfbuzz --enable-libfontconfig --enable-libopenh264 --enable-libdav1d --ld=lld-link --target-os=win64 --enable-cross-compile --toolchain=msvc --host-cc=clang.exe --extra-libs=ucrt.lib --extra-libs=vcruntime.lib --extra-libs=oldnames.lib --strip=llvm-strip --disable-stripping --host-extralibs= --disable-libopenvino --enable-gpl --enable-libx264 --enable-libx265 --enable-libmp3lame --enable-libaom --enable-libsvtav1 --enable-libxml2 --enable-pic --enable-shared --disable-static --enable-version3 --enable-zlib --enable-libvorbis --enable-libopus --enable-librsvg --enable-ffplay --pkg-config=/d/bld/ffmpeg_1748704128577/_build_env/Library/bin/pkg-config
  libavutil      59. 39.100 / 59. 39.100
  libavcodec     61. 19.101 / 61. 19.101
  libavformat    61.  7.100 / 61.  7.100
  libavdevice    61.  3.100 / 61.  3.100
  libavfilter    10.  4.100 / 10.  4.100
  libswscale      8.  3.100 /  8.  3.100
  libswresample   5.  3.100 /  5.  3.100
  libpostproc    58.  3.100 / 58.  3.100
Input #0, avi, from 'C:\Users\<USER>\Desktop\curiosity_pupil\data\20250718_142425_dulu\video\dulu_facial_expression_2025-07-18_14-30-51.avi':
  Metadata:
    software        : Lavf61.7.100
  Duration: 00:00:45.27, start: 0.000000, bitrate: 70438 kb/s
  Stream #0:0: Video: mjpeg (Baseline) (MJPG / 0x47504A4D), yuvj420p(pc, bt470bg/unknown/unknown), 1920x1080, 70479 kb/s, 30 fps, 30 tbr, 30 tbn
[out#0/mp4 @ 000002B65403BC80] Codec AVOption b:a (set bitrate (in bits/s)) has not been used for any stream. The most likely reason is either wrong type (e.g. a video option with no video streams) or that it is a private option of some decoder which was not actually used for any stream.
Stream mapping:
  Stream #0:0 -> #0:0 (mjpeg (native) -> hevc (libx265))
Press [q] to stop, [?] for help
x265 [info]: HEVC encoder version 3.5+1-f0c1022b6
x265 [info]: build info [Windows][MSVC 1916][64 bit] 8bit+10bit+12bit
x265 [info]: using cpu capabilities: MMX2 SSE2Fast LZCNT SSSE3 SSE4.2 AVX FMA3 BMI2 AVX2
x265 [info]: Main profile, Level-4 (Main tier)
x265 [info]: Thread pool created using 8 threads
x265 [info]: Slices                              : 1
x265 [info]: frame threads / pool features       : 3 / wpp(17 rows)
x265 [info]: Coding QT: max CU size, min CU size : 64 / 8
x265 [info]: Residual QT: max TU size, max depth : 32 / 1 inter / 1 intra
x265 [info]: ME / range / subpel / merge         : hex / 57 / 2 / 3
x265 [info]: Keyframe min / max / scenecut / bias  : 25 / 250 / 40 / 5.00 
x265 [info]: Lookahead / bframes / badapt        : 20 / 4 / 2
x265 [info]: b-pyramid / weightp / weightb       : 1 / 1 / 0
x265 [info]: References / ref-limit  cu / depth  : 3 / off / on
x265 [info]: AQ: mode / str / qg-size / cu-tree  : 2 / 1.0 / 32 / 1
x265 [info]: Rate Control / qCompress            : CRF-23.0 / 0.60
x265 [info]: tools: rd=3 psy-rd=2.00 early-skip rskip mode=1 signhide tmvp
x265 [info]: tools: b-intra strong-intra-smoothing lslices=6 deblock sao
Output #0, mp4, to 'C:\Users\<USER>\Desktop\curiosity_pupil\data\20250718_142425_dulu\video_mp4\dulu_facial_expression_2025-07-18_14-30-51.mp4':
  Metadata:
    software        : Lavf61.7.100
    encoder         : Lavf61.7.100
  Stream #0:0: Video: hevc (hev1 / 0x31766568), yuvj420p(pc, bt470bg/unknown/unknown, progressive), 1920x1080, q=2-31, 30 fps, 15360 tbn
      Metadata:
        encoder         : Lavc61.19.101 libx265
      Side data:
        cpb: bitrate max/min/avg: 0/0/0 buffer size: 0 vbv_delay: N/A
frame=    0 fps=0.0 q=0.0 size=       0KiB time=N/A bitrate=N/A speed=N/A    
frame=    0 fps=0.0 q=0.0 size=       0KiB time=N/A bitrate=N/A speed=N/A    
[vost#0:0/libx265 @ 000002B65409F380] Error submitting a packet to the muxer: No space left on device
    Last message repeated 1 times
[out#0/mp4 @ 000002B65403BC80] Error muxing a packet
[out#0/mp4 @ 000002B65403BC80] Task finished with error code: -28 (No space left on device)
[out#0/mp4 @ 000002B65403BC80] Terminating thread with return code -28 (No space left on device)
[out#0/mp4 @ 000002B65403BC80] Error writing trailer: No space left on device
[out#0/mp4 @ 000002B65403BC80] Error closing file: No space left on device
[out#0/mp4 @ 000002B65403BC80] video:295KiB audio:0KiB subtitle:0KiB other streams:0KiB global headers:2KiB muxing overhead: unknown
frame=    3 fps=0.4 q=29.3 Lsize=       0KiB time=00:00:00.03 bitrate=  10.6kbits/s speed=0.00445x    
x265 [info]: frame I:      1, Avg QP:25.05  kb/s: 30741.12
x265 [info]: frame P:     12, Avg QP:25.83  kb/s: 22358.34
x265 [info]: frame B:     47, Avg QP:29.07  kb/s: 7499.09 
x265 [info]: Weighted P-Frames: Y:0.0% UV:0.0%
x265 [info]: consecutive B-frames: 7.7% 0.0% 0.0% 7.7% 84.6% 

encoded 60 frames in 7.48s (8.02 fps), 10858.31 kb/s, Avg QP:28.36
Conversion failed!

2025-07-21 12:57:33,681 - INFO -   处理进度: 12/30 - dulu_facial_expression_2025-07-18_14-31-43.avi
2025-07-21 12:57:33,681 - INFO - 开始转换: dulu_facial_expression_2025-07-18_14-31-43.avi
2025-07-21 12:57:41,646 - ERROR - 转换失败: dulu_facial_expression_2025-07-18_14-31-43.avi
2025-07-21 12:57:41,646 - ERROR - 错误信息: ffmpeg version 7.1.1 Copyright (c) 2000-2025 the FFmpeg developers
  built with clang version 20.1.6
  configuration: --prefix=/d/bld/ffmpeg_1748704128577/_h_env/Library --cc=clang.exe --cxx=clang++.exe --nm=llvm-nm --ar=llvm-ar --disable-doc --enable-openssl --enable-demuxer=dash --enable-hardcoded-tables --enable-libfreetype --enable-libharfbuzz --enable-libfontconfig --enable-libopenh264 --enable-libdav1d --ld=lld-link --target-os=win64 --enable-cross-compile --toolchain=msvc --host-cc=clang.exe --extra-libs=ucrt.lib --extra-libs=vcruntime.lib --extra-libs=oldnames.lib --strip=llvm-strip --disable-stripping --host-extralibs= --disable-libopenvino --enable-gpl --enable-libx264 --enable-libx265 --enable-libmp3lame --enable-libaom --enable-libsvtav1 --enable-libxml2 --enable-pic --enable-shared --disable-static --enable-version3 --enable-zlib --enable-libvorbis --enable-libopus --enable-librsvg --enable-ffplay --pkg-config=/d/bld/ffmpeg_1748704128577/_build_env/Library/bin/pkg-config
  libavutil      59. 39.100 / 59. 39.100
  libavcodec     61. 19.101 / 61. 19.101
  libavformat    61.  7.100 / 61.  7.100
  libavdevice    61.  3.100 / 61.  3.100
  libavfilter    10.  4.100 / 10.  4.100
  libswscale      8.  3.100 /  8.  3.100
  libswresample   5.  3.100 /  5.  3.100
  libpostproc    58.  3.100 / 58.  3.100
Input #0, avi, from 'C:\Users\<USER>\Desktop\curiosity_pupil\data\20250718_142425_dulu\video\dulu_facial_expression_2025-07-18_14-31-43.avi':
  Metadata:
    software        : Lavf61.7.100
  Duration: 00:00:27.47, start: 0.000000, bitrate: 72301 kb/s
  Stream #0:0: Video: mjpeg (Baseline) (MJPG / 0x47504A4D), yuvj420p(pc, bt470bg/unknown/unknown), 1920x1080, 72374 kb/s, 30 fps, 30 tbr, 30 tbn
[out#0/mp4 @ 000001375F5EA980] Codec AVOption b:a (set bitrate (in bits/s)) has not been used for any stream. The most likely reason is either wrong type (e.g. a video option with no video streams) or that it is a private option of some decoder which was not actually used for any stream.
Stream mapping:
  Stream #0:0 -> #0:0 (mjpeg (native) -> hevc (libx265))
Press [q] to stop, [?] for help
x265 [info]: HEVC encoder version 3.5+1-f0c1022b6
x265 [info]: build info [Windows][MSVC 1916][64 bit] 8bit+10bit+12bit
x265 [info]: using cpu capabilities: MMX2 SSE2Fast LZCNT SSSE3 SSE4.2 AVX FMA3 BMI2 AVX2
x265 [info]: Main profile, Level-4 (Main tier)
x265 [info]: Thread pool created using 8 threads
x265 [info]: Slices                              : 1
x265 [info]: frame threads / pool features       : 3 / wpp(17 rows)
x265 [info]: Coding QT: max CU size, min CU size : 64 / 8
x265 [info]: Residual QT: max TU size, max depth : 32 / 1 inter / 1 intra
x265 [info]: ME / range / subpel / merge         : hex / 57 / 2 / 3
x265 [info]: Keyframe min / max / scenecut / bias  : 25 / 250 / 40 / 5.00 
x265 [info]: Lookahead / bframes / badapt        : 20 / 4 / 2
x265 [info]: b-pyramid / weightp / weightb       : 1 / 1 / 0
x265 [info]: References / ref-limit  cu / depth  : 3 / off / on
x265 [info]: AQ: mode / str / qg-size / cu-tree  : 2 / 1.0 / 32 / 1
x265 [info]: Rate Control / qCompress            : CRF-23.0 / 0.60
x265 [info]: tools: rd=3 psy-rd=2.00 early-skip rskip mode=1 signhide tmvp
x265 [info]: tools: b-intra strong-intra-smoothing lslices=6 deblock sao
Output #0, mp4, to 'C:\Users\<USER>\Desktop\curiosity_pupil\data\20250718_142425_dulu\video_mp4\dulu_facial_expression_2025-07-18_14-31-43.mp4':
  Metadata:
    software        : Lavf61.7.100
    encoder         : Lavf61.7.100
  Stream #0:0: Video: hevc (hev1 / 0x31766568), yuvj420p(pc, bt470bg/unknown/unknown, progressive), 1920x1080, q=2-31, 30 fps, 15360 tbn
      Metadata:
        encoder         : Lavc61.19.101 libx265
      Side data:
        cpb: bitrate max/min/avg: 0/0/0 buffer size: 0 vbv_delay: N/A
frame=    0 fps=0.0 q=0.0 size=       0KiB time=N/A bitrate=N/A speed=N/A    
frame=    0 fps=0.0 q=0.0 size=       0KiB time=N/A bitrate=N/A speed=N/A    
[vost#0:0/libx265 @ 000001375F6505C0] Error submitting a packet to the muxer: No space left on device
    Last message repeated 1 times
[out#0/mp4 @ 000001375F5EA980] Error muxing a packet
[out#0/mp4 @ 000001375F5EA980] Task finished with error code: -28 (No space left on device)
[out#0/mp4 @ 000001375F5EA980] Terminating thread with return code -28 (No space left on device)
[out#0/mp4 @ 000001375F5EA980] Error writing trailer: No space left on device
[out#0/mp4 @ 000001375F5EA980] Error closing file: No space left on device
[out#0/mp4 @ 000001375F5EA980] video:258KiB audio:0KiB subtitle:0KiB other streams:0KiB global headers:2KiB muxing overhead: unknown
frame=    2 fps=0.3 q=29.4 Lsize=       0KiB time=00:00:00.00 bitrate=N/A speed=   0x    
x265 [info]: frame I:      1, Avg QP:25.17  kb/s: 32742.24
x265 [info]: frame P:     12, Avg QP:25.99  kb/s: 23342.36
x265 [info]: frame B:     46, Avg QP:29.09  kb/s: 8239.39 
x265 [info]: Weighted P-Frames: Y:0.0% UV:0.0%
x265 [info]: consecutive B-frames: 7.7% 0.0% 0.0% 15.4% 76.9% 

encoded 59 frames in 7.67s (7.70 fps), 11726.49 kb/s, Avg QP:28.39
Conversion failed!

2025-07-21 12:57:41,646 - INFO -   处理进度: 13/30 - dulu_facial_expression_2025-07-18_14-32-16.avi
2025-07-21 12:57:41,646 - INFO - 开始转换: dulu_facial_expression_2025-07-18_14-32-16.avi
2025-07-21 12:57:50,166 - ERROR - 转换失败: dulu_facial_expression_2025-07-18_14-32-16.avi
2025-07-21 12:57:50,166 - ERROR - 错误信息: ffmpeg version 7.1.1 Copyright (c) 2000-2025 the FFmpeg developers
  built with clang version 20.1.6
  configuration: --prefix=/d/bld/ffmpeg_1748704128577/_h_env/Library --cc=clang.exe --cxx=clang++.exe --nm=llvm-nm --ar=llvm-ar --disable-doc --enable-openssl --enable-demuxer=dash --enable-hardcoded-tables --enable-libfreetype --enable-libharfbuzz --enable-libfontconfig --enable-libopenh264 --enable-libdav1d --ld=lld-link --target-os=win64 --enable-cross-compile --toolchain=msvc --host-cc=clang.exe --extra-libs=ucrt.lib --extra-libs=vcruntime.lib --extra-libs=oldnames.lib --strip=llvm-strip --disable-stripping --host-extralibs= --disable-libopenvino --enable-gpl --enable-libx264 --enable-libx265 --enable-libmp3lame --enable-libaom --enable-libsvtav1 --enable-libxml2 --enable-pic --enable-shared --disable-static --enable-version3 --enable-zlib --enable-libvorbis --enable-libopus --enable-librsvg --enable-ffplay --pkg-config=/d/bld/ffmpeg_1748704128577/_build_env/Library/bin/pkg-config
  libavutil      59. 39.100 / 59. 39.100
  libavcodec     61. 19.101 / 61. 19.101
  libavformat    61.  7.100 / 61.  7.100
  libavdevice    61.  3.100 / 61.  3.100
  libavfilter    10.  4.100 / 10.  4.100
  libswscale      8.  3.100 /  8.  3.100
  libswresample   5.  3.100 /  5.  3.100
  libpostproc    58.  3.100 / 58.  3.100
Input #0, avi, from 'C:\Users\<USER>\Desktop\curiosity_pupil\data\20250718_142425_dulu\video\dulu_facial_expression_2025-07-18_14-32-16.avi':
  Metadata:
    software        : Lavf61.7.100
  Duration: 00:00:24.43, start: 0.000000, bitrate: 73580 kb/s
  Stream #0:0: Video: mjpeg (Baseline) (MJPG / 0x47504A4D), yuvj420p(pc, bt470bg/unknown/unknown), 1920x1080, 73666 kb/s, 30 fps, 30 tbr, 30 tbn
[out#0/mp4 @ 0000021A8D3D1740] Codec AVOption b:a (set bitrate (in bits/s)) has not been used for any stream. The most likely reason is either wrong type (e.g. a video option with no video streams) or that it is a private option of some decoder which was not actually used for any stream.
Stream mapping:
  Stream #0:0 -> #0:0 (mjpeg (native) -> hevc (libx265))
Press [q] to stop, [?] for help
x265 [info]: HEVC encoder version 3.5+1-f0c1022b6
x265 [info]: build info [Windows][MSVC 1916][64 bit] 8bit+10bit+12bit
x265 [info]: using cpu capabilities: MMX2 SSE2Fast LZCNT SSSE3 SSE4.2 AVX FMA3 BMI2 AVX2
x265 [info]: Main profile, Level-4 (Main tier)
x265 [info]: Thread pool created using 8 threads
x265 [info]: Slices                              : 1
x265 [info]: frame threads / pool features       : 3 / wpp(17 rows)
x265 [info]: Coding QT: max CU size, min CU size : 64 / 8
x265 [info]: Residual QT: max TU size, max depth : 32 / 1 inter / 1 intra
x265 [info]: ME / range / subpel / merge         : hex / 57 / 2 / 3
x265 [info]: Keyframe min / max / scenecut / bias  : 25 / 250 / 40 / 5.00 
x265 [info]: Lookahead / bframes / badapt        : 20 / 4 / 2
x265 [info]: b-pyramid / weightp / weightb       : 1 / 1 / 0
x265 [info]: References / ref-limit  cu / depth  : 3 / off / on
x265 [info]: AQ: mode / str / qg-size / cu-tree  : 2 / 1.0 / 32 / 1
x265 [info]: Rate Control / qCompress            : CRF-23.0 / 0.60
x265 [info]: tools: rd=3 psy-rd=2.00 early-skip rskip mode=1 signhide tmvp
x265 [info]: tools: b-intra strong-intra-smoothing lslices=6 deblock sao
Output #0, mp4, to 'C:\Users\<USER>\Desktop\curiosity_pupil\data\20250718_142425_dulu\video_mp4\dulu_facial_expression_2025-07-18_14-32-16.mp4':
  Metadata:
    software        : Lavf61.7.100
    encoder         : Lavf61.7.100
  Stream #0:0: Video: hevc (hev1 / 0x31766568), yuvj420p(pc, bt470bg/unknown/unknown, progressive), 1920x1080, q=2-31, 30 fps, 15360 tbn
      Metadata:
        encoder         : Lavc61.19.101 libx265
      Side data:
        cpb: bitrate max/min/avg: 0/0/0 buffer size: 0 vbv_delay: N/A
frame=    0 fps=0.0 q=0.0 size=       0KiB time=N/A bitrate=N/A speed=N/A    
frame=    0 fps=0.0 q=0.0 size=       0KiB time=N/A bitrate=N/A speed=N/A    
[vost#0:0/libx265 @ 0000021A8D426B00] Error submitting a packet to the muxer: No space left on device
    Last message repeated 1 times
[out#0/mp4 @ 0000021A8D3D1740] Error muxing a packet
[out#0/mp4 @ 0000021A8D3D1740] Task finished with error code: -28 (No space left on device)
[out#0/mp4 @ 0000021A8D3D1740] Terminating thread with return code -28 (No space left on device)
[out#0/mp4 @ 0000021A8D3D1740] Error writing trailer: No space left on device
[out#0/mp4 @ 0000021A8D3D1740] Error closing file: No space left on device
[out#0/mp4 @ 0000021A8D3D1740] video:263KiB audio:0KiB subtitle:0KiB other streams:0KiB global headers:2KiB muxing overhead: unknown
frame=    2 fps=0.2 q=29.4 Lsize=       0KiB time=00:00:00.00 bitrate=N/A speed=   0x    
x265 [info]: frame I:      1, Avg QP:25.01  kb/s: 34057.92
x265 [info]: frame P:     13, Avg QP:25.91  kb/s: 22877.93
x265 [info]: frame B:     45, Avg QP:29.07  kb/s: 9014.64 
x265 [info]: Weighted P-Frames: Y:0.0% UV:0.0%
x265 [info]: consecutive B-frames: 14.3% 0.0% 7.1% 7.1% 71.4% 

encoded 59 frames in 8.15s (7.24 fps), 12493.73 kb/s, Avg QP:28.31
Conversion failed!

2025-07-21 12:57:50,166 - INFO -   处理进度: 14/30 - dulu_facial_expression_2025-07-18_14-32-44.avi
2025-07-21 12:57:50,166 - INFO - 开始转换: dulu_facial_expression_2025-07-18_14-32-44.avi
2025-07-21 12:57:58,603 - ERROR - 转换失败: dulu_facial_expression_2025-07-18_14-32-44.avi
2025-07-21 12:57:58,603 - ERROR - 错误信息: ffmpeg version 7.1.1 Copyright (c) 2000-2025 the FFmpeg developers
  built with clang version 20.1.6
  configuration: --prefix=/d/bld/ffmpeg_1748704128577/_h_env/Library --cc=clang.exe --cxx=clang++.exe --nm=llvm-nm --ar=llvm-ar --disable-doc --enable-openssl --enable-demuxer=dash --enable-hardcoded-tables --enable-libfreetype --enable-libharfbuzz --enable-libfontconfig --enable-libopenh264 --enable-libdav1d --ld=lld-link --target-os=win64 --enable-cross-compile --toolchain=msvc --host-cc=clang.exe --extra-libs=ucrt.lib --extra-libs=vcruntime.lib --extra-libs=oldnames.lib --strip=llvm-strip --disable-stripping --host-extralibs= --disable-libopenvino --enable-gpl --enable-libx264 --enable-libx265 --enable-libmp3lame --enable-libaom --enable-libsvtav1 --enable-libxml2 --enable-pic --enable-shared --disable-static --enable-version3 --enable-zlib --enable-libvorbis --enable-libopus --enable-librsvg --enable-ffplay --pkg-config=/d/bld/ffmpeg_1748704128577/_build_env/Library/bin/pkg-config
  libavutil      59. 39.100 / 59. 39.100
  libavcodec     61. 19.101 / 61. 19.101
  libavformat    61.  7.100 / 61.  7.100
  libavdevice    61.  3.100 / 61.  3.100
  libavfilter    10.  4.100 / 10.  4.100
  libswscale      8.  3.100 /  8.  3.100
  libswresample   5.  3.100 /  5.  3.100
  libpostproc    58.  3.100 / 58.  3.100
Input #0, avi, from 'C:\Users\<USER>\Desktop\curiosity_pupil\data\20250718_142425_dulu\video\dulu_facial_expression_2025-07-18_14-32-44.avi':
  Metadata:
    software        : Lavf61.7.100
  Duration: 00:00:27.03, start: 0.000000, bitrate: 74701 kb/s
  Stream #0:0: Video: mjpeg (Baseline) (MJPG / 0x47504A4D), yuvj420p(pc, bt470bg/unknown/unknown), 1920x1080, 74779 kb/s, 30 fps, 30 tbr, 30 tbn
[out#0/mp4 @ 000001AD6DBFF400] Codec AVOption b:a (set bitrate (in bits/s)) has not been used for any stream. The most likely reason is either wrong type (e.g. a video option with no video streams) or that it is a private option of some decoder which was not actually used for any stream.
Stream mapping:
  Stream #0:0 -> #0:0 (mjpeg (native) -> hevc (libx265))
Press [q] to stop, [?] for help
x265 [info]: HEVC encoder version 3.5+1-f0c1022b6
x265 [info]: build info [Windows][MSVC 1916][64 bit] 8bit+10bit+12bit
x265 [info]: using cpu capabilities: MMX2 SSE2Fast LZCNT SSSE3 SSE4.2 AVX FMA3 BMI2 AVX2
x265 [info]: Main profile, Level-4 (Main tier)
x265 [info]: Thread pool created using 8 threads
x265 [info]: Slices                              : 1
x265 [info]: frame threads / pool features       : 3 / wpp(17 rows)
x265 [info]: Coding QT: max CU size, min CU size : 64 / 8
x265 [info]: Residual QT: max TU size, max depth : 32 / 1 inter / 1 intra
x265 [info]: ME / range / subpel / merge         : hex / 57 / 2 / 3
x265 [info]: Keyframe min / max / scenecut / bias  : 25 / 250 / 40 / 5.00 
x265 [info]: Lookahead / bframes / badapt        : 20 / 4 / 2
x265 [info]: b-pyramid / weightp / weightb       : 1 / 1 / 0
x265 [info]: References / ref-limit  cu / depth  : 3 / off / on
x265 [info]: AQ: mode / str / qg-size / cu-tree  : 2 / 1.0 / 32 / 1
x265 [info]: Rate Control / qCompress            : CRF-23.0 / 0.60
x265 [info]: tools: rd=3 psy-rd=2.00 early-skip rskip mode=1 signhide tmvp
x265 [info]: tools: b-intra strong-intra-smoothing lslices=6 deblock sao
Output #0, mp4, to 'C:\Users\<USER>\Desktop\curiosity_pupil\data\20250718_142425_dulu\video_mp4\dulu_facial_expression_2025-07-18_14-32-44.mp4':
  Metadata:
    software        : Lavf61.7.100
    encoder         : Lavf61.7.100
  Stream #0:0: Video: hevc (hev1 / 0x31766568), yuvj420p(pc, bt470bg/unknown/unknown, progressive), 1920x1080, q=2-31, 30 fps, 15360 tbn
      Metadata:
        encoder         : Lavc61.19.101 libx265
      Side data:
        cpb: bitrate max/min/avg: 0/0/0 buffer size: 0 vbv_delay: N/A
frame=    0 fps=0.0 q=0.0 size=       0KiB time=N/A bitrate=N/A speed=N/A    
frame=    0 fps=0.0 q=0.0 size=       0KiB time=N/A bitrate=N/A speed=N/A    
[vost#0:0/libx265 @ 000001AD6FA78740] Error submitting a packet to the muxer: No space left on device
    Last message repeated 1 times
[out#0/mp4 @ 000001AD6DBFF400] Error muxing a packet
[out#0/mp4 @ 000001AD6DBFF400] Task finished with error code: -28 (No space left on device)
[out#0/mp4 @ 000001AD6DBFF400] Terminating thread with return code -28 (No space left on device)
[out#0/mp4 @ 000001AD6DBFF400] Error writing trailer: No space left on device
[out#0/mp4 @ 000001AD6DBFF400] Error closing file: No space left on device
[out#0/mp4 @ 000001AD6DBFF400] video:304KiB audio:0KiB subtitle:0KiB other streams:0KiB global headers:2KiB muxing overhead: unknown
frame=    2 fps=0.2 q=29.4 Lsize=       0KiB time=00:00:00.00 bitrate=N/A speed=   0x    
x265 [info]: frame I:      1, Avg QP:25.02  kb/s: 34996.80
x265 [info]: frame P:     13, Avg QP:25.93  kb/s: 22949.00
x265 [info]: frame B:     45, Avg QP:29.13  kb/s: 9213.09 
x265 [info]: Weighted P-Frames: Y:0.0% UV:0.0%
x265 [info]: consecutive B-frames: 14.3% 0.0% 7.1% 7.1% 71.4% 

encoded 59 frames in 8.09s (7.29 fps), 12676.66 kb/s, Avg QP:28.36
Conversion failed!

2025-07-21 12:57:58,603 - INFO -   处理进度: 15/30 - dulu_facial_expression_2025-07-18_14-33-15.avi
2025-07-21 12:57:58,615 - INFO - 开始转换: dulu_facial_expression_2025-07-18_14-33-15.avi
2025-07-21 12:58:07,406 - ERROR - 转换失败: dulu_facial_expression_2025-07-18_14-33-15.avi
2025-07-21 12:58:07,406 - ERROR - 错误信息: ffmpeg version 7.1.1 Copyright (c) 2000-2025 the FFmpeg developers
  built with clang version 20.1.6
  configuration: --prefix=/d/bld/ffmpeg_1748704128577/_h_env/Library --cc=clang.exe --cxx=clang++.exe --nm=llvm-nm --ar=llvm-ar --disable-doc --enable-openssl --enable-demuxer=dash --enable-hardcoded-tables --enable-libfreetype --enable-libharfbuzz --enable-libfontconfig --enable-libopenh264 --enable-libdav1d --ld=lld-link --target-os=win64 --enable-cross-compile --toolchain=msvc --host-cc=clang.exe --extra-libs=ucrt.lib --extra-libs=vcruntime.lib --extra-libs=oldnames.lib --strip=llvm-strip --disable-stripping --host-extralibs= --disable-libopenvino --enable-gpl --enable-libx264 --enable-libx265 --enable-libmp3lame --enable-libaom --enable-libsvtav1 --enable-libxml2 --enable-pic --enable-shared --disable-static --enable-version3 --enable-zlib --enable-libvorbis --enable-libopus --enable-librsvg --enable-ffplay --pkg-config=/d/bld/ffmpeg_1748704128577/_build_env/Library/bin/pkg-config
  libavutil      59. 39.100 / 59. 39.100
  libavcodec     61. 19.101 / 61. 19.101
  libavformat    61.  7.100 / 61.  7.100
  libavdevice    61.  3.100 / 61.  3.100
  libavfilter    10.  4.100 / 10.  4.100
  libswscale      8.  3.100 /  8.  3.100
  libswresample   5.  3.100 /  5.  3.100
  libpostproc    58.  3.100 / 58.  3.100
Input #0, avi, from 'C:\Users\<USER>\Desktop\curiosity_pupil\data\20250718_142425_dulu\video\dulu_facial_expression_2025-07-18_14-33-15.avi':
  Metadata:
    software        : Lavf61.7.100
  Duration: 00:00:26.53, start: 0.000000, bitrate: 75660 kb/s
  Stream #0:0: Video: mjpeg (Baseline) (MJPG / 0x47504A4D), yuvj420p(pc, bt470bg/unknown/unknown), 1920x1080, 75740 kb/s, 30 fps, 30 tbr, 30 tbn
[out#0/mp4 @ 0000029620251540] Codec AVOption b:a (set bitrate (in bits/s)) has not been used for any stream. The most likely reason is either wrong type (e.g. a video option with no video streams) or that it is a private option of some decoder which was not actually used for any stream.
Stream mapping:
  Stream #0:0 -> #0:0 (mjpeg (native) -> hevc (libx265))
Press [q] to stop, [?] for help
x265 [info]: HEVC encoder version 3.5+1-f0c1022b6
x265 [info]: build info [Windows][MSVC 1916][64 bit] 8bit+10bit+12bit
x265 [info]: using cpu capabilities: MMX2 SSE2Fast LZCNT SSSE3 SSE4.2 AVX FMA3 BMI2 AVX2
x265 [info]: Main profile, Level-4 (Main tier)
x265 [info]: Thread pool created using 8 threads
x265 [info]: Slices                              : 1
x265 [info]: frame threads / pool features       : 3 / wpp(17 rows)
x265 [info]: Coding QT: max CU size, min CU size : 64 / 8
x265 [info]: Residual QT: max TU size, max depth : 32 / 1 inter / 1 intra
x265 [info]: ME / range / subpel / merge         : hex / 57 / 2 / 3
x265 [info]: Keyframe min / max / scenecut / bias  : 25 / 250 / 40 / 5.00 
x265 [info]: Lookahead / bframes / badapt        : 20 / 4 / 2
x265 [info]: b-pyramid / weightp / weightb       : 1 / 1 / 0
x265 [info]: References / ref-limit  cu / depth  : 3 / off / on
x265 [info]: AQ: mode / str / qg-size / cu-tree  : 2 / 1.0 / 32 / 1
x265 [info]: Rate Control / qCompress            : CRF-23.0 / 0.60
x265 [info]: tools: rd=3 psy-rd=2.00 early-skip rskip mode=1 signhide tmvp
x265 [info]: tools: b-intra strong-intra-smoothing lslices=6 deblock sao
Output #0, mp4, to 'C:\Users\<USER>\Desktop\curiosity_pupil\data\20250718_142425_dulu\video_mp4\dulu_facial_expression_2025-07-18_14-33-15.mp4':
  Metadata:
    software        : Lavf61.7.100
    encoder         : Lavf61.7.100
  Stream #0:0: Video: hevc (hev1 / 0x31766568), yuvj420p(pc, bt470bg/unknown/unknown, progressive), 1920x1080, q=2-31, 30 fps, 15360 tbn
      Metadata:
        encoder         : Lavc61.19.101 libx265
      Side data:
        cpb: bitrate max/min/avg: 0/0/0 buffer size: 0 vbv_delay: N/A
frame=    0 fps=0.0 q=0.0 size=       0KiB time=N/A bitrate=N/A speed=N/A    
frame=    0 fps=0.0 q=0.0 size=       0KiB time=N/A bitrate=N/A speed=N/A    
[vost#0:0/libx265 @ 0000029620295680] Error submitting a packet to the muxer: No space left on device
    Last message repeated 1 times
[out#0/mp4 @ 0000029620251540] Error muxing a packet
[out#0/mp4 @ 0000029620251540] Task finished with error code: -28 (No space left on device)
[out#0/mp4 @ 0000029620251540] Terminating thread with return code -28 (No space left on device)
[out#0/mp4 @ 0000029620251540] Error writing trailer: No space left on device
[out#0/mp4 @ 0000029620251540] Error closing file: No space left on device
[out#0/mp4 @ 0000029620251540] video:276KiB audio:0KiB subtitle:0KiB other streams:0KiB global headers:2KiB muxing overhead: unknown
frame=    2 fps=0.2 q=29.4 Lsize=       0KiB time=00:00:00.00 bitrate=N/A speed=   0x    
x265 [info]: frame I:      1, Avg QP:25.24  kb/s: 35421.36
x265 [info]: frame P:     12, Avg QP:26.08  kb/s: 25328.40
x265 [info]: frame B:     46, Avg QP:29.16  kb/s: 9854.15 
x265 [info]: Weighted P-Frames: Y:0.0% UV:0.0%
x265 [info]: consecutive B-frames: 7.7% 0.0% 0.0% 15.4% 76.9% 

encoded 59 frames in 8.48s (6.96 fps), 13434.80 kb/s, Avg QP:28.47
Conversion failed!

2025-07-21 12:58:07,420 - INFO -   处理进度: 16/30 - dulu_facial_expression_2025-07-18_14-33-47.avi
2025-07-21 12:58:07,420 - INFO - 开始转换: dulu_facial_expression_2025-07-18_14-33-47.avi
2025-07-21 12:58:16,445 - ERROR - 转换失败: dulu_facial_expression_2025-07-18_14-33-47.avi
2025-07-21 12:58:16,445 - ERROR - 错误信息: ffmpeg version 7.1.1 Copyright (c) 2000-2025 the FFmpeg developers
  built with clang version 20.1.6
  configuration: --prefix=/d/bld/ffmpeg_1748704128577/_h_env/Library --cc=clang.exe --cxx=clang++.exe --nm=llvm-nm --ar=llvm-ar --disable-doc --enable-openssl --enable-demuxer=dash --enable-hardcoded-tables --enable-libfreetype --enable-libharfbuzz --enable-libfontconfig --enable-libopenh264 --enable-libdav1d --ld=lld-link --target-os=win64 --enable-cross-compile --toolchain=msvc --host-cc=clang.exe --extra-libs=ucrt.lib --extra-libs=vcruntime.lib --extra-libs=oldnames.lib --strip=llvm-strip --disable-stripping --host-extralibs= --disable-libopenvino --enable-gpl --enable-libx264 --enable-libx265 --enable-libmp3lame --enable-libaom --enable-libsvtav1 --enable-libxml2 --enable-pic --enable-shared --disable-static --enable-version3 --enable-zlib --enable-libvorbis --enable-libopus --enable-librsvg --enable-ffplay --pkg-config=/d/bld/ffmpeg_1748704128577/_build_env/Library/bin/pkg-config
  libavutil      59. 39.100 / 59. 39.100
  libavcodec     61. 19.101 / 61. 19.101
  libavformat    61.  7.100 / 61.  7.100
  libavdevice    61.  3.100 / 61.  3.100
  libavfilter    10.  4.100 / 10.  4.100
  libswscale      8.  3.100 /  8.  3.100
  libswresample   5.  3.100 /  5.  3.100
  libpostproc    58.  3.100 / 58.  3.100
Input #0, avi, from 'C:\Users\<USER>\Desktop\curiosity_pupil\data\20250718_142425_dulu\video\dulu_facial_expression_2025-07-18_14-33-47.avi':
  Metadata:
    software        : Lavf61.7.100
  Duration: 00:00:31.43, start: 0.000000, bitrate: 76646 kb/s
  Stream #0:0: Video: mjpeg (Baseline) (MJPG / 0x47504A4D), yuvj420p(pc, bt470bg/unknown/unknown), 1920x1080, 76714 kb/s, 30 fps, 30 tbr, 30 tbn
[out#0/mp4 @ 0000028829A0CB40] Codec AVOption b:a (set bitrate (in bits/s)) has not been used for any stream. The most likely reason is either wrong type (e.g. a video option with no video streams) or that it is a private option of some decoder which was not actually used for any stream.
Stream mapping:
  Stream #0:0 -> #0:0 (mjpeg (native) -> hevc (libx265))
Press [q] to stop, [?] for help
x265 [info]: HEVC encoder version 3.5+1-f0c1022b6
x265 [info]: build info [Windows][MSVC 1916][64 bit] 8bit+10bit+12bit
x265 [info]: using cpu capabilities: MMX2 SSE2Fast LZCNT SSSE3 SSE4.2 AVX FMA3 BMI2 AVX2
x265 [info]: Main profile, Level-4 (Main tier)
x265 [info]: Thread pool created using 8 threads
x265 [info]: Slices                              : 1
x265 [info]: frame threads / pool features       : 3 / wpp(17 rows)
x265 [info]: Coding QT: max CU size, min CU size : 64 / 8
x265 [info]: Residual QT: max TU size, max depth : 32 / 1 inter / 1 intra
x265 [info]: ME / range / subpel / merge         : hex / 57 / 2 / 3
x265 [info]: Keyframe min / max / scenecut / bias  : 25 / 250 / 40 / 5.00 
x265 [info]: Lookahead / bframes / badapt        : 20 / 4 / 2
x265 [info]: b-pyramid / weightp / weightb       : 1 / 1 / 0
x265 [info]: References / ref-limit  cu / depth  : 3 / off / on
x265 [info]: AQ: mode / str / qg-size / cu-tree  : 2 / 1.0 / 32 / 1
x265 [info]: Rate Control / qCompress            : CRF-23.0 / 0.60
x265 [info]: tools: rd=3 psy-rd=2.00 early-skip rskip mode=1 signhide tmvp
x265 [info]: tools: b-intra strong-intra-smoothing lslices=6 deblock sao
Output #0, mp4, to 'C:\Users\<USER>\Desktop\curiosity_pupil\data\20250718_142425_dulu\video_mp4\dulu_facial_expression_2025-07-18_14-33-47.mp4':
  Metadata:
    software        : Lavf61.7.100
    encoder         : Lavf61.7.100
  Stream #0:0: Video: hevc (hev1 / 0x31766568), yuvj420p(pc, bt470bg/unknown/unknown, progressive), 1920x1080, q=2-31, 30 fps, 15360 tbn
      Metadata:
        encoder         : Lavc61.19.101 libx265
      Side data:
        cpb: bitrate max/min/avg: 0/0/0 buffer size: 0 vbv_delay: N/A
frame=    0 fps=0.0 q=0.0 size=       0KiB time=N/A bitrate=N/A speed=N/A    
frame=    0 fps=0.0 q=0.0 size=       0KiB time=N/A bitrate=N/A speed=N/A    
[vost#0:0/libx265 @ 0000028829A4F300] Error submitting a packet to the muxer: No space left on device
    Last message repeated 1 times
[out#0/mp4 @ 0000028829A0CB40] Error muxing a packet
[out#0/mp4 @ 0000028829A0CB40] Task finished with error code: -28 (No space left on device)
[out#0/mp4 @ 0000028829A0CB40] Terminating thread with return code -28 (No space left on device)
[out#0/mp4 @ 0000028829A0CB40] Error writing trailer: No space left on device
[out#0/mp4 @ 0000028829A0CB40] Error closing file: No space left on device
[out#0/mp4 @ 0000028829A0CB40] video:287KiB audio:0KiB subtitle:0KiB other streams:0KiB global headers:2KiB muxing overhead: unknown
frame=    2 fps=0.2 q=29.5 Lsize=       0KiB time=00:00:00.00 bitrate=N/A speed=   0x    
x265 [info]: frame I:      1, Avg QP:25.34  kb/s: 35987.52
x265 [info]: frame P:     12, Avg QP:25.91  kb/s: 27017.04
x265 [info]: frame B:     46, Avg QP:29.23  kb/s: 9617.61 
x265 [info]: Weighted P-Frames: Y:0.0% UV:0.0%
x265 [info]: consecutive B-frames: 7.7% 0.0% 0.0% 15.4% 76.9% 

encoded 59 frames in 8.59s (6.87 fps), 13603.42 kb/s, Avg QP:28.49
Conversion failed!

2025-07-21 12:58:16,445 - INFO -   处理进度: 17/30 - dulu_facial_expression_2025-07-18_14-34-24.avi
2025-07-21 12:58:16,445 - INFO - 开始转换: dulu_facial_expression_2025-07-18_14-34-24.avi
2025-07-21 12:58:25,256 - ERROR - 转换失败: dulu_facial_expression_2025-07-18_14-34-24.avi
2025-07-21 12:58:25,256 - ERROR - 错误信息: ffmpeg version 7.1.1 Copyright (c) 2000-2025 the FFmpeg developers
  built with clang version 20.1.6
  configuration: --prefix=/d/bld/ffmpeg_1748704128577/_h_env/Library --cc=clang.exe --cxx=clang++.exe --nm=llvm-nm --ar=llvm-ar --disable-doc --enable-openssl --enable-demuxer=dash --enable-hardcoded-tables --enable-libfreetype --enable-libharfbuzz --enable-libfontconfig --enable-libopenh264 --enable-libdav1d --ld=lld-link --target-os=win64 --enable-cross-compile --toolchain=msvc --host-cc=clang.exe --extra-libs=ucrt.lib --extra-libs=vcruntime.lib --extra-libs=oldnames.lib --strip=llvm-strip --disable-stripping --host-extralibs= --disable-libopenvino --enable-gpl --enable-libx264 --enable-libx265 --enable-libmp3lame --enable-libaom --enable-libsvtav1 --enable-libxml2 --enable-pic --enable-shared --disable-static --enable-version3 --enable-zlib --enable-libvorbis --enable-libopus --enable-librsvg --enable-ffplay --pkg-config=/d/bld/ffmpeg_1748704128577/_build_env/Library/bin/pkg-config
  libavutil      59. 39.100 / 59. 39.100
  libavcodec     61. 19.101 / 61. 19.101
  libavformat    61.  7.100 / 61.  7.100
  libavdevice    61.  3.100 / 61.  3.100
  libavfilter    10.  4.100 / 10.  4.100
  libswscale      8.  3.100 /  8.  3.100
  libswresample   5.  3.100 /  5.  3.100
  libpostproc    58.  3.100 / 58.  3.100
Input #0, avi, from 'C:\Users\<USER>\Desktop\curiosity_pupil\data\20250718_142425_dulu\video\dulu_facial_expression_2025-07-18_14-34-24.avi':
  Metadata:
    software        : Lavf61.7.100
  Duration: 00:00:26.83, start: 0.000000, bitrate: 77791 kb/s
  Stream #0:0: Video: mjpeg (Baseline) (MJPG / 0x47504A4D), yuvj420p(pc, bt470bg/unknown/unknown), 1920x1080, 77873 kb/s, 30 fps, 30 tbr, 30 tbn
[out#0/mp4 @ 000002CF0887DDC0] Codec AVOption b:a (set bitrate (in bits/s)) has not been used for any stream. The most likely reason is either wrong type (e.g. a video option with no video streams) or that it is a private option of some decoder which was not actually used for any stream.
Stream mapping:
  Stream #0:0 -> #0:0 (mjpeg (native) -> hevc (libx265))
Press [q] to stop, [?] for help
x265 [info]: HEVC encoder version 3.5+1-f0c1022b6
x265 [info]: build info [Windows][MSVC 1916][64 bit] 8bit+10bit+12bit
x265 [info]: using cpu capabilities: MMX2 SSE2Fast LZCNT SSSE3 SSE4.2 AVX FMA3 BMI2 AVX2
x265 [info]: Main profile, Level-4 (Main tier)
x265 [info]: Thread pool created using 8 threads
x265 [info]: Slices                              : 1
x265 [info]: frame threads / pool features       : 3 / wpp(17 rows)
x265 [info]: Coding QT: max CU size, min CU size : 64 / 8
x265 [info]: Residual QT: max TU size, max depth : 32 / 1 inter / 1 intra
x265 [info]: ME / range / subpel / merge         : hex / 57 / 2 / 3
x265 [info]: Keyframe min / max / scenecut / bias  : 25 / 250 / 40 / 5.00 
x265 [info]: Lookahead / bframes / badapt        : 20 / 4 / 2
x265 [info]: b-pyramid / weightp / weightb       : 1 / 1 / 0
x265 [info]: References / ref-limit  cu / depth  : 3 / off / on
x265 [info]: AQ: mode / str / qg-size / cu-tree  : 2 / 1.0 / 32 / 1
x265 [info]: Rate Control / qCompress            : CRF-23.0 / 0.60
x265 [info]: tools: rd=3 psy-rd=2.00 early-skip rskip mode=1 signhide tmvp
x265 [info]: tools: b-intra strong-intra-smoothing lslices=6 deblock sao
Output #0, mp4, to 'C:\Users\<USER>\Desktop\curiosity_pupil\data\20250718_142425_dulu\video_mp4\dulu_facial_expression_2025-07-18_14-34-24.mp4':
  Metadata:
    software        : Lavf61.7.100
    encoder         : Lavf61.7.100
  Stream #0:0: Video: hevc (hev1 / 0x31766568), yuvj420p(pc, bt470bg/unknown/unknown, progressive), 1920x1080, q=2-31, 30 fps, 15360 tbn
      Metadata:
        encoder         : Lavc61.19.101 libx265
      Side data:
        cpb: bitrate max/min/avg: 0/0/0 buffer size: 0 vbv_delay: N/A
frame=    0 fps=0.0 q=0.0 size=       0KiB time=N/A bitrate=N/A speed=N/A    
frame=    0 fps=0.0 q=0.0 size=       0KiB time=N/A bitrate=N/A speed=N/A    
frame=    1 fps=0.7 q=25.3 size=       0KiB time=-00:00:00.03 bitrate=N/A speed=N/A    
[vost#0:0/libx265 @ 000002CF08917D40] Error submitting a packet to the muxer: No space left on device
    Last message repeated 1 times
[out#0/mp4 @ 000002CF0887DDC0] Error muxing a packet
[out#0/mp4 @ 000002CF0887DDC0] Task finished with error code: -28 (No space left on device)
[out#0/mp4 @ 000002CF0887DDC0] Terminating thread with return code -28 (No space left on device)
[out#0/mp4 @ 000002CF0887DDC0] Error writing trailer: No space left on device
[out#0/mp4 @ 000002CF0887DDC0] Error closing file: No space left on device
[out#0/mp4 @ 000002CF0887DDC0] video:289KiB audio:0KiB subtitle:0KiB other streams:0KiB global headers:2KiB muxing overhead: unknown
frame=    2 fps=0.2 q=29.5 Lsize=       0KiB time=00:00:00.00 bitrate=N/A speed=   0x    
x265 [info]: frame I:      1, Avg QP:25.27  kb/s: 37262.16
x265 [info]: frame P:     12, Avg QP:25.92  kb/s: 26569.66
x265 [info]: frame B:     46, Avg QP:29.28  kb/s: 9363.11 
x265 [info]: Weighted P-Frames: Y:0.0% UV:0.0%
x265 [info]: consecutive B-frames: 7.7% 0.0% 0.0% 15.4% 76.9% 

encoded 59 frames in 8.44s (6.99 fps), 13335.62 kb/s, Avg QP:28.53
Conversion failed!

2025-07-21 12:58:25,272 - INFO -   处理进度: 18/30 - dulu_facial_expression_2025-07-18_14-34-55.avi
2025-07-21 12:58:25,272 - INFO - 开始转换: dulu_facial_expression_2025-07-18_14-34-55.avi
2025-07-21 12:58:34,824 - ERROR - 转换失败: dulu_facial_expression_2025-07-18_14-34-55.avi
2025-07-21 12:58:34,824 - ERROR - 错误信息: ffmpeg version 7.1.1 Copyright (c) 2000-2025 the FFmpeg developers
  built with clang version 20.1.6
  configuration: --prefix=/d/bld/ffmpeg_1748704128577/_h_env/Library --cc=clang.exe --cxx=clang++.exe --nm=llvm-nm --ar=llvm-ar --disable-doc --enable-openssl --enable-demuxer=dash --enable-hardcoded-tables --enable-libfreetype --enable-libharfbuzz --enable-libfontconfig --enable-libopenh264 --enable-libdav1d --ld=lld-link --target-os=win64 --enable-cross-compile --toolchain=msvc --host-cc=clang.exe --extra-libs=ucrt.lib --extra-libs=vcruntime.lib --extra-libs=oldnames.lib --strip=llvm-strip --disable-stripping --host-extralibs= --disable-libopenvino --enable-gpl --enable-libx264 --enable-libx265 --enable-libmp3lame --enable-libaom --enable-libsvtav1 --enable-libxml2 --enable-pic --enable-shared --disable-static --enable-version3 --enable-zlib --enable-libvorbis --enable-libopus --enable-librsvg --enable-ffplay --pkg-config=/d/bld/ffmpeg_1748704128577/_build_env/Library/bin/pkg-config
  libavutil      59. 39.100 / 59. 39.100
  libavcodec     61. 19.101 / 61. 19.101
  libavformat    61.  7.100 / 61.  7.100
  libavdevice    61.  3.100 / 61.  3.100
  libavfilter    10.  4.100 / 10.  4.100
  libswscale      8.  3.100 /  8.  3.100
  libswresample   5.  3.100 /  5.  3.100
  libpostproc    58.  3.100 / 58.  3.100
Input #0, avi, from 'C:\Users\<USER>\Desktop\curiosity_pupil\data\20250718_142425_dulu\video\dulu_facial_expression_2025-07-18_14-34-55.avi':
  Metadata:
    software        : Lavf61.7.100
  Duration: 00:00:27.07, start: 0.000000, bitrate: 78454 kb/s
  Stream #0:0: Video: mjpeg (Baseline) (MJPG / 0x47504A4D), yuvj420p(pc, bt470bg/unknown/unknown), 1920x1080, 78536 kb/s, 30 fps, 30 tbr, 30 tbn
[out#0/mp4 @ 000001B47C93A700] Codec AVOption b:a (set bitrate (in bits/s)) has not been used for any stream. The most likely reason is either wrong type (e.g. a video option with no video streams) or that it is a private option of some decoder which was not actually used for any stream.
Stream mapping:
  Stream #0:0 -> #0:0 (mjpeg (native) -> hevc (libx265))
Press [q] to stop, [?] for help
x265 [info]: HEVC encoder version 3.5+1-f0c1022b6
x265 [info]: build info [Windows][MSVC 1916][64 bit] 8bit+10bit+12bit
x265 [info]: using cpu capabilities: MMX2 SSE2Fast LZCNT SSSE3 SSE4.2 AVX FMA3 BMI2 AVX2
x265 [info]: Main profile, Level-4 (Main tier)
x265 [info]: Thread pool created using 8 threads
x265 [info]: Slices                              : 1
x265 [info]: frame threads / pool features       : 3 / wpp(17 rows)
x265 [info]: Coding QT: max CU size, min CU size : 64 / 8
x265 [info]: Residual QT: max TU size, max depth : 32 / 1 inter / 1 intra
x265 [info]: ME / range / subpel / merge         : hex / 57 / 2 / 3
x265 [info]: Keyframe min / max / scenecut / bias  : 25 / 250 / 40 / 5.00 
x265 [info]: Lookahead / bframes / badapt        : 20 / 4 / 2
x265 [info]: b-pyramid / weightp / weightb       : 1 / 1 / 0
x265 [info]: References / ref-limit  cu / depth  : 3 / off / on
x265 [info]: AQ: mode / str / qg-size / cu-tree  : 2 / 1.0 / 32 / 1
x265 [info]: Rate Control / qCompress            : CRF-23.0 / 0.60
x265 [info]: tools: rd=3 psy-rd=2.00 early-skip rskip mode=1 signhide tmvp
x265 [info]: tools: b-intra strong-intra-smoothing lslices=6 deblock sao
Output #0, mp4, to 'C:\Users\<USER>\Desktop\curiosity_pupil\data\20250718_142425_dulu\video_mp4\dulu_facial_expression_2025-07-18_14-34-55.mp4':
  Metadata:
    software        : Lavf61.7.100
    encoder         : Lavf61.7.100
  Stream #0:0: Video: hevc (hev1 / 0x31766568), yuvj420p(pc, bt470bg/unknown/unknown, progressive), 1920x1080, q=2-31, 30 fps, 15360 tbn
      Metadata:
        encoder         : Lavc61.19.101 libx265
      Side data:
        cpb: bitrate max/min/avg: 0/0/0 buffer size: 0 vbv_delay: N/A
frame=    0 fps=0.0 q=0.0 size=       0KiB time=N/A bitrate=N/A speed=N/A    
frame=    0 fps=0.0 q=0.0 size=       0KiB time=N/A bitrate=N/A speed=N/A    
[vost#0:0/libx265 @ 000001B47C9961C0] Error submitting a packet to the muxer: No space left on device
    Last message repeated 1 times
[out#0/mp4 @ 000001B47C93A700] Error muxing a packet
[out#0/mp4 @ 000001B47C93A700] Task finished with error code: -28 (No space left on device)
[out#0/mp4 @ 000001B47C93A700] Terminating thread with return code -28 (No space left on device)
[out#0/mp4 @ 000001B47C93A700] Error writing trailer: No space left on device
[out#0/mp4 @ 000001B47C93A700] Error closing file: No space left on device
[out#0/mp4 @ 000001B47C93A700] video:290KiB audio:0KiB subtitle:0KiB other streams:0KiB global headers:2KiB muxing overhead: unknown
frame=    2 fps=0.2 q=29.6 Lsize=       0KiB time=00:00:00.00 bitrate=N/A speed=   0x    
x265 [info]: frame I:      1, Avg QP:25.43  kb/s: 37458.96
x265 [info]: frame P:     12, Avg QP:26.08  kb/s: 26000.32
x265 [info]: frame B:     46, Avg QP:29.22  kb/s: 10499.62
x265 [info]: Weighted P-Frames: Y:0.0% UV:0.0%
x265 [info]: consecutive B-frames: 7.7% 0.0% 0.0% 15.4% 76.9% 

encoded 59 frames in 9.22s (6.40 fps), 14109.25 kb/s, Avg QP:28.51
Conversion failed!

2025-07-21 12:58:34,824 - INFO -   处理进度: 19/30 - dulu_facial_expression_2025-07-18_14-35-28.avi
2025-07-21 12:58:34,829 - INFO - 开始转换: dulu_facial_expression_2025-07-18_14-35-28.avi
2025-07-21 12:58:44,101 - ERROR - 转换失败: dulu_facial_expression_2025-07-18_14-35-28.avi
2025-07-21 12:58:44,101 - ERROR - 错误信息: ffmpeg version 7.1.1 Copyright (c) 2000-2025 the FFmpeg developers
  built with clang version 20.1.6
  configuration: --prefix=/d/bld/ffmpeg_1748704128577/_h_env/Library --cc=clang.exe --cxx=clang++.exe --nm=llvm-nm --ar=llvm-ar --disable-doc --enable-openssl --enable-demuxer=dash --enable-hardcoded-tables --enable-libfreetype --enable-libharfbuzz --enable-libfontconfig --enable-libopenh264 --enable-libdav1d --ld=lld-link --target-os=win64 --enable-cross-compile --toolchain=msvc --host-cc=clang.exe --extra-libs=ucrt.lib --extra-libs=vcruntime.lib --extra-libs=oldnames.lib --strip=llvm-strip --disable-stripping --host-extralibs= --disable-libopenvino --enable-gpl --enable-libx264 --enable-libx265 --enable-libmp3lame --enable-libaom --enable-libsvtav1 --enable-libxml2 --enable-pic --enable-shared --disable-static --enable-version3 --enable-zlib --enable-libvorbis --enable-libopus --enable-librsvg --enable-ffplay --pkg-config=/d/bld/ffmpeg_1748704128577/_build_env/Library/bin/pkg-config
  libavutil      59. 39.100 / 59. 39.100
  libavcodec     61. 19.101 / 61. 19.101
  libavformat    61.  7.100 / 61.  7.100
  libavdevice    61.  3.100 / 61.  3.100
  libavfilter    10.  4.100 / 10.  4.100
  libswscale      8.  3.100 /  8.  3.100
  libswresample   5.  3.100 /  5.  3.100
  libpostproc    58.  3.100 / 58.  3.100
Input #0, avi, from 'C:\Users\<USER>\Desktop\curiosity_pupil\data\20250718_142425_dulu\video\dulu_facial_expression_2025-07-18_14-35-28.avi':
  Metadata:
    software        : Lavf61.7.100
  Duration: 00:00:22.93, start: 0.000000, bitrate: 78790 kb/s
  Stream #0:0: Video: mjpeg (Baseline) (MJPG / 0x47504A4D), yuvj420p(pc, bt470bg/unknown/unknown), 1920x1080, 78888 kb/s, 30 fps, 30 tbr, 30 tbn
[out#0/mp4 @ 000002503DB8A240] Codec AVOption b:a (set bitrate (in bits/s)) has not been used for any stream. The most likely reason is either wrong type (e.g. a video option with no video streams) or that it is a private option of some decoder which was not actually used for any stream.
Stream mapping:
  Stream #0:0 -> #0:0 (mjpeg (native) -> hevc (libx265))
Press [q] to stop, [?] for help
x265 [info]: HEVC encoder version 3.5+1-f0c1022b6
x265 [info]: build info [Windows][MSVC 1916][64 bit] 8bit+10bit+12bit
x265 [info]: using cpu capabilities: MMX2 SSE2Fast LZCNT SSSE3 SSE4.2 AVX FMA3 BMI2 AVX2
x265 [info]: Main profile, Level-4 (Main tier)
x265 [info]: Thread pool created using 8 threads
x265 [info]: Slices                              : 1
x265 [info]: frame threads / pool features       : 3 / wpp(17 rows)
x265 [info]: Coding QT: max CU size, min CU size : 64 / 8
x265 [info]: Residual QT: max TU size, max depth : 32 / 1 inter / 1 intra
x265 [info]: ME / range / subpel / merge         : hex / 57 / 2 / 3
x265 [info]: Keyframe min / max / scenecut / bias  : 25 / 250 / 40 / 5.00 
x265 [info]: Lookahead / bframes / badapt        : 20 / 4 / 2
x265 [info]: b-pyramid / weightp / weightb       : 1 / 1 / 0
x265 [info]: References / ref-limit  cu / depth  : 3 / off / on
x265 [info]: AQ: mode / str / qg-size / cu-tree  : 2 / 1.0 / 32 / 1
x265 [info]: Rate Control / qCompress            : CRF-23.0 / 0.60
x265 [info]: tools: rd=3 psy-rd=2.00 early-skip rskip mode=1 signhide tmvp
x265 [info]: tools: b-intra strong-intra-smoothing lslices=6 deblock sao
Output #0, mp4, to 'C:\Users\<USER>\Desktop\curiosity_pupil\data\20250718_142425_dulu\video_mp4\dulu_facial_expression_2025-07-18_14-35-28.mp4':
  Metadata:
    software        : Lavf61.7.100
    encoder         : Lavf61.7.100
  Stream #0:0: Video: hevc (hev1 / 0x31766568), yuvj420p(pc, bt470bg/unknown/unknown, progressive), 1920x1080, q=2-31, 30 fps, 15360 tbn
      Metadata:
        encoder         : Lavc61.19.101 libx265
      Side data:
        cpb: bitrate max/min/avg: 0/0/0 buffer size: 0 vbv_delay: N/A
frame=    0 fps=0.0 q=0.0 size=       0KiB time=N/A bitrate=N/A speed=N/A    
frame=    0 fps=0.0 q=0.0 size=       0KiB time=N/A bitrate=N/A speed=N/A    
[vost#0:0/libx265 @ 000002503DBDEC80] Error submitting a packet to the muxer: No space left on device
    Last message repeated 1 times
[out#0/mp4 @ 000002503DB8A240] Error muxing a packet
[out#0/mp4 @ 000002503DB8A240] Task finished with error code: -28 (No space left on device)
[out#0/mp4 @ 000002503DB8A240] Terminating thread with return code -28 (No space left on device)
[out#0/mp4 @ 000002503DB8A240] Error writing trailer: No space left on device
[out#0/mp4 @ 000002503DB8A240] Error closing file: No space left on device
[out#0/mp4 @ 000002503DB8A240] video:293KiB audio:0KiB subtitle:0KiB other streams:0KiB global headers:2KiB muxing overhead: unknown
frame=    2 fps=0.2 q=29.6 Lsize=       0KiB time=00:00:00.00 bitrate=N/A speed=   0x    
x265 [info]: frame I:      1, Avg QP:25.42  kb/s: 37931.76
x265 [info]: frame P:     12, Avg QP:26.10  kb/s: 26073.72
x265 [info]: frame B:     46, Avg QP:29.23  kb/s: 10593.95
x265 [info]: Weighted P-Frames: Y:0.0% UV:0.0%
x265 [info]: consecutive B-frames: 7.7% 0.0% 0.0% 15.4% 76.9% 

encoded 59 frames in 8.93s (6.61 fps), 14205.73 kb/s, Avg QP:28.53
Conversion failed!

2025-07-21 12:58:44,101 - INFO -   处理进度: 20/30 - dulu_facial_expression_2025-07-18_14-35-57.avi
2025-07-21 12:58:44,101 - INFO - 开始转换: dulu_facial_expression_2025-07-18_14-35-57.avi
2025-07-21 12:58:53,315 - ERROR - 转换失败: dulu_facial_expression_2025-07-18_14-35-57.avi
2025-07-21 12:58:53,315 - ERROR - 错误信息: ffmpeg version 7.1.1 Copyright (c) 2000-2025 the FFmpeg developers
  built with clang version 20.1.6
  configuration: --prefix=/d/bld/ffmpeg_1748704128577/_h_env/Library --cc=clang.exe --cxx=clang++.exe --nm=llvm-nm --ar=llvm-ar --disable-doc --enable-openssl --enable-demuxer=dash --enable-hardcoded-tables --enable-libfreetype --enable-libharfbuzz --enable-libfontconfig --enable-libopenh264 --enable-libdav1d --ld=lld-link --target-os=win64 --enable-cross-compile --toolchain=msvc --host-cc=clang.exe --extra-libs=ucrt.lib --extra-libs=vcruntime.lib --extra-libs=oldnames.lib --strip=llvm-strip --disable-stripping --host-extralibs= --disable-libopenvino --enable-gpl --enable-libx264 --enable-libx265 --enable-libmp3lame --enable-libaom --enable-libsvtav1 --enable-libxml2 --enable-pic --enable-shared --disable-static --enable-version3 --enable-zlib --enable-libvorbis --enable-libopus --enable-librsvg --enable-ffplay --pkg-config=/d/bld/ffmpeg_1748704128577/_build_env/Library/bin/pkg-config
  libavutil      59. 39.100 / 59. 39.100
  libavcodec     61. 19.101 / 61. 19.101
  libavformat    61.  7.100 / 61.  7.100
  libavdevice    61.  3.100 / 61.  3.100
  libavfilter    10.  4.100 / 10.  4.100
  libswscale      8.  3.100 /  8.  3.100
  libswresample   5.  3.100 /  5.  3.100
  libpostproc    58.  3.100 / 58.  3.100
Input #0, avi, from 'C:\Users\<USER>\Desktop\curiosity_pupil\data\20250718_142425_dulu\video\dulu_facial_expression_2025-07-18_14-35-57.avi':
  Metadata:
    software        : Lavf61.7.100
  Duration: 00:00:28.83, start: 0.000000, bitrate: 79477 kb/s
  Stream #0:0: Video: mjpeg (Baseline) (MJPG / 0x47504A4D), yuvj420p(pc, bt470bg/unknown/unknown), 1920x1080, 79555 kb/s, 30 fps, 30 tbr, 30 tbn
[out#0/mp4 @ 000002417C2C7840] Codec AVOption b:a (set bitrate (in bits/s)) has not been used for any stream. The most likely reason is either wrong type (e.g. a video option with no video streams) or that it is a private option of some decoder which was not actually used for any stream.
Stream mapping:
  Stream #0:0 -> #0:0 (mjpeg (native) -> hevc (libx265))
Press [q] to stop, [?] for help
x265 [info]: HEVC encoder version 3.5+1-f0c1022b6
x265 [info]: build info [Windows][MSVC 1916][64 bit] 8bit+10bit+12bit
x265 [info]: using cpu capabilities: MMX2 SSE2Fast LZCNT SSSE3 SSE4.2 AVX FMA3 BMI2 AVX2
x265 [info]: Main profile, Level-4 (Main tier)
x265 [info]: Thread pool created using 8 threads
x265 [info]: Slices                              : 1
x265 [info]: frame threads / pool features       : 3 / wpp(17 rows)
x265 [info]: Coding QT: max CU size, min CU size : 64 / 8
x265 [info]: Residual QT: max TU size, max depth : 32 / 1 inter / 1 intra
x265 [info]: ME / range / subpel / merge         : hex / 57 / 2 / 3
x265 [info]: Keyframe min / max / scenecut / bias  : 25 / 250 / 40 / 5.00 
x265 [info]: Lookahead / bframes / badapt        : 20 / 4 / 2
x265 [info]: b-pyramid / weightp / weightb       : 1 / 1 / 0
x265 [info]: References / ref-limit  cu / depth  : 3 / off / on
x265 [info]: AQ: mode / str / qg-size / cu-tree  : 2 / 1.0 / 32 / 1
x265 [info]: Rate Control / qCompress            : CRF-23.0 / 0.60
x265 [info]: tools: rd=3 psy-rd=2.00 early-skip rskip mode=1 signhide tmvp
x265 [info]: tools: b-intra strong-intra-smoothing lslices=6 deblock sao
Output #0, mp4, to 'C:\Users\<USER>\Desktop\curiosity_pupil\data\20250718_142425_dulu\video_mp4\dulu_facial_expression_2025-07-18_14-35-57.mp4':
  Metadata:
    software        : Lavf61.7.100
    encoder         : Lavf61.7.100
  Stream #0:0: Video: hevc (hev1 / 0x31766568), yuvj420p(pc, bt470bg/unknown/unknown, progressive), 1920x1080, q=2-31, 30 fps, 15360 tbn
      Metadata:
        encoder         : Lavc61.19.101 libx265
      Side data:
        cpb: bitrate max/min/avg: 0/0/0 buffer size: 0 vbv_delay: N/A
frame=    0 fps=0.0 q=0.0 size=       0KiB time=N/A bitrate=N/A speed=N/A    
frame=    0 fps=0.0 q=0.0 size=       0KiB time=N/A bitrate=N/A speed=N/A    
[vost#0:0/libx265 @ 000002417C31D980] Error submitting a packet to the muxer: No space left on device
    Last message repeated 1 times
[out#0/mp4 @ 000002417C2C7840] Error muxing a packet
[out#0/mp4 @ 000002417C2C7840] Task finished with error code: -28 (No space left on device)
[out#0/mp4 @ 000002417C2C7840] Terminating thread with return code -28 (No space left on device)
[out#0/mp4 @ 000002417C2C7840] Error writing trailer: No space left on device
[out#0/mp4 @ 000002417C2C7840] Error closing file: No space left on device
[out#0/mp4 @ 000002417C2C7840] video:297KiB audio:0KiB subtitle:0KiB other streams:0KiB global headers:2KiB muxing overhead: unknown
frame=    2 fps=0.2 q=29.6 Lsize=       0KiB time=00:00:00.00 bitrate=N/A speed=   0x    
x265 [info]: frame I:      1, Avg QP:25.36  kb/s: 38534.88
x265 [info]: frame P:     12, Avg QP:26.10  kb/s: 26322.40
x265 [info]: frame B:     46, Avg QP:29.26  kb/s: 10572.33
x265 [info]: Weighted P-Frames: Y:0.0% UV:0.0%
x265 [info]: consecutive B-frames: 7.7% 0.0% 7.7% 0.0% 84.6% 

encoded 59 frames in 8.86s (6.66 fps), 14249.68 kb/s, Avg QP:28.55
Conversion failed!

2025-07-21 12:58:53,321 - INFO -   处理进度: 21/30 - dulu_facial_expression_2025-07-18_14-36-36.avi
2025-07-21 12:58:53,321 - INFO - 开始转换: dulu_facial_expression_2025-07-18_14-36-36.avi
2025-07-21 12:59:09,424 - ERROR - 转换失败: dulu_facial_expression_2025-07-18_14-36-36.avi
2025-07-21 12:59:09,424 - ERROR - 错误信息: ffmpeg version 7.1.1 Copyright (c) 2000-2025 the FFmpeg developers
  built with clang version 20.1.6
  configuration: --prefix=/d/bld/ffmpeg_1748704128577/_h_env/Library --cc=clang.exe --cxx=clang++.exe --nm=llvm-nm --ar=llvm-ar --disable-doc --enable-openssl --enable-demuxer=dash --enable-hardcoded-tables --enable-libfreetype --enable-libharfbuzz --enable-libfontconfig --enable-libopenh264 --enable-libdav1d --ld=lld-link --target-os=win64 --enable-cross-compile --toolchain=msvc --host-cc=clang.exe --extra-libs=ucrt.lib --extra-libs=vcruntime.lib --extra-libs=oldnames.lib --strip=llvm-strip --disable-stripping --host-extralibs= --disable-libopenvino --enable-gpl --enable-libx264 --enable-libx265 --enable-libmp3lame --enable-libaom --enable-libsvtav1 --enable-libxml2 --enable-pic --enable-shared --disable-static --enable-version3 --enable-zlib --enable-libvorbis --enable-libopus --enable-librsvg --enable-ffplay --pkg-config=/d/bld/ffmpeg_1748704128577/_build_env/Library/bin/pkg-config
  libavutil      59. 39.100 / 59. 39.100
  libavcodec     61. 19.101 / 61. 19.101
  libavformat    61.  7.100 / 61.  7.100
  libavdevice    61.  3.100 / 61.  3.100
  libavfilter    10.  4.100 / 10.  4.100
  libswscale      8.  3.100 /  8.  3.100
  libswresample   5.  3.100 /  5.  3.100
  libpostproc    58.  3.100 / 58.  3.100
Input #0, avi, from 'C:\Users\<USER>\Desktop\curiosity_pupil\data\20250718_142425_dulu\video\dulu_facial_expression_2025-07-18_14-36-36.avi':
  Metadata:
    software        : Lavf61.7.100
  Duration: 00:00:25.27, start: 0.000000, bitrate: 79896 kb/s
  Stream #0:0: Video: mjpeg (Baseline) (MJPG / 0x47504A4D), yuvj420p(pc, bt470bg/unknown/unknown), 1920x1080, 79987 kb/s, 30 fps, 30 tbr, 30 tbn
[out#0/mp4 @ 0000016C41E94280] Codec AVOption b:a (set bitrate (in bits/s)) has not been used for any stream. The most likely reason is either wrong type (e.g. a video option with no video streams) or that it is a private option of some decoder which was not actually used for any stream.
Stream mapping:
  Stream #0:0 -> #0:0 (mjpeg (native) -> hevc (libx265))
Press [q] to stop, [?] for help
x265 [info]: HEVC encoder version 3.5+1-f0c1022b6
x265 [info]: build info [Windows][MSVC 1916][64 bit] 8bit+10bit+12bit
x265 [info]: using cpu capabilities: MMX2 SSE2Fast LZCNT SSSE3 SSE4.2 AVX FMA3 BMI2 AVX2
x265 [info]: Main profile, Level-4 (Main tier)
x265 [info]: Thread pool created using 8 threads
x265 [info]: Slices                              : 1
x265 [info]: frame threads / pool features       : 3 / wpp(17 rows)
x265 [info]: Coding QT: max CU size, min CU size : 64 / 8
x265 [info]: Residual QT: max TU size, max depth : 32 / 1 inter / 1 intra
x265 [info]: ME / range / subpel / merge         : hex / 57 / 2 / 3
x265 [info]: Keyframe min / max / scenecut / bias  : 25 / 250 / 40 / 5.00 
x265 [info]: Lookahead / bframes / badapt        : 20 / 4 / 2
x265 [info]: b-pyramid / weightp / weightb       : 1 / 1 / 0
x265 [info]: References / ref-limit  cu / depth  : 3 / off / on
x265 [info]: AQ: mode / str / qg-size / cu-tree  : 2 / 1.0 / 32 / 1
x265 [info]: Rate Control / qCompress            : CRF-23.0 / 0.60
x265 [info]: tools: rd=3 psy-rd=2.00 early-skip rskip mode=1 signhide tmvp
x265 [info]: tools: b-intra strong-intra-smoothing lslices=6 deblock sao
Output #0, mp4, to 'C:\Users\<USER>\Desktop\curiosity_pupil\data\20250718_142425_dulu\video_mp4\dulu_facial_expression_2025-07-18_14-36-36.mp4':
  Metadata:
    software        : Lavf61.7.100
    encoder         : Lavf61.7.100
  Stream #0:0: Video: hevc (hev1 / 0x31766568), yuvj420p(pc, bt470bg/unknown/unknown, progressive), 1920x1080, q=2-31, 30 fps, 15360 tbn
      Metadata:
        encoder         : Lavc61.19.101 libx265
      Side data:
        cpb: bitrate max/min/avg: 0/0/0 buffer size: 0 vbv_delay: N/A
frame=    0 fps=0.0 q=0.0 size=       0KiB time=N/A bitrate=N/A speed=N/A    
frame=    0 fps=0.0 q=0.0 size=       0KiB time=N/A bitrate=N/A speed=N/A    
frame=    1 fps=0.7 q=25.4 size=       0KiB time=-00:00:00.03 bitrate=N/A speed=N/A    
[vost#0:0/libx265 @ 0000016C41ED8F80] Error submitting a packet to the muxer: No space left on device
    Last message repeated 1 times
[out#0/mp4 @ 0000016C41E94280] Error muxing a packet
[out#0/mp4 @ 0000016C41E94280] Task finished with error code: -28 (No space left on device)
[out#0/mp4 @ 0000016C41E94280] Terminating thread with return code -28 (No space left on device)
[out#0/mp4 @ 0000016C41E94280] Error writing trailer: No space left on device
[out#0/mp4 @ 0000016C41E94280] Error closing file: No space left on device
[out#0/mp4 @ 0000016C41E94280] video:304KiB audio:0KiB subtitle:0KiB other streams:0KiB global headers:2KiB muxing overhead: unknown
frame=    2 fps=0.1 q=29.6 Lsize=       0KiB time=00:00:00.00 bitrate=N/A speed=   0x    
x265 [info]: frame I:      1, Avg QP:25.46  kb/s: 38941.92
x265 [info]: frame P:     12, Avg QP:26.11  kb/s: 27111.12
x265 [info]: frame B:     46, Avg QP:29.27  kb/s: 10995.99
x265 [info]: Weighted P-Frames: Y:0.0% UV:0.0%
x265 [info]: consecutive B-frames: 7.7% 0.0% 0.0% 15.4% 76.9% 

encoded 59 frames in 15.65s (3.77 fps), 14747.30 kb/s, Avg QP:28.56
Conversion failed!

2025-07-21 12:59:09,424 - INFO -   处理进度: 22/30 - dulu_facial_expression_2025-07-18_14-37-06.avi
2025-07-21 12:59:09,424 - INFO - 开始转换: dulu_facial_expression_2025-07-18_14-37-06.avi
2025-07-21 12:59:18,295 - ERROR - 转换失败: dulu_facial_expression_2025-07-18_14-37-06.avi
2025-07-21 12:59:18,295 - ERROR - 错误信息: ffmpeg version 7.1.1 Copyright (c) 2000-2025 the FFmpeg developers
  built with clang version 20.1.6
  configuration: --prefix=/d/bld/ffmpeg_1748704128577/_h_env/Library --cc=clang.exe --cxx=clang++.exe --nm=llvm-nm --ar=llvm-ar --disable-doc --enable-openssl --enable-demuxer=dash --enable-hardcoded-tables --enable-libfreetype --enable-libharfbuzz --enable-libfontconfig --enable-libopenh264 --enable-libdav1d --ld=lld-link --target-os=win64 --enable-cross-compile --toolchain=msvc --host-cc=clang.exe --extra-libs=ucrt.lib --extra-libs=vcruntime.lib --extra-libs=oldnames.lib --strip=llvm-strip --disable-stripping --host-extralibs= --disable-libopenvino --enable-gpl --enable-libx264 --enable-libx265 --enable-libmp3lame --enable-libaom --enable-libsvtav1 --enable-libxml2 --enable-pic --enable-shared --disable-static --enable-version3 --enable-zlib --enable-libvorbis --enable-libopus --enable-librsvg --enable-ffplay --pkg-config=/d/bld/ffmpeg_1748704128577/_build_env/Library/bin/pkg-config
  libavutil      59. 39.100 / 59. 39.100
  libavcodec     61. 19.101 / 61. 19.101
  libavformat    61.  7.100 / 61.  7.100
  libavdevice    61.  3.100 / 61.  3.100
  libavfilter    10.  4.100 / 10.  4.100
  libswscale      8.  3.100 /  8.  3.100
  libswresample   5.  3.100 /  5.  3.100
  libpostproc    58.  3.100 / 58.  3.100
Input #0, avi, from 'C:\Users\<USER>\Desktop\curiosity_pupil\data\20250718_142425_dulu\video\dulu_facial_expression_2025-07-18_14-37-06.avi':
  Metadata:
    software        : Lavf61.7.100
  Duration: 00:00:28.13, start: 0.000000, bitrate: 80177 kb/s
  Stream #0:0: Video: mjpeg (Baseline) (MJPG / 0x47504A4D), yuvj420p(pc, bt470bg/unknown/unknown), 1920x1080, 80258 kb/s, 30 fps, 30 tbr, 30 tbn
[out#0/mp4 @ 0000022BBB92D5C0] Codec AVOption b:a (set bitrate (in bits/s)) has not been used for any stream. The most likely reason is either wrong type (e.g. a video option with no video streams) or that it is a private option of some decoder which was not actually used for any stream.
Stream mapping:
  Stream #0:0 -> #0:0 (mjpeg (native) -> hevc (libx265))
Press [q] to stop, [?] for help
x265 [info]: HEVC encoder version 3.5+1-f0c1022b6
x265 [info]: build info [Windows][MSVC 1916][64 bit] 8bit+10bit+12bit
x265 [info]: using cpu capabilities: MMX2 SSE2Fast LZCNT SSSE3 SSE4.2 AVX FMA3 BMI2 AVX2
x265 [info]: Main profile, Level-4 (Main tier)
x265 [info]: Thread pool created using 8 threads
x265 [info]: Slices                              : 1
x265 [info]: frame threads / pool features       : 3 / wpp(17 rows)
x265 [info]: Coding QT: max CU size, min CU size : 64 / 8
x265 [info]: Residual QT: max TU size, max depth : 32 / 1 inter / 1 intra
x265 [info]: ME / range / subpel / merge         : hex / 57 / 2 / 3
x265 [info]: Keyframe min / max / scenecut / bias  : 25 / 250 / 40 / 5.00 
x265 [info]: Lookahead / bframes / badapt        : 20 / 4 / 2
x265 [info]: b-pyramid / weightp / weightb       : 1 / 1 / 0
x265 [info]: References / ref-limit  cu / depth  : 3 / off / on
x265 [info]: AQ: mode / str / qg-size / cu-tree  : 2 / 1.0 / 32 / 1
x265 [info]: Rate Control / qCompress            : CRF-23.0 / 0.60
x265 [info]: tools: rd=3 psy-rd=2.00 early-skip rskip mode=1 signhide tmvp
x265 [info]: tools: b-intra strong-intra-smoothing lslices=6 deblock sao
Output #0, mp4, to 'C:\Users\<USER>\Desktop\curiosity_pupil\data\20250718_142425_dulu\video_mp4\dulu_facial_expression_2025-07-18_14-37-06.mp4':
  Metadata:
    software        : Lavf61.7.100
    encoder         : Lavf61.7.100
  Stream #0:0: Video: hevc (hev1 / 0x31766568), yuvj420p(pc, bt470bg/unknown/unknown, progressive), 1920x1080, q=2-31, 30 fps, 15360 tbn
      Metadata:
        encoder         : Lavc61.19.101 libx265
      Side data:
        cpb: bitrate max/min/avg: 0/0/0 buffer size: 0 vbv_delay: N/A
frame=    0 fps=0.0 q=0.0 size=       0KiB time=N/A bitrate=N/A speed=N/A    
frame=    0 fps=0.0 q=0.0 size=       0KiB time=N/A bitrate=N/A speed=N/A    
[vost#0:0/libx265 @ 0000022BBD82C9C0] Error submitting a packet to the muxer: No space left on device
    Last message repeated 1 times
[out#0/mp4 @ 0000022BBB92D5C0] Error muxing a packet
[out#0/mp4 @ 0000022BBB92D5C0] Task finished with error code: -28 (No space left on device)
[out#0/mp4 @ 0000022BBB92D5C0] Terminating thread with return code -28 (No space left on device)
[out#0/mp4 @ 0000022BBB92D5C0] Error writing trailer: No space left on device
[out#0/mp4 @ 0000022BBB92D5C0] Error closing file: No space left on device
[out#0/mp4 @ 0000022BBB92D5C0] video:302KiB audio:0KiB subtitle:0KiB other streams:0KiB global headers:2KiB muxing overhead: unknown
frame=    2 fps=0.2 q=29.6 Lsize=       0KiB time=00:00:00.00 bitrate=N/A speed=   0x    
x265 [info]: frame I:      1, Avg QP:25.38  kb/s: 39150.24
x265 [info]: frame P:     12, Avg QP:26.10  kb/s: 26857.96
x265 [info]: frame B:     46, Avg QP:29.27  kb/s: 10898.67
x265 [info]: Weighted P-Frames: Y:0.0% UV:0.0%
x265 [info]: consecutive B-frames: 7.7% 0.0% 0.0% 15.4% 76.9% 

encoded 59 frames in 8.53s (6.92 fps), 14623.46 kb/s, Avg QP:28.56
Conversion failed!

2025-07-21 12:59:18,295 - INFO -   处理进度: 23/30 - dulu_facial_expression_2025-07-18_14-37-37.avi
2025-07-21 12:59:18,295 - INFO - 开始转换: dulu_facial_expression_2025-07-18_14-37-37.avi
2025-07-21 12:59:27,481 - ERROR - 转换失败: dulu_facial_expression_2025-07-18_14-37-37.avi
2025-07-21 12:59:27,481 - ERROR - 错误信息: ffmpeg version 7.1.1 Copyright (c) 2000-2025 the FFmpeg developers
  built with clang version 20.1.6
  configuration: --prefix=/d/bld/ffmpeg_1748704128577/_h_env/Library --cc=clang.exe --cxx=clang++.exe --nm=llvm-nm --ar=llvm-ar --disable-doc --enable-openssl --enable-demuxer=dash --enable-hardcoded-tables --enable-libfreetype --enable-libharfbuzz --enable-libfontconfig --enable-libopenh264 --enable-libdav1d --ld=lld-link --target-os=win64 --enable-cross-compile --toolchain=msvc --host-cc=clang.exe --extra-libs=ucrt.lib --extra-libs=vcruntime.lib --extra-libs=oldnames.lib --strip=llvm-strip --disable-stripping --host-extralibs= --disable-libopenvino --enable-gpl --enable-libx264 --enable-libx265 --enable-libmp3lame --enable-libaom --enable-libsvtav1 --enable-libxml2 --enable-pic --enable-shared --disable-static --enable-version3 --enable-zlib --enable-libvorbis --enable-libopus --enable-librsvg --enable-ffplay --pkg-config=/d/bld/ffmpeg_1748704128577/_build_env/Library/bin/pkg-config
  libavutil      59. 39.100 / 59. 39.100
  libavcodec     61. 19.101 / 61. 19.101
  libavformat    61.  7.100 / 61.  7.100
  libavdevice    61.  3.100 / 61.  3.100
  libavfilter    10.  4.100 / 10.  4.100
  libswscale      8.  3.100 /  8.  3.100
  libswresample   5.  3.100 /  5.  3.100
  libpostproc    58.  3.100 / 58.  3.100
Input #0, avi, from 'C:\Users\<USER>\Desktop\curiosity_pupil\data\20250718_142425_dulu\video\dulu_facial_expression_2025-07-18_14-37-37.avi':
  Metadata:
    software        : Lavf61.7.100
  Duration: 00:00:25.70, start: 0.000000, bitrate: 80594 kb/s
  Stream #0:0: Video: mjpeg (Baseline) (MJPG / 0x47504A4D), yuvj420p(pc, bt470bg/unknown/unknown), 1920x1080, 80684 kb/s, 30 fps, 30 tbr, 30 tbn
[out#0/mp4 @ 000001971007C540] Codec AVOption b:a (set bitrate (in bits/s)) has not been used for any stream. The most likely reason is either wrong type (e.g. a video option with no video streams) or that it is a private option of some decoder which was not actually used for any stream.
Stream mapping:
  Stream #0:0 -> #0:0 (mjpeg (native) -> hevc (libx265))
Press [q] to stop, [?] for help
x265 [info]: HEVC encoder version 3.5+1-f0c1022b6
x265 [info]: build info [Windows][MSVC 1916][64 bit] 8bit+10bit+12bit
x265 [info]: using cpu capabilities: MMX2 SSE2Fast LZCNT SSSE3 SSE4.2 AVX FMA3 BMI2 AVX2
x265 [info]: Main profile, Level-4 (Main tier)
x265 [info]: Thread pool created using 8 threads
x265 [info]: Slices                              : 1
x265 [info]: frame threads / pool features       : 3 / wpp(17 rows)
x265 [info]: Coding QT: max CU size, min CU size : 64 / 8
x265 [info]: Residual QT: max TU size, max depth : 32 / 1 inter / 1 intra
x265 [info]: ME / range / subpel / merge         : hex / 57 / 2 / 3
x265 [info]: Keyframe min / max / scenecut / bias  : 25 / 250 / 40 / 5.00 
x265 [info]: Lookahead / bframes / badapt        : 20 / 4 / 2
x265 [info]: b-pyramid / weightp / weightb       : 1 / 1 / 0
x265 [info]: References / ref-limit  cu / depth  : 3 / off / on
x265 [info]: AQ: mode / str / qg-size / cu-tree  : 2 / 1.0 / 32 / 1
x265 [info]: Rate Control / qCompress            : CRF-23.0 / 0.60
x265 [info]: tools: rd=3 psy-rd=2.00 early-skip rskip mode=1 signhide tmvp
x265 [info]: tools: b-intra strong-intra-smoothing lslices=6 deblock sao
Output #0, mp4, to 'C:\Users\<USER>\Desktop\curiosity_pupil\data\20250718_142425_dulu\video_mp4\dulu_facial_expression_2025-07-18_14-37-37.mp4':
  Metadata:
    software        : Lavf61.7.100
    encoder         : Lavf61.7.100
  Stream #0:0: Video: hevc (hev1 / 0x31766568), yuvj420p(pc, bt470bg/unknown/unknown, progressive), 1920x1080, q=2-31, 30 fps, 15360 tbn
      Metadata:
        encoder         : Lavc61.19.101 libx265
      Side data:
        cpb: bitrate max/min/avg: 0/0/0 buffer size: 0 vbv_delay: N/A
frame=    0 fps=0.0 q=0.0 size=       0KiB time=N/A bitrate=N/A speed=N/A    
frame=    0 fps=0.0 q=0.0 size=       0KiB time=N/A bitrate=N/A speed=N/A    
frame=    0 fps=0.0 q=0.0 size=       0KiB time=N/A bitrate=N/A speed=N/A    
[vost#0:0/libx265 @ 00000197100C7040] Error submitting a packet to the muxer: No space left on device
    Last message repeated 1 times
[out#0/mp4 @ 000001971007C540] Error muxing a packet
[out#0/mp4 @ 000001971007C540] Task finished with error code: -28 (No space left on device)
[out#0/mp4 @ 000001971007C540] Terminating thread with return code -28 (No space left on device)
[out#0/mp4 @ 000001971007C540] Error writing trailer: No space left on device
[out#0/mp4 @ 000001971007C540] Error closing file: No space left on device
[out#0/mp4 @ 000001971007C540] video:304KiB audio:0KiB subtitle:0KiB other streams:0KiB global headers:2KiB muxing overhead: unknown
frame=    2 fps=0.2 q=29.6 Lsize=       0KiB time=00:00:00.00 bitrate=N/A speed=   0x    
x265 [info]: frame I:      1, Avg QP:25.45  kb/s: 39446.40
x265 [info]: frame P:     12, Avg QP:26.09  kb/s: 27127.52
x265 [info]: frame B:     46, Avg QP:29.29  kb/s: 10947.51
x265 [info]: Weighted P-Frames: Y:0.0% UV:0.0%
x265 [info]: consecutive B-frames: 7.7% 0.0% 7.7% 0.0% 84.6% 

encoded 59 frames in 8.86s (6.66 fps), 14721.39 kb/s, Avg QP:28.58
Conversion failed!

2025-07-21 12:59:27,497 - INFO -   处理进度: 24/30 - dulu_facial_expression_2025-07-18_14-38-06.avi
2025-07-21 12:59:27,499 - INFO - 开始转换: dulu_facial_expression_2025-07-18_14-38-06.avi
2025-07-21 12:59:37,043 - ERROR - 转换失败: dulu_facial_expression_2025-07-18_14-38-06.avi
2025-07-21 12:59:37,043 - ERROR - 错误信息: ffmpeg version 7.1.1 Copyright (c) 2000-2025 the FFmpeg developers
  built with clang version 20.1.6
  configuration: --prefix=/d/bld/ffmpeg_1748704128577/_h_env/Library --cc=clang.exe --cxx=clang++.exe --nm=llvm-nm --ar=llvm-ar --disable-doc --enable-openssl --enable-demuxer=dash --enable-hardcoded-tables --enable-libfreetype --enable-libharfbuzz --enable-libfontconfig --enable-libopenh264 --enable-libdav1d --ld=lld-link --target-os=win64 --enable-cross-compile --toolchain=msvc --host-cc=clang.exe --extra-libs=ucrt.lib --extra-libs=vcruntime.lib --extra-libs=oldnames.lib --strip=llvm-strip --disable-stripping --host-extralibs= --disable-libopenvino --enable-gpl --enable-libx264 --enable-libx265 --enable-libmp3lame --enable-libaom --enable-libsvtav1 --enable-libxml2 --enable-pic --enable-shared --disable-static --enable-version3 --enable-zlib --enable-libvorbis --enable-libopus --enable-librsvg --enable-ffplay --pkg-config=/d/bld/ffmpeg_1748704128577/_build_env/Library/bin/pkg-config
  libavutil      59. 39.100 / 59. 39.100
  libavcodec     61. 19.101 / 61. 19.101
  libavformat    61.  7.100 / 61.  7.100
  libavdevice    61.  3.100 / 61.  3.100
  libavfilter    10.  4.100 / 10.  4.100
  libswscale      8.  3.100 /  8.  3.100
  libswresample   5.  3.100 /  5.  3.100
  libpostproc    58.  3.100 / 58.  3.100
Input #0, avi, from 'C:\Users\<USER>\Desktop\curiosity_pupil\data\20250718_142425_dulu\video\dulu_facial_expression_2025-07-18_14-38-06.avi':
  Metadata:
    software        : Lavf61.7.100
  Duration: 00:00:43.80, start: 0.000000, bitrate: 80965 kb/s
  Stream #0:0: Video: mjpeg (Baseline) (MJPG / 0x47504A4D), yuvj420p(pc, bt470bg/unknown/unknown), 1920x1080, 81015 kb/s, 30 fps, 30 tbr, 30 tbn
[out#0/mp4 @ 000002077EEB5FC0] Codec AVOption b:a (set bitrate (in bits/s)) has not been used for any stream. The most likely reason is either wrong type (e.g. a video option with no video streams) or that it is a private option of some decoder which was not actually used for any stream.
Stream mapping:
  Stream #0:0 -> #0:0 (mjpeg (native) -> hevc (libx265))
Press [q] to stop, [?] for help
x265 [info]: HEVC encoder version 3.5+1-f0c1022b6
x265 [info]: build info [Windows][MSVC 1916][64 bit] 8bit+10bit+12bit
x265 [info]: using cpu capabilities: MMX2 SSE2Fast LZCNT SSSE3 SSE4.2 AVX FMA3 BMI2 AVX2
x265 [info]: Main profile, Level-4 (Main tier)
x265 [info]: Thread pool created using 8 threads
x265 [info]: Slices                              : 1
x265 [info]: frame threads / pool features       : 3 / wpp(17 rows)
x265 [info]: Coding QT: max CU size, min CU size : 64 / 8
x265 [info]: Residual QT: max TU size, max depth : 32 / 1 inter / 1 intra
x265 [info]: ME / range / subpel / merge         : hex / 57 / 2 / 3
x265 [info]: Keyframe min / max / scenecut / bias  : 25 / 250 / 40 / 5.00 
x265 [info]: Lookahead / bframes / badapt        : 20 / 4 / 2
x265 [info]: b-pyramid / weightp / weightb       : 1 / 1 / 0
x265 [info]: References / ref-limit  cu / depth  : 3 / off / on
x265 [info]: AQ: mode / str / qg-size / cu-tree  : 2 / 1.0 / 32 / 1
x265 [info]: Rate Control / qCompress            : CRF-23.0 / 0.60
x265 [info]: tools: rd=3 psy-rd=2.00 early-skip rskip mode=1 signhide tmvp
x265 [info]: tools: b-intra strong-intra-smoothing lslices=6 deblock sao
Output #0, mp4, to 'C:\Users\<USER>\Desktop\curiosity_pupil\data\20250718_142425_dulu\video_mp4\dulu_facial_expression_2025-07-18_14-38-06.mp4':
  Metadata:
    software        : Lavf61.7.100
    encoder         : Lavf61.7.100
  Stream #0:0: Video: hevc (hev1 / 0x31766568), yuvj420p(pc, bt470bg/unknown/unknown, progressive), 1920x1080, q=2-31, 30 fps, 15360 tbn
      Metadata:
        encoder         : Lavc61.19.101 libx265
      Side data:
        cpb: bitrate max/min/avg: 0/0/0 buffer size: 0 vbv_delay: N/A
frame=    0 fps=0.0 q=0.0 size=       0KiB time=N/A bitrate=N/A speed=N/A    
frame=    0 fps=0.0 q=0.0 size=       0KiB time=N/A bitrate=N/A speed=N/A    
[vost#0:0/libx265 @ 000002077EEFA340] Error submitting a packet to the muxer: No space left on device
    Last message repeated 1 times
[out#0/mp4 @ 000002077EEB5FC0] Error muxing a packet
[out#0/mp4 @ 000002077EEB5FC0] Task finished with error code: -28 (No space left on device)
[out#0/mp4 @ 000002077EEB5FC0] Terminating thread with return code -28 (No space left on device)
[out#0/mp4 @ 000002077EEB5FC0] Error writing trailer: No space left on device
[out#0/mp4 @ 000002077EEB5FC0] Error closing file: No space left on device
[out#0/mp4 @ 000002077EEB5FC0] video:307KiB audio:0KiB subtitle:0KiB other streams:0KiB global headers:2KiB muxing overhead: unknown
frame=    2 fps=0.2 q=29.6 Lsize=       0KiB time=00:00:00.00 bitrate=N/A speed=   0x    
x265 [info]: frame I:      1, Avg QP:25.47  kb/s: 39774.48
x265 [info]: frame P:     12, Avg QP:26.11  kb/s: 27923.76
x265 [info]: frame B:     46, Avg QP:29.31  kb/s: 11252.44
x265 [info]: Weighted P-Frames: Y:0.0% UV:0.0%
x265 [info]: consecutive B-frames: 7.7% 0.0% 7.7% 0.0% 84.6% 

encoded 59 frames in 9.22s (6.40 fps), 15126.64 kb/s, Avg QP:28.59
Conversion failed!

2025-07-21 12:59:37,043 - INFO -   处理进度: 25/30 - dulu_facial_expression_2025-07-18_14-38-58.avi
2025-07-21 12:59:37,043 - INFO - 开始转换: dulu_facial_expression_2025-07-18_14-38-58.avi
2025-07-21 12:59:46,584 - ERROR - 转换失败: dulu_facial_expression_2025-07-18_14-38-58.avi
2025-07-21 12:59:46,584 - ERROR - 错误信息: ffmpeg version 7.1.1 Copyright (c) 2000-2025 the FFmpeg developers
  built with clang version 20.1.6
  configuration: --prefix=/d/bld/ffmpeg_1748704128577/_h_env/Library --cc=clang.exe --cxx=clang++.exe --nm=llvm-nm --ar=llvm-ar --disable-doc --enable-openssl --enable-demuxer=dash --enable-hardcoded-tables --enable-libfreetype --enable-libharfbuzz --enable-libfontconfig --enable-libopenh264 --enable-libdav1d --ld=lld-link --target-os=win64 --enable-cross-compile --toolchain=msvc --host-cc=clang.exe --extra-libs=ucrt.lib --extra-libs=vcruntime.lib --extra-libs=oldnames.lib --strip=llvm-strip --disable-stripping --host-extralibs= --disable-libopenvino --enable-gpl --enable-libx264 --enable-libx265 --enable-libmp3lame --enable-libaom --enable-libsvtav1 --enable-libxml2 --enable-pic --enable-shared --disable-static --enable-version3 --enable-zlib --enable-libvorbis --enable-libopus --enable-librsvg --enable-ffplay --pkg-config=/d/bld/ffmpeg_1748704128577/_build_env/Library/bin/pkg-config
  libavutil      59. 39.100 / 59. 39.100
  libavcodec     61. 19.101 / 61. 19.101
  libavformat    61.  7.100 / 61.  7.100
  libavdevice    61.  3.100 / 61.  3.100
  libavfilter    10.  4.100 / 10.  4.100
  libswscale      8.  3.100 /  8.  3.100
  libswresample   5.  3.100 /  5.  3.100
  libpostproc    58.  3.100 / 58.  3.100
Input #0, avi, from 'C:\Users\<USER>\Desktop\curiosity_pupil\data\20250718_142425_dulu\video\dulu_facial_expression_2025-07-18_14-38-58.avi':
  Metadata:
    software        : Lavf61.7.100
  Duration: 00:00:24.33, start: 0.000000, bitrate: 81087 kb/s
  Stream #0:0: Video: mjpeg (Baseline) (MJPG / 0x47504A4D), yuvj420p(pc, bt470bg/unknown/unknown), 1920x1080, 81183 kb/s, 30 fps, 30 tbr, 30 tbn
[out#0/mp4 @ 00000222E9C2D980] Codec AVOption b:a (set bitrate (in bits/s)) has not been used for any stream. The most likely reason is either wrong type (e.g. a video option with no video streams) or that it is a private option of some decoder which was not actually used for any stream.
Stream mapping:
  Stream #0:0 -> #0:0 (mjpeg (native) -> hevc (libx265))
Press [q] to stop, [?] for help
x265 [info]: HEVC encoder version 3.5+1-f0c1022b6
x265 [info]: build info [Windows][MSVC 1916][64 bit] 8bit+10bit+12bit
x265 [info]: using cpu capabilities: MMX2 SSE2Fast LZCNT SSSE3 SSE4.2 AVX FMA3 BMI2 AVX2
x265 [info]: Main profile, Level-4 (Main tier)
x265 [info]: Thread pool created using 8 threads
x265 [info]: Slices                              : 1
x265 [info]: frame threads / pool features       : 3 / wpp(17 rows)
x265 [info]: Coding QT: max CU size, min CU size : 64 / 8
x265 [info]: Residual QT: max TU size, max depth : 32 / 1 inter / 1 intra
x265 [info]: ME / range / subpel / merge         : hex / 57 / 2 / 3
x265 [info]: Keyframe min / max / scenecut / bias  : 25 / 250 / 40 / 5.00 
x265 [info]: Lookahead / bframes / badapt        : 20 / 4 / 2
x265 [info]: b-pyramid / weightp / weightb       : 1 / 1 / 0
x265 [info]: References / ref-limit  cu / depth  : 3 / off / on
x265 [info]: AQ: mode / str / qg-size / cu-tree  : 2 / 1.0 / 32 / 1
x265 [info]: Rate Control / qCompress            : CRF-23.0 / 0.60
x265 [info]: tools: rd=3 psy-rd=2.00 early-skip rskip mode=1 signhide tmvp
x265 [info]: tools: b-intra strong-intra-smoothing lslices=6 deblock sao
Output #0, mp4, to 'C:\Users\<USER>\Desktop\curiosity_pupil\data\20250718_142425_dulu\video_mp4\dulu_facial_expression_2025-07-18_14-38-58.mp4':
  Metadata:
    software        : Lavf61.7.100
    encoder         : Lavf61.7.100
  Stream #0:0: Video: hevc (hev1 / 0x31766568), yuvj420p(pc, bt470bg/unknown/unknown, progressive), 1920x1080, q=2-31, 30 fps, 15360 tbn
      Metadata:
        encoder         : Lavc61.19.101 libx265
      Side data:
        cpb: bitrate max/min/avg: 0/0/0 buffer size: 0 vbv_delay: N/A
frame=    0 fps=0.0 q=0.0 size=       0KiB time=N/A bitrate=N/A speed=N/A    
frame=    0 fps=0.0 q=0.0 size=       0KiB time=N/A bitrate=N/A speed=N/A    
[vost#0:0/libx265 @ 00000222E9C8DE00] Error submitting a packet to the muxer: No space left on device
    Last message repeated 1 times
[out#0/mp4 @ 00000222E9C2D980] Error muxing a packet
[out#0/mp4 @ 00000222E9C2D980] Task finished with error code: -28 (No space left on device)
[out#0/mp4 @ 00000222E9C2D980] Terminating thread with return code -28 (No space left on device)
[out#0/mp4 @ 00000222E9C2D980] Error writing trailer: No space left on device
[out#0/mp4 @ 00000222E9C2D980] Error closing file: No space left on device
[out#0/mp4 @ 00000222E9C2D980] video:308KiB audio:0KiB subtitle:0KiB other streams:0KiB global headers:2KiB muxing overhead: unknown
frame=    2 fps=0.2 q=29.6 Lsize=       0KiB time=00:00:00.00 bitrate=N/A speed=   0x    
x265 [info]: frame I:      1, Avg QP:25.44  kb/s: 40024.80
x265 [info]: frame P:     12, Avg QP:26.11  kb/s: 27721.12
x265 [info]: frame B:     46, Avg QP:29.30  kb/s: 11296.20
x265 [info]: Weighted P-Frames: Y:0.0% UV:0.0%
x265 [info]: consecutive B-frames: 7.7% 0.0% 0.0% 15.4% 76.9% 

encoded 59 frames in 9.17s (6.44 fps), 15123.79 kb/s, Avg QP:28.59
Conversion failed!

2025-07-21 12:59:46,584 - INFO -   处理进度: 26/30 - dulu_facial_expression_2025-07-18_14-39-27.avi
2025-07-21 12:59:46,584 - INFO - 开始转换: dulu_facial_expression_2025-07-18_14-39-27.avi
2025-07-21 12:59:56,077 - ERROR - 转换失败: dulu_facial_expression_2025-07-18_14-39-27.avi
2025-07-21 12:59:56,077 - ERROR - 错误信息: ffmpeg version 7.1.1 Copyright (c) 2000-2025 the FFmpeg developers
  built with clang version 20.1.6
  configuration: --prefix=/d/bld/ffmpeg_1748704128577/_h_env/Library --cc=clang.exe --cxx=clang++.exe --nm=llvm-nm --ar=llvm-ar --disable-doc --enable-openssl --enable-demuxer=dash --enable-hardcoded-tables --enable-libfreetype --enable-libharfbuzz --enable-libfontconfig --enable-libopenh264 --enable-libdav1d --ld=lld-link --target-os=win64 --enable-cross-compile --toolchain=msvc --host-cc=clang.exe --extra-libs=ucrt.lib --extra-libs=vcruntime.lib --extra-libs=oldnames.lib --strip=llvm-strip --disable-stripping --host-extralibs= --disable-libopenvino --enable-gpl --enable-libx264 --enable-libx265 --enable-libmp3lame --enable-libaom --enable-libsvtav1 --enable-libxml2 --enable-pic --enable-shared --disable-static --enable-version3 --enable-zlib --enable-libvorbis --enable-libopus --enable-librsvg --enable-ffplay --pkg-config=/d/bld/ffmpeg_1748704128577/_build_env/Library/bin/pkg-config
  libavutil      59. 39.100 / 59. 39.100
  libavcodec     61. 19.101 / 61. 19.101
  libavformat    61.  7.100 / 61.  7.100
  libavdevice    61.  3.100 / 61.  3.100
  libavfilter    10.  4.100 / 10.  4.100
  libswscale      8.  3.100 /  8.  3.100
  libswresample   5.  3.100 /  5.  3.100
  libpostproc    58.  3.100 / 58.  3.100
Input #0, avi, from 'C:\Users\<USER>\Desktop\curiosity_pupil\data\20250718_142425_dulu\video\dulu_facial_expression_2025-07-18_14-39-27.avi':
  Metadata:
    software        : Lavf61.7.100
  Duration: 00:00:22.97, start: 0.000000, bitrate: 81190 kb/s
  Stream #0:0: Video: mjpeg (Baseline) (MJPG / 0x47504A4D), yuvj420p(pc, bt470bg/unknown/unknown), 1920x1080, 81292 kb/s, 30 fps, 30 tbr, 30 tbn
[out#0/mp4 @ 000001C7DDF989C0] Codec AVOption b:a (set bitrate (in bits/s)) has not been used for any stream. The most likely reason is either wrong type (e.g. a video option with no video streams) or that it is a private option of some decoder which was not actually used for any stream.
Stream mapping:
  Stream #0:0 -> #0:0 (mjpeg (native) -> hevc (libx265))
Press [q] to stop, [?] for help
x265 [info]: HEVC encoder version 3.5+1-f0c1022b6
x265 [info]: build info [Windows][MSVC 1916][64 bit] 8bit+10bit+12bit
x265 [info]: using cpu capabilities: MMX2 SSE2Fast LZCNT SSSE3 SSE4.2 AVX FMA3 BMI2 AVX2
x265 [info]: Main profile, Level-4 (Main tier)
x265 [info]: Thread pool created using 8 threads
x265 [info]: Slices                              : 1
x265 [info]: frame threads / pool features       : 3 / wpp(17 rows)
x265 [info]: Coding QT: max CU size, min CU size : 64 / 8
x265 [info]: Residual QT: max TU size, max depth : 32 / 1 inter / 1 intra
x265 [info]: ME / range / subpel / merge         : hex / 57 / 2 / 3
x265 [info]: Keyframe min / max / scenecut / bias  : 25 / 250 / 40 / 5.00 
x265 [info]: Lookahead / bframes / badapt        : 20 / 4 / 2
x265 [info]: b-pyramid / weightp / weightb       : 1 / 1 / 0
x265 [info]: References / ref-limit  cu / depth  : 3 / off / on
x265 [info]: AQ: mode / str / qg-size / cu-tree  : 2 / 1.0 / 32 / 1
x265 [info]: Rate Control / qCompress            : CRF-23.0 / 0.60
x265 [info]: tools: rd=3 psy-rd=2.00 early-skip rskip mode=1 signhide tmvp
x265 [info]: tools: b-intra strong-intra-smoothing lslices=6 deblock sao
Output #0, mp4, to 'C:\Users\<USER>\Desktop\curiosity_pupil\data\20250718_142425_dulu\video_mp4\dulu_facial_expression_2025-07-18_14-39-27.mp4':
  Metadata:
    software        : Lavf61.7.100
    encoder         : Lavf61.7.100
  Stream #0:0: Video: hevc (hev1 / 0x31766568), yuvj420p(pc, bt470bg/unknown/unknown, progressive), 1920x1080, q=2-31, 30 fps, 15360 tbn
      Metadata:
        encoder         : Lavc61.19.101 libx265
      Side data:
        cpb: bitrate max/min/avg: 0/0/0 buffer size: 0 vbv_delay: N/A
frame=    0 fps=0.0 q=0.0 size=       0KiB time=N/A bitrate=N/A speed=N/A    
frame=    0 fps=0.0 q=0.0 size=       0KiB time=N/A bitrate=N/A speed=N/A    
[vost#0:0/libx265 @ 000001C7DDFF2B00] Error submitting a packet to the muxer: No space left on device
    Last message repeated 1 times
[out#0/mp4 @ 000001C7DDF989C0] Error muxing a packet
[out#0/mp4 @ 000001C7DDF989C0] Task finished with error code: -28 (No space left on device)
[out#0/mp4 @ 000001C7DDF989C0] Terminating thread with return code -28 (No space left on device)
[out#0/mp4 @ 000001C7DDF989C0] Error writing trailer: No space left on device
[out#0/mp4 @ 000001C7DDF989C0] Error closing file: No space left on device
[out#0/mp4 @ 000001C7DDF989C0] video:308KiB audio:0KiB subtitle:0KiB other streams:0KiB global headers:2KiB muxing overhead: unknown
frame=    2 fps=0.2 q=29.6 Lsize=       0KiB time=00:00:00.00 bitrate=N/A speed=   0x    
x265 [info]: frame I:      1, Avg QP:25.48  kb/s: 39979.20
x265 [info]: frame P:     12, Avg QP:26.10  kb/s: 27392.82
x265 [info]: frame B:     46, Avg QP:29.31  kb/s: 11152.75
x265 [info]: Weighted P-Frames: Y:0.0% UV:0.0%
x265 [info]: consecutive B-frames: 7.7% 0.0% 0.0% 15.4% 76.9% 

encoded 59 frames in 9.14s (6.45 fps), 14944.40 kb/s, Avg QP:28.59
Conversion failed!

2025-07-21 12:59:56,087 - INFO -   处理进度: 27/30 - dulu_facial_expression_2025-07-18_14-39-53.avi
2025-07-21 12:59:56,089 - INFO - 开始转换: dulu_facial_expression_2025-07-18_14-39-53.avi
2025-07-21 13:00:05,329 - ERROR - 转换失败: dulu_facial_expression_2025-07-18_14-39-53.avi
2025-07-21 13:00:05,329 - ERROR - 错误信息: ffmpeg version 7.1.1 Copyright (c) 2000-2025 the FFmpeg developers
  built with clang version 20.1.6
  configuration: --prefix=/d/bld/ffmpeg_1748704128577/_h_env/Library --cc=clang.exe --cxx=clang++.exe --nm=llvm-nm --ar=llvm-ar --disable-doc --enable-openssl --enable-demuxer=dash --enable-hardcoded-tables --enable-libfreetype --enable-libharfbuzz --enable-libfontconfig --enable-libopenh264 --enable-libdav1d --ld=lld-link --target-os=win64 --enable-cross-compile --toolchain=msvc --host-cc=clang.exe --extra-libs=ucrt.lib --extra-libs=vcruntime.lib --extra-libs=oldnames.lib --strip=llvm-strip --disable-stripping --host-extralibs= --disable-libopenvino --enable-gpl --enable-libx264 --enable-libx265 --enable-libmp3lame --enable-libaom --enable-libsvtav1 --enable-libxml2 --enable-pic --enable-shared --disable-static --enable-version3 --enable-zlib --enable-libvorbis --enable-libopus --enable-librsvg --enable-ffplay --pkg-config=/d/bld/ffmpeg_1748704128577/_build_env/Library/bin/pkg-config
  libavutil      59. 39.100 / 59. 39.100
  libavcodec     61. 19.101 / 61. 19.101
  libavformat    61.  7.100 / 61.  7.100
  libavdevice    61.  3.100 / 61.  3.100
  libavfilter    10.  4.100 / 10.  4.100
  libswscale      8.  3.100 /  8.  3.100
  libswresample   5.  3.100 /  5.  3.100
  libpostproc    58.  3.100 / 58.  3.100
Input #0, avi, from 'C:\Users\<USER>\Desktop\curiosity_pupil\data\20250718_142425_dulu\video\dulu_facial_expression_2025-07-18_14-39-53.avi':
  Metadata:
    software        : Lavf61.7.100
  Duration: 00:00:26.43, start: 0.000000, bitrate: 81508 kb/s
  Stream #0:0: Video: mjpeg (Baseline) (MJPG / 0x47504A4D), yuvj420p(pc, bt470bg/unknown/unknown), 1920x1080, 81596 kb/s, 30 fps, 30 tbr, 30 tbn
[out#0/mp4 @ 0000020958EC00C0] Codec AVOption b:a (set bitrate (in bits/s)) has not been used for any stream. The most likely reason is either wrong type (e.g. a video option with no video streams) or that it is a private option of some decoder which was not actually used for any stream.
Stream mapping:
  Stream #0:0 -> #0:0 (mjpeg (native) -> hevc (libx265))
Press [q] to stop, [?] for help
x265 [info]: HEVC encoder version 3.5+1-f0c1022b6
x265 [info]: build info [Windows][MSVC 1916][64 bit] 8bit+10bit+12bit
x265 [info]: using cpu capabilities: MMX2 SSE2Fast LZCNT SSSE3 SSE4.2 AVX FMA3 BMI2 AVX2
x265 [info]: Main profile, Level-4 (Main tier)
x265 [info]: Thread pool created using 8 threads
x265 [info]: Slices                              : 1
x265 [info]: frame threads / pool features       : 3 / wpp(17 rows)
x265 [info]: Coding QT: max CU size, min CU size : 64 / 8
x265 [info]: Residual QT: max TU size, max depth : 32 / 1 inter / 1 intra
x265 [info]: ME / range / subpel / merge         : hex / 57 / 2 / 3
x265 [info]: Keyframe min / max / scenecut / bias  : 25 / 250 / 40 / 5.00 
x265 [info]: Lookahead / bframes / badapt        : 20 / 4 / 2
x265 [info]: b-pyramid / weightp / weightb       : 1 / 1 / 0
x265 [info]: References / ref-limit  cu / depth  : 3 / off / on
x265 [info]: AQ: mode / str / qg-size / cu-tree  : 2 / 1.0 / 32 / 1
x265 [info]: Rate Control / qCompress            : CRF-23.0 / 0.60
x265 [info]: tools: rd=3 psy-rd=2.00 early-skip rskip mode=1 signhide tmvp
x265 [info]: tools: b-intra strong-intra-smoothing lslices=6 deblock sao
Output #0, mp4, to 'C:\Users\<USER>\Desktop\curiosity_pupil\data\20250718_142425_dulu\video_mp4\dulu_facial_expression_2025-07-18_14-39-53.mp4':
  Metadata:
    software        : Lavf61.7.100
    encoder         : Lavf61.7.100
  Stream #0:0: Video: hevc (hev1 / 0x31766568), yuvj420p(pc, bt470bg/unknown/unknown, progressive), 1920x1080, q=2-31, 30 fps, 15360 tbn
      Metadata:
        encoder         : Lavc61.19.101 libx265
      Side data:
        cpb: bitrate max/min/avg: 0/0/0 buffer size: 0 vbv_delay: N/A
frame=    0 fps=0.0 q=0.0 size=       0KiB time=N/A bitrate=N/A speed=N/A    
frame=    0 fps=0.0 q=0.0 size=       0KiB time=N/A bitrate=N/A speed=N/A    
[vost#0:0/libx265 @ 0000020958F19A40] Error submitting a packet to the muxer: No space left on device
    Last message repeated 1 times
[out#0/mp4 @ 0000020958EC00C0] Error muxing a packet
[out#0/mp4 @ 0000020958EC00C0] Task finished with error code: -28 (No space left on device)
[out#0/mp4 @ 0000020958EC00C0] Terminating thread with return code -28 (No space left on device)
[out#0/mp4 @ 0000020958EC00C0] Error writing trailer: No space left on device
[out#0/mp4 @ 0000020958EC00C0] Error closing file: No space left on device
[out#0/mp4 @ 0000020958EC00C0] video:309KiB audio:0KiB subtitle:0KiB other streams:0KiB global headers:2KiB muxing overhead: unknown
frame=    2 fps=0.2 q=29.6 Lsize=       0KiB time=00:00:00.00 bitrate=N/A speed=   0x    
x265 [info]: frame I:      1, Avg QP:25.40  kb/s: 40243.20
x265 [info]: frame P:     12, Avg QP:26.10  kb/s: 27306.16
x265 [info]: frame B:     46, Avg QP:29.32  kb/s: 11233.57
x265 [info]: Weighted P-Frames: Y:0.0% UV:0.0%
x265 [info]: consecutive B-frames: 7.7% 0.0% 0.0% 15.4% 76.9% 

encoded 59 frames in 8.89s (6.63 fps), 14994.26 kb/s, Avg QP:28.60
Conversion failed!

2025-07-21 13:00:05,334 - INFO -   处理进度: 28/30 - dulu_facial_expression_2025-07-18_14-40-25.avi
2025-07-21 13:00:05,334 - INFO - 开始转换: dulu_facial_expression_2025-07-18_14-40-25.avi
2025-07-21 13:00:14,795 - ERROR - 转换失败: dulu_facial_expression_2025-07-18_14-40-25.avi
2025-07-21 13:00:14,795 - ERROR - 错误信息: ffmpeg version 7.1.1 Copyright (c) 2000-2025 the FFmpeg developers
  built with clang version 20.1.6
  configuration: --prefix=/d/bld/ffmpeg_1748704128577/_h_env/Library --cc=clang.exe --cxx=clang++.exe --nm=llvm-nm --ar=llvm-ar --disable-doc --enable-openssl --enable-demuxer=dash --enable-hardcoded-tables --enable-libfreetype --enable-libharfbuzz --enable-libfontconfig --enable-libopenh264 --enable-libdav1d --ld=lld-link --target-os=win64 --enable-cross-compile --toolchain=msvc --host-cc=clang.exe --extra-libs=ucrt.lib --extra-libs=vcruntime.lib --extra-libs=oldnames.lib --strip=llvm-strip --disable-stripping --host-extralibs= --disable-libopenvino --enable-gpl --enable-libx264 --enable-libx265 --enable-libmp3lame --enable-libaom --enable-libsvtav1 --enable-libxml2 --enable-pic --enable-shared --disable-static --enable-version3 --enable-zlib --enable-libvorbis --enable-libopus --enable-librsvg --enable-ffplay --pkg-config=/d/bld/ffmpeg_1748704128577/_build_env/Library/bin/pkg-config
  libavutil      59. 39.100 / 59. 39.100
  libavcodec     61. 19.101 / 61. 19.101
  libavformat    61.  7.100 / 61.  7.100
  libavdevice    61.  3.100 / 61.  3.100
  libavfilter    10.  4.100 / 10.  4.100
  libswscale      8.  3.100 /  8.  3.100
  libswresample   5.  3.100 /  5.  3.100
  libpostproc    58.  3.100 / 58.  3.100
Input #0, avi, from 'C:\Users\<USER>\Desktop\curiosity_pupil\data\20250718_142425_dulu\video\dulu_facial_expression_2025-07-18_14-40-25.avi':
  Metadata:
    software        : Lavf61.7.100
  Duration: 00:00:19.27, start: 0.000000, bitrate: 81602 kb/s
  Stream #0:0: Video: mjpeg (Baseline) (MJPG / 0x47504A4D), yuvj420p(pc, bt470bg/unknown/unknown), 1920x1080, 81726 kb/s, 30 fps, 30 tbr, 30 tbn
[out#0/mp4 @ 000002670E467740] Codec AVOption b:a (set bitrate (in bits/s)) has not been used for any stream. The most likely reason is either wrong type (e.g. a video option with no video streams) or that it is a private option of some decoder which was not actually used for any stream.
Stream mapping:
  Stream #0:0 -> #0:0 (mjpeg (native) -> hevc (libx265))
Press [q] to stop, [?] for help
x265 [info]: HEVC encoder version 3.5+1-f0c1022b6
x265 [info]: build info [Windows][MSVC 1916][64 bit] 8bit+10bit+12bit
x265 [info]: using cpu capabilities: MMX2 SSE2Fast LZCNT SSSE3 SSE4.2 AVX FMA3 BMI2 AVX2
x265 [info]: Main profile, Level-4 (Main tier)
x265 [info]: Thread pool created using 8 threads
x265 [info]: Slices                              : 1
x265 [info]: frame threads / pool features       : 3 / wpp(17 rows)
x265 [info]: Coding QT: max CU size, min CU size : 64 / 8
x265 [info]: Residual QT: max TU size, max depth : 32 / 1 inter / 1 intra
x265 [info]: ME / range / subpel / merge         : hex / 57 / 2 / 3
x265 [info]: Keyframe min / max / scenecut / bias  : 25 / 250 / 40 / 5.00 
x265 [info]: Lookahead / bframes / badapt        : 20 / 4 / 2
x265 [info]: b-pyramid / weightp / weightb       : 1 / 1 / 0
x265 [info]: References / ref-limit  cu / depth  : 3 / off / on
x265 [info]: AQ: mode / str / qg-size / cu-tree  : 2 / 1.0 / 32 / 1
x265 [info]: Rate Control / qCompress            : CRF-23.0 / 0.60
x265 [info]: tools: rd=3 psy-rd=2.00 early-skip rskip mode=1 signhide tmvp
x265 [info]: tools: b-intra strong-intra-smoothing lslices=6 deblock sao
Output #0, mp4, to 'C:\Users\<USER>\Desktop\curiosity_pupil\data\20250718_142425_dulu\video_mp4\dulu_facial_expression_2025-07-18_14-40-25.mp4':
  Metadata:
    software        : Lavf61.7.100
    encoder         : Lavf61.7.100
  Stream #0:0: Video: hevc (hev1 / 0x31766568), yuvj420p(pc, bt470bg/unknown/unknown, progressive), 1920x1080, q=2-31, 30 fps, 15360 tbn
      Metadata:
        encoder         : Lavc61.19.101 libx265
      Side data:
        cpb: bitrate max/min/avg: 0/0/0 buffer size: 0 vbv_delay: N/A
frame=    0 fps=0.0 q=0.0 size=       0KiB time=N/A bitrate=N/A speed=N/A    
frame=    0 fps=0.0 q=0.0 size=       0KiB time=N/A bitrate=N/A speed=N/A    
[vost#0:0/libx265 @ 000002670E4F34C0] Error submitting a packet to the muxer: No space left on device
    Last message repeated 1 times
[out#0/mp4 @ 000002670E467740] Error muxing a packet
[out#0/mp4 @ 000002670E467740] Task finished with error code: -28 (No space left on device)
[out#0/mp4 @ 000002670E467740] Terminating thread with return code -28 (No space left on device)
[out#0/mp4 @ 000002670E467740] Error writing trailer: No space left on device
[out#0/mp4 @ 000002670E467740] Error closing file: No space left on device
[out#0/mp4 @ 000002670E467740] video:312KiB audio:0KiB subtitle:0KiB other streams:0KiB global headers:2KiB muxing overhead: unknown
frame=    2 fps=0.2 q=29.6 Lsize=       0KiB time=00:00:00.00 bitrate=N/A speed=   0x    
x265 [info]: frame I:      1, Avg QP:25.46  kb/s: 40404.96
x265 [info]: frame P:     12, Avg QP:26.12  kb/s: 27671.82
x265 [info]: frame B:     46, Avg QP:29.32  kb/s: 11281.23
x265 [info]: Weighted P-Frames: Y:0.0% UV:0.0%
x265 [info]: consecutive B-frames: 7.7% 0.0% 0.0% 15.4% 76.9% 

encoded 59 frames in 9.02s (6.54 fps), 15108.53 kb/s, Avg QP:28.60
Conversion failed!

2025-07-21 13:00:14,795 - INFO -   处理进度: 29/30 - dulu_facial_expression_2025-07-18_14-40-53.avi
2025-07-21 13:00:14,795 - INFO - 开始转换: dulu_facial_expression_2025-07-18_14-40-53.avi
2025-07-21 13:00:24,032 - ERROR - 转换失败: dulu_facial_expression_2025-07-18_14-40-53.avi
2025-07-21 13:00:24,032 - ERROR - 错误信息: ffmpeg version 7.1.1 Copyright (c) 2000-2025 the FFmpeg developers
  built with clang version 20.1.6
  configuration: --prefix=/d/bld/ffmpeg_1748704128577/_h_env/Library --cc=clang.exe --cxx=clang++.exe --nm=llvm-nm --ar=llvm-ar --disable-doc --enable-openssl --enable-demuxer=dash --enable-hardcoded-tables --enable-libfreetype --enable-libharfbuzz --enable-libfontconfig --enable-libopenh264 --enable-libdav1d --ld=lld-link --target-os=win64 --enable-cross-compile --toolchain=msvc --host-cc=clang.exe --extra-libs=ucrt.lib --extra-libs=vcruntime.lib --extra-libs=oldnames.lib --strip=llvm-strip --disable-stripping --host-extralibs= --disable-libopenvino --enable-gpl --enable-libx264 --enable-libx265 --enable-libmp3lame --enable-libaom --enable-libsvtav1 --enable-libxml2 --enable-pic --enable-shared --disable-static --enable-version3 --enable-zlib --enable-libvorbis --enable-libopus --enable-librsvg --enable-ffplay --pkg-config=/d/bld/ffmpeg_1748704128577/_build_env/Library/bin/pkg-config
  libavutil      59. 39.100 / 59. 39.100
  libavcodec     61. 19.101 / 61. 19.101
  libavformat    61.  7.100 / 61.  7.100
  libavdevice    61.  3.100 / 61.  3.100
  libavfilter    10.  4.100 / 10.  4.100
  libswscale      8.  3.100 /  8.  3.100
  libswresample   5.  3.100 /  5.  3.100
  libpostproc    58.  3.100 / 58.  3.100
Input #0, avi, from 'C:\Users\<USER>\Desktop\curiosity_pupil\data\20250718_142425_dulu\video\dulu_facial_expression_2025-07-18_14-40-53.avi':
  Metadata:
    software        : Lavf61.7.100
  Duration: 00:00:23.80, start: 0.000000, bitrate: 81771 kb/s
  Stream #0:0: Video: mjpeg (Baseline) (MJPG / 0x47504A4D), yuvj420p(pc, bt470bg/unknown/unknown), 1920x1080, 81870 kb/s, 30 fps, 30 tbr, 30 tbn
[out#0/mp4 @ 0000026DA8E56300] Codec AVOption b:a (set bitrate (in bits/s)) has not been used for any stream. The most likely reason is either wrong type (e.g. a video option with no video streams) or that it is a private option of some decoder which was not actually used for any stream.
Stream mapping:
  Stream #0:0 -> #0:0 (mjpeg (native) -> hevc (libx265))
Press [q] to stop, [?] for help
x265 [info]: HEVC encoder version 3.5+1-f0c1022b6
x265 [info]: build info [Windows][MSVC 1916][64 bit] 8bit+10bit+12bit
x265 [info]: using cpu capabilities: MMX2 SSE2Fast LZCNT SSSE3 SSE4.2 AVX FMA3 BMI2 AVX2
x265 [info]: Main profile, Level-4 (Main tier)
x265 [info]: Thread pool created using 8 threads
x265 [info]: Slices                              : 1
x265 [info]: frame threads / pool features       : 3 / wpp(17 rows)
x265 [info]: Coding QT: max CU size, min CU size : 64 / 8
x265 [info]: Residual QT: max TU size, max depth : 32 / 1 inter / 1 intra
x265 [info]: ME / range / subpel / merge         : hex / 57 / 2 / 3
x265 [info]: Keyframe min / max / scenecut / bias  : 25 / 250 / 40 / 5.00 
x265 [info]: Lookahead / bframes / badapt        : 20 / 4 / 2
x265 [info]: b-pyramid / weightp / weightb       : 1 / 1 / 0
x265 [info]: References / ref-limit  cu / depth  : 3 / off / on
x265 [info]: AQ: mode / str / qg-size / cu-tree  : 2 / 1.0 / 32 / 1
x265 [info]: Rate Control / qCompress            : CRF-23.0 / 0.60
x265 [info]: tools: rd=3 psy-rd=2.00 early-skip rskip mode=1 signhide tmvp
x265 [info]: tools: b-intra strong-intra-smoothing lslices=6 deblock sao
Output #0, mp4, to 'C:\Users\<USER>\Desktop\curiosity_pupil\data\20250718_142425_dulu\video_mp4\dulu_facial_expression_2025-07-18_14-40-53.mp4':
  Metadata:
    software        : Lavf61.7.100
    encoder         : Lavf61.7.100
  Stream #0:0: Video: hevc (hev1 / 0x31766568), yuvj420p(pc, bt470bg/unknown/unknown, progressive), 1920x1080, q=2-31, 30 fps, 15360 tbn
      Metadata:
        encoder         : Lavc61.19.101 libx265
      Side data:
        cpb: bitrate max/min/avg: 0/0/0 buffer size: 0 vbv_delay: N/A
frame=    0 fps=0.0 q=0.0 size=       0KiB time=N/A bitrate=N/A speed=N/A    
frame=    0 fps=0.0 q=0.0 size=       0KiB time=N/A bitrate=N/A speed=N/A    
[vost#0:0/libx265 @ 0000026DA8E8AD40] Error submitting a packet to the muxer: No space left on device
    Last message repeated 1 times
[out#0/mp4 @ 0000026DA8E56300] Error muxing a packet
[out#0/mp4 @ 0000026DA8E56300] Task finished with error code: -28 (No space left on device)
[out#0/mp4 @ 0000026DA8E56300] Terminating thread with return code -28 (No space left on device)
[out#0/mp4 @ 0000026DA8E56300] Error writing trailer: No space left on device
[out#0/mp4 @ 0000026DA8E56300] Error closing file: No space left on device
[out#0/mp4 @ 0000026DA8E56300] video:312KiB audio:0KiB subtitle:0KiB other streams:0KiB global headers:2KiB muxing overhead: unknown
frame=    2 fps=0.2 q=29.6 Lsize=       0KiB time=00:00:00.00 bitrate=N/A speed=   0x    
x265 [info]: frame I:      1, Avg QP:25.50  kb/s: 40464.00
x265 [info]: frame P:     12, Avg QP:26.10  kb/s: 27728.86
x265 [info]: frame B:     46, Avg QP:29.32  kb/s: 11266.10
x265 [info]: Weighted P-Frames: Y:0.0% UV:0.0%
x265 [info]: consecutive B-frames: 7.7% 0.0% 0.0% 15.4% 76.9% 

encoded 59 frames in 8.89s (6.63 fps), 15109.33 kb/s, Avg QP:28.60
Conversion failed!

2025-07-21 13:00:24,032 - INFO -   处理进度: 30/30 - dulu_facial_expression_2025-07-18_14-41-20.avi
2025-07-21 13:00:24,032 - INFO - 开始转换: dulu_facial_expression_2025-07-18_14-41-20.avi
2025-07-21 13:00:33,846 - ERROR - 转换失败: dulu_facial_expression_2025-07-18_14-41-20.avi
2025-07-21 13:00:33,846 - ERROR - 错误信息: ffmpeg version 7.1.1 Copyright (c) 2000-2025 the FFmpeg developers
  built with clang version 20.1.6
  configuration: --prefix=/d/bld/ffmpeg_1748704128577/_h_env/Library --cc=clang.exe --cxx=clang++.exe --nm=llvm-nm --ar=llvm-ar --disable-doc --enable-openssl --enable-demuxer=dash --enable-hardcoded-tables --enable-libfreetype --enable-libharfbuzz --enable-libfontconfig --enable-libopenh264 --enable-libdav1d --ld=lld-link --target-os=win64 --enable-cross-compile --toolchain=msvc --host-cc=clang.exe --extra-libs=ucrt.lib --extra-libs=vcruntime.lib --extra-libs=oldnames.lib --strip=llvm-strip --disable-stripping --host-extralibs= --disable-libopenvino --enable-gpl --enable-libx264 --enable-libx265 --enable-libmp3lame --enable-libaom --enable-libsvtav1 --enable-libxml2 --enable-pic --enable-shared --disable-static --enable-version3 --enable-zlib --enable-libvorbis --enable-libopus --enable-librsvg --enable-ffplay --pkg-config=/d/bld/ffmpeg_1748704128577/_build_env/Library/bin/pkg-config
  libavutil      59. 39.100 / 59. 39.100
  libavcodec     61. 19.101 / 61. 19.101
  libavformat    61.  7.100 / 61.  7.100
  libavdevice    61.  3.100 / 61.  3.100
  libavfilter    10.  4.100 / 10.  4.100
  libswscale      8.  3.100 /  8.  3.100
  libswresample   5.  3.100 /  5.  3.100
  libpostproc    58.  3.100 / 58.  3.100
Input #0, avi, from 'C:\Users\<USER>\Desktop\curiosity_pupil\data\20250718_142425_dulu\video\dulu_facial_expression_2025-07-18_14-41-20.avi':
  Metadata:
    software        : Lavf61.7.100
  Duration: 00:00:36.80, start: 0.000000, bitrate: 81984 kb/s
  Stream #0:0: Video: mjpeg (Baseline) (MJPG / 0x47504A4D), yuvj420p(pc, bt470bg/unknown/unknown), 1920x1080, 82047 kb/s, 30 fps, 30 tbr, 30 tbn
[out#0/mp4 @ 00000222B205E440] Codec AVOption b:a (set bitrate (in bits/s)) has not been used for any stream. The most likely reason is either wrong type (e.g. a video option with no video streams) or that it is a private option of some decoder which was not actually used for any stream.
Stream mapping:
  Stream #0:0 -> #0:0 (mjpeg (native) -> hevc (libx265))
Press [q] to stop, [?] for help
x265 [info]: HEVC encoder version 3.5+1-f0c1022b6
x265 [info]: build info [Windows][MSVC 1916][64 bit] 8bit+10bit+12bit
x265 [info]: using cpu capabilities: MMX2 SSE2Fast LZCNT SSSE3 SSE4.2 AVX FMA3 BMI2 AVX2
x265 [info]: Main profile, Level-4 (Main tier)
x265 [info]: Thread pool created using 8 threads
x265 [info]: Slices                              : 1
x265 [info]: frame threads / pool features       : 3 / wpp(17 rows)
x265 [info]: Coding QT: max CU size, min CU size : 64 / 8
x265 [info]: Residual QT: max TU size, max depth : 32 / 1 inter / 1 intra
x265 [info]: ME / range / subpel / merge         : hex / 57 / 2 / 3
x265 [info]: Keyframe min / max / scenecut / bias  : 25 / 250 / 40 / 5.00 
x265 [info]: Lookahead / bframes / badapt        : 20 / 4 / 2
x265 [info]: b-pyramid / weightp / weightb       : 1 / 1 / 0
x265 [info]: References / ref-limit  cu / depth  : 3 / off / on
x265 [info]: AQ: mode / str / qg-size / cu-tree  : 2 / 1.0 / 32 / 1
x265 [info]: Rate Control / qCompress            : CRF-23.0 / 0.60
x265 [info]: tools: rd=3 psy-rd=2.00 early-skip rskip mode=1 signhide tmvp
x265 [info]: tools: b-intra strong-intra-smoothing lslices=6 deblock sao
Output #0, mp4, to 'C:\Users\<USER>\Desktop\curiosity_pupil\data\20250718_142425_dulu\video_mp4\dulu_facial_expression_2025-07-18_14-41-20.mp4':
  Metadata:
    software        : Lavf61.7.100
    encoder         : Lavf61.7.100
  Stream #0:0: Video: hevc (hev1 / 0x31766568), yuvj420p(pc, bt470bg/unknown/unknown, progressive), 1920x1080, q=2-31, 30 fps, 15360 tbn
      Metadata:
        encoder         : Lavc61.19.101 libx265
      Side data:
        cpb: bitrate max/min/avg: 0/0/0 buffer size: 0 vbv_delay: N/A
frame=    0 fps=0.0 q=0.0 size=       0KiB time=N/A bitrate=N/A speed=N/A    
frame=    0 fps=0.0 q=0.0 size=       0KiB time=N/A bitrate=N/A speed=N/A    
[vost#0:0/libx265 @ 00000222B20B3EC0] Error submitting a packet to the muxer: No space left on device
    Last message repeated 1 times
[out#0/mp4 @ 00000222B205E440] Error muxing a packet
[out#0/mp4 @ 00000222B205E440] Task finished with error code: -28 (No space left on device)
[out#0/mp4 @ 00000222B205E440] Terminating thread with return code -28 (No space left on device)
[out#0/mp4 @ 00000222B205E440] Error writing trailer: No space left on device
[out#0/mp4 @ 00000222B205E440] Error closing file: No space left on device
[out#0/mp4 @ 00000222B205E440] video:313KiB audio:0KiB subtitle:0KiB other streams:0KiB global headers:2KiB muxing overhead: unknown
frame=    2 fps=0.2 q=29.6 Lsize=       0KiB time=00:00:00.00 bitrate=N/A speed=   0x    
x265 [info]: frame I:      1, Avg QP:25.45  kb/s: 40901.76
x265 [info]: frame P:     12, Avg QP:26.09  kb/s: 27655.40
x265 [info]: frame B:     46, Avg QP:29.34  kb/s: 11217.91
x265 [info]: Weighted P-Frames: Y:0.0% UV:0.0%
x265 [info]: consecutive B-frames: 7.7% 0.0% 7.7% 0.0% 84.6% 

encoded 59 frames in 9.47s (6.23 fps), 15064.24 kb/s, Avg QP:28.61
Conversion failed!

2025-07-21 13:00:33,846 - INFO -   该目录转换完成: 7/30 个文件
2025-07-21 13:00:33,846 - INFO - 处理video目录: C:\Users\<USER>\Desktop\curiosity_pupil\data\20250718_145240_dulu\video
2025-07-21 13:00:33,846 - INFO -   找到 30 个AVI文件
2025-07-21 13:00:33,855 - INFO -   处理进度: 1/30 - dulu_facial_expression_2025-07-18_14-53-36.avi
2025-07-21 13:00:33,856 - INFO - 开始转换: dulu_facial_expression_2025-07-18_14-53-36.avi
2025-07-21 13:00:40,300 - ERROR - 转换失败: dulu_facial_expression_2025-07-18_14-53-36.avi
2025-07-21 13:00:40,300 - ERROR - 错误信息: ffmpeg version 7.1.1 Copyright (c) 2000-2025 the FFmpeg developers
  built with clang version 20.1.6
  configuration: --prefix=/d/bld/ffmpeg_1748704128577/_h_env/Library --cc=clang.exe --cxx=clang++.exe --nm=llvm-nm --ar=llvm-ar --disable-doc --enable-openssl --enable-demuxer=dash --enable-hardcoded-tables --enable-libfreetype --enable-libharfbuzz --enable-libfontconfig --enable-libopenh264 --enable-libdav1d --ld=lld-link --target-os=win64 --enable-cross-compile --toolchain=msvc --host-cc=clang.exe --extra-libs=ucrt.lib --extra-libs=vcruntime.lib --extra-libs=oldnames.lib --strip=llvm-strip --disable-stripping --host-extralibs= --disable-libopenvino --enable-gpl --enable-libx264 --enable-libx265 --enable-libmp3lame --enable-libaom --enable-libsvtav1 --enable-libxml2 --enable-pic --enable-shared --disable-static --enable-version3 --enable-zlib --enable-libvorbis --enable-libopus --enable-librsvg --enable-ffplay --pkg-config=/d/bld/ffmpeg_1748704128577/_build_env/Library/bin/pkg-config
  libavutil      59. 39.100 / 59. 39.100
  libavcodec     61. 19.101 / 61. 19.101
  libavformat    61.  7.100 / 61.  7.100
  libavdevice    61.  3.100 / 61.  3.100
  libavfilter    10.  4.100 / 10.  4.100
  libswscale      8.  3.100 /  8.  3.100
  libswresample   5.  3.100 /  5.  3.100
  libpostproc    58.  3.100 / 58.  3.100
Input #0, avi, from 'C:\Users\<USER>\Desktop\curiosity_pupil\data\20250718_145240_dulu\video\dulu_facial_expression_2025-07-18_14-53-36.avi':
  Metadata:
    software        : Lavf61.7.100
  Duration: 00:00:29.17, start: 0.000000, bitrate: 43562 kb/s
  Stream #0:0: Video: mjpeg (Baseline) (MJPG / 0x47504A4D), yuvj420p(pc, bt470bg/unknown/unknown), 1920x1080, 43598 kb/s, 30 fps, 30 tbr, 30 tbn
[out#0/mp4 @ 000001E461349340] Codec AVOption b:a (set bitrate (in bits/s)) has not been used for any stream. The most likely reason is either wrong type (e.g. a video option with no video streams) or that it is a private option of some decoder which was not actually used for any stream.
Stream mapping:
  Stream #0:0 -> #0:0 (mjpeg (native) -> hevc (libx265))
Press [q] to stop, [?] for help
x265 [info]: HEVC encoder version 3.5+1-f0c1022b6
x265 [info]: build info [Windows][MSVC 1916][64 bit] 8bit+10bit+12bit
x265 [info]: using cpu capabilities: MMX2 SSE2Fast LZCNT SSSE3 SSE4.2 AVX FMA3 BMI2 AVX2
x265 [info]: Main profile, Level-4 (Main tier)
x265 [info]: Thread pool created using 8 threads
x265 [info]: Slices                              : 1
x265 [info]: frame threads / pool features       : 3 / wpp(17 rows)
x265 [info]: Coding QT: max CU size, min CU size : 64 / 8
x265 [info]: Residual QT: max TU size, max depth : 32 / 1 inter / 1 intra
x265 [info]: ME / range / subpel / merge         : hex / 57 / 2 / 3
x265 [info]: Keyframe min / max / scenecut / bias  : 25 / 250 / 40 / 5.00 
x265 [info]: Lookahead / bframes / badapt        : 20 / 4 / 2
x265 [info]: b-pyramid / weightp / weightb       : 1 / 1 / 0
x265 [info]: References / ref-limit  cu / depth  : 3 / off / on
x265 [info]: AQ: mode / str / qg-size / cu-tree  : 2 / 1.0 / 32 / 1
x265 [info]: Rate Control / qCompress            : CRF-23.0 / 0.60
x265 [info]: tools: rd=3 psy-rd=2.00 early-skip rskip mode=1 signhide tmvp
x265 [info]: tools: b-intra strong-intra-smoothing lslices=6 deblock sao
Output #0, mp4, to 'C:\Users\<USER>\Desktop\curiosity_pupil\data\20250718_145240_dulu\video_mp4\dulu_facial_expression_2025-07-18_14-53-36.mp4':
  Metadata:
    software        : Lavf61.7.100
    encoder         : Lavf61.7.100
  Stream #0:0: Video: hevc (hev1 / 0x31766568), yuvj420p(pc, bt470bg/unknown/unknown, progressive), 1920x1080, q=2-31, 30 fps, 15360 tbn
      Metadata:
        encoder         : Lavc61.19.101 libx265
      Side data:
        cpb: bitrate max/min/avg: 0/0/0 buffer size: 0 vbv_delay: N/A
frame=    0 fps=0.0 q=0.0 size=       0KiB time=N/A bitrate=N/A speed=N/A    
frame=    0 fps=0.0 q=0.0 size=       0KiB time=N/A bitrate=N/A speed=N/A    
frame=    5 fps=3.3 q=28.8 size=       0KiB time=00:00:00.10 bitrate=   3.5kbits/s speed=0.0662x    
[vost#0:0/libx265 @ 000001E4613B07C0] Error submitting a packet to the muxer: No space left on device
    Last message repeated 1 times
[out#0/mp4 @ 000001E461349340] Error muxing a packet
[out#0/mp4 @ 000001E461349340] Task finished with error code: -28 (No space left on device)
[out#0/mp4 @ 000001E461349340] Terminating thread with return code -28 (No space left on device)
[out#0/mp4 @ 000001E461349340] Error writing trailer: No space left on device
[out#0/mp4 @ 000001E461349340] Error closing file: No space left on device
[out#0/mp4 @ 000001E461349340] video:263KiB audio:0KiB subtitle:0KiB other streams:0KiB global headers:2KiB muxing overhead: unknown
frame=   10 fps=1.6 q=28.8 Lsize=       0KiB time=00:00:00.26 bitrate=   1.3kbits/s speed=0.0431x    
x265 [info]: frame I:      1, Avg QP:24.24  kb/s: 17529.84
x265 [info]: frame P:     13, Avg QP:25.57  kb/s: 10031.24
x265 [info]: frame B:     52, Avg QP:28.51  kb/s: 2692.40 
x265 [info]: Weighted P-Frames: Y:0.0% UV:0.0%
x265 [info]: consecutive B-frames: 7.1% 0.0% 0.0% 0.0% 92.9% 

encoded 66 frames in 6.16s (10.71 fps), 4362.74 kb/s, Avg QP:27.87
Conversion failed!

2025-07-21 13:00:40,300 - INFO -   处理进度: 2/30 - dulu_facial_expression_2025-07-18_14-54-11.avi
2025-07-21 13:00:40,300 - INFO - 开始转换: dulu_facial_expression_2025-07-18_14-54-11.avi
2025-07-21 13:00:40,526 - ERROR - 转换失败: dulu_facial_expression_2025-07-18_14-54-11.avi
2025-07-21 13:00:40,526 - ERROR - 错误信息: ffmpeg version 7.1.1 Copyright (c) 2000-2025 the FFmpeg developers
  built with clang version 20.1.6
  configuration: --prefix=/d/bld/ffmpeg_1748704128577/_h_env/Library --cc=clang.exe --cxx=clang++.exe --nm=llvm-nm --ar=llvm-ar --disable-doc --enable-openssl --enable-demuxer=dash --enable-hardcoded-tables --enable-libfreetype --enable-libharfbuzz --enable-libfontconfig --enable-libopenh264 --enable-libdav1d --ld=lld-link --target-os=win64 --enable-cross-compile --toolchain=msvc --host-cc=clang.exe --extra-libs=ucrt.lib --extra-libs=vcruntime.lib --extra-libs=oldnames.lib --strip=llvm-strip --disable-stripping --host-extralibs= --disable-libopenvino --enable-gpl --enable-libx264 --enable-libx265 --enable-libmp3lame --enable-libaom --enable-libsvtav1 --enable-libxml2 --enable-pic --enable-shared --disable-static --enable-version3 --enable-zlib --enable-libvorbis --enable-libopus --enable-librsvg --enable-ffplay --pkg-config=/d/bld/ffmpeg_1748704128577/_build_env/Library/bin/pkg-config
  libavutil      59. 39.100 / 59. 39.100
  libavcodec     61. 19.101 / 61. 19.101
  libavformat    61.  7.100 / 61.  7.100
  libavdevice    61.  3.100 / 61.  3.100
  libavfilter    10.  4.100 / 10.  4.100
  libswscale      8.  3.100 /  8.  3.100
  libswresample   5.  3.100 /  5.  3.100
  libpostproc    58.  3.100 / 58.  3.100
Input #0, avi, from 'C:\Users\<USER>\Desktop\curiosity_pupil\data\20250718_145240_dulu\video\dulu_facial_expression_2025-07-18_14-54-11.avi':
  Metadata:
    software        : Lavf61.7.100
  Duration: 00:00:28.00, start: 0.000000, bitrate: 43818 kb/s
  Stream #0:0: Video: mjpeg (Baseline) (MJPG / 0x47504A4D), yuvj420p(pc, bt470bg/unknown/unknown), 1920x1080, 43856 kb/s, 30 fps, 30 tbr, 30 tbn
[out#0/mp4 @ 0000020EE4A57E40] Codec AVOption b:a (set bitrate (in bits/s)) has not been used for any stream. The most likely reason is either wrong type (e.g. a video option with no video streams) or that it is a private option of some decoder which was not actually used for any stream.
[out#0/mp4 @ 0000020EE4A57E40] Error opening output C:\Users\<USER>\Desktop\curiosity_pupil\data\20250718_145240_dulu\video_mp4\dulu_facial_expression_2025-07-18_14-54-11.mp4: No space left on device
Error opening output file C:\Users\<USER>\Desktop\curiosity_pupil\data\20250718_145240_dulu\video_mp4\dulu_facial_expression_2025-07-18_14-54-11.mp4.
Error opening output files: No space left on device

2025-07-21 13:00:40,529 - INFO -   处理进度: 3/30 - dulu_facial_expression_2025-07-18_14-54-40.avi
2025-07-21 13:00:40,529 - INFO - 开始转换: dulu_facial_expression_2025-07-18_14-54-40.avi
2025-07-21 13:00:40,747 - ERROR - 转换失败: dulu_facial_expression_2025-07-18_14-54-40.avi
2025-07-21 13:00:40,747 - ERROR - 错误信息: ffmpeg version 7.1.1 Copyright (c) 2000-2025 the FFmpeg developers
  built with clang version 20.1.6
  configuration: --prefix=/d/bld/ffmpeg_1748704128577/_h_env/Library --cc=clang.exe --cxx=clang++.exe --nm=llvm-nm --ar=llvm-ar --disable-doc --enable-openssl --enable-demuxer=dash --enable-hardcoded-tables --enable-libfreetype --enable-libharfbuzz --enable-libfontconfig --enable-libopenh264 --enable-libdav1d --ld=lld-link --target-os=win64 --enable-cross-compile --toolchain=msvc --host-cc=clang.exe --extra-libs=ucrt.lib --extra-libs=vcruntime.lib --extra-libs=oldnames.lib --strip=llvm-strip --disable-stripping --host-extralibs= --disable-libopenvino --enable-gpl --enable-libx264 --enable-libx265 --enable-libmp3lame --enable-libaom --enable-libsvtav1 --enable-libxml2 --enable-pic --enable-shared --disable-static --enable-version3 --enable-zlib --enable-libvorbis --enable-libopus --enable-librsvg --enable-ffplay --pkg-config=/d/bld/ffmpeg_1748704128577/_build_env/Library/bin/pkg-config
  libavutil      59. 39.100 / 59. 39.100
  libavcodec     61. 19.101 / 61. 19.101
  libavformat    61.  7.100 / 61.  7.100
  libavdevice    61.  3.100 / 61.  3.100
  libavfilter    10.  4.100 / 10.  4.100
  libswscale      8.  3.100 /  8.  3.100
  libswresample   5.  3.100 /  5.  3.100
  libpostproc    58.  3.100 / 58.  3.100
Input #0, avi, from 'C:\Users\<USER>\Desktop\curiosity_pupil\data\20250718_145240_dulu\video\dulu_facial_expression_2025-07-18_14-54-40.avi':
  Metadata:
    software        : Lavf61.7.100
  Duration: 00:00:25.27, start: 0.000000, bitrate: 44498 kb/s
  Stream #0:0: Video: mjpeg (Baseline) (MJPG / 0x47504A4D), yuvj420p(pc, bt470bg/unknown/unknown), 1920x1080, 44542 kb/s, 30 fps, 30 tbr, 30 tbn
[out#0/mp4 @ 0000018FD3186240] Codec AVOption b:a (set bitrate (in bits/s)) has not been used for any stream. The most likely reason is either wrong type (e.g. a video option with no video streams) or that it is a private option of some decoder which was not actually used for any stream.
[out#0/mp4 @ 0000018FD3186240] Error opening output C:\Users\<USER>\Desktop\curiosity_pupil\data\20250718_145240_dulu\video_mp4\dulu_facial_expression_2025-07-18_14-54-40.mp4: No space left on device
Error opening output file C:\Users\<USER>\Desktop\curiosity_pupil\data\20250718_145240_dulu\video_mp4\dulu_facial_expression_2025-07-18_14-54-40.mp4.
Error opening output files: No space left on device

2025-07-21 13:00:40,747 - INFO -   处理进度: 4/30 - dulu_facial_expression_2025-07-18_14-55-06.avi
2025-07-21 13:00:40,747 - INFO - 开始转换: dulu_facial_expression_2025-07-18_14-55-06.avi
2025-07-21 13:00:40,978 - ERROR - 转换失败: dulu_facial_expression_2025-07-18_14-55-06.avi
2025-07-21 13:00:40,978 - ERROR - 错误信息: ffmpeg version 7.1.1 Copyright (c) 2000-2025 the FFmpeg developers
  built with clang version 20.1.6
  configuration: --prefix=/d/bld/ffmpeg_1748704128577/_h_env/Library --cc=clang.exe --cxx=clang++.exe --nm=llvm-nm --ar=llvm-ar --disable-doc --enable-openssl --enable-demuxer=dash --enable-hardcoded-tables --enable-libfreetype --enable-libharfbuzz --enable-libfontconfig --enable-libopenh264 --enable-libdav1d --ld=lld-link --target-os=win64 --enable-cross-compile --toolchain=msvc --host-cc=clang.exe --extra-libs=ucrt.lib --extra-libs=vcruntime.lib --extra-libs=oldnames.lib --strip=llvm-strip --disable-stripping --host-extralibs= --disable-libopenvino --enable-gpl --enable-libx264 --enable-libx265 --enable-libmp3lame --enable-libaom --enable-libsvtav1 --enable-libxml2 --enable-pic --enable-shared --disable-static --enable-version3 --enable-zlib --enable-libvorbis --enable-libopus --enable-librsvg --enable-ffplay --pkg-config=/d/bld/ffmpeg_1748704128577/_build_env/Library/bin/pkg-config
  libavutil      59. 39.100 / 59. 39.100
  libavcodec     61. 19.101 / 61. 19.101
  libavformat    61.  7.100 / 61.  7.100
  libavdevice    61.  3.100 / 61.  3.100
  libavfilter    10.  4.100 / 10.  4.100
  libswscale      8.  3.100 /  8.  3.100
  libswresample   5.  3.100 /  5.  3.100
  libpostproc    58.  3.100 / 58.  3.100
Input #0, avi, from 'C:\Users\<USER>\Desktop\curiosity_pupil\data\20250718_145240_dulu\video\dulu_facial_expression_2025-07-18_14-55-06.avi':
  Metadata:
    software        : Lavf61.7.100
  Duration: 00:00:24.97, start: 0.000000, bitrate: 45305 kb/s
  Stream #0:0: Video: mjpeg (Baseline) (MJPG / 0x47504A4D), yuvj420p(pc, bt470bg/unknown/unknown), 1920x1080, 45350 kb/s, 30 fps, 30 tbr, 30 tbn
[out#0/mp4 @ 000001C8E13BC000] Codec AVOption b:a (set bitrate (in bits/s)) has not been used for any stream. The most likely reason is either wrong type (e.g. a video option with no video streams) or that it is a private option of some decoder which was not actually used for any stream.
[out#0/mp4 @ 000001C8E13BC000] Error opening output C:\Users\<USER>\Desktop\curiosity_pupil\data\20250718_145240_dulu\video_mp4\dulu_facial_expression_2025-07-18_14-55-06.mp4: No space left on device
Error opening output file C:\Users\<USER>\Desktop\curiosity_pupil\data\20250718_145240_dulu\video_mp4\dulu_facial_expression_2025-07-18_14-55-06.mp4.
Error opening output files: No space left on device

2025-07-21 13:00:40,978 - INFO -   处理进度: 5/30 - dulu_facial_expression_2025-07-18_14-55-32.avi
2025-07-21 13:00:40,983 - INFO - 开始转换: dulu_facial_expression_2025-07-18_14-55-32.avi
2025-07-21 13:00:41,200 - ERROR - 转换失败: dulu_facial_expression_2025-07-18_14-55-32.avi
2025-07-21 13:00:41,200 - ERROR - 错误信息: ffmpeg version 7.1.1 Copyright (c) 2000-2025 the FFmpeg developers
  built with clang version 20.1.6
  configuration: --prefix=/d/bld/ffmpeg_1748704128577/_h_env/Library --cc=clang.exe --cxx=clang++.exe --nm=llvm-nm --ar=llvm-ar --disable-doc --enable-openssl --enable-demuxer=dash --enable-hardcoded-tables --enable-libfreetype --enable-libharfbuzz --enable-libfontconfig --enable-libopenh264 --enable-libdav1d --ld=lld-link --target-os=win64 --enable-cross-compile --toolchain=msvc --host-cc=clang.exe --extra-libs=ucrt.lib --extra-libs=vcruntime.lib --extra-libs=oldnames.lib --strip=llvm-strip --disable-stripping --host-extralibs= --disable-libopenvino --enable-gpl --enable-libx264 --enable-libx265 --enable-libmp3lame --enable-libaom --enable-libsvtav1 --enable-libxml2 --enable-pic --enable-shared --disable-static --enable-version3 --enable-zlib --enable-libvorbis --enable-libopus --enable-librsvg --enable-ffplay --pkg-config=/d/bld/ffmpeg_1748704128577/_build_env/Library/bin/pkg-config
  libavutil      59. 39.100 / 59. 39.100
  libavcodec     61. 19.101 / 61. 19.101
  libavformat    61.  7.100 / 61.  7.100
  libavdevice    61.  3.100 / 61.  3.100
  libavfilter    10.  4.100 / 10.  4.100
  libswscale      8.  3.100 /  8.  3.100
  libswresample   5.  3.100 /  5.  3.100
  libpostproc    58.  3.100 / 58.  3.100
Input #0, avi, from 'C:\Users\<USER>\Desktop\curiosity_pupil\data\20250718_145240_dulu\video\dulu_facial_expression_2025-07-18_14-55-32.avi':
  Metadata:
    software        : Lavf61.7.100
  Duration: 00:00:28.87, start: 0.000000, bitrate: 46316 kb/s
  Stream #0:0: Video: mjpeg (Baseline) (MJPG / 0x47504A4D), yuvj420p(pc, bt470bg/unknown/unknown), 1920x1080, 46356 kb/s, 30 fps, 30 tbr, 30 tbn
[out#0/mp4 @ 0000027F554CCD80] Codec AVOption b:a (set bitrate (in bits/s)) has not been used for any stream. The most likely reason is either wrong type (e.g. a video option with no video streams) or that it is a private option of some decoder which was not actually used for any stream.
[out#0/mp4 @ 0000027F554CCD80] Error opening output C:\Users\<USER>\Desktop\curiosity_pupil\data\20250718_145240_dulu\video_mp4\dulu_facial_expression_2025-07-18_14-55-32.mp4: No space left on device
Error opening output file C:\Users\<USER>\Desktop\curiosity_pupil\data\20250718_145240_dulu\video_mp4\dulu_facial_expression_2025-07-18_14-55-32.mp4.
Error opening output files: No space left on device

2025-07-21 13:00:41,203 - INFO -   处理进度: 6/30 - dulu_facial_expression_2025-07-18_14-56-02.avi
2025-07-21 13:00:41,203 - INFO - 开始转换: dulu_facial_expression_2025-07-18_14-56-02.avi
2025-07-21 13:00:41,406 - ERROR - 转换失败: dulu_facial_expression_2025-07-18_14-56-02.avi
2025-07-21 13:00:41,408 - ERROR - 错误信息: ffmpeg version 7.1.1 Copyright (c) 2000-2025 the FFmpeg developers
  built with clang version 20.1.6
  configuration: --prefix=/d/bld/ffmpeg_1748704128577/_h_env/Library --cc=clang.exe --cxx=clang++.exe --nm=llvm-nm --ar=llvm-ar --disable-doc --enable-openssl --enable-demuxer=dash --enable-hardcoded-tables --enable-libfreetype --enable-libharfbuzz --enable-libfontconfig --enable-libopenh264 --enable-libdav1d --ld=lld-link --target-os=win64 --enable-cross-compile --toolchain=msvc --host-cc=clang.exe --extra-libs=ucrt.lib --extra-libs=vcruntime.lib --extra-libs=oldnames.lib --strip=llvm-strip --disable-stripping --host-extralibs= --disable-libopenvino --enable-gpl --enable-libx264 --enable-libx265 --enable-libmp3lame --enable-libaom --enable-libsvtav1 --enable-libxml2 --enable-pic --enable-shared --disable-static --enable-version3 --enable-zlib --enable-libvorbis --enable-libopus --enable-librsvg --enable-ffplay --pkg-config=/d/bld/ffmpeg_1748704128577/_build_env/Library/bin/pkg-config
  libavutil      59. 39.100 / 59. 39.100
  libavcodec     61. 19.101 / 61. 19.101
  libavformat    61.  7.100 / 61.  7.100
  libavdevice    61.  3.100 / 61.  3.100
  libavfilter    10.  4.100 / 10.  4.100
  libswscale      8.  3.100 /  8.  3.100
  libswresample   5.  3.100 /  5.  3.100
  libpostproc    58.  3.100 / 58.  3.100
Input #0, avi, from 'C:\Users\<USER>\Desktop\curiosity_pupil\data\20250718_145240_dulu\video\dulu_facial_expression_2025-07-18_14-56-02.avi':
  Metadata:
    software        : Lavf61.7.100
  Duration: 00:00:31.77, start: 0.000000, bitrate: 47664 kb/s
  Stream #0:0: Video: mjpeg (Baseline) (MJPG / 0x47504A4D), yuvj420p(pc, bt470bg/unknown/unknown), 1920x1080, 47701 kb/s, 30 fps, 30 tbr, 30 tbn
[out#0/mp4 @ 000001CC4E5395C0] Codec AVOption b:a (set bitrate (in bits/s)) has not been used for any stream. The most likely reason is either wrong type (e.g. a video option with no video streams) or that it is a private option of some decoder which was not actually used for any stream.
[out#0/mp4 @ 000001CC4E5395C0] Error opening output C:\Users\<USER>\Desktop\curiosity_pupil\data\20250718_145240_dulu\video_mp4\dulu_facial_expression_2025-07-18_14-56-02.mp4: No space left on device
Error opening output file C:\Users\<USER>\Desktop\curiosity_pupil\data\20250718_145240_dulu\video_mp4\dulu_facial_expression_2025-07-18_14-56-02.mp4.
Error opening output files: No space left on device

2025-07-21 13:00:41,409 - INFO -   处理进度: 7/30 - dulu_facial_expression_2025-07-18_14-56-35.avi
2025-07-21 13:00:41,410 - INFO - 开始转换: dulu_facial_expression_2025-07-18_14-56-35.avi
2025-07-21 13:00:41,718 - ERROR - 转换失败: dulu_facial_expression_2025-07-18_14-56-35.avi
2025-07-21 13:00:41,718 - ERROR - 错误信息: ffmpeg version 7.1.1 Copyright (c) 2000-2025 the FFmpeg developers
  built with clang version 20.1.6
  configuration: --prefix=/d/bld/ffmpeg_1748704128577/_h_env/Library --cc=clang.exe --cxx=clang++.exe --nm=llvm-nm --ar=llvm-ar --disable-doc --enable-openssl --enable-demuxer=dash --enable-hardcoded-tables --enable-libfreetype --enable-libharfbuzz --enable-libfontconfig --enable-libopenh264 --enable-libdav1d --ld=lld-link --target-os=win64 --enable-cross-compile --toolchain=msvc --host-cc=clang.exe --extra-libs=ucrt.lib --extra-libs=vcruntime.lib --extra-libs=oldnames.lib --strip=llvm-strip --disable-stripping --host-extralibs= --disable-libopenvino --enable-gpl --enable-libx264 --enable-libx265 --enable-libmp3lame --enable-libaom --enable-libsvtav1 --enable-libxml2 --enable-pic --enable-shared --disable-static --enable-version3 --enable-zlib --enable-libvorbis --enable-libopus --enable-librsvg --enable-ffplay --pkg-config=/d/bld/ffmpeg_1748704128577/_build_env/Library/bin/pkg-config
  libavutil      59. 39.100 / 59. 39.100
  libavcodec     61. 19.101 / 61. 19.101
  libavformat    61.  7.100 / 61.  7.100
  libavdevice    61.  3.100 / 61.  3.100
  libavfilter    10.  4.100 / 10.  4.100
  libswscale      8.  3.100 /  8.  3.100
  libswresample   5.  3.100 /  5.  3.100
  libpostproc    58.  3.100 / 58.  3.100
Input #0, avi, from 'C:\Users\<USER>\Desktop\curiosity_pupil\data\20250718_145240_dulu\video\dulu_facial_expression_2025-07-18_14-56-35.avi':
  Metadata:
    software        : Lavf61.7.100
  Duration: 00:00:55.77, start: 0.000000, bitrate: 50069 kb/s
  Stream #0:0: Video: mjpeg (Baseline) (MJPG / 0x47504A4D), yuvj420p(pc, bt470bg/unknown/unknown), 1920x1080, 50088 kb/s, 30 fps, 30 tbr, 30 tbn
[out#0/mp4 @ 000002D9C0158B00] Codec AVOption b:a (set bitrate (in bits/s)) has not been used for any stream. The most likely reason is either wrong type (e.g. a video option with no video streams) or that it is a private option of some decoder which was not actually used for any stream.
[out#0/mp4 @ 000002D9C0158B00] Error opening output C:\Users\<USER>\Desktop\curiosity_pupil\data\20250718_145240_dulu\video_mp4\dulu_facial_expression_2025-07-18_14-56-35.mp4: No space left on device
Error opening output file C:\Users\<USER>\Desktop\curiosity_pupil\data\20250718_145240_dulu\video_mp4\dulu_facial_expression_2025-07-18_14-56-35.mp4.
Error opening output files: No space left on device

2025-07-21 13:00:41,718 - INFO -   处理进度: 8/30 - dulu_facial_expression_2025-07-18_14-57-35.avi
2025-07-21 13:00:41,729 - INFO - 开始转换: dulu_facial_expression_2025-07-18_14-57-35.avi
2025-07-21 13:00:41,932 - ERROR - 转换失败: dulu_facial_expression_2025-07-18_14-57-35.avi
2025-07-21 13:00:41,932 - ERROR - 错误信息: ffmpeg version 7.1.1 Copyright (c) 2000-2025 the FFmpeg developers
  built with clang version 20.1.6
  configuration: --prefix=/d/bld/ffmpeg_1748704128577/_h_env/Library --cc=clang.exe --cxx=clang++.exe --nm=llvm-nm --ar=llvm-ar --disable-doc --enable-openssl --enable-demuxer=dash --enable-hardcoded-tables --enable-libfreetype --enable-libharfbuzz --enable-libfontconfig --enable-libopenh264 --enable-libdav1d --ld=lld-link --target-os=win64 --enable-cross-compile --toolchain=msvc --host-cc=clang.exe --extra-libs=ucrt.lib --extra-libs=vcruntime.lib --extra-libs=oldnames.lib --strip=llvm-strip --disable-stripping --host-extralibs= --disable-libopenvino --enable-gpl --enable-libx264 --enable-libx265 --enable-libmp3lame --enable-libaom --enable-libsvtav1 --enable-libxml2 --enable-pic --enable-shared --disable-static --enable-version3 --enable-zlib --enable-libvorbis --enable-libopus --enable-librsvg --enable-ffplay --pkg-config=/d/bld/ffmpeg_1748704128577/_build_env/Library/bin/pkg-config
  libavutil      59. 39.100 / 59. 39.100
  libavcodec     61. 19.101 / 61. 19.101
  libavformat    61.  7.100 / 61.  7.100
  libavdevice    61.  3.100 / 61.  3.100
  libavfilter    10.  4.100 / 10.  4.100
  libswscale      8.  3.100 /  8.  3.100
  libswresample   5.  3.100 /  5.  3.100
  libpostproc    58.  3.100 / 58.  3.100
Input #0, avi, from 'C:\Users\<USER>\Desktop\curiosity_pupil\data\20250718_145240_dulu\video\dulu_facial_expression_2025-07-18_14-57-35.avi':
  Metadata:
    software        : Lavf61.7.100
  Duration: 00:00:28.30, start: 0.000000, bitrate: 52857 kb/s
  Stream #0:0: Video: mjpeg (Baseline) (MJPG / 0x47504A4D), yuvj420p(pc, bt470bg/unknown/unknown), 1920x1080, 52905 kb/s, 30 fps, 30 tbr, 30 tbn
[out#0/mp4 @ 000001EFF4005940] Codec AVOption b:a (set bitrate (in bits/s)) has not been used for any stream. The most likely reason is either wrong type (e.g. a video option with no video streams) or that it is a private option of some decoder which was not actually used for any stream.
[out#0/mp4 @ 000001EFF4005940] Error opening output C:\Users\<USER>\Desktop\curiosity_pupil\data\20250718_145240_dulu\video_mp4\dulu_facial_expression_2025-07-18_14-57-35.mp4: No space left on device
Error opening output file C:\Users\<USER>\Desktop\curiosity_pupil\data\20250718_145240_dulu\video_mp4\dulu_facial_expression_2025-07-18_14-57-35.mp4.
Error opening output files: No space left on device

2025-07-21 13:00:41,932 - INFO -   处理进度: 9/30 - dulu_facial_expression_2025-07-18_14-58-06.avi
2025-07-21 13:00:41,932 - INFO - 开始转换: dulu_facial_expression_2025-07-18_14-58-06.avi
2025-07-21 13:00:42,155 - ERROR - 转换失败: dulu_facial_expression_2025-07-18_14-58-06.avi
2025-07-21 13:00:42,155 - ERROR - 错误信息: ffmpeg version 7.1.1 Copyright (c) 2000-2025 the FFmpeg developers
  built with clang version 20.1.6
  configuration: --prefix=/d/bld/ffmpeg_1748704128577/_h_env/Library --cc=clang.exe --cxx=clang++.exe --nm=llvm-nm --ar=llvm-ar --disable-doc --enable-openssl --enable-demuxer=dash --enable-hardcoded-tables --enable-libfreetype --enable-libharfbuzz --enable-libfontconfig --enable-libopenh264 --enable-libdav1d --ld=lld-link --target-os=win64 --enable-cross-compile --toolchain=msvc --host-cc=clang.exe --extra-libs=ucrt.lib --extra-libs=vcruntime.lib --extra-libs=oldnames.lib --strip=llvm-strip --disable-stripping --host-extralibs= --disable-libopenvino --enable-gpl --enable-libx264 --enable-libx265 --enable-libmp3lame --enable-libaom --enable-libsvtav1 --enable-libxml2 --enable-pic --enable-shared --disable-static --enable-version3 --enable-zlib --enable-libvorbis --enable-libopus --enable-librsvg --enable-ffplay --pkg-config=/d/bld/ffmpeg_1748704128577/_build_env/Library/bin/pkg-config
  libavutil      59. 39.100 / 59. 39.100
  libavcodec     61. 19.101 / 61. 19.101
  libavformat    61.  7.100 / 61.  7.100
  libavdevice    61.  3.100 / 61.  3.100
  libavfilter    10.  4.100 / 10.  4.100
  libswscale      8.  3.100 /  8.  3.100
  libswresample   5.  3.100 /  5.  3.100
  libpostproc    58.  3.100 / 58.  3.100
Input #0, avi, from 'C:\Users\<USER>\Desktop\curiosity_pupil\data\20250718_145240_dulu\video\dulu_facial_expression_2025-07-18_14-58-06.avi':
  Metadata:
    software        : Lavf61.7.100
  Duration: 00:00:24.00, start: 0.000000, bitrate: 54445 kb/s
  Stream #0:0: Video: mjpeg (Baseline) (MJPG / 0x47504A4D), yuvj420p(pc, bt470bg/unknown/unknown), 1920x1080, 54505 kb/s, 30 fps, 30 tbr, 30 tbn
[out#0/mp4 @ 00000261DC7D5DC0] Codec AVOption b:a (set bitrate (in bits/s)) has not been used for any stream. The most likely reason is either wrong type (e.g. a video option with no video streams) or that it is a private option of some decoder which was not actually used for any stream.
[out#0/mp4 @ 00000261DC7D5DC0] Error opening output C:\Users\<USER>\Desktop\curiosity_pupil\data\20250718_145240_dulu\video_mp4\dulu_facial_expression_2025-07-18_14-58-06.mp4: No space left on device
Error opening output file C:\Users\<USER>\Desktop\curiosity_pupil\data\20250718_145240_dulu\video_mp4\dulu_facial_expression_2025-07-18_14-58-06.mp4.
Error opening output files: No space left on device

2025-07-21 13:00:42,157 - INFO -   处理进度: 10/30 - dulu_facial_expression_2025-07-18_14-58-32.avi
2025-07-21 13:00:42,157 - INFO - 开始转换: dulu_facial_expression_2025-07-18_14-58-32.avi
2025-07-21 13:00:42,474 - ERROR - 转换失败: dulu_facial_expression_2025-07-18_14-58-32.avi
2025-07-21 13:00:42,475 - ERROR - 错误信息: ffmpeg version 7.1.1 Copyright (c) 2000-2025 the FFmpeg developers
  built with clang version 20.1.6
  configuration: --prefix=/d/bld/ffmpeg_1748704128577/_h_env/Library --cc=clang.exe --cxx=clang++.exe --nm=llvm-nm --ar=llvm-ar --disable-doc --enable-openssl --enable-demuxer=dash --enable-hardcoded-tables --enable-libfreetype --enable-libharfbuzz --enable-libfontconfig --enable-libopenh264 --enable-libdav1d --ld=lld-link --target-os=win64 --enable-cross-compile --toolchain=msvc --host-cc=clang.exe --extra-libs=ucrt.lib --extra-libs=vcruntime.lib --extra-libs=oldnames.lib --strip=llvm-strip --disable-stripping --host-extralibs= --disable-libopenvino --enable-gpl --enable-libx264 --enable-libx265 --enable-libmp3lame --enable-libaom --enable-libsvtav1 --enable-libxml2 --enable-pic --enable-shared --disable-static --enable-version3 --enable-zlib --enable-libvorbis --enable-libopus --enable-librsvg --enable-ffplay --pkg-config=/d/bld/ffmpeg_1748704128577/_build_env/Library/bin/pkg-config
  libavutil      59. 39.100 / 59. 39.100
  libavcodec     61. 19.101 / 61. 19.101
  libavformat    61.  7.100 / 61.  7.100
  libavdevice    61.  3.100 / 61.  3.100
  libavfilter    10.  4.100 / 10.  4.100
  libswscale      8.  3.100 /  8.  3.100
  libswresample   5.  3.100 /  5.  3.100
  libpostproc    58.  3.100 / 58.  3.100
Input #0, avi, from 'C:\Users\<USER>\Desktop\curiosity_pupil\data\20250718_145240_dulu\video\dulu_facial_expression_2025-07-18_14-58-32.avi':
  Metadata:
    software        : Lavf61.7.100
  Duration: 00:00:36.00, start: 0.000000, bitrate: 56508 kb/s
  Stream #0:0: Video: mjpeg (Baseline) (MJPG / 0x47504A4D), yuvj420p(pc, bt470bg/unknown/unknown), 1920x1080, 56548 kb/s, 30 fps, 30 tbr, 30 tbn
[out#0/mp4 @ 000001A4F4A5A000] Codec AVOption b:a (set bitrate (in bits/s)) has not been used for any stream. The most likely reason is either wrong type (e.g. a video option with no video streams) or that it is a private option of some decoder which was not actually used for any stream.
[out#0/mp4 @ 000001A4F4A5A000] Error opening output C:\Users\<USER>\Desktop\curiosity_pupil\data\20250718_145240_dulu\video_mp4\dulu_facial_expression_2025-07-18_14-58-32.mp4: No space left on device
Error opening output file C:\Users\<USER>\Desktop\curiosity_pupil\data\20250718_145240_dulu\video_mp4\dulu_facial_expression_2025-07-18_14-58-32.mp4.
Error opening output files: No space left on device

2025-07-21 13:00:42,476 - INFO -   处理进度: 11/30 - dulu_facial_expression_2025-07-18_14-59-12.avi
2025-07-21 13:00:42,477 - INFO - 开始转换: dulu_facial_expression_2025-07-18_14-59-12.avi
2025-07-21 13:00:42,689 - ERROR - 转换失败: dulu_facial_expression_2025-07-18_14-59-12.avi
2025-07-21 13:00:42,689 - ERROR - 错误信息: ffmpeg version 7.1.1 Copyright (c) 2000-2025 the FFmpeg developers
  built with clang version 20.1.6
  configuration: --prefix=/d/bld/ffmpeg_1748704128577/_h_env/Library --cc=clang.exe --cxx=clang++.exe --nm=llvm-nm --ar=llvm-ar --disable-doc --enable-openssl --enable-demuxer=dash --enable-hardcoded-tables --enable-libfreetype --enable-libharfbuzz --enable-libfontconfig --enable-libopenh264 --enable-libdav1d --ld=lld-link --target-os=win64 --enable-cross-compile --toolchain=msvc --host-cc=clang.exe --extra-libs=ucrt.lib --extra-libs=vcruntime.lib --extra-libs=oldnames.lib --strip=llvm-strip --disable-stripping --host-extralibs= --disable-libopenvino --enable-gpl --enable-libx264 --enable-libx265 --enable-libmp3lame --enable-libaom --enable-libsvtav1 --enable-libxml2 --enable-pic --enable-shared --disable-static --enable-version3 --enable-zlib --enable-libvorbis --enable-libopus --enable-librsvg --enable-ffplay --pkg-config=/d/bld/ffmpeg_1748704128577/_build_env/Library/bin/pkg-config
  libavutil      59. 39.100 / 59. 39.100
  libavcodec     61. 19.101 / 61. 19.101
  libavformat    61.  7.100 / 61.  7.100
  libavdevice    61.  3.100 / 61.  3.100
  libavfilter    10.  4.100 / 10.  4.100
  libswscale      8.  3.100 /  8.  3.100
  libswresample   5.  3.100 /  5.  3.100
  libpostproc    58.  3.100 / 58.  3.100
Input #0, avi, from 'C:\Users\<USER>\Desktop\curiosity_pupil\data\20250718_145240_dulu\video\dulu_facial_expression_2025-07-18_14-59-12.avi':
  Metadata:
    software        : Lavf61.7.100
  Duration: 00:00:23.53, start: 0.000000, bitrate: 58281 kb/s
  Stream #0:0: Video: mjpeg (Baseline) (MJPG / 0x47504A4D), yuvj420p(pc, bt470bg/unknown/unknown), 1920x1080, 58348 kb/s, 30 fps, 30 tbr, 30 tbn
[out#0/mp4 @ 0000024470E8D580] Codec AVOption b:a (set bitrate (in bits/s)) has not been used for any stream. The most likely reason is either wrong type (e.g. a video option with no video streams) or that it is a private option of some decoder which was not actually used for any stream.
[out#0/mp4 @ 0000024470E8D580] Error opening output C:\Users\<USER>\Desktop\curiosity_pupil\data\20250718_145240_dulu\video_mp4\dulu_facial_expression_2025-07-18_14-59-12.mp4: No space left on device
Error opening output file C:\Users\<USER>\Desktop\curiosity_pupil\data\20250718_145240_dulu\video_mp4\dulu_facial_expression_2025-07-18_14-59-12.mp4.
Error opening output files: No space left on device

2025-07-21 13:00:42,689 - INFO -   处理进度: 12/30 - dulu_facial_expression_2025-07-18_14-59-38.avi
2025-07-21 13:00:42,689 - INFO - 开始转换: dulu_facial_expression_2025-07-18_14-59-38.avi
2025-07-21 13:00:42,914 - ERROR - 转换失败: dulu_facial_expression_2025-07-18_14-59-38.avi
2025-07-21 13:00:42,914 - ERROR - 错误信息: ffmpeg version 7.1.1 Copyright (c) 2000-2025 the FFmpeg developers
  built with clang version 20.1.6
  configuration: --prefix=/d/bld/ffmpeg_1748704128577/_h_env/Library --cc=clang.exe --cxx=clang++.exe --nm=llvm-nm --ar=llvm-ar --disable-doc --enable-openssl --enable-demuxer=dash --enable-hardcoded-tables --enable-libfreetype --enable-libharfbuzz --enable-libfontconfig --enable-libopenh264 --enable-libdav1d --ld=lld-link --target-os=win64 --enable-cross-compile --toolchain=msvc --host-cc=clang.exe --extra-libs=ucrt.lib --extra-libs=vcruntime.lib --extra-libs=oldnames.lib --strip=llvm-strip --disable-stripping --host-extralibs= --disable-libopenvino --enable-gpl --enable-libx264 --enable-libx265 --enable-libmp3lame --enable-libaom --enable-libsvtav1 --enable-libxml2 --enable-pic --enable-shared --disable-static --enable-version3 --enable-zlib --enable-libvorbis --enable-libopus --enable-librsvg --enable-ffplay --pkg-config=/d/bld/ffmpeg_1748704128577/_build_env/Library/bin/pkg-config
  libavutil      59. 39.100 / 59. 39.100
  libavcodec     61. 19.101 / 61. 19.101
  libavformat    61.  7.100 / 61.  7.100
  libavdevice    61.  3.100 / 61.  3.100
  libavfilter    10.  4.100 / 10.  4.100
  libswscale      8.  3.100 /  8.  3.100
  libswresample   5.  3.100 /  5.  3.100
  libpostproc    58.  3.100 / 58.  3.100
Input #0, avi, from 'C:\Users\<USER>\Desktop\curiosity_pupil\data\20250718_145240_dulu\video\dulu_facial_expression_2025-07-18_14-59-38.avi':
  Metadata:
    software        : Lavf61.7.100
  Duration: 00:00:35.73, start: 0.000000, bitrate: 59876 kb/s
  Stream #0:0: Video: mjpeg (Baseline) (MJPG / 0x47504A4D), yuvj420p(pc, bt470bg/unknown/unknown), 1920x1080, 59919 kb/s, 30 fps, 30 tbr, 30 tbn
[out#0/mp4 @ 0000018E9CDA9F80] Codec AVOption b:a (set bitrate (in bits/s)) has not been used for any stream. The most likely reason is either wrong type (e.g. a video option with no video streams) or that it is a private option of some decoder which was not actually used for any stream.
[out#0/mp4 @ 0000018E9CDA9F80] Error opening output C:\Users\<USER>\Desktop\curiosity_pupil\data\20250718_145240_dulu\video_mp4\dulu_facial_expression_2025-07-18_14-59-38.mp4: No space left on device
Error opening output file C:\Users\<USER>\Desktop\curiosity_pupil\data\20250718_145240_dulu\video_mp4\dulu_facial_expression_2025-07-18_14-59-38.mp4.
Error opening output files: No space left on device

2025-07-21 13:00:42,917 - INFO -   处理进度: 13/30 - dulu_facial_expression_2025-07-18_15-00-18.avi
2025-07-21 13:00:42,917 - INFO - 开始转换: dulu_facial_expression_2025-07-18_15-00-18.avi
2025-07-21 13:00:43,118 - ERROR - 转换失败: dulu_facial_expression_2025-07-18_15-00-18.avi
2025-07-21 13:00:43,118 - ERROR - 错误信息: ffmpeg version 7.1.1 Copyright (c) 2000-2025 the FFmpeg developers
  built with clang version 20.1.6
  configuration: --prefix=/d/bld/ffmpeg_1748704128577/_h_env/Library --cc=clang.exe --cxx=clang++.exe --nm=llvm-nm --ar=llvm-ar --disable-doc --enable-openssl --enable-demuxer=dash --enable-hardcoded-tables --enable-libfreetype --enable-libharfbuzz --enable-libfontconfig --enable-libopenh264 --enable-libdav1d --ld=lld-link --target-os=win64 --enable-cross-compile --toolchain=msvc --host-cc=clang.exe --extra-libs=ucrt.lib --extra-libs=vcruntime.lib --extra-libs=oldnames.lib --strip=llvm-strip --disable-stripping --host-extralibs= --disable-libopenvino --enable-gpl --enable-libx264 --enable-libx265 --enable-libmp3lame --enable-libaom --enable-libsvtav1 --enable-libxml2 --enable-pic --enable-shared --disable-static --enable-version3 --enable-zlib --enable-libvorbis --enable-libopus --enable-librsvg --enable-ffplay --pkg-config=/d/bld/ffmpeg_1748704128577/_build_env/Library/bin/pkg-config
  libavutil      59. 39.100 / 59. 39.100
  libavcodec     61. 19.101 / 61. 19.101
  libavformat    61.  7.100 / 61.  7.100
  libavdevice    61.  3.100 / 61.  3.100
  libavfilter    10.  4.100 / 10.  4.100
  libswscale      8.  3.100 /  8.  3.100
  libswresample   5.  3.100 /  5.  3.100
  libpostproc    58.  3.100 / 58.  3.100
Input #0, avi, from 'C:\Users\<USER>\Desktop\curiosity_pupil\data\20250718_145240_dulu\video\dulu_facial_expression_2025-07-18_15-00-18.avi':
  Metadata:
    software        : Lavf61.7.100
  Duration: 00:00:25.73, start: 0.000000, bitrate: 61695 kb/s
  Stream #0:0: Video: mjpeg (Baseline) (MJPG / 0x47504A4D), yuvj420p(pc, bt470bg/unknown/unknown), 1920x1080, 61760 kb/s, 30 fps, 30 tbr, 30 tbn
[out#0/mp4 @ 000001F4CEAC1BC0] Codec AVOption b:a (set bitrate (in bits/s)) has not been used for any stream. The most likely reason is either wrong type (e.g. a video option with no video streams) or that it is a private option of some decoder which was not actually used for any stream.
[out#0/mp4 @ 000001F4CEAC1BC0] Error opening output C:\Users\<USER>\Desktop\curiosity_pupil\data\20250718_145240_dulu\video_mp4\dulu_facial_expression_2025-07-18_15-00-18.mp4: No space left on device
Error opening output file C:\Users\<USER>\Desktop\curiosity_pupil\data\20250718_145240_dulu\video_mp4\dulu_facial_expression_2025-07-18_15-00-18.mp4.
Error opening output files: No space left on device

2025-07-21 13:00:43,130 - INFO -   处理进度: 14/30 - dulu_facial_expression_2025-07-18_15-00-47.avi
2025-07-21 13:00:43,130 - INFO - 开始转换: dulu_facial_expression_2025-07-18_15-00-47.avi
2025-07-21 13:00:43,354 - ERROR - 转换失败: dulu_facial_expression_2025-07-18_15-00-47.avi
2025-07-21 13:00:43,355 - ERROR - 错误信息: ffmpeg version 7.1.1 Copyright (c) 2000-2025 the FFmpeg developers
  built with clang version 20.1.6
  configuration: --prefix=/d/bld/ffmpeg_1748704128577/_h_env/Library --cc=clang.exe --cxx=clang++.exe --nm=llvm-nm --ar=llvm-ar --disable-doc --enable-openssl --enable-demuxer=dash --enable-hardcoded-tables --enable-libfreetype --enable-libharfbuzz --enable-libfontconfig --enable-libopenh264 --enable-libdav1d --ld=lld-link --target-os=win64 --enable-cross-compile --toolchain=msvc --host-cc=clang.exe --extra-libs=ucrt.lib --extra-libs=vcruntime.lib --extra-libs=oldnames.lib --strip=llvm-strip --disable-stripping --host-extralibs= --disable-libopenvino --enable-gpl --enable-libx264 --enable-libx265 --enable-libmp3lame --enable-libaom --enable-libsvtav1 --enable-libxml2 --enable-pic --enable-shared --disable-static --enable-version3 --enable-zlib --enable-libvorbis --enable-libopus --enable-librsvg --enable-ffplay --pkg-config=/d/bld/ffmpeg_1748704128577/_build_env/Library/bin/pkg-config
  libavutil      59. 39.100 / 59. 39.100
  libavcodec     61. 19.101 / 61. 19.101
  libavformat    61.  7.100 / 61.  7.100
  libavdevice    61.  3.100 / 61.  3.100
  libavfilter    10.  4.100 / 10.  4.100
  libswscale      8.  3.100 /  8.  3.100
  libswresample   5.  3.100 /  5.  3.100
  libpostproc    58.  3.100 / 58.  3.100
Input #0, avi, from 'C:\Users\<USER>\Desktop\curiosity_pupil\data\20250718_145240_dulu\video\dulu_facial_expression_2025-07-18_15-00-47.avi':
  Metadata:
    software        : Lavf61.7.100
  Duration: 00:00:30.83, start: 0.000000, bitrate: 63564 kb/s
  Stream #0:0: Video: mjpeg (Baseline) (MJPG / 0x47504A4D), yuvj420p(pc, bt470bg/unknown/unknown), 1920x1080, 63619 kb/s, 30 fps, 30 tbr, 30 tbn
[out#0/mp4 @ 0000027EC0FB9840] Codec AVOption b:a (set bitrate (in bits/s)) has not been used for any stream. The most likely reason is either wrong type (e.g. a video option with no video streams) or that it is a private option of some decoder which was not actually used for any stream.
[out#0/mp4 @ 0000027EC0FB9840] Error opening output C:\Users\<USER>\Desktop\curiosity_pupil\data\20250718_145240_dulu\video_mp4\dulu_facial_expression_2025-07-18_15-00-47.mp4: No space left on device
Error opening output file C:\Users\<USER>\Desktop\curiosity_pupil\data\20250718_145240_dulu\video_mp4\dulu_facial_expression_2025-07-18_15-00-47.mp4.
Error opening output files: No space left on device

2025-07-21 13:00:43,357 - INFO -   处理进度: 15/30 - dulu_facial_expression_2025-07-18_15-01-21.avi
2025-07-21 13:00:43,357 - INFO - 开始转换: dulu_facial_expression_2025-07-18_15-01-21.avi
2025-07-21 13:00:43,569 - ERROR - 转换失败: dulu_facial_expression_2025-07-18_15-01-21.avi
2025-07-22 11:25:25,562 - INFO - 搜索所有video子目录: C:/Users/<USER>/Desktop/curiosity_pupil/data
2025-07-22 11:25:25,571 - INFO - 找到 4 个video目录
2025-07-22 11:25:25,571 - INFO - 处理video目录: C:\Users\<USER>\Desktop\curiosity_pupil\data\20250718_142425_dulu\video
2025-07-22 11:25:25,571 - INFO -   找到 30 个AVI文件
2025-07-22 11:25:25,577 - INFO -   处理进度: 1/30 - dulu_facial_expression_2025-07-18_14-25-24.avi
2025-07-22 11:25:25,578 - INFO - 开始转换: dulu_facial_expression_2025-07-18_14-25-24.avi
2025-07-22 11:26:53,670 - INFO - 转换成功: dulu_facial_expression_2025-07-18_14-25-24.avi
2025-07-22 11:26:53,670 - INFO -   原始大小: 218.7 MB
2025-07-22 11:26:53,670 - INFO -   转换后大小: 25.4 MB
2025-07-22 11:26:53,670 - INFO -   压缩率: 88.4%
2025-07-22 11:26:53,670 - INFO -   处理进度: 2/30 - dulu_facial_expression_2025-07-18_14-26-02.avi
2025-07-22 11:26:53,670 - INFO - 开始转换: dulu_facial_expression_2025-07-18_14-26-02.avi
2025-07-22 11:28:56,304 - INFO - 转换成功: dulu_facial_expression_2025-07-18_14-26-02.avi
2025-07-22 11:28:56,304 - INFO -   原始大小: 318.6 MB
2025-07-22 11:28:56,304 - INFO -   转换后大小: 38.3 MB
2025-07-22 11:28:56,304 - INFO -   压缩率: 88.0%
2025-07-22 11:28:56,304 - INFO -   处理进度: 3/30 - dulu_facial_expression_2025-07-18_14-26-56.avi
2025-07-22 11:28:56,304 - INFO - 开始转换: dulu_facial_expression_2025-07-18_14-26-56.avi
2025-07-22 11:30:05,131 - INFO - 转换成功: dulu_facial_expression_2025-07-18_14-26-56.avi
2025-07-22 11:30:05,131 - INFO -   原始大小: 178.1 MB
2025-07-22 11:30:05,131 - INFO -   转换后大小: 22.6 MB
2025-07-22 11:30:05,131 - INFO -   压缩率: 87.3%
2025-07-22 11:30:05,131 - INFO -   处理进度: 4/30 - dulu_facial_expression_2025-07-18_14-27-25.avi
2025-07-22 11:30:05,131 - INFO - 开始转换: dulu_facial_expression_2025-07-18_14-27-25.avi
2025-07-22 11:31:21,199 - INFO - 转换成功: dulu_facial_expression_2025-07-18_14-27-25.avi
2025-07-22 11:31:21,201 - INFO -   原始大小: 208.1 MB
2025-07-22 11:31:21,201 - INFO -   转换后大小: 26.9 MB
2025-07-22 11:31:21,201 - INFO -   压缩率: 87.1%
2025-07-22 11:31:21,201 - INFO -   处理进度: 5/30 - dulu_facial_expression_2025-07-18_14-27-58.avi
2025-07-22 11:31:21,201 - INFO - 开始转换: dulu_facial_expression_2025-07-18_14-27-58.avi
2025-07-22 11:32:34,450 - INFO - 转换成功: dulu_facial_expression_2025-07-18_14-27-58.avi
2025-07-22 11:32:34,450 - INFO -   原始大小: 195.9 MB
2025-07-22 11:32:34,450 - INFO -   转换后大小: 26.2 MB
2025-07-22 11:32:34,450 - INFO -   压缩率: 86.6%
2025-07-22 11:32:34,450 - INFO -   处理进度: 6/30 - dulu_facial_expression_2025-07-18_14-28-29.avi
2025-07-22 11:32:34,450 - INFO - 开始转换: dulu_facial_expression_2025-07-18_14-28-29.avi
2025-07-22 11:33:45,177 - INFO - 转换成功: dulu_facial_expression_2025-07-18_14-28-29.avi
2025-07-22 11:33:45,177 - INFO -   原始大小: 185.7 MB
2025-07-22 11:33:45,177 - INFO -   转换后大小: 25.2 MB
2025-07-22 11:33:45,177 - INFO -   压缩率: 86.4%
2025-07-22 11:33:45,177 - INFO -   处理进度: 7/30 - dulu_facial_expression_2025-07-18_14-28-57.avi
2025-07-22 11:33:45,177 - INFO - 开始转换: dulu_facial_expression_2025-07-18_14-28-57.avi
2025-07-22 11:35:20,081 - INFO - 转换成功: dulu_facial_expression_2025-07-18_14-28-57.avi
2025-07-22 11:35:20,082 - INFO -   原始大小: 177.5 MB
2025-07-22 11:35:20,082 - INFO -   转换后大小: 24.8 MB
2025-07-22 11:35:20,082 - INFO -   压缩率: 86.0%
2025-07-22 11:35:20,083 - INFO -   处理进度: 8/30 - dulu_facial_expression_2025-07-18_14-29-24.avi
2025-07-22 11:35:20,084 - INFO - 开始转换: dulu_facial_expression_2025-07-18_14-29-24.avi
2025-07-22 11:36:49,813 - INFO - 转换成功: dulu_facial_expression_2025-07-18_14-29-24.avi
2025-07-22 11:36:49,813 - INFO -   原始大小: 210.3 MB
2025-07-22 11:36:49,813 - INFO -   转换后大小: 30.4 MB
2025-07-22 11:36:49,813 - INFO -   压缩率: 85.6%
2025-07-22 11:36:49,828 - INFO -   处理进度: 9/30 - dulu_facial_expression_2025-07-18_14-29-54.avi
2025-07-22 11:36:49,828 - INFO - 开始转换: dulu_facial_expression_2025-07-18_14-29-54.avi
2025-07-22 11:38:34,155 - INFO - 转换成功: dulu_facial_expression_2025-07-18_14-29-54.avi
2025-07-22 11:38:34,156 - INFO -   原始大小: 190.6 MB
2025-07-22 11:38:34,157 - INFO -   转换后大小: 28.1 MB
2025-07-22 11:38:34,157 - INFO -   压缩率: 85.3%
2025-07-22 11:38:34,158 - INFO -   处理进度: 10/30 - dulu_facial_expression_2025-07-18_14-30-21.avi
2025-07-22 11:38:34,159 - INFO - 开始转换: dulu_facial_expression_2025-07-18_14-30-21.avi
2025-07-22 11:40:30,452 - INFO - 转换成功: dulu_facial_expression_2025-07-18_14-30-21.avi
2025-07-22 11:40:30,453 - INFO -   原始大小: 212.9 MB
2025-07-22 11:40:30,453 - INFO -   转换后大小: 32.5 MB
2025-07-22 11:40:30,454 - INFO -   压缩率: 84.8%
2025-07-22 11:40:30,455 - INFO -   处理进度: 11/30 - dulu_facial_expression_2025-07-18_14-30-51.avi
2025-07-22 11:40:30,456 - INFO - 开始转换: dulu_facial_expression_2025-07-18_14-30-51.avi
2025-07-22 11:44:05,554 - INFO - 转换成功: dulu_facial_expression_2025-07-18_14-30-51.avi
2025-07-22 11:44:05,554 - INFO -   原始大小: 380.1 MB
2025-07-22 11:44:05,555 - INFO -   转换后大小: 62.1 MB
2025-07-22 11:44:05,555 - INFO -   压缩率: 83.7%
2025-07-22 11:44:05,556 - INFO -   处理进度: 12/30 - dulu_facial_expression_2025-07-18_14-31-43.avi
2025-07-22 11:44:05,557 - INFO - 开始转换: dulu_facial_expression_2025-07-18_14-31-43.avi
2025-07-22 11:46:13,716 - INFO - 转换成功: dulu_facial_expression_2025-07-18_14-31-43.avi
2025-07-22 11:46:13,716 - INFO -   原始大小: 236.7 MB
2025-07-22 11:46:13,717 - INFO -   转换后大小: 38.2 MB
2025-07-22 11:46:13,717 - INFO -   压缩率: 83.9%
2025-07-22 11:46:13,718 - INFO -   处理进度: 13/30 - dulu_facial_expression_2025-07-18_14-32-16.avi
2025-07-22 11:46:13,718 - INFO - 开始转换: dulu_facial_expression_2025-07-18_14-32-16.avi
2025-07-22 11:48:12,337 - INFO - 转换成功: dulu_facial_expression_2025-07-18_14-32-16.avi
2025-07-22 11:48:12,337 - INFO -   原始大小: 214.3 MB
2025-07-22 11:48:12,337 - INFO -   转换后大小: 35.3 MB
2025-07-22 11:48:12,337 - INFO -   压缩率: 83.5%
2025-07-22 11:48:12,343 - INFO -   处理进度: 14/30 - dulu_facial_expression_2025-07-18_14-32-44.avi
2025-07-22 11:48:12,343 - INFO - 开始转换: dulu_facial_expression_2025-07-18_14-32-44.avi
2025-07-22 11:50:23,288 - INFO - 转换成功: dulu_facial_expression_2025-07-18_14-32-44.avi
2025-07-22 11:50:23,288 - INFO -   原始大小: 240.7 MB
2025-07-22 11:50:23,288 - INFO -   转换后大小: 40.0 MB
2025-07-22 11:50:23,288 - INFO -   压缩率: 83.4%
2025-07-22 11:50:23,288 - INFO -   处理进度: 15/30 - dulu_facial_expression_2025-07-18_14-33-15.avi
2025-07-22 11:50:23,288 - INFO - 开始转换: dulu_facial_expression_2025-07-18_14-33-15.avi
2025-07-22 11:52:32,029 - INFO - 转换成功: dulu_facial_expression_2025-07-18_14-33-15.avi
2025-07-22 11:52:32,029 - INFO -   原始大小: 239.3 MB
2025-07-22 11:52:32,029 - INFO -   转换后大小: 39.8 MB
2025-07-22 11:52:32,029 - INFO -   压缩率: 83.4%
2025-07-22 11:52:32,029 - INFO -   处理进度: 16/30 - dulu_facial_expression_2025-07-18_14-33-47.avi
2025-07-22 11:52:32,029 - INFO - 开始转换: dulu_facial_expression_2025-07-18_14-33-47.avi
2025-07-22 11:54:50,368 - INFO - 转换成功: dulu_facial_expression_2025-07-18_14-33-47.avi
2025-07-22 11:54:50,368 - INFO -   原始大小: 287.2 MB
2025-07-22 11:54:50,368 - INFO -   转换后大小: 49.1 MB
2025-07-22 11:54:50,368 - INFO -   压缩率: 82.9%
2025-07-22 11:54:50,368 - INFO -   处理进度: 17/30 - dulu_facial_expression_2025-07-18_14-34-24.avi
2025-07-22 11:54:50,373 - INFO - 开始转换: dulu_facial_expression_2025-07-18_14-34-24.avi
2025-07-22 11:56:34,512 - INFO - 转换成功: dulu_facial_expression_2025-07-18_14-34-24.avi
2025-07-22 11:56:34,517 - INFO -   原始大小: 248.8 MB
2025-07-22 11:56:34,517 - INFO -   转换后大小: 42.0 MB
2025-07-22 11:56:34,517 - INFO -   压缩率: 83.1%
2025-07-22 11:56:34,517 - INFO -   处理进度: 18/30 - dulu_facial_expression_2025-07-18_14-34-55.avi
2025-07-22 11:56:34,517 - INFO - 开始转换: dulu_facial_expression_2025-07-18_14-34-55.avi
2025-07-22 11:58:38,118 - INFO - 转换成功: dulu_facial_expression_2025-07-18_14-34-55.avi
2025-07-22 11:58:38,118 - INFO -   原始大小: 253.1 MB
2025-07-22 11:58:38,118 - INFO -   转换后大小: 43.2 MB
2025-07-22 11:58:38,118 - INFO -   压缩率: 82.9%
2025-07-22 11:58:38,124 - INFO -   处理进度: 19/30 - dulu_facial_expression_2025-07-18_14-35-28.avi
2025-07-22 11:58:38,124 - INFO - 开始转换: dulu_facial_expression_2025-07-18_14-35-28.avi
2025-07-22 12:00:32,332 - INFO - 转换成功: dulu_facial_expression_2025-07-18_14-35-28.avi
2025-07-22 12:00:32,332 - INFO -   原始大小: 215.4 MB
2025-07-22 12:00:32,332 - INFO -   转换后大小: 37.0 MB
2025-07-22 12:00:32,332 - INFO -   压缩率: 82.8%
2025-07-22 12:00:32,332 - INFO -   处理进度: 20/30 - dulu_facial_expression_2025-07-18_14-35-57.avi
2025-07-22 12:00:32,332 - INFO - 开始转换: dulu_facial_expression_2025-07-18_14-35-57.avi
2025-07-22 12:02:57,107 - INFO - 转换成功: dulu_facial_expression_2025-07-18_14-35-57.avi
2025-07-22 12:02:57,107 - INFO -   原始大小: 273.2 MB
2025-07-22 12:02:57,108 - INFO -   转换后大小: 47.3 MB
2025-07-22 12:02:57,108 - INFO -   压缩率: 82.7%
2025-07-22 12:02:57,110 - INFO -   处理进度: 21/30 - dulu_facial_expression_2025-07-18_14-36-36.avi
2025-07-22 12:02:57,110 - INFO - 开始转换: dulu_facial_expression_2025-07-18_14-36-36.avi
2025-07-22 12:04:53,936 - INFO - 转换成功: dulu_facial_expression_2025-07-18_14-36-36.avi
2025-07-22 12:04:53,936 - INFO -   原始大小: 240.7 MB
2025-07-22 12:04:53,936 - INFO -   转换后大小: 41.8 MB
2025-07-22 12:04:53,936 - INFO -   压缩率: 82.6%
2025-07-22 12:04:53,937 - INFO -   处理进度: 22/30 - dulu_facial_expression_2025-07-18_14-37-06.avi
2025-07-22 12:04:53,937 - INFO - 开始转换: dulu_facial_expression_2025-07-18_14-37-06.avi
2025-07-22 12:06:53,162 - INFO - 转换成功: dulu_facial_expression_2025-07-18_14-37-06.avi
2025-07-22 12:06:53,162 - INFO -   原始大小: 268.9 MB
2025-07-22 12:06:53,162 - INFO -   转换后大小: 46.5 MB
2025-07-22 12:06:53,162 - INFO -   压缩率: 82.7%
2025-07-22 12:06:53,162 - INFO -   处理进度: 23/30 - dulu_facial_expression_2025-07-18_14-37-37.avi
2025-07-22 12:06:53,162 - INFO - 开始转换: dulu_facial_expression_2025-07-18_14-37-37.avi
2025-07-22 12:08:31,980 - INFO - 转换成功: dulu_facial_expression_2025-07-18_14-37-37.avi
2025-07-22 12:08:31,980 - INFO -   原始大小: 246.9 MB
2025-07-22 12:08:31,980 - INFO -   转换后大小: 42.8 MB
2025-07-22 12:08:31,980 - INFO -   压缩率: 82.7%
2025-07-22 12:08:31,980 - INFO -   处理进度: 24/30 - dulu_facial_expression_2025-07-18_14-38-06.avi
2025-07-22 12:08:31,980 - INFO - 开始转换: dulu_facial_expression_2025-07-18_14-38-06.avi
2025-07-22 12:11:17,053 - INFO - 转换成功: dulu_facial_expression_2025-07-18_14-38-06.avi
2025-07-22 12:11:17,053 - INFO -   原始大小: 422.8 MB
2025-07-22 12:11:17,053 - INFO -   转换后大小: 73.8 MB
2025-07-22 12:11:17,053 - INFO -   压缩率: 82.5%
2025-07-22 12:11:17,053 - INFO -   处理进度: 25/30 - dulu_facial_expression_2025-07-18_14-38-58.avi
2025-07-22 12:11:17,058 - INFO - 开始转换: dulu_facial_expression_2025-07-18_14-38-58.avi
2025-07-22 12:12:46,510 - INFO - 转换成功: dulu_facial_expression_2025-07-18_14-38-58.avi
2025-07-22 12:12:46,511 - INFO -   原始大小: 235.2 MB
2025-07-22 12:12:46,511 - INFO -   转换后大小: 41.0 MB
2025-07-22 12:12:46,512 - INFO -   压缩率: 82.6%
2025-07-22 12:12:46,512 - INFO -   处理进度: 26/30 - dulu_facial_expression_2025-07-18_14-39-27.avi
2025-07-22 12:12:46,512 - INFO - 开始转换: dulu_facial_expression_2025-07-18_14-39-27.avi
2025-07-22 12:14:13,443 - INFO - 转换成功: dulu_facial_expression_2025-07-18_14-39-27.avi
2025-07-22 12:14:13,444 - INFO -   原始大小: 222.3 MB
2025-07-22 12:14:13,444 - INFO -   转换后大小: 38.5 MB
2025-07-22 12:14:13,444 - INFO -   压缩率: 82.7%
2025-07-22 12:14:13,444 - INFO -   处理进度: 27/30 - dulu_facial_expression_2025-07-18_14-39-53.avi
2025-07-22 12:14:13,444 - INFO - 开始转换: dulu_facial_expression_2025-07-18_14-39-53.avi
2025-07-22 12:15:54,565 - INFO - 转换成功: dulu_facial_expression_2025-07-18_14-39-53.avi
2025-07-22 12:15:54,567 - INFO -   原始大小: 256.8 MB
2025-07-22 12:15:54,567 - INFO -   转换后大小: 45.1 MB
2025-07-22 12:15:54,567 - INFO -   压缩率: 82.4%
2025-07-22 12:15:54,567 - INFO -   处理进度: 28/30 - dulu_facial_expression_2025-07-18_14-40-25.avi
2025-07-22 12:15:54,567 - INFO - 开始转换: dulu_facial_expression_2025-07-18_14-40-25.avi
2025-07-22 12:17:07,697 - INFO - 转换成功: dulu_facial_expression_2025-07-18_14-40-25.avi
2025-07-22 12:17:07,697 - INFO -   原始大小: 187.4 MB
2025-07-22 12:17:07,697 - INFO -   转换后大小: 33.1 MB
2025-07-22 12:17:07,697 - INFO -   压缩率: 82.3%
2025-07-22 12:17:07,702 - INFO -   处理进度: 29/30 - dulu_facial_expression_2025-07-18_14-40-53.avi
2025-07-22 12:17:07,703 - INFO - 开始转换: dulu_facial_expression_2025-07-18_14-40-53.avi
2025-07-22 12:18:38,369 - INFO - 转换成功: dulu_facial_expression_2025-07-18_14-40-53.avi
2025-07-22 12:18:38,369 - INFO -   原始大小: 232.0 MB
2025-07-22 12:18:38,369 - INFO -   转换后大小: 40.4 MB
2025-07-22 12:18:38,369 - INFO -   压缩率: 82.6%
2025-07-22 12:18:38,374 - INFO -   处理进度: 30/30 - dulu_facial_expression_2025-07-18_14-41-20.avi
2025-07-22 12:18:38,374 - INFO - 开始转换: dulu_facial_expression_2025-07-18_14-41-20.avi
2025-07-22 12:21:03,627 - INFO - 转换成功: dulu_facial_expression_2025-07-18_14-41-20.avi
2025-07-22 12:21:03,627 - INFO -   原始大小: 359.7 MB
2025-07-22 12:21:03,627 - INFO -   转换后大小: 62.8 MB
2025-07-22 12:21:03,627 - INFO -   压缩率: 82.6%
2025-07-22 12:21:03,627 - INFO -   该目录转换完成: 30/30 个文件
2025-07-22 12:21:03,627 - INFO - 处理video目录: C:\Users\<USER>\Desktop\curiosity_pupil\data\20250718_145240_dulu\video
2025-07-22 12:21:03,632 - INFO -   找到 30 个AVI文件
2025-07-22 12:21:03,632 - INFO -   处理进度: 1/30 - dulu_facial_expression_2025-07-18_14-53-36.avi
2025-07-22 12:21:03,632 - INFO - 开始转换: dulu_facial_expression_2025-07-18_14-53-36.avi
2025-07-22 12:22:06,010 - INFO - 转换成功: dulu_facial_expression_2025-07-18_14-53-36.avi
2025-07-22 12:22:06,010 - INFO -   原始大小: 151.5 MB
2025-07-22 12:22:06,010 - INFO -   转换后大小: 14.8 MB
2025-07-22 12:22:06,010 - INFO -   压缩率: 90.2%
2025-07-22 12:22:06,010 - INFO -   处理进度: 2/30 - dulu_facial_expression_2025-07-18_14-54-11.avi
2025-07-22 12:22:06,010 - INFO - 开始转换: dulu_facial_expression_2025-07-18_14-54-11.avi
2025-07-22 12:23:06,725 - INFO - 转换成功: dulu_facial_expression_2025-07-18_14-54-11.avi
2025-07-22 12:23:06,731 - INFO -   原始大小: 146.3 MB
2025-07-22 12:23:06,731 - INFO -   转换后大小: 14.3 MB
2025-07-22 12:23:06,731 - INFO -   压缩率: 90.2%
2025-07-22 12:23:06,731 - INFO -   处理进度: 3/30 - dulu_facial_expression_2025-07-18_14-54-40.avi
2025-07-22 12:23:06,731 - INFO - 开始转换: dulu_facial_expression_2025-07-18_14-54-40.avi
2025-07-22 12:24:01,442 - INFO - 转换成功: dulu_facial_expression_2025-07-18_14-54-40.avi
2025-07-22 12:24:01,442 - INFO -   原始大小: 134.0 MB
2025-07-22 12:24:01,442 - INFO -   转换后大小: 12.9 MB
2025-07-22 12:24:01,442 - INFO -   压缩率: 90.4%
2025-07-22 12:24:01,442 - INFO -   处理进度: 4/30 - dulu_facial_expression_2025-07-18_14-55-06.avi
2025-07-22 12:24:01,442 - INFO - 开始转换: dulu_facial_expression_2025-07-18_14-55-06.avi
2025-07-22 12:24:56,325 - INFO - 转换成功: dulu_facial_expression_2025-07-18_14-55-06.avi
2025-07-22 12:24:56,325 - INFO -   原始大小: 134.8 MB
2025-07-22 12:24:56,325 - INFO -   转换后大小: 13.0 MB
2025-07-22 12:24:56,325 - INFO -   压缩率: 90.4%
2025-07-22 12:24:56,325 - INFO -   处理进度: 5/30 - dulu_facial_expression_2025-07-18_14-55-32.avi
2025-07-22 12:24:56,325 - INFO - 开始转换: dulu_facial_expression_2025-07-18_14-55-32.avi
2025-07-22 12:26:09,587 - INFO - 转换成功: dulu_facial_expression_2025-07-18_14-55-32.avi
2025-07-22 12:26:09,587 - INFO -   原始大小: 159.4 MB
2025-07-22 12:26:09,587 - INFO -   转换后大小: 15.8 MB
2025-07-22 12:26:09,587 - INFO -   压缩率: 90.1%
2025-07-22 12:26:09,587 - INFO -   处理进度: 6/30 - dulu_facial_expression_2025-07-18_14-56-02.avi
2025-07-22 12:26:09,587 - INFO - 开始转换: dulu_facial_expression_2025-07-18_14-56-02.avi
2025-07-22 12:27:21,511 - INFO - 转换成功: dulu_facial_expression_2025-07-18_14-56-02.avi
2025-07-22 12:27:21,511 - INFO -   原始大小: 180.5 MB
2025-07-22 12:27:21,511 - INFO -   转换后大小: 18.8 MB
2025-07-22 12:27:21,511 - INFO -   压缩率: 89.6%
2025-07-22 12:27:21,516 - INFO -   处理进度: 7/30 - dulu_facial_expression_2025-07-18_14-56-35.avi
2025-07-22 12:27:21,516 - INFO - 开始转换: dulu_facial_expression_2025-07-18_14-56-35.avi
2025-07-22 12:29:39,134 - INFO - 转换成功: dulu_facial_expression_2025-07-18_14-56-35.avi
2025-07-22 12:29:39,134 - INFO -   原始大小: 332.9 MB
2025-07-22 12:29:39,134 - INFO -   转换后大小: 36.8 MB
2025-07-22 12:29:39,134 - INFO -   压缩率: 88.9%
2025-07-22 12:29:39,134 - INFO -   处理进度: 8/30 - dulu_facial_expression_2025-07-18_14-57-35.avi
2025-07-22 12:29:39,139 - INFO - 开始转换: dulu_facial_expression_2025-07-18_14-57-35.avi
2025-07-22 12:30:49,218 - INFO - 转换成功: dulu_facial_expression_2025-07-18_14-57-35.avi
2025-07-22 12:30:49,218 - INFO -   原始大小: 178.3 MB
2025-07-22 12:30:49,218 - INFO -   转换后大小: 21.1 MB
2025-07-22 12:30:49,218 - INFO -   压缩率: 88.2%
2025-07-22 12:30:49,218 - INFO -   处理进度: 9/30 - dulu_facial_expression_2025-07-18_14-58-06.avi
2025-07-22 12:30:49,218 - INFO - 开始转换: dulu_facial_expression_2025-07-18_14-58-06.avi
2025-07-22 12:31:51,272 - INFO - 转换成功: dulu_facial_expression_2025-07-18_14-58-06.avi
2025-07-22 12:31:51,272 - INFO -   原始大小: 155.8 MB
2025-07-22 12:31:51,272 - INFO -   转换后大小: 18.9 MB
2025-07-22 12:31:51,272 - INFO -   压缩率: 87.8%
2025-07-22 12:31:51,277 - INFO -   处理进度: 10/30 - dulu_facial_expression_2025-07-18_14-58-32.avi
2025-07-22 12:31:51,277 - INFO - 开始转换: dulu_facial_expression_2025-07-18_14-58-32.avi
2025-07-22 12:33:26,946 - INFO - 转换成功: dulu_facial_expression_2025-07-18_14-58-32.avi
2025-07-22 12:33:26,946 - INFO -   原始大小: 242.5 MB
2025-07-22 12:33:26,951 - INFO -   转换后大小: 30.6 MB
2025-07-22 12:33:26,951 - INFO -   压缩率: 87.4%
2025-07-22 12:33:26,952 - INFO -   处理进度: 11/30 - dulu_facial_expression_2025-07-18_14-59-12.avi
2025-07-22 12:33:26,952 - INFO - 开始转换: dulu_facial_expression_2025-07-18_14-59-12.avi
2025-07-22 12:34:38,111 - INFO - 转换成功: dulu_facial_expression_2025-07-18_14-59-12.avi
2025-07-22 12:34:38,111 - INFO -   原始大小: 163.5 MB
2025-07-22 12:34:38,111 - INFO -   转换后大小: 21.3 MB
2025-07-22 12:34:38,111 - INFO -   压缩率: 87.0%
2025-07-22 12:34:38,111 - INFO -   处理进度: 12/30 - dulu_facial_expression_2025-07-18_14-59-38.avi
2025-07-22 12:34:38,116 - INFO - 开始转换: dulu_facial_expression_2025-07-18_14-59-38.avi
2025-07-22 12:36:16,823 - INFO - 转换成功: dulu_facial_expression_2025-07-18_14-59-38.avi
2025-07-22 12:36:16,823 - INFO -   原始大小: 255.1 MB
2025-07-22 12:36:16,823 - INFO -   转换后大小: 34.5 MB
2025-07-22 12:36:16,823 - INFO -   压缩率: 86.5%
2025-07-22 12:36:16,823 - INFO -   处理进度: 13/30 - dulu_facial_expression_2025-07-18_15-00-18.avi
2025-07-22 12:36:16,829 - INFO - 开始转换: dulu_facial_expression_2025-07-18_15-00-18.avi
2025-07-22 12:37:31,656 - INFO - 转换成功: dulu_facial_expression_2025-07-18_15-00-18.avi
2025-07-22 12:37:31,656 - INFO -   原始大小: 189.3 MB
2025-07-22 12:37:31,656 - INFO -   转换后大小: 26.1 MB
2025-07-22 12:37:31,656 - INFO -   压缩率: 86.2%
2025-07-22 12:37:31,656 - INFO -   处理进度: 14/30 - dulu_facial_expression_2025-07-18_15-00-47.avi
2025-07-22 12:37:31,656 - INFO - 开始转换: dulu_facial_expression_2025-07-18_15-00-47.avi
2025-07-22 12:39:00,619 - INFO - 转换成功: dulu_facial_expression_2025-07-18_15-00-47.avi
2025-07-22 12:39:00,619 - INFO -   原始大小: 233.6 MB
2025-07-22 12:39:00,619 - INFO -   转换后大小: 32.6 MB
2025-07-22 12:39:00,619 - INFO -   压缩率: 86.1%
2025-07-22 12:39:00,619 - INFO -   处理进度: 15/30 - dulu_facial_expression_2025-07-18_15-01-21.avi
2025-07-22 12:39:00,619 - INFO - 开始转换: dulu_facial_expression_2025-07-18_15-01-21.avi
2025-07-22 12:40:32,114 - INFO - 转换成功: dulu_facial_expression_2025-07-18_15-01-21.avi
2025-07-22 12:40:32,119 - INFO -   原始大小: 234.6 MB
2025-07-22 12:40:32,120 - INFO -   转换后大小: 33.8 MB
2025-07-22 12:40:32,121 - INFO -   压缩率: 85.6%
2025-07-22 12:40:32,121 - INFO -   处理进度: 16/30 - dulu_facial_expression_2025-07-18_15-01-55.avi
2025-07-22 12:40:32,121 - INFO - 开始转换: dulu_facial_expression_2025-07-18_15-01-55.avi
2025-07-22 12:41:51,618 - INFO - 转换成功: dulu_facial_expression_2025-07-18_15-01-55.avi
2025-07-22 12:41:51,623 - INFO -   原始大小: 204.4 MB
2025-07-22 12:41:51,623 - INFO -   转换后大小: 30.4 MB
2025-07-22 12:41:51,623 - INFO -   压缩率: 85.1%
2025-07-22 12:41:51,623 - INFO -   处理进度: 17/30 - dulu_facial_expression_2025-07-18_15-02-24.avi
2025-07-22 12:41:51,623 - INFO - 开始转换: dulu_facial_expression_2025-07-18_15-02-24.avi
2025-07-22 12:43:36,040 - INFO - 转换成功: dulu_facial_expression_2025-07-18_15-02-24.avi
2025-07-22 12:43:36,040 - INFO -   原始大小: 248.4 MB
2025-07-22 12:43:36,040 - INFO -   转换后大小: 37.6 MB
2025-07-22 12:43:36,040 - INFO -   压缩率: 84.9%
2025-07-22 12:43:36,045 - INFO -   处理进度: 18/30 - dulu_facial_expression_2025-07-18_15-02-58.avi
2025-07-22 12:43:36,046 - INFO - 开始转换: dulu_facial_expression_2025-07-18_15-02-58.avi
2025-07-22 12:44:50,627 - INFO - 转换成功: dulu_facial_expression_2025-07-18_15-02-58.avi
2025-07-22 12:44:50,627 - INFO -   原始大小: 198.9 MB
2025-07-22 12:44:50,627 - INFO -   转换后大小: 30.7 MB
2025-07-22 12:44:50,627 - INFO -   压缩率: 84.6%
2025-07-22 12:44:50,627 - INFO -   处理进度: 19/30 - dulu_facial_expression_2025-07-18_15-03-25.avi
2025-07-22 12:44:50,632 - INFO - 开始转换: dulu_facial_expression_2025-07-18_15-03-25.avi
2025-07-22 12:46:22,702 - INFO - 转换成功: dulu_facial_expression_2025-07-18_15-03-25.avi
2025-07-22 12:46:22,702 - INFO -   原始大小: 242.0 MB
2025-07-22 12:46:22,702 - INFO -   转换后大小: 38.1 MB
2025-07-22 12:46:22,702 - INFO -   压缩率: 84.2%
2025-07-22 12:46:22,702 - INFO -   处理进度: 20/30 - dulu_facial_expression_2025-07-18_15-03-58.avi
2025-07-22 12:46:22,702 - INFO - 开始转换: dulu_facial_expression_2025-07-18_15-03-58.avi
2025-07-22 12:47:41,705 - INFO - 转换成功: dulu_facial_expression_2025-07-18_15-03-58.avi
2025-07-22 12:47:41,705 - INFO -   原始大小: 202.6 MB
2025-07-22 12:47:41,705 - INFO -   转换后大小: 32.5 MB
2025-07-22 12:47:41,705 - INFO -   压缩率: 84.0%
2025-07-22 12:47:41,705 - INFO -   处理进度: 21/30 - dulu_facial_expression_2025-07-18_15-04-27.avi
2025-07-22 12:47:41,705 - INFO - 开始转换: dulu_facial_expression_2025-07-18_15-04-27.avi
2025-07-22 12:49:30,417 - INFO - 转换成功: dulu_facial_expression_2025-07-18_15-04-27.avi
2025-07-22 12:49:30,419 - INFO -   原始大小: 239.9 MB
2025-07-22 12:49:30,423 - INFO -   转换后大小: 38.6 MB
2025-07-22 12:49:30,423 - INFO -   压缩率: 83.9%
2025-07-22 12:49:30,424 - INFO -   处理进度: 22/30 - dulu_facial_expression_2025-07-18_15-04-59.avi
2025-07-22 12:49:30,424 - INFO - 开始转换: dulu_facial_expression_2025-07-18_15-04-59.avi
2025-07-22 12:51:03,926 - INFO - 转换成功: dulu_facial_expression_2025-07-18_15-04-59.avi
2025-07-22 12:51:03,927 - INFO -   原始大小: 225.7 MB
2025-07-22 12:51:03,927 - INFO -   转换后大小: 36.3 MB
2025-07-22 12:51:03,928 - INFO -   压缩率: 83.9%
2025-07-22 12:51:03,928 - INFO -   处理进度: 23/30 - dulu_facial_expression_2025-07-18_15-05-29.avi
2025-07-22 12:51:03,928 - INFO - 开始转换: dulu_facial_expression_2025-07-18_15-05-29.avi
2025-07-22 12:52:36,208 - INFO - 转换成功: dulu_facial_expression_2025-07-18_15-05-29.avi
2025-07-22 12:52:36,208 - INFO -   原始大小: 194.0 MB
2025-07-22 12:52:36,208 - INFO -   转换后大小: 31.5 MB
2025-07-22 12:52:36,208 - INFO -   压缩率: 83.8%
2025-07-22 12:52:36,208 - INFO -   处理进度: 24/30 - dulu_facial_expression_2025-07-18_15-05-54.avi
2025-07-22 12:52:36,208 - INFO - 开始转换: dulu_facial_expression_2025-07-18_15-05-54.avi
2025-07-22 12:54:29,074 - INFO - 转换成功: dulu_facial_expression_2025-07-18_15-05-54.avi
2025-07-22 12:54:29,074 - INFO -   原始大小: 228.3 MB
2025-07-22 12:54:29,074 - INFO -   转换后大小: 37.6 MB
2025-07-22 12:54:29,074 - INFO -   压缩率: 83.6%
2025-07-22 12:54:29,074 - INFO -   处理进度: 25/30 - dulu_facial_expression_2025-07-18_15-06-25.avi
2025-07-22 12:54:29,074 - INFO - 开始转换: dulu_facial_expression_2025-07-18_15-06-25.avi
2025-07-22 12:56:25,175 - INFO - 转换成功: dulu_facial_expression_2025-07-18_15-06-25.avi
2025-07-22 12:56:25,175 - INFO -   原始大小: 254.0 MB
2025-07-22 12:56:25,175 - INFO -   转换后大小: 42.2 MB
2025-07-22 12:56:25,175 - INFO -   压缩率: 83.4%
2025-07-22 12:56:25,175 - INFO -   处理进度: 26/30 - dulu_facial_expression_2025-07-18_15-07-06.avi
2025-07-22 12:56:25,175 - INFO - 开始转换: dulu_facial_expression_2025-07-18_15-07-06.avi
2025-07-22 12:57:59,885 - INFO - 转换成功: dulu_facial_expression_2025-07-18_15-07-06.avi
2025-07-22 12:57:59,885 - INFO -   原始大小: 200.1 MB
2025-07-22 12:57:59,885 - INFO -   转换后大小: 33.1 MB
2025-07-22 12:57:59,885 - INFO -   压缩率: 83.4%
2025-07-22 12:57:59,885 - INFO -   处理进度: 27/30 - dulu_facial_expression_2025-07-18_15-07-31.avi
2025-07-22 12:57:59,885 - INFO - 开始转换: dulu_facial_expression_2025-07-18_15-07-31.avi
2025-07-22 12:59:36,818 - INFO - 转换成功: dulu_facial_expression_2025-07-18_15-07-31.avi
2025-07-22 12:59:36,818 - INFO -   原始大小: 227.3 MB
2025-07-22 12:59:36,818 - INFO -   转换后大小: 37.8 MB
2025-07-22 12:59:36,818 - INFO -   压缩率: 83.4%
2025-07-22 12:59:36,833 - INFO -   处理进度: 28/30 - dulu_facial_expression_2025-07-18_15-08-00.avi
2025-07-22 12:59:36,833 - INFO - 开始转换: dulu_facial_expression_2025-07-18_15-08-00.avi
2025-07-22 13:01:11,591 - INFO - 转换成功: dulu_facial_expression_2025-07-18_15-08-00.avi
2025-07-22 13:01:11,591 - INFO -   原始大小: 208.3 MB
2025-07-22 13:01:11,591 - INFO -   转换后大小: 34.5 MB
2025-07-22 13:01:11,591 - INFO -   压缩率: 83.4%
2025-07-22 13:01:11,591 - INFO -   处理进度: 29/30 - dulu_facial_expression_2025-07-18_15-08-25.avi
2025-07-22 13:01:11,591 - INFO - 开始转换: dulu_facial_expression_2025-07-18_15-08-25.avi
2025-07-22 13:03:03,690 - INFO - 转换成功: dulu_facial_expression_2025-07-18_15-08-25.avi
2025-07-22 13:03:03,690 - INFO -   原始大小: 231.2 MB
2025-07-22 13:03:03,690 - INFO -   转换后大小: 38.6 MB
2025-07-22 13:03:03,690 - INFO -   压缩率: 83.3%
2025-07-22 13:03:03,690 - INFO -   处理进度: 30/30 - dulu_facial_expression_2025-07-18_15-08-55.avi
2025-07-22 13:03:03,694 - INFO - 开始转换: dulu_facial_expression_2025-07-18_15-08-55.avi
2025-07-22 13:04:43,093 - INFO - 转换成功: dulu_facial_expression_2025-07-18_15-08-55.avi
2025-07-22 13:04:43,093 - INFO -   原始大小: 205.8 MB
2025-07-22 13:04:43,093 - INFO -   转换后大小: 34.4 MB
2025-07-22 13:04:43,093 - INFO -   压缩率: 83.3%
2025-07-22 13:04:43,093 - INFO -   该目录转换完成: 30/30 个文件
2025-07-22 13:04:43,093 - INFO - 处理video目录: C:\Users\<USER>\Desktop\curiosity_pupil\data\20250718_153841_dulu\video
2025-07-22 13:04:43,093 - INFO -   找到 30 个AVI文件
2025-07-22 13:04:43,103 - INFO -   处理进度: 1/30 - dulu_facial_expression_2025-07-18_15-39-37.avi
2025-07-22 13:04:43,104 - INFO - 开始转换: dulu_facial_expression_2025-07-18_15-39-37.avi
2025-07-22 13:05:55,865 - INFO - 转换成功: dulu_facial_expression_2025-07-18_15-39-37.avi
2025-07-22 13:05:55,865 - INFO -   原始大小: 140.2 MB
2025-07-22 13:05:55,865 - INFO -   转换后大小: 14.0 MB
2025-07-22 13:05:55,865 - INFO -   压缩率: 90.0%
2025-07-22 13:05:55,865 - INFO -   处理进度: 2/30 - dulu_facial_expression_2025-07-18_15-40-05.avi
2025-07-22 13:05:55,865 - INFO - 开始转换: dulu_facial_expression_2025-07-18_15-40-05.avi
2025-07-22 13:07:20,006 - INFO - 转换成功: dulu_facial_expression_2025-07-18_15-40-05.avi
2025-07-22 13:07:20,007 - INFO -   原始大小: 155.3 MB
2025-07-22 13:07:20,007 - INFO -   转换后大小: 15.2 MB
2025-07-22 13:07:20,007 - INFO -   压缩率: 90.2%
2025-07-22 13:07:20,008 - INFO -   处理进度: 3/30 - dulu_facial_expression_2025-07-18_15-40-36.avi
2025-07-22 13:07:20,008 - INFO - 开始转换: dulu_facial_expression_2025-07-18_15-40-36.avi
2025-07-22 13:08:50,518 - INFO - 转换成功: dulu_facial_expression_2025-07-18_15-40-36.avi
2025-07-22 13:08:50,518 - INFO -   原始大小: 160.2 MB
2025-07-22 13:08:50,518 - INFO -   转换后大小: 15.9 MB
2025-07-22 13:08:50,518 - INFO -   压缩率: 90.1%
2025-07-22 13:08:50,518 - INFO -   处理进度: 4/30 - dulu_facial_expression_2025-07-18_15-41-09.avi
2025-07-22 13:08:50,518 - INFO - 开始转换: dulu_facial_expression_2025-07-18_15-41-09.avi
2025-07-22 13:09:53,873 - INFO - 转换成功: dulu_facial_expression_2025-07-18_15-41-09.avi
2025-07-22 13:09:53,873 - INFO -   原始大小: 136.8 MB
2025-07-22 13:09:53,873 - INFO -   转换后大小: 13.7 MB
2025-07-22 13:09:53,873 - INFO -   压缩率: 90.0%
2025-07-22 13:09:53,873 - INFO -   处理进度: 5/30 - dulu_facial_expression_2025-07-18_15-41-40.avi
2025-07-22 13:09:53,873 - INFO - 开始转换: dulu_facial_expression_2025-07-18_15-41-40.avi
2025-07-22 13:11:16,017 - INFO - 转换成功: dulu_facial_expression_2025-07-18_15-41-40.avi
2025-07-22 13:11:16,017 - INFO -   原始大小: 185.0 MB
2025-07-22 13:11:16,017 - INFO -   转换后大小: 29.5 MB
2025-07-22 13:11:16,017 - INFO -   压缩率: 84.0%
2025-07-22 13:11:16,017 - INFO -   处理进度: 6/30 - dulu_facial_expression_2025-07-18_15-42-06.avi
2025-07-22 13:11:16,017 - INFO - 开始转换: dulu_facial_expression_2025-07-18_15-42-06.avi
2025-07-22 13:12:54,562 - INFO - 转换成功: dulu_facial_expression_2025-07-18_15-42-06.avi
2025-07-22 13:12:54,562 - INFO -   原始大小: 223.8 MB
2025-07-22 13:12:54,562 - INFO -   转换后大小: 38.9 MB
2025-07-22 13:12:54,562 - INFO -   压缩率: 82.6%
2025-07-22 13:12:54,562 - INFO -   处理进度: 7/30 - dulu_facial_expression_2025-07-18_15-42-34.avi
2025-07-22 13:12:54,562 - INFO - 开始转换: dulu_facial_expression_2025-07-18_15-42-34.avi
2025-07-22 13:15:22,783 - INFO - 转换成功: dulu_facial_expression_2025-07-18_15-42-34.avi
2025-07-22 13:15:22,783 - INFO -   原始大小: 225.0 MB
2025-07-22 13:15:22,783 - INFO -   转换后大小: 40.4 MB
2025-07-22 13:15:22,783 - INFO -   压缩率: 82.1%
2025-07-22 13:15:22,783 - INFO -   处理进度: 8/30 - dulu_facial_expression_2025-07-18_15-43-01.avi
2025-07-22 13:15:22,783 - INFO - 开始转换: dulu_facial_expression_2025-07-18_15-43-01.avi
2025-07-22 13:16:55,139 - INFO - 转换成功: dulu_facial_expression_2025-07-18_15-43-01.avi
2025-07-22 13:16:55,139 - INFO -   原始大小: 242.6 MB
2025-07-22 13:16:55,139 - INFO -   转换后大小: 44.3 MB
2025-07-22 13:16:55,139 - INFO -   压缩率: 81.7%
2025-07-22 13:16:55,139 - INFO -   处理进度: 9/30 - dulu_facial_expression_2025-07-18_15-43-30.avi
2025-07-22 13:16:55,139 - INFO - 开始转换: dulu_facial_expression_2025-07-18_15-43-30.avi
2025-07-22 13:18:34,237 - INFO - 转换成功: dulu_facial_expression_2025-07-18_15-43-30.avi
2025-07-22 13:18:34,237 - INFO -   原始大小: 238.1 MB
2025-07-22 13:18:34,237 - INFO -   转换后大小: 43.9 MB
2025-07-22 13:18:34,237 - INFO -   压缩率: 81.6%
2025-07-22 13:18:34,237 - INFO -   处理进度: 10/30 - dulu_facial_expression_2025-07-18_15-43-58.avi
2025-07-22 13:18:34,237 - INFO - 开始转换: dulu_facial_expression_2025-07-18_15-43-58.avi
2025-07-22 13:20:09,928 - INFO - 转换成功: dulu_facial_expression_2025-07-18_15-43-58.avi
2025-07-22 13:20:09,928 - INFO -   原始大小: 227.8 MB
2025-07-22 13:20:09,928 - INFO -   转换后大小: 37.8 MB
2025-07-22 13:20:09,929 - INFO -   压缩率: 83.4%
2025-07-22 13:20:09,929 - INFO -   处理进度: 11/30 - dulu_facial_expression_2025-07-18_15-44-29.avi
2025-07-22 13:20:09,930 - INFO - 开始转换: dulu_facial_expression_2025-07-18_15-44-29.avi
2025-07-22 13:21:37,824 - INFO - 转换成功: dulu_facial_expression_2025-07-18_15-44-29.avi
2025-07-22 13:21:37,824 - INFO -   原始大小: 197.8 MB
2025-07-22 13:21:37,824 - INFO -   转换后大小: 32.2 MB
2025-07-22 13:21:37,824 - INFO -   压缩率: 83.7%
2025-07-22 13:21:37,824 - INFO -   处理进度: 12/30 - dulu_facial_expression_2025-07-18_15-44-55.avi
2025-07-22 13:21:37,824 - INFO - 开始转换: dulu_facial_expression_2025-07-18_15-44-55.avi
2025-07-22 13:22:59,622 - INFO - 转换成功: dulu_facial_expression_2025-07-18_15-44-55.avi
2025-07-22 13:22:59,622 - INFO -   原始大小: 210.5 MB
2025-07-22 13:22:59,622 - INFO -   转换后大小: 34.8 MB
2025-07-22 13:22:59,633 - INFO -   压缩率: 83.4%
2025-07-22 13:22:59,633 - INFO -   处理进度: 13/30 - dulu_facial_expression_2025-07-18_15-45-22.avi
2025-07-22 13:22:59,633 - INFO - 开始转换: dulu_facial_expression_2025-07-18_15-45-22.avi
2025-07-22 13:24:14,682 - INFO - 转换成功: dulu_facial_expression_2025-07-18_15-45-22.avi
2025-07-22 13:24:14,682 - INFO -   原始大小: 190.0 MB
2025-07-22 13:24:14,682 - INFO -   转换后大小: 32.3 MB
2025-07-22 13:24:14,682 - INFO -   压缩率: 83.0%
2025-07-22 13:24:14,682 - INFO -   处理进度: 14/30 - dulu_facial_expression_2025-07-18_15-45-46.avi
2025-07-22 13:24:14,682 - INFO - 开始转换: dulu_facial_expression_2025-07-18_15-45-46.avi
2025-07-22 13:25:52,499 - INFO - 转换成功: dulu_facial_expression_2025-07-18_15-45-46.avi
2025-07-22 13:25:52,499 - INFO -   原始大小: 229.9 MB
2025-07-22 13:25:52,499 - INFO -   转换后大小: 39.3 MB
2025-07-22 13:25:52,499 - INFO -   压缩率: 82.9%
2025-07-22 13:25:52,514 - INFO -   处理进度: 15/30 - dulu_facial_expression_2025-07-18_15-46-15.avi
2025-07-22 13:25:52,514 - INFO - 开始转换: dulu_facial_expression_2025-07-18_15-46-15.avi
2025-07-22 13:27:18,231 - INFO - 转换成功: dulu_facial_expression_2025-07-18_15-46-15.avi
2025-07-22 13:27:18,231 - INFO -   原始大小: 223.5 MB
2025-07-22 13:27:18,231 - INFO -   转换后大小: 39.7 MB
2025-07-22 13:27:18,231 - INFO -   压缩率: 82.3%
2025-07-22 13:27:18,231 - INFO -   处理进度: 16/30 - dulu_facial_expression_2025-07-18_15-46-42.avi
2025-07-22 13:27:18,231 - INFO - 开始转换: dulu_facial_expression_2025-07-18_15-46-42.avi
2025-07-22 13:29:12,168 - INFO - 转换成功: dulu_facial_expression_2025-07-18_15-46-42.avi
2025-07-22 13:29:12,168 - INFO -   原始大小: 232.1 MB
2025-07-22 13:29:12,168 - INFO -   转换后大小: 40.8 MB
2025-07-22 13:29:12,168 - INFO -   压缩率: 82.4%
2025-07-22 13:29:12,168 - INFO -   处理进度: 17/30 - dulu_facial_expression_2025-07-18_15-47-11.avi
2025-07-22 13:29:12,168 - INFO - 开始转换: dulu_facial_expression_2025-07-18_15-47-11.avi
2025-07-22 13:30:31,506 - INFO - 转换成功: dulu_facial_expression_2025-07-18_15-47-11.avi
2025-07-22 13:30:31,506 - INFO -   原始大小: 212.9 MB
2025-07-22 13:30:31,506 - INFO -   转换后大小: 37.9 MB
2025-07-22 13:30:31,506 - INFO -   压缩率: 82.2%
2025-07-22 13:30:31,506 - INFO -   处理进度: 18/30 - dulu_facial_expression_2025-07-18_15-47-37.avi
2025-07-22 13:30:31,506 - INFO - 开始转换: dulu_facial_expression_2025-07-18_15-47-37.avi
2025-07-22 13:32:05,335 - INFO - 转换成功: dulu_facial_expression_2025-07-18_15-47-37.avi
2025-07-22 13:32:05,345 - INFO -   原始大小: 256.1 MB
2025-07-22 13:32:05,345 - INFO -   转换后大小: 46.5 MB
2025-07-22 13:32:05,345 - INFO -   压缩率: 81.8%
2025-07-22 13:32:05,345 - INFO -   处理进度: 19/30 - dulu_facial_expression_2025-07-18_15-48-09.avi
2025-07-22 13:32:05,345 - INFO - 开始转换: dulu_facial_expression_2025-07-18_15-48-09.avi
2025-07-22 13:33:32,973 - INFO - 转换成功: dulu_facial_expression_2025-07-18_15-48-09.avi
2025-07-22 13:33:32,973 - INFO -   原始大小: 210.7 MB
2025-07-22 13:33:32,973 - INFO -   转换后大小: 38.5 MB
2025-07-22 13:33:32,973 - INFO -   压缩率: 81.7%
2025-07-22 13:33:32,973 - INFO -   处理进度: 20/30 - dulu_facial_expression_2025-07-18_15-48-35.avi
2025-07-22 13:33:32,973 - INFO - 开始转换: dulu_facial_expression_2025-07-18_15-48-35.avi
2025-07-22 13:34:50,681 - INFO - 转换成功: dulu_facial_expression_2025-07-18_15-48-35.avi
2025-07-22 13:34:50,681 - INFO -   原始大小: 198.0 MB
2025-07-22 13:34:50,681 - INFO -   转换后大小: 36.4 MB
2025-07-22 13:34:50,681 - INFO -   压缩率: 81.6%
2025-07-22 13:34:50,681 - INFO -   处理进度: 21/30 - dulu_facial_expression_2025-07-18_15-48-59.avi
2025-07-22 13:34:50,681 - INFO - 开始转换: dulu_facial_expression_2025-07-18_15-48-59.avi
2025-07-22 13:36:01,452 - INFO - 转换成功: dulu_facial_expression_2025-07-18_15-48-59.avi
2025-07-22 13:36:01,452 - INFO -   原始大小: 176.3 MB
2025-07-22 13:36:01,452 - INFO -   转换后大小: 27.8 MB
2025-07-22 13:36:01,467 - INFO -   压缩率: 84.2%
2025-07-22 13:36:01,467 - INFO -   处理进度: 22/30 - dulu_facial_expression_2025-07-18_15-49-23.avi
2025-07-22 13:36:01,467 - INFO - 开始转换: dulu_facial_expression_2025-07-18_15-49-23.avi
2025-07-22 13:37:21,695 - INFO - 转换成功: dulu_facial_expression_2025-07-18_15-49-23.avi
2025-07-22 13:37:21,695 - INFO -   原始大小: 179.1 MB
2025-07-22 13:37:21,695 - INFO -   转换后大小: 28.0 MB
2025-07-22 13:37:21,695 - INFO -   压缩率: 84.4%
2025-07-22 13:37:21,695 - INFO -   处理进度: 23/30 - dulu_facial_expression_2025-07-18_15-49-48.avi
2025-07-22 13:37:21,695 - INFO - 开始转换: dulu_facial_expression_2025-07-18_15-49-48.avi
2025-07-22 13:38:56,094 - INFO - 转换成功: dulu_facial_expression_2025-07-18_15-49-48.avi
2025-07-22 13:38:56,094 - INFO -   原始大小: 226.2 MB
2025-07-22 13:38:56,094 - INFO -   转换后大小: 35.6 MB
2025-07-22 13:38:56,094 - INFO -   压缩率: 84.2%
2025-07-22 13:38:56,094 - INFO -   处理进度: 24/30 - dulu_facial_expression_2025-07-18_15-50-20.avi
2025-07-22 13:38:56,094 - INFO - 开始转换: dulu_facial_expression_2025-07-18_15-50-20.avi
2025-07-22 13:40:27,298 - INFO - 转换成功: dulu_facial_expression_2025-07-18_15-50-20.avi
2025-07-22 13:40:27,298 - INFO -   原始大小: 223.0 MB
2025-07-22 13:40:27,298 - INFO -   转换后大小: 36.0 MB
2025-07-22 13:40:27,298 - INFO -   压缩率: 83.8%
2025-07-22 13:40:27,298 - INFO -   处理进度: 25/30 - dulu_facial_expression_2025-07-18_15-50-50.avi
2025-07-22 13:40:27,298 - INFO - 开始转换: dulu_facial_expression_2025-07-18_15-50-50.avi
2025-07-22 13:42:04,758 - INFO - 转换成功: dulu_facial_expression_2025-07-18_15-50-50.avi
2025-07-22 13:42:04,758 - INFO -   原始大小: 240.0 MB
2025-07-22 13:42:04,758 - INFO -   转换后大小: 39.2 MB
2025-07-22 13:42:04,773 - INFO -   压缩率: 83.7%
2025-07-22 13:42:04,773 - INFO -   处理进度: 26/30 - dulu_facial_expression_2025-07-18_15-51-23.avi
2025-07-22 13:42:04,773 - INFO - 开始转换: dulu_facial_expression_2025-07-18_15-51-23.avi
2025-07-22 13:43:15,111 - INFO - 转换成功: dulu_facial_expression_2025-07-18_15-51-23.avi
2025-07-22 13:43:15,112 - INFO -   原始大小: 163.6 MB
2025-07-22 13:43:15,112 - INFO -   转换后大小: 26.7 MB
2025-07-22 13:43:15,113 - INFO -   压缩率: 83.7%
2025-07-22 13:43:15,114 - INFO -   处理进度: 27/30 - dulu_facial_expression_2025-07-18_15-51-53.avi
2025-07-22 13:43:15,115 - INFO - 开始转换: dulu_facial_expression_2025-07-18_15-51-53.avi
2025-07-22 13:44:59,416 - INFO - 转换成功: dulu_facial_expression_2025-07-18_15-51-53.avi
2025-07-22 13:44:59,416 - INFO -   原始大小: 223.9 MB
2025-07-22 13:44:59,416 - INFO -   转换后大小: 36.9 MB
2025-07-22 13:44:59,416 - INFO -   压缩率: 83.5%
2025-07-22 13:44:59,416 - INFO -   处理进度: 28/30 - dulu_facial_expression_2025-07-18_15-52-22.avi
2025-07-22 13:44:59,416 - INFO - 开始转换: dulu_facial_expression_2025-07-18_15-52-22.avi
2025-07-22 13:46:20,239 - INFO - 转换成功: dulu_facial_expression_2025-07-18_15-52-22.avi
2025-07-22 13:46:20,239 - INFO -   原始大小: 203.8 MB
2025-07-22 13:46:20,239 - INFO -   转换后大小: 33.6 MB
2025-07-22 13:46:20,239 - INFO -   压缩率: 83.5%
2025-07-22 13:46:20,239 - INFO -   处理进度: 29/30 - dulu_facial_expression_2025-07-18_15-52-47.avi
2025-07-22 13:46:20,239 - INFO - 开始转换: dulu_facial_expression_2025-07-18_15-52-47.avi
2025-07-22 13:47:44,214 - INFO - 转换成功: dulu_facial_expression_2025-07-18_15-52-47.avi
2025-07-22 13:47:44,214 - INFO -   原始大小: 206.3 MB
2025-07-22 13:47:44,214 - INFO -   转换后大小: 34.0 MB
2025-07-22 13:47:44,214 - INFO -   压缩率: 83.5%
2025-07-22 13:47:44,214 - INFO -   处理进度: 30/30 - dulu_facial_expression_2025-07-18_15-53-13.avi
2025-07-22 13:47:44,214 - INFO - 开始转换: dulu_facial_expression_2025-07-18_15-53-13.avi
2025-07-22 13:49:08,753 - INFO - 转换成功: dulu_facial_expression_2025-07-18_15-53-13.avi
2025-07-22 13:49:08,754 - INFO -   原始大小: 213.2 MB
2025-07-22 13:49:08,754 - INFO -   转换后大小: 35.4 MB
2025-07-22 13:49:08,754 - INFO -   压缩率: 83.4%
2025-07-22 13:49:08,754 - INFO -   该目录转换完成: 30/30 个文件
2025-07-22 13:49:08,756 - INFO - 处理video目录: C:\Users\<USER>\Desktop\curiosity_pupil\data\20250718_160729_dulu\video
2025-07-22 13:49:08,756 - INFO -   找到 30 个AVI文件
2025-07-22 13:49:08,756 - INFO -   处理进度: 1/30 - dulu_facial_expression_2025-07-18_16-08-31.avi
2025-07-22 13:49:08,756 - INFO - 开始转换: dulu_facial_expression_2025-07-18_16-08-31.avi
2025-07-22 13:50:42,512 - INFO - 转换成功: dulu_facial_expression_2025-07-18_16-08-31.avi
2025-07-22 13:50:42,512 - INFO -   原始大小: 210.2 MB
2025-07-22 13:50:42,512 - INFO -   转换后大小: 37.0 MB
2025-07-22 13:50:42,512 - INFO -   压缩率: 82.4%
2025-07-22 13:50:42,512 - INFO -   处理进度: 2/30 - dulu_facial_expression_2025-07-18_16-08-57.avi
2025-07-22 13:50:42,512 - INFO - 开始转换: dulu_facial_expression_2025-07-18_16-08-57.avi
2025-07-22 13:52:18,302 - INFO - 转换成功: dulu_facial_expression_2025-07-18_16-08-57.avi
2025-07-22 13:52:18,302 - INFO -   原始大小: 250.6 MB
2025-07-22 13:52:18,302 - INFO -   转换后大小: 45.1 MB
2025-07-22 13:52:18,302 - INFO -   压缩率: 82.0%
2025-07-22 13:52:18,302 - INFO -   处理进度: 3/30 - dulu_facial_expression_2025-07-18_16-09-26.avi
2025-07-22 13:52:18,302 - INFO - 开始转换: dulu_facial_expression_2025-07-18_16-09-26.avi
2025-07-22 13:53:51,558 - INFO - 转换成功: dulu_facial_expression_2025-07-18_16-09-26.avi
2025-07-22 13:53:51,558 - INFO -   原始大小: 242.2 MB
2025-07-22 13:53:51,558 - INFO -   转换后大小: 45.5 MB
2025-07-22 13:53:51,558 - INFO -   压缩率: 81.2%
2025-07-22 13:53:51,558 - INFO -   处理进度: 4/30 - dulu_facial_expression_2025-07-18_16-09-54.avi
2025-07-22 13:53:51,558 - INFO - 开始转换: dulu_facial_expression_2025-07-18_16-09-54.avi
2025-07-22 13:55:19,586 - INFO - 转换成功: dulu_facial_expression_2025-07-18_16-09-54.avi
2025-07-22 13:55:19,586 - INFO -   原始大小: 234.0 MB
2025-07-22 13:55:19,586 - INFO -   转换后大小: 44.7 MB
2025-07-22 13:55:19,586 - INFO -   压缩率: 80.9%
2025-07-22 13:55:19,586 - INFO -   处理进度: 5/30 - dulu_facial_expression_2025-07-18_16-10-20.avi
2025-07-22 13:55:19,586 - INFO - 开始转换: dulu_facial_expression_2025-07-18_16-10-20.avi
2025-07-22 13:57:05,313 - INFO - 转换成功: dulu_facial_expression_2025-07-18_16-10-20.avi
2025-07-22 13:57:05,313 - INFO -   原始大小: 263.5 MB
2025-07-22 13:57:05,313 - INFO -   转换后大小: 49.1 MB
2025-07-22 13:57:05,313 - INFO -   压缩率: 81.4%
2025-07-22 13:57:05,313 - INFO -   处理进度: 6/30 - dulu_facial_expression_2025-07-18_16-10-51.avi
2025-07-22 13:57:05,313 - INFO - 开始转换: dulu_facial_expression_2025-07-18_16-10-51.avi
2025-07-22 13:58:29,384 - INFO - 转换成功: dulu_facial_expression_2025-07-18_16-10-51.avi
2025-07-22 13:58:29,384 - INFO -   原始大小: 195.7 MB
2025-07-22 13:58:29,384 - INFO -   转换后大小: 34.3 MB
2025-07-22 13:58:29,384 - INFO -   压缩率: 82.5%
2025-07-22 13:58:29,384 - INFO -   处理进度: 7/30 - dulu_facial_expression_2025-07-18_16-11-16.avi
2025-07-22 13:58:29,384 - INFO - 开始转换: dulu_facial_expression_2025-07-18_16-11-16.avi
2025-07-22 14:00:14,120 - INFO - 转换成功: dulu_facial_expression_2025-07-18_16-11-16.avi
2025-07-22 14:00:14,120 - INFO -   原始大小: 229.9 MB
2025-07-22 14:00:14,120 - INFO -   转换后大小: 41.6 MB
2025-07-22 14:00:14,120 - INFO -   压缩率: 81.9%
2025-07-22 14:00:14,120 - INFO -   处理进度: 8/30 - dulu_facial_expression_2025-07-18_16-11-43.avi
2025-07-22 14:00:14,120 - INFO - 开始转换: dulu_facial_expression_2025-07-18_16-11-43.avi
2025-07-22 14:02:42,823 - INFO - 转换成功: dulu_facial_expression_2025-07-18_16-11-43.avi
2025-07-22 14:02:42,823 - INFO -   原始大小: 338.4 MB
2025-07-22 14:02:42,823 - INFO -   转换后大小: 63.7 MB
2025-07-22 14:02:42,823 - INFO -   压缩率: 81.2%
2025-07-22 14:02:42,823 - INFO -   处理进度: 9/30 - dulu_facial_expression_2025-07-18_16-12-21.avi
2025-07-22 14:02:42,823 - INFO - 开始转换: dulu_facial_expression_2025-07-18_16-12-21.avi
2025-07-22 14:04:52,871 - INFO - 转换成功: dulu_facial_expression_2025-07-18_16-12-21.avi
2025-07-22 14:04:52,871 - INFO -   原始大小: 307.1 MB
2025-07-22 14:04:52,871 - INFO -   转换后大小: 53.3 MB
2025-07-22 14:04:52,871 - INFO -   压缩率: 82.6%
2025-07-22 14:04:52,871 - INFO -   处理进度: 10/30 - dulu_facial_expression_2025-07-18_16-13-00.avi
2025-07-22 14:04:52,871 - INFO - 开始转换: dulu_facial_expression_2025-07-18_16-13-00.avi
2025-07-22 14:06:26,391 - INFO - 转换成功: dulu_facial_expression_2025-07-18_16-13-00.avi
2025-07-22 14:06:26,391 - INFO -   原始大小: 209.5 MB
2025-07-22 14:06:26,391 - INFO -   转换后大小: 34.7 MB
2025-07-22 14:06:26,391 - INFO -   压缩率: 83.4%
2025-07-22 14:06:26,391 - INFO -   处理进度: 11/30 - dulu_facial_expression_2025-07-18_16-13-30.avi
2025-07-22 14:06:26,391 - INFO - 开始转换: dulu_facial_expression_2025-07-18_16-13-30.avi
2025-07-22 14:07:57,178 - INFO - 转换成功: dulu_facial_expression_2025-07-18_16-13-30.avi
2025-07-22 14:07:57,178 - INFO -   原始大小: 220.2 MB
2025-07-22 14:07:57,179 - INFO -   转换后大小: 37.1 MB
2025-07-22 14:07:57,179 - INFO -   压缩率: 83.1%
2025-07-22 14:07:57,179 - INFO -   处理进度: 12/30 - dulu_facial_expression_2025-07-18_16-13-58.avi
2025-07-22 14:07:57,180 - INFO - 开始转换: dulu_facial_expression_2025-07-18_16-13-58.avi
2025-07-22 14:09:24,709 - INFO - 转换成功: dulu_facial_expression_2025-07-18_16-13-58.avi
2025-07-22 14:09:24,709 - INFO -   原始大小: 210.3 MB
2025-07-22 14:09:24,709 - INFO -   转换后大小: 36.3 MB
2025-07-22 14:09:24,709 - INFO -   压缩率: 82.7%
2025-07-22 14:09:24,709 - INFO -   处理进度: 13/30 - dulu_facial_expression_2025-07-18_16-14-26.avi
2025-07-22 14:09:24,709 - INFO - 开始转换: dulu_facial_expression_2025-07-18_16-14-26.avi
2025-07-22 14:11:11,114 - INFO - 转换成功: dulu_facial_expression_2025-07-18_16-14-26.avi
2025-07-22 14:11:11,114 - INFO -   原始大小: 231.3 MB
2025-07-22 14:11:11,114 - INFO -   转换后大小: 40.8 MB
2025-07-22 14:11:11,114 - INFO -   压缩率: 82.4%
2025-07-22 14:11:11,114 - INFO -   处理进度: 14/30 - dulu_facial_expression_2025-07-18_16-14-54.avi
2025-07-22 14:11:11,114 - INFO - 开始转换: dulu_facial_expression_2025-07-18_16-14-54.avi
2025-07-22 14:12:54,813 - INFO - 转换成功: dulu_facial_expression_2025-07-18_16-14-54.avi
2025-07-22 14:12:54,813 - INFO -   原始大小: 231.7 MB
2025-07-22 14:12:54,813 - INFO -   转换后大小: 41.1 MB
2025-07-22 14:12:54,813 - INFO -   压缩率: 82.3%
2025-07-22 14:12:54,813 - INFO -   处理进度: 15/30 - dulu_facial_expression_2025-07-18_16-15-21.avi
2025-07-22 14:12:54,813 - INFO - 开始转换: dulu_facial_expression_2025-07-18_16-15-21.avi
2025-07-22 14:14:18,332 - INFO - 转换成功: dulu_facial_expression_2025-07-18_16-15-21.avi
2025-07-22 14:14:18,332 - INFO -   原始大小: 210.3 MB
2025-07-22 14:14:18,333 - INFO -   转换后大小: 38.1 MB
2025-07-22 14:14:18,333 - INFO -   压缩率: 81.9%
2025-07-22 14:14:18,333 - INFO -   处理进度: 16/30 - dulu_facial_expression_2025-07-18_16-15-46.avi
2025-07-22 14:14:18,334 - INFO - 开始转换: dulu_facial_expression_2025-07-18_16-15-46.avi
2025-07-22 14:17:49,236 - INFO - 转换成功: dulu_facial_expression_2025-07-18_16-15-46.avi
2025-07-22 14:17:49,236 - INFO -   原始大小: 473.5 MB
2025-07-22 14:17:49,236 - INFO -   转换后大小: 77.9 MB
2025-07-22 14:17:49,236 - INFO -   压缩率: 83.5%
2025-07-22 14:17:49,236 - INFO -   处理进度: 17/30 - dulu_facial_expression_2025-07-18_16-16-55.avi
2025-07-22 14:17:49,236 - INFO - 开始转换: dulu_facial_expression_2025-07-18_16-16-55.avi
2025-07-22 14:19:47,370 - INFO - 转换成功: dulu_facial_expression_2025-07-18_16-16-55.avi
2025-07-22 14:19:47,370 - INFO -   原始大小: 234.3 MB
2025-07-22 14:19:47,370 - INFO -   转换后大小: 38.1 MB
2025-07-22 14:19:47,370 - INFO -   压缩率: 83.8%
2025-07-22 14:19:47,370 - INFO -   处理进度: 18/30 - dulu_facial_expression_2025-07-18_16-17-27.avi
2025-07-22 14:19:47,370 - INFO - 开始转换: dulu_facial_expression_2025-07-18_16-17-27.avi
2025-07-22 14:21:02,225 - INFO - 转换成功: dulu_facial_expression_2025-07-18_16-17-27.avi
2025-07-22 14:21:02,225 - INFO -   原始大小: 186.7 MB
2025-07-22 14:21:02,225 - INFO -   转换后大小: 30.6 MB
2025-07-22 14:21:02,225 - INFO -   压缩率: 83.6%
2025-07-22 14:21:02,225 - INFO -   处理进度: 19/30 - dulu_facial_expression_2025-07-18_16-17-52.avi
2025-07-22 14:21:02,225 - INFO - 开始转换: dulu_facial_expression_2025-07-18_16-17-52.avi
2025-07-22 14:22:20,400 - INFO - 转换成功: dulu_facial_expression_2025-07-18_16-17-52.avi
2025-07-22 14:22:20,400 - INFO -   原始大小: 187.4 MB
2025-07-22 14:22:20,400 - INFO -   转换后大小: 30.8 MB
2025-07-22 14:22:20,400 - INFO -   压缩率: 83.6%
2025-07-22 14:22:20,400 - INFO -   处理进度: 20/30 - dulu_facial_expression_2025-07-18_16-18-17.avi
2025-07-22 14:22:20,400 - INFO - 开始转换: dulu_facial_expression_2025-07-18_16-18-17.avi
2025-07-22 14:23:54,102 - INFO - 转换成功: dulu_facial_expression_2025-07-18_16-18-17.avi
2025-07-22 14:23:54,102 - INFO -   原始大小: 207.9 MB
2025-07-22 14:23:54,102 - INFO -   转换后大小: 34.5 MB
2025-07-22 14:23:54,102 - INFO -   压缩率: 83.4%
2025-07-22 14:23:54,117 - INFO -   处理进度: 21/30 - dulu_facial_expression_2025-07-18_16-18-44.avi
2025-07-22 14:23:54,119 - INFO - 开始转换: dulu_facial_expression_2025-07-18_16-18-44.avi
2025-07-22 14:25:39,919 - INFO - 转换成功: dulu_facial_expression_2025-07-18_16-18-44.avi
2025-07-22 14:25:39,919 - INFO -   原始大小: 209.8 MB
2025-07-22 14:25:39,920 - INFO -   转换后大小: 35.3 MB
2025-07-22 14:25:39,920 - INFO -   压缩率: 83.2%
2025-07-22 14:25:39,921 - INFO -   处理进度: 22/30 - dulu_facial_expression_2025-07-18_16-19-10.avi
2025-07-22 14:25:39,922 - INFO - 开始转换: dulu_facial_expression_2025-07-18_16-19-10.avi
2025-07-22 14:27:29,036 - INFO - 转换成功: dulu_facial_expression_2025-07-18_16-19-10.avi
2025-07-22 14:27:29,036 - INFO -   原始大小: 242.5 MB
2025-07-22 14:27:29,036 - INFO -   转换后大小: 41.3 MB
2025-07-22 14:27:29,036 - INFO -   压缩率: 83.0%
2025-07-22 14:27:29,036 - INFO -   处理进度: 23/30 - dulu_facial_expression_2025-07-18_16-19-42.avi
2025-07-22 14:27:29,036 - INFO - 开始转换: dulu_facial_expression_2025-07-18_16-19-42.avi
2025-07-22 14:29:13,940 - INFO - 转换成功: dulu_facial_expression_2025-07-18_16-19-42.avi
2025-07-22 14:29:13,941 - INFO -   原始大小: 205.2 MB
2025-07-22 14:29:13,941 - INFO -   转换后大小: 35.1 MB
2025-07-22 14:29:13,941 - INFO -   压缩率: 82.9%
2025-07-22 14:29:13,942 - INFO -   处理进度: 24/30 - dulu_facial_expression_2025-07-18_16-20-09.avi
2025-07-22 14:29:13,943 - INFO - 开始转换: dulu_facial_expression_2025-07-18_16-20-09.avi
2025-07-22 14:30:47,652 - INFO - 转换成功: dulu_facial_expression_2025-07-18_16-20-09.avi
2025-07-22 14:30:47,652 - INFO -   原始大小: 199.9 MB
2025-07-22 14:30:47,652 - INFO -   转换后大小: 34.0 MB
2025-07-22 14:30:47,652 - INFO -   压缩率: 83.0%
2025-07-22 14:30:47,667 - INFO -   处理进度: 25/30 - dulu_facial_expression_2025-07-18_16-20-35.avi
2025-07-22 14:30:47,667 - INFO - 开始转换: dulu_facial_expression_2025-07-18_16-20-35.avi
2025-07-22 14:32:48,497 - INFO - 转换成功: dulu_facial_expression_2025-07-18_16-20-35.avi
2025-07-22 14:32:48,497 - INFO -   原始大小: 252.9 MB
2025-07-22 14:32:48,498 - INFO -   转换后大小: 43.3 MB
2025-07-22 14:32:48,498 - INFO -   压缩率: 82.9%
2025-07-22 14:32:48,499 - INFO -   处理进度: 26/30 - dulu_facial_expression_2025-07-18_16-21-07.avi
2025-07-22 14:32:48,500 - INFO - 开始转换: dulu_facial_expression_2025-07-18_16-21-07.avi
2025-07-22 14:34:46,243 - INFO - 转换成功: dulu_facial_expression_2025-07-18_16-21-07.avi
2025-07-22 14:34:46,244 - INFO -   原始大小: 240.8 MB
2025-07-22 14:34:46,244 - INFO -   转换后大小: 42.5 MB
2025-07-22 14:34:46,244 - INFO -   压缩率: 82.4%
2025-07-22 14:34:46,245 - INFO -   处理进度: 27/30 - dulu_facial_expression_2025-07-18_16-21-45.avi
2025-07-22 14:34:46,245 - INFO - 开始转换: dulu_facial_expression_2025-07-18_16-21-45.avi
2025-07-22 14:36:28,433 - INFO - 转换成功: dulu_facial_expression_2025-07-18_16-21-45.avi
2025-07-22 14:36:28,433 - INFO -   原始大小: 231.8 MB
2025-07-22 14:36:28,434 - INFO -   转换后大小: 41.1 MB
2025-07-22 14:36:28,434 - INFO -   压缩率: 82.3%
2025-07-22 14:36:28,435 - INFO -   处理进度: 28/30 - dulu_facial_expression_2025-07-18_16-22-13.avi
2025-07-22 14:36:28,435 - INFO - 开始转换: dulu_facial_expression_2025-07-18_16-22-13.avi
2025-07-22 14:38:17,143 - INFO - 转换成功: dulu_facial_expression_2025-07-18_16-22-13.avi
2025-07-22 14:38:17,144 - INFO -   原始大小: 238.5 MB
2025-07-22 14:38:17,144 - INFO -   转换后大小: 42.5 MB
2025-07-22 14:38:17,144 - INFO -   压缩率: 82.2%
2025-07-22 14:38:17,145 - INFO -   处理进度: 29/30 - dulu_facial_expression_2025-07-18_16-22-43.avi
2025-07-22 14:38:17,145 - INFO - 开始转换: dulu_facial_expression_2025-07-18_16-22-43.avi
2025-07-22 14:39:58,286 - INFO - 转换成功: dulu_facial_expression_2025-07-18_16-22-43.avi
2025-07-22 14:39:58,286 - INFO -   原始大小: 222.2 MB
2025-07-22 14:39:58,287 - INFO -   转换后大小: 39.7 MB
2025-07-22 14:39:58,287 - INFO -   压缩率: 82.1%
2025-07-22 14:39:58,288 - INFO -   处理进度: 30/30 - dulu_facial_expression_2025-07-18_16-23-11.avi
2025-07-22 14:39:58,288 - INFO - 开始转换: dulu_facial_expression_2025-07-18_16-23-11.avi
2025-07-22 14:42:08,410 - INFO - 转换成功: dulu_facial_expression_2025-07-18_16-23-11.avi
2025-07-22 14:42:08,411 - INFO -   原始大小: 295.5 MB
2025-07-22 14:42:08,411 - INFO -   转换后大小: 53.4 MB
2025-07-22 14:42:08,411 - INFO -   压缩率: 81.9%
2025-07-22 14:42:08,411 - INFO -   该目录转换完成: 30/30 个文件
2025-07-22 14:42:08,412 - INFO - 所有video目录转换完成!
2025-07-22 14:42:08,412 - INFO - 总计成功转换: 120/120 个文件
2025-07-25 10:30:51,224 - INFO - 搜索AVI文件: C:/Users/<USER>/Desktop/curiosity_pupil/data/20250724_122823_zhuqingchao_continuous_reading/video/zhuqingchao_0.avi
2025-07-25 10:30:51,224 - WARNING - 在目录 C:/Users/<USER>/Desktop/curiosity_pupil/data/20250724_122823_zhuqingchao_continuous_reading/video/zhuqingchao_0.avi 中未找到AVI文件
2025-07-25 10:31:04,548 - INFO - 搜索AVI文件: C:/Users/<USER>/Desktop/curiosity_pupil/data/20250724_122823_zhuqingchao_continuous_reading/video
2025-07-25 10:31:04,548 - INFO - 找到 1 个AVI文件
2025-07-25 10:31:04,548 - ERROR - 程序执行出错: expected str, bytes or os.PathLike object, not NoneType
2025-07-25 10:33:08,487 - INFO - 搜索AVI文件: C:/Users/<USER>/Desktop/curiosity_pupil/data/20250724_122823_zhuqingchao_continuous_reading/video
2025-07-25 10:33:08,487 - INFO - 找到 1 个AVI文件
2025-07-25 10:33:08,487 - INFO - 处理进度: 1/1
2025-07-25 10:33:08,487 - INFO - 开始转换: zhuqingchao_0.avi
2025-07-25 17:38:18,099 - INFO - 搜索所有video子目录: C:/Users/<USER>/Desktop/curiosity_pupil/data
2025-07-25 17:38:18,099 - INFO - 找到 8 个video目录
2025-07-25 17:38:18,114 - INFO - 处理video目录: C:\Users\<USER>\Desktop\curiosity_pupil\data\20250725_140627_sunyan\video
2025-07-25 17:38:18,115 - INFO -   找到 30 个AVI文件
2025-07-25 17:38:18,116 - INFO -   处理进度: 1/30 - sunyan_1.avi
2025-07-25 17:38:18,117 - INFO - 开始转换: sunyan_1.avi
2025-07-25 17:39:55,908 - INFO - 转换成功: sunyan_1.avi
2025-07-25 17:39:55,909 - INFO -   原始大小: 268.2 MB
2025-07-25 17:39:55,909 - INFO -   转换后大小: 35.4 MB
2025-07-25 17:39:55,909 - INFO -   压缩率: 86.8%
2025-07-25 17:39:55,909 - INFO -   处理进度: 2/30 - sunyan_10.avi
2025-07-25 17:39:55,910 - INFO - 开始转换: sunyan_10.avi
2025-07-25 17:41:13,911 - INFO - 转换成功: sunyan_10.avi
2025-07-25 17:41:13,911 - INFO -   原始大小: 224.0 MB
2025-07-25 17:41:13,911 - INFO -   转换后大小: 25.7 MB
2025-07-25 17:41:13,911 - INFO -   压缩率: 88.5%
2025-07-25 17:41:13,911 - INFO -   处理进度: 3/30 - sunyan_11.avi
2025-07-25 17:41:13,911 - INFO - 开始转换: sunyan_11.avi
2025-07-25 17:42:34,693 - INFO - 转换成功: sunyan_11.avi
2025-07-25 17:42:34,693 - INFO -   原始大小: 212.5 MB
2025-07-25 17:42:34,693 - INFO -   转换后大小: 24.5 MB
2025-07-25 17:42:34,693 - INFO -   压缩率: 88.5%
2025-07-25 17:42:34,693 - INFO -   处理进度: 4/30 - sunyan_12.avi
2025-07-25 17:42:34,693 - INFO - 开始转换: sunyan_12.avi
2025-07-25 17:43:51,128 - INFO - 转换成功: sunyan_12.avi
2025-07-25 17:43:51,128 - INFO -   原始大小: 204.8 MB
2025-07-25 17:43:51,128 - INFO -   转换后大小: 23.9 MB
2025-07-25 17:43:51,128 - INFO -   压缩率: 88.3%
2025-07-25 17:43:51,128 - INFO -   处理进度: 5/30 - sunyan_13.avi
2025-07-25 17:43:51,128 - INFO - 开始转换: sunyan_13.avi
2025-07-25 17:45:09,965 - INFO - 转换成功: sunyan_13.avi
2025-07-25 17:45:09,966 - INFO -   原始大小: 213.5 MB
2025-07-25 17:45:09,966 - INFO -   转换后大小: 25.1 MB
2025-07-25 17:45:09,966 - INFO -   压缩率: 88.2%
2025-07-25 17:45:09,966 - INFO -   处理进度: 6/30 - sunyan_14.avi
2025-07-25 17:45:09,967 - INFO - 开始转换: sunyan_14.avi
2025-07-25 17:46:36,334 - INFO - 转换成功: sunyan_14.avi
2025-07-25 17:46:36,334 - INFO -   原始大小: 221.7 MB
2025-07-25 17:46:36,334 - INFO -   转换后大小: 26.4 MB
2025-07-25 17:46:36,334 - INFO -   压缩率: 88.1%
2025-07-25 17:46:36,334 - INFO -   处理进度: 7/30 - sunyan_15.avi
2025-07-25 17:46:36,334 - INFO - 开始转换: sunyan_15.avi
2025-07-25 17:47:53,602 - INFO - 转换成功: sunyan_15.avi
2025-07-25 17:47:53,602 - INFO -   原始大小: 215.3 MB
2025-07-25 17:47:53,602 - INFO -   转换后大小: 25.8 MB
2025-07-25 17:47:53,602 - INFO -   压缩率: 88.0%
2025-07-25 17:47:53,602 - INFO -   处理进度: 8/30 - sunyan_16.avi
2025-07-25 17:47:53,602 - INFO - 开始转换: sunyan_16.avi
2025-07-25 17:49:17,527 - INFO - 转换成功: sunyan_16.avi
2025-07-25 17:49:17,527 - INFO -   原始大小: 226.5 MB
2025-07-25 17:49:17,527 - INFO -   转换后大小: 26.8 MB
2025-07-25 17:49:17,527 - INFO -   压缩率: 88.2%
2025-07-25 17:49:17,527 - INFO -   处理进度: 9/30 - sunyan_17.avi
2025-07-25 17:49:17,527 - INFO - 开始转换: sunyan_17.avi
2025-07-25 17:50:35,752 - INFO - 转换成功: sunyan_17.avi
2025-07-25 17:50:35,752 - INFO -   原始大小: 218.2 MB
2025-07-25 17:50:35,752 - INFO -   转换后大小: 25.9 MB
2025-07-25 17:50:35,752 - INFO -   压缩率: 88.1%
2025-07-25 17:50:35,752 - INFO -   处理进度: 10/30 - sunyan_18.avi
2025-07-25 17:50:35,752 - INFO - 开始转换: sunyan_18.avi
2025-07-25 17:51:52,639 - INFO - 转换成功: sunyan_18.avi
2025-07-25 17:51:52,639 - INFO -   原始大小: 200.1 MB
2025-07-25 17:51:52,639 - INFO -   转换后大小: 24.1 MB
2025-07-25 17:51:52,639 - INFO -   压缩率: 87.9%
2025-07-25 17:51:52,639 - INFO -   处理进度: 11/30 - sunyan_19.avi
2025-07-25 17:51:52,639 - INFO - 开始转换: sunyan_19.avi
2025-07-25 17:53:06,581 - INFO - 转换成功: sunyan_19.avi
2025-07-25 17:53:06,581 - INFO -   原始大小: 193.4 MB
2025-07-25 17:53:06,581 - INFO -   转换后大小: 22.9 MB
2025-07-25 17:53:06,585 - INFO -   压缩率: 88.1%
2025-07-25 17:53:06,585 - INFO -   处理进度: 12/30 - sunyan_2.avi
2025-07-25 17:53:06,585 - INFO - 开始转换: sunyan_2.avi
2025-07-25 17:54:50,856 - INFO - 转换成功: sunyan_2.avi
2025-07-25 17:54:50,856 - INFO -   原始大小: 282.6 MB
2025-07-25 17:54:50,856 - INFO -   转换后大小: 39.0 MB
2025-07-25 17:54:50,856 - INFO -   压缩率: 86.2%
2025-07-25 17:54:50,856 - INFO -   处理进度: 13/30 - sunyan_20.avi
2025-07-25 17:54:50,856 - INFO - 开始转换: sunyan_20.avi
2025-07-25 17:56:13,484 - INFO - 转换成功: sunyan_20.avi
2025-07-25 17:56:13,484 - INFO -   原始大小: 221.1 MB
2025-07-25 17:56:13,484 - INFO -   转换后大小: 26.8 MB
2025-07-25 17:56:13,484 - INFO -   压缩率: 87.9%
2025-07-25 17:56:13,484 - INFO -   处理进度: 14/30 - sunyan_21.avi
2025-07-25 17:56:13,484 - INFO - 开始转换: sunyan_21.avi
2025-07-25 17:57:39,648 - INFO - 转换成功: sunyan_21.avi
2025-07-25 17:57:39,648 - INFO -   原始大小: 239.7 MB
2025-07-25 17:57:39,648 - INFO -   转换后大小: 28.8 MB
2025-07-25 17:57:39,648 - INFO -   压缩率: 88.0%
2025-07-25 17:57:39,648 - INFO -   处理进度: 15/30 - sunyan_22.avi
2025-07-25 17:57:39,648 - INFO - 开始转换: sunyan_22.avi
2025-07-25 17:59:29,111 - INFO - 转换成功: sunyan_22.avi
2025-07-25 17:59:29,111 - INFO -   原始大小: 298.6 MB
2025-07-25 17:59:29,111 - INFO -   转换后大小: 36.0 MB
2025-07-25 17:59:29,111 - INFO -   压缩率: 88.0%
2025-07-25 17:59:29,111 - INFO -   处理进度: 16/30 - sunyan_23.avi
2025-07-25 17:59:29,111 - INFO - 开始转换: sunyan_23.avi
2025-07-25 18:01:04,841 - INFO - 转换成功: sunyan_23.avi
2025-07-25 18:01:04,841 - INFO -   原始大小: 245.0 MB
2025-07-25 18:01:04,841 - INFO -   转换后大小: 29.3 MB
2025-07-25 18:01:04,841 - INFO -   压缩率: 88.0%
2025-07-25 18:01:04,841 - INFO -   处理进度: 17/30 - sunyan_24.avi
2025-07-25 18:01:04,841 - INFO - 开始转换: sunyan_24.avi
2025-07-25 18:02:26,836 - INFO - 转换成功: sunyan_24.avi
2025-07-25 18:02:26,836 - INFO -   原始大小: 213.2 MB
2025-07-25 18:02:26,836 - INFO -   转换后大小: 25.8 MB
2025-07-25 18:02:26,836 - INFO -   压缩率: 87.9%
2025-07-25 18:02:26,836 - INFO -   处理进度: 18/30 - sunyan_25.avi
2025-07-25 18:02:26,836 - INFO - 开始转换: sunyan_25.avi
2025-07-25 18:04:07,282 - INFO - 转换成功: sunyan_25.avi
2025-07-25 18:04:07,282 - INFO -   原始大小: 267.9 MB
2025-07-25 18:04:07,282 - INFO -   转换后大小: 31.4 MB
2025-07-25 18:04:07,282 - INFO -   压缩率: 88.3%
2025-07-25 18:04:07,282 - INFO -   处理进度: 19/30 - sunyan_26.avi
2025-07-25 18:04:07,282 - INFO - 开始转换: sunyan_26.avi
2025-07-25 18:06:05,811 - INFO - 转换成功: sunyan_26.avi
2025-07-25 18:06:05,811 - INFO -   原始大小: 306.5 MB
2025-07-25 18:06:05,811 - INFO -   转换后大小: 35.2 MB
2025-07-25 18:06:05,811 - INFO -   压缩率: 88.5%
2025-07-25 18:06:05,811 - INFO -   处理进度: 20/30 - sunyan_27.avi
2025-07-25 18:06:05,811 - INFO - 开始转换: sunyan_27.avi
2025-07-25 18:07:26,945 - INFO - 转换成功: sunyan_27.avi
2025-07-25 18:07:26,945 - INFO -   原始大小: 214.0 MB
2025-07-25 18:07:26,945 - INFO -   转换后大小: 24.5 MB
2025-07-25 18:07:26,945 - INFO -   压缩率: 88.5%
2025-07-25 18:07:26,945 - INFO -   处理进度: 21/30 - sunyan_28.avi
2025-07-25 18:07:26,945 - INFO - 开始转换: sunyan_28.avi
2025-07-25 18:08:33,597 - INFO - 转换成功: sunyan_28.avi
2025-07-25 18:08:33,597 - INFO -   原始大小: 185.7 MB
2025-07-25 18:08:33,597 - INFO -   转换后大小: 21.2 MB
2025-07-25 18:08:33,597 - INFO -   压缩率: 88.6%
2025-07-25 18:08:33,597 - INFO -   处理进度: 22/30 - sunyan_29.avi
2025-07-25 18:08:33,597 - INFO - 开始转换: sunyan_29.avi
2025-07-25 18:09:53,592 - INFO - 转换成功: sunyan_29.avi
2025-07-25 18:09:53,592 - INFO -   原始大小: 218.0 MB
2025-07-25 18:09:53,607 - INFO -   转换后大小: 24.9 MB
2025-07-25 18:09:53,607 - INFO -   压缩率: 88.6%
2025-07-25 18:09:53,607 - INFO -   处理进度: 23/30 - sunyan_3.avi
2025-07-25 18:09:53,607 - INFO - 开始转换: sunyan_3.avi
2025-07-25 18:11:43,214 - INFO - 转换成功: sunyan_3.avi
2025-07-25 18:11:43,214 - INFO -   原始大小: 268.2 MB
2025-07-25 18:11:43,214 - INFO -   转换后大小: 36.9 MB
2025-07-25 18:11:43,214 - INFO -   压缩率: 86.2%
2025-07-25 18:11:43,214 - INFO -   处理进度: 24/30 - sunyan_30.avi
2025-07-25 18:11:43,214 - INFO - 开始转换: sunyan_30.avi
2025-07-25 18:13:06,676 - INFO - 转换成功: sunyan_30.avi
2025-07-25 18:13:06,676 - INFO -   原始大小: 210.0 MB
2025-07-25 18:13:06,676 - INFO -   转换后大小: 24.5 MB
2025-07-25 18:13:06,676 - INFO -   压缩率: 88.3%
2025-07-25 18:13:06,676 - INFO -   处理进度: 25/30 - sunyan_4.avi
2025-07-25 18:13:06,676 - INFO - 开始转换: sunyan_4.avi
2025-07-25 18:15:09,762 - INFO - 转换成功: sunyan_4.avi
2025-07-25 18:15:09,777 - INFO -   原始大小: 302.9 MB
2025-07-25 18:15:09,777 - INFO -   转换后大小: 39.0 MB
2025-07-25 18:15:09,777 - INFO -   压缩率: 87.1%
2025-07-25 18:15:09,777 - INFO -   处理进度: 26/30 - sunyan_5.avi
2025-07-25 18:15:09,777 - INFO - 开始转换: sunyan_5.avi
2025-07-25 18:17:05,671 - INFO - 转换成功: sunyan_5.avi
2025-07-25 18:17:05,671 - INFO -   原始大小: 271.9 MB
2025-07-25 18:17:05,671 - INFO -   转换后大小: 36.6 MB
2025-07-25 18:17:05,671 - INFO -   压缩率: 86.5%
2025-07-25 18:17:05,671 - INFO -   处理进度: 27/30 - sunyan_6.avi
2025-07-25 18:17:05,671 - INFO - 开始转换: sunyan_6.avi
2025-07-25 18:18:56,746 - INFO - 转换成功: sunyan_6.avi
2025-07-25 18:18:56,746 - INFO -   原始大小: 273.9 MB
2025-07-25 18:18:56,746 - INFO -   转换后大小: 34.8 MB
2025-07-25 18:18:56,746 - INFO -   压缩率: 87.3%
2025-07-25 18:18:56,746 - INFO -   处理进度: 28/30 - sunyan_7.avi
2025-07-25 18:18:56,746 - INFO - 开始转换: sunyan_7.avi
2025-07-25 18:20:54,199 - INFO - 转换成功: sunyan_7.avi
2025-07-25 18:20:54,199 - INFO -   原始大小: 293.3 MB
2025-07-25 18:20:54,199 - INFO -   转换后大小: 36.0 MB
2025-07-25 18:20:54,199 - INFO -   压缩率: 87.7%
2025-07-25 18:20:54,199 - INFO -   处理进度: 29/30 - sunyan_8.avi
2025-07-25 18:20:54,199 - INFO - 开始转换: sunyan_8.avi
2025-07-25 18:22:33,193 - INFO - 转换成功: sunyan_8.avi
2025-07-25 18:22:33,193 - INFO -   原始大小: 252.8 MB
2025-07-25 18:22:33,193 - INFO -   转换后大小: 30.8 MB
2025-07-25 18:22:33,193 - INFO -   压缩率: 87.8%
2025-07-25 18:22:33,193 - INFO -   处理进度: 30/30 - sunyan_9.avi
2025-07-25 18:22:33,193 - INFO - 开始转换: sunyan_9.avi
2025-07-25 18:24:03,981 - INFO - 转换成功: sunyan_9.avi
2025-07-25 18:24:03,981 - INFO -   原始大小: 231.0 MB
2025-07-25 18:24:03,981 - INFO -   转换后大小: 27.4 MB
2025-07-25 18:24:03,981 - INFO -   压缩率: 88.2%
2025-07-25 18:24:03,981 - INFO -   该目录转换完成: 30/30 个文件
2025-07-25 18:24:03,981 - INFO - 处理video目录: C:\Users\<USER>\Desktop\curiosity_pupil\data\20250725_142821_sunyan\video
2025-07-25 18:24:03,989 - INFO -   找到 60 个AVI文件
2025-07-25 18:24:03,989 - INFO -   处理进度: 1/60 - sunyan_1.avi
2025-07-25 18:24:03,989 - INFO - 开始转换: sunyan_1.avi
2025-07-25 18:25:39,768 - INFO - 转换成功: sunyan_1.avi
2025-07-25 18:25:39,768 - INFO -   原始大小: 209.2 MB
2025-07-25 18:25:39,768 - INFO -   转换后大小: 25.3 MB
2025-07-25 18:25:39,768 - INFO -   压缩率: 87.9%
2025-07-25 18:25:39,768 - INFO -   处理进度: 2/60 - sunyan_10.avi
2025-07-25 18:25:39,768 - INFO - 开始转换: sunyan_10.avi
2025-07-25 18:27:07,047 - INFO - 转换成功: sunyan_10.avi
2025-07-25 18:27:07,047 - INFO -   原始大小: 207.2 MB
2025-07-25 18:27:07,047 - INFO -   转换后大小: 24.4 MB
2025-07-25 18:27:07,047 - INFO -   压缩率: 88.2%
2025-07-25 18:27:07,047 - INFO -   处理进度: 3/60 - sunyan_11.avi
2025-07-25 18:27:07,062 - INFO - 开始转换: sunyan_11.avi
2025-07-25 18:28:25,811 - INFO - 转换成功: sunyan_11.avi
2025-07-25 18:28:25,811 - INFO -   原始大小: 185.4 MB
2025-07-25 18:28:25,811 - INFO -   转换后大小: 21.4 MB
2025-07-25 18:28:25,811 - INFO -   压缩率: 88.5%
2025-07-25 18:28:25,811 - INFO -   处理进度: 4/60 - sunyan_12.avi
2025-07-25 18:28:25,811 - INFO - 开始转换: sunyan_12.avi
2025-07-25 18:29:44,738 - INFO - 转换成功: sunyan_12.avi
2025-07-25 18:29:44,738 - INFO -   原始大小: 185.6 MB
2025-07-25 18:29:44,738 - INFO -   转换后大小: 21.9 MB
2025-07-25 18:29:44,738 - INFO -   压缩率: 88.2%
2025-07-25 18:29:44,738 - INFO -   处理进度: 5/60 - sunyan_13.avi
2025-07-25 18:29:44,738 - INFO - 开始转换: sunyan_13.avi
2025-07-25 18:31:17,131 - INFO - 转换成功: sunyan_13.avi
2025-07-25 18:31:17,135 - INFO -   原始大小: 197.5 MB
2025-07-25 18:31:17,135 - INFO -   转换后大小: 23.8 MB
2025-07-25 18:31:17,135 - INFO -   压缩率: 87.9%
2025-07-25 18:31:17,135 - INFO -   处理进度: 6/60 - sunyan_14.avi
2025-07-25 18:31:17,135 - INFO - 开始转换: sunyan_14.avi
2025-07-25 18:32:38,057 - INFO - 转换成功: sunyan_14.avi
2025-07-25 18:32:38,057 - INFO -   原始大小: 186.5 MB
2025-07-25 18:32:38,065 - INFO -   转换后大小: 22.5 MB
2025-07-25 18:32:38,065 - INFO -   压缩率: 87.9%
2025-07-25 18:32:38,065 - INFO -   处理进度: 7/60 - sunyan_15.avi
2025-07-25 18:32:38,066 - INFO - 开始转换: sunyan_15.avi
2025-07-25 18:33:57,751 - INFO - 转换成功: sunyan_15.avi
2025-07-25 18:33:57,751 - INFO -   原始大小: 186.1 MB
2025-07-25 18:33:57,751 - INFO -   转换后大小: 22.4 MB
2025-07-25 18:33:57,751 - INFO -   压缩率: 88.0%
2025-07-25 18:33:57,751 - INFO -   处理进度: 8/60 - sunyan_16.avi
2025-07-25 18:33:57,751 - INFO - 开始转换: sunyan_16.avi
2025-07-25 18:35:22,008 - INFO - 转换成功: sunyan_16.avi
2025-07-25 18:35:22,008 - INFO -   原始大小: 194.6 MB
2025-07-25 18:35:22,008 - INFO -   转换后大小: 23.2 MB
2025-07-25 18:35:22,008 - INFO -   压缩率: 88.1%
2025-07-25 18:35:22,008 - INFO -   处理进度: 9/60 - sunyan_17.avi
2025-07-25 18:35:22,008 - INFO - 开始转换: sunyan_17.avi
2025-07-25 18:36:48,262 - INFO - 转换成功: sunyan_17.avi
2025-07-25 18:36:48,262 - INFO -   原始大小: 200.1 MB
2025-07-25 18:36:48,273 - INFO -   转换后大小: 24.0 MB
2025-07-25 18:36:48,273 - INFO -   压缩率: 88.0%
2025-07-25 18:36:48,273 - INFO -   处理进度: 10/60 - sunyan_18.avi
2025-07-25 18:36:48,274 - INFO - 开始转换: sunyan_18.avi
2025-07-25 18:38:10,913 - INFO - 转换成功: sunyan_18.avi
2025-07-25 18:38:10,913 - INFO -   原始大小: 192.6 MB
2025-07-25 18:38:10,913 - INFO -   转换后大小: 22.8 MB
2025-07-25 18:38:10,913 - INFO -   压缩率: 88.2%
2025-07-25 18:38:10,913 - INFO -   处理进度: 11/60 - sunyan_19.avi
2025-07-25 18:38:10,913 - INFO - 开始转换: sunyan_19.avi
2025-07-25 18:39:32,612 - INFO - 转换成功: sunyan_19.avi
2025-07-25 18:39:32,612 - INFO -   原始大小: 185.4 MB
2025-07-25 18:39:32,627 - INFO -   转换后大小: 21.9 MB
2025-07-25 18:39:32,627 - INFO -   压缩率: 88.2%
2025-07-25 18:39:32,627 - INFO -   处理进度: 12/60 - sunyan_2.avi
2025-07-25 18:39:32,627 - INFO - 开始转换: sunyan_2.avi
2025-07-25 18:41:07,244 - INFO - 转换成功: sunyan_2.avi
2025-07-25 18:41:07,252 - INFO -   原始大小: 215.5 MB
2025-07-25 18:41:07,252 - INFO -   转换后大小: 24.8 MB
2025-07-25 18:41:07,252 - INFO -   压缩率: 88.5%
2025-07-25 18:41:07,252 - INFO -   处理进度: 13/60 - sunyan_20.avi
2025-07-25 18:41:07,252 - INFO - 开始转换: sunyan_20.avi
2025-07-25 18:42:30,847 - INFO - 转换成功: sunyan_20.avi
2025-07-25 18:42:30,847 - INFO -   原始大小: 190.4 MB
2025-07-25 18:42:30,847 - INFO -   转换后大小: 22.8 MB
2025-07-25 18:42:30,847 - INFO -   压缩率: 88.0%
2025-07-25 18:42:30,847 - INFO -   处理进度: 14/60 - sunyan_21.avi
2025-07-25 18:42:30,847 - INFO - 开始转换: sunyan_21.avi
2025-07-25 18:43:48,419 - INFO - 转换成功: sunyan_21.avi
2025-07-25 18:43:48,419 - INFO -   原始大小: 177.5 MB
2025-07-25 18:43:48,419 - INFO -   转换后大小: 21.4 MB
2025-07-25 18:43:48,419 - INFO -   压缩率: 87.9%
2025-07-25 18:43:48,419 - INFO -   处理进度: 15/60 - sunyan_22.avi
2025-07-25 18:43:48,419 - INFO - 开始转换: sunyan_22.avi
2025-07-25 18:45:10,555 - INFO - 转换成功: sunyan_22.avi
2025-07-25 18:45:10,555 - INFO -   原始大小: 170.9 MB
2025-07-25 18:45:10,555 - INFO -   转换后大小: 20.6 MB
2025-07-25 18:45:10,555 - INFO -   压缩率: 88.0%
2025-07-25 18:45:10,555 - INFO -   处理进度: 16/60 - sunyan_23.avi
2025-07-25 18:45:10,555 - INFO - 开始转换: sunyan_23.avi
2025-07-25 18:46:32,710 - INFO - 转换成功: sunyan_23.avi
2025-07-25 18:46:32,710 - INFO -   原始大小: 167.9 MB
2025-07-25 18:46:32,710 - INFO -   转换后大小: 19.4 MB
2025-07-25 18:46:32,710 - INFO -   压缩率: 88.5%
2025-07-25 18:46:32,710 - INFO -   处理进度: 17/60 - sunyan_24.avi
2025-07-25 18:46:32,710 - INFO - 开始转换: sunyan_24.avi
2025-07-25 18:48:02,942 - INFO - 转换成功: sunyan_24.avi
2025-07-25 18:48:02,942 - INFO -   原始大小: 208.8 MB
2025-07-25 18:48:02,942 - INFO -   转换后大小: 22.7 MB
2025-07-25 18:48:02,942 - INFO -   压缩率: 89.1%
2025-07-25 18:48:02,942 - INFO -   处理进度: 18/60 - sunyan_25.avi
2025-07-25 18:48:02,942 - INFO - 开始转换: sunyan_25.avi
2025-07-25 18:49:19,428 - INFO - 转换成功: sunyan_25.avi
2025-07-25 18:49:19,428 - INFO -   原始大小: 172.3 MB
2025-07-25 18:49:19,428 - INFO -   转换后大小: 18.8 MB
2025-07-25 18:49:19,428 - INFO -   压缩率: 89.1%
2025-07-25 18:49:19,428 - INFO -   处理进度: 19/60 - sunyan_26.avi
2025-07-25 18:49:19,428 - INFO - 开始转换: sunyan_26.avi
2025-07-25 18:50:40,452 - INFO - 转换成功: sunyan_26.avi
2025-07-25 18:50:40,452 - INFO -   原始大小: 179.1 MB
2025-07-25 18:50:40,452 - INFO -   转换后大小: 20.5 MB
2025-07-25 18:50:40,452 - INFO -   压缩率: 88.5%
2025-07-25 18:50:40,452 - INFO -   处理进度: 20/60 - sunyan_27.avi
2025-07-25 18:50:40,452 - INFO - 开始转换: sunyan_27.avi
2025-07-25 18:52:03,388 - INFO - 转换成功: sunyan_27.avi
2025-07-25 18:52:03,398 - INFO -   原始大小: 185.2 MB
2025-07-25 18:52:03,398 - INFO -   转换后大小: 21.5 MB
2025-07-25 18:52:03,398 - INFO -   压缩率: 88.4%
2025-07-25 18:52:03,398 - INFO -   处理进度: 21/60 - sunyan_28.avi
2025-07-25 18:52:03,398 - INFO - 开始转换: sunyan_28.avi
2025-07-25 18:53:13,547 - INFO - 转换成功: sunyan_28.avi
2025-07-25 18:53:13,547 - INFO -   原始大小: 157.8 MB
2025-07-25 18:53:13,559 - INFO -   转换后大小: 17.7 MB
2025-07-25 18:53:13,559 - INFO -   压缩率: 88.8%
2025-07-25 18:53:13,559 - INFO -   处理进度: 22/60 - sunyan_29.avi
2025-07-25 18:53:13,559 - INFO - 开始转换: sunyan_29.avi
2025-07-25 18:54:37,882 - INFO - 转换成功: sunyan_29.avi
2025-07-25 18:54:37,882 - INFO -   原始大小: 188.9 MB
2025-07-25 18:54:37,882 - INFO -   转换后大小: 20.9 MB
2025-07-25 18:54:37,882 - INFO -   压缩率: 88.9%
2025-07-25 18:54:37,882 - INFO -   处理进度: 23/60 - sunyan_3.avi
2025-07-25 18:54:37,882 - INFO - 开始转换: sunyan_3.avi
2025-07-25 18:56:06,759 - INFO - 转换成功: sunyan_3.avi
2025-07-25 18:56:06,759 - INFO -   原始大小: 201.6 MB
2025-07-25 18:56:06,759 - INFO -   转换后大小: 23.3 MB
2025-07-25 18:56:06,759 - INFO -   压缩率: 88.4%
2025-07-25 18:56:06,759 - INFO -   处理进度: 24/60 - sunyan_30.avi
2025-07-25 18:56:06,759 - INFO - 开始转换: sunyan_30.avi
2025-07-25 18:57:19,529 - INFO - 转换成功: sunyan_30.avi
2025-07-25 18:57:19,529 - INFO -   原始大小: 161.1 MB
2025-07-25 18:57:19,529 - INFO -   转换后大小: 18.1 MB
2025-07-25 18:57:19,529 - INFO -   压缩率: 88.8%
2025-07-25 18:57:19,529 - INFO -   处理进度: 25/60 - sunyan_31.avi
2025-07-25 18:57:19,529 - INFO - 开始转换: sunyan_31.avi
2025-07-25 18:58:42,700 - INFO - 转换成功: sunyan_31.avi
2025-07-25 18:58:42,700 - INFO -   原始大小: 186.0 MB
2025-07-25 18:58:42,700 - INFO -   转换后大小: 21.2 MB
2025-07-25 18:58:42,712 - INFO -   压缩率: 88.6%
2025-07-25 18:58:42,712 - INFO -   处理进度: 26/60 - sunyan_32.avi
2025-07-25 18:58:42,712 - INFO - 开始转换: sunyan_32.avi
2025-07-25 19:00:10,644 - INFO - 转换成功: sunyan_32.avi
2025-07-25 19:00:10,644 - INFO -   原始大小: 194.7 MB
2025-07-25 19:00:10,644 - INFO -   转换后大小: 22.4 MB
2025-07-25 19:00:10,644 - INFO -   压缩率: 88.5%
2025-07-25 19:00:10,644 - INFO -   处理进度: 27/60 - sunyan_33.avi
2025-07-25 19:00:10,644 - INFO - 开始转换: sunyan_33.avi
2025-07-25 19:01:34,494 - INFO - 转换成功: sunyan_33.avi
2025-07-25 19:01:34,494 - INFO -   原始大小: 167.2 MB
2025-07-25 19:01:34,494 - INFO -   转换后大小: 19.3 MB
2025-07-25 19:01:34,494 - INFO -   压缩率: 88.5%
2025-07-25 19:01:34,494 - INFO -   处理进度: 28/60 - sunyan_34.avi
2025-07-25 19:01:34,494 - INFO - 开始转换: sunyan_34.avi
2025-07-25 19:02:56,310 - INFO - 转换成功: sunyan_34.avi
2025-07-25 19:02:56,310 - INFO -   原始大小: 187.6 MB
2025-07-25 19:02:56,310 - INFO -   转换后大小: 20.8 MB
2025-07-25 19:02:56,310 - INFO -   压缩率: 88.9%
2025-07-25 19:02:56,310 - INFO -   处理进度: 29/60 - sunyan_35.avi
2025-07-25 19:02:56,310 - INFO - 开始转换: sunyan_35.avi
2025-07-25 19:04:25,627 - INFO - 转换成功: sunyan_35.avi
2025-07-25 19:04:25,627 - INFO -   原始大小: 199.8 MB
2025-07-25 19:04:25,627 - INFO -   转换后大小: 22.2 MB
2025-07-25 19:04:25,627 - INFO -   压缩率: 88.9%
2025-07-25 19:04:25,627 - INFO -   处理进度: 30/60 - sunyan_36.avi
2025-07-25 19:04:25,627 - INFO - 开始转换: sunyan_36.avi
2025-07-25 19:06:15,354 - INFO - 转换成功: sunyan_36.avi
2025-07-25 19:06:15,354 - INFO -   原始大小: 181.9 MB
2025-07-25 19:06:15,354 - INFO -   转换后大小: 20.7 MB
2025-07-25 19:06:15,354 - INFO -   压缩率: 88.6%
2025-07-25 19:06:15,354 - INFO -   处理进度: 31/60 - sunyan_37.avi
2025-07-25 19:06:15,354 - INFO - 开始转换: sunyan_37.avi
2025-07-25 19:08:06,702 - INFO - 转换成功: sunyan_37.avi
2025-07-25 19:08:06,702 - INFO -   原始大小: 182.0 MB
2025-07-25 19:08:06,711 - INFO -   转换后大小: 20.4 MB
2025-07-25 19:08:06,713 - INFO -   压缩率: 88.8%
2025-07-25 19:08:06,715 - INFO -   处理进度: 32/60 - sunyan_38.avi
2025-07-25 19:08:06,716 - INFO - 开始转换: sunyan_38.avi
2025-07-25 19:10:13,928 - INFO - 转换成功: sunyan_38.avi
2025-07-25 19:10:13,928 - INFO -   原始大小: 179.5 MB
2025-07-25 19:10:13,928 - INFO -   转换后大小: 19.9 MB
2025-07-25 19:10:13,928 - INFO -   压缩率: 88.9%
2025-07-25 19:10:13,942 - INFO -   处理进度: 33/60 - sunyan_39.avi
2025-07-25 19:10:13,942 - INFO - 开始转换: sunyan_39.avi
2025-07-25 19:11:37,970 - INFO - 转换成功: sunyan_39.avi
2025-07-25 19:11:37,970 - INFO -   原始大小: 159.2 MB
2025-07-25 19:11:37,970 - INFO -   转换后大小: 18.7 MB
2025-07-25 19:11:37,970 - INFO -   压缩率: 88.3%
2025-07-25 19:11:37,970 - INFO -   处理进度: 34/60 - sunyan_4.avi
2025-07-25 19:11:37,970 - INFO - 开始转换: sunyan_4.avi
2025-07-25 19:13:20,822 - INFO - 转换成功: sunyan_4.avi
2025-07-25 19:13:20,822 - INFO -   原始大小: 200.1 MB
2025-07-25 19:13:20,822 - INFO -   转换后大小: 24.3 MB
2025-07-25 19:13:20,822 - INFO -   压缩率: 87.9%
2025-07-25 19:13:20,822 - INFO -   处理进度: 35/60 - sunyan_40.avi
2025-07-25 19:13:20,822 - INFO - 开始转换: sunyan_40.avi
2025-07-25 19:14:34,918 - INFO - 转换成功: sunyan_40.avi
2025-07-25 19:14:34,918 - INFO -   原始大小: 159.0 MB
2025-07-25 19:14:34,918 - INFO -   转换后大小: 17.6 MB
2025-07-25 19:14:34,918 - INFO -   压缩率: 88.9%
2025-07-25 19:14:34,918 - INFO -   处理进度: 36/60 - sunyan_41.avi
2025-07-25 19:14:34,918 - INFO - 开始转换: sunyan_41.avi
2025-07-25 19:16:00,758 - INFO - 转换成功: sunyan_41.avi
2025-07-25 19:16:00,758 - INFO -   原始大小: 169.1 MB
2025-07-25 19:16:00,758 - INFO -   转换后大小: 19.3 MB
2025-07-25 19:16:00,758 - INFO -   压缩率: 88.6%
2025-07-25 19:16:00,758 - INFO -   处理进度: 37/60 - sunyan_42.avi
2025-07-25 19:16:00,758 - INFO - 开始转换: sunyan_42.avi
2025-07-25 19:17:16,399 - INFO - 转换成功: sunyan_42.avi
2025-07-25 19:17:16,399 - INFO -   原始大小: 165.7 MB
2025-07-25 19:17:16,399 - INFO -   转换后大小: 18.4 MB
2025-07-25 19:17:16,399 - INFO -   压缩率: 88.9%
2025-07-25 19:17:16,399 - INFO -   处理进度: 38/60 - sunyan_43.avi
2025-07-25 19:17:16,399 - INFO - 开始转换: sunyan_43.avi
2025-07-25 19:19:00,168 - INFO - 转换成功: sunyan_43.avi
2025-07-25 19:19:00,183 - INFO -   原始大小: 228.4 MB
2025-07-25 19:19:00,183 - INFO -   转换后大小: 26.0 MB
2025-07-25 19:19:00,183 - INFO -   压缩率: 88.6%
2025-07-25 19:19:00,183 - INFO -   处理进度: 39/60 - sunyan_44.avi
2025-07-25 19:19:00,183 - INFO - 开始转换: sunyan_44.avi
2025-07-25 19:20:27,540 - INFO - 转换成功: sunyan_44.avi
2025-07-25 19:20:27,540 - INFO -   原始大小: 194.4 MB
2025-07-25 19:20:27,540 - INFO -   转换后大小: 22.5 MB
2025-07-25 19:20:27,540 - INFO -   压缩率: 88.4%
2025-07-25 19:20:27,540 - INFO -   处理进度: 40/60 - sunyan_45.avi
2025-07-25 19:20:27,540 - INFO - 开始转换: sunyan_45.avi
2025-07-25 19:21:48,414 - INFO - 转换成功: sunyan_45.avi
2025-07-25 19:21:48,414 - INFO -   原始大小: 182.0 MB
2025-07-25 19:21:48,414 - INFO -   转换后大小: 21.5 MB
2025-07-25 19:21:48,414 - INFO -   压缩率: 88.2%
2025-07-25 19:21:48,414 - INFO -   处理进度: 41/60 - sunyan_46.avi
2025-07-25 19:21:48,414 - INFO - 开始转换: sunyan_46.avi
2025-07-25 19:23:22,930 - INFO - 转换成功: sunyan_46.avi
2025-07-25 19:23:22,930 - INFO -   原始大小: 209.1 MB
2025-07-25 19:23:22,945 - INFO -   转换后大小: 24.7 MB
2025-07-25 19:23:22,945 - INFO -   压缩率: 88.2%
2025-07-25 19:23:22,945 - INFO -   处理进度: 42/60 - sunyan_47.avi
2025-07-25 19:23:22,945 - INFO - 开始转换: sunyan_47.avi
2025-07-25 19:24:43,974 - INFO - 转换成功: sunyan_47.avi
2025-07-25 19:24:43,974 - INFO -   原始大小: 182.4 MB
2025-07-25 19:24:43,974 - INFO -   转换后大小: 21.2 MB
2025-07-25 19:24:43,974 - INFO -   压缩率: 88.4%
2025-07-25 19:24:43,974 - INFO -   处理进度: 43/60 - sunyan_48.avi
2025-07-25 19:24:43,974 - INFO - 开始转换: sunyan_48.avi
2025-07-25 19:26:01,286 - INFO - 转换成功: sunyan_48.avi
2025-07-25 19:26:01,301 - INFO -   原始大小: 169.7 MB
2025-07-25 19:26:01,301 - INFO -   转换后大小: 19.4 MB
2025-07-25 19:26:01,301 - INFO -   压缩率: 88.6%
2025-07-25 19:26:01,301 - INFO -   处理进度: 44/60 - sunyan_49.avi
2025-07-25 19:26:01,301 - INFO - 开始转换: sunyan_49.avi
2025-07-25 19:27:34,031 - INFO - 转换成功: sunyan_49.avi
2025-07-25 19:27:34,031 - INFO -   原始大小: 202.2 MB
2025-07-25 19:27:34,031 - INFO -   转换后大小: 23.2 MB
2025-07-25 19:27:34,031 - INFO -   压缩率: 88.5%
2025-07-25 19:27:34,031 - INFO -   处理进度: 45/60 - sunyan_5.avi
2025-07-25 19:27:34,031 - INFO - 开始转换: sunyan_5.avi
2025-07-25 19:29:05,183 - INFO - 转换成功: sunyan_5.avi
2025-07-25 19:29:05,183 - INFO -   原始大小: 203.4 MB
2025-07-25 19:29:05,183 - INFO -   转换后大小: 24.9 MB
2025-07-25 19:29:05,183 - INFO -   压缩率: 87.7%
2025-07-25 19:29:05,183 - INFO -   处理进度: 46/60 - sunyan_50.avi
2025-07-25 19:29:05,183 - INFO - 开始转换: sunyan_50.avi
2025-07-25 19:30:38,746 - INFO - 转换成功: sunyan_50.avi
2025-07-25 19:30:38,746 - INFO -   原始大小: 186.4 MB
2025-07-25 19:30:38,746 - INFO -   转换后大小: 21.7 MB
2025-07-25 19:30:38,746 - INFO -   压缩率: 88.3%
2025-07-25 19:30:38,746 - INFO -   处理进度: 47/60 - sunyan_51.avi
2025-07-25 19:30:38,746 - INFO - 开始转换: sunyan_51.avi
2025-07-25 19:31:56,401 - INFO - 转换成功: sunyan_51.avi
2025-07-25 19:31:56,401 - INFO -   原始大小: 168.7 MB
2025-07-25 19:31:56,401 - INFO -   转换后大小: 19.6 MB
2025-07-25 19:31:56,401 - INFO -   压缩率: 88.4%
2025-07-25 19:31:56,401 - INFO -   处理进度: 48/60 - sunyan_52.avi
2025-07-25 19:31:56,401 - INFO - 开始转换: sunyan_52.avi
2025-07-25 19:33:04,877 - INFO - 转换成功: sunyan_52.avi
2025-07-25 19:33:04,877 - INFO -   原始大小: 148.2 MB
2025-07-25 19:33:04,877 - INFO -   转换后大小: 17.4 MB
2025-07-25 19:33:04,877 - INFO -   压缩率: 88.3%
2025-07-25 19:33:04,884 - INFO -   处理进度: 49/60 - sunyan_53.avi
2025-07-25 19:33:04,884 - INFO - 开始转换: sunyan_53.avi
2025-07-25 19:34:15,874 - INFO - 转换成功: sunyan_53.avi
2025-07-25 19:34:15,874 - INFO -   原始大小: 156.8 MB
2025-07-25 19:34:15,875 - INFO -   转换后大小: 18.0 MB
2025-07-25 19:34:15,875 - INFO -   压缩率: 88.5%
2025-07-25 19:34:15,875 - INFO -   处理进度: 50/60 - sunyan_54.avi
2025-07-25 19:34:15,876 - INFO - 开始转换: sunyan_54.avi
2025-07-25 19:35:26,941 - INFO - 转换成功: sunyan_54.avi
2025-07-25 19:35:26,941 - INFO -   原始大小: 179.6 MB
2025-07-25 19:35:26,941 - INFO -   转换后大小: 20.7 MB
2025-07-25 19:35:26,941 - INFO -   压缩率: 88.5%
2025-07-25 19:35:26,941 - INFO -   处理进度: 51/60 - sunyan_55.avi
2025-07-25 19:35:26,941 - INFO - 开始转换: sunyan_55.avi
2025-07-25 19:37:05,892 - INFO - 转换成功: sunyan_55.avi
2025-07-25 19:37:05,892 - INFO -   原始大小: 194.8 MB
2025-07-25 19:37:05,892 - INFO -   转换后大小: 22.3 MB
2025-07-25 19:37:05,892 - INFO -   压缩率: 88.6%
2025-07-25 19:37:05,892 - INFO -   处理进度: 52/60 - sunyan_56.avi
2025-07-25 19:37:05,892 - INFO - 开始转换: sunyan_56.avi
2025-07-25 19:38:15,041 - INFO - 转换成功: sunyan_56.avi
2025-07-25 19:38:15,042 - INFO -   原始大小: 172.9 MB
2025-07-25 19:38:15,042 - INFO -   转换后大小: 20.6 MB
2025-07-25 19:38:15,042 - INFO -   压缩率: 88.1%
2025-07-25 19:38:15,042 - INFO -   处理进度: 53/60 - sunyan_57.avi
2025-07-25 19:38:15,043 - INFO - 开始转换: sunyan_57.avi
2025-07-25 19:39:18,132 - INFO - 转换成功: sunyan_57.avi
2025-07-25 19:39:18,132 - INFO -   原始大小: 160.0 MB
2025-07-25 19:39:18,132 - INFO -   转换后大小: 18.4 MB
2025-07-25 19:39:18,132 - INFO -   压缩率: 88.5%
2025-07-25 19:39:18,132 - INFO -   处理进度: 54/60 - sunyan_58.avi
2025-07-25 19:39:18,132 - INFO - 开始转换: sunyan_58.avi
2025-07-25 19:40:26,786 - INFO - 转换成功: sunyan_58.avi
2025-07-25 19:40:26,786 - INFO -   原始大小: 175.7 MB
2025-07-25 19:40:26,786 - INFO -   转换后大小: 19.7 MB
2025-07-25 19:40:26,786 - INFO -   压缩率: 88.8%
2025-07-25 19:40:26,786 - INFO -   处理进度: 55/60 - sunyan_59.avi
2025-07-25 19:40:26,786 - INFO - 开始转换: sunyan_59.avi
2025-07-25 19:41:44,986 - INFO - 转换成功: sunyan_59.avi
2025-07-25 19:41:44,986 - INFO -   原始大小: 191.7 MB
2025-07-25 19:41:44,986 - INFO -   转换后大小: 23.2 MB
2025-07-25 19:41:44,986 - INFO -   压缩率: 87.9%
2025-07-25 19:41:44,986 - INFO -   处理进度: 56/60 - sunyan_6.avi
2025-07-25 19:41:45,001 - INFO - 开始转换: sunyan_6.avi
2025-07-25 19:43:00,366 - INFO - 转换成功: sunyan_6.avi
2025-07-25 19:43:00,366 - INFO -   原始大小: 194.5 MB
2025-07-25 19:43:00,366 - INFO -   转换后大小: 23.1 MB
2025-07-25 19:43:00,366 - INFO -   压缩率: 88.1%
2025-07-25 19:43:00,366 - INFO -   处理进度: 57/60 - sunyan_60.avi
2025-07-25 19:43:00,366 - INFO - 开始转换: sunyan_60.avi
2025-07-25 19:44:10,470 - INFO - 转换成功: sunyan_60.avi
2025-07-25 19:44:10,471 - INFO -   原始大小: 176.0 MB
2025-07-25 19:44:10,471 - INFO -   转换后大小: 19.6 MB
2025-07-25 19:44:10,472 - INFO -   压缩率: 88.8%
2025-07-25 19:44:10,472 - INFO -   处理进度: 58/60 - sunyan_7.avi
2025-07-25 19:44:10,472 - INFO - 开始转换: sunyan_7.avi
2025-07-25 19:45:35,477 - INFO - 转换成功: sunyan_7.avi
2025-07-25 19:45:35,477 - INFO -   原始大小: 181.5 MB
2025-07-25 19:45:35,478 - INFO -   转换后大小: 22.3 MB
2025-07-25 19:45:35,478 - INFO -   压缩率: 87.7%
2025-07-25 19:45:35,478 - INFO -   处理进度: 59/60 - sunyan_8.avi
2025-07-25 19:45:35,478 - INFO - 开始转换: sunyan_8.avi
2025-07-25 19:47:48,501 - INFO - 转换成功: sunyan_8.avi
2025-07-25 19:47:48,502 - INFO -   原始大小: 285.8 MB
2025-07-25 19:47:48,502 - INFO -   转换后大小: 34.1 MB
2025-07-25 19:47:48,503 - INFO -   压缩率: 88.1%
2025-07-25 19:47:48,503 - INFO -   处理进度: 60/60 - sunyan_9.avi
2025-07-25 19:47:48,504 - INFO - 开始转换: sunyan_9.avi
2025-07-25 19:49:43,644 - INFO - 转换成功: sunyan_9.avi
2025-07-25 19:49:43,644 - INFO -   原始大小: 198.6 MB
2025-07-25 19:49:43,644 - INFO -   转换后大小: 23.6 MB
2025-07-25 19:49:43,644 - INFO -   压缩率: 88.1%
2025-07-25 19:49:43,644 - INFO -   该目录转换完成: 60/60 个文件
2025-07-25 19:49:43,644 - INFO - 处理video目录: C:\Users\<USER>\Desktop\curiosity_pupil\data\20250725_151950_sunyan\video
2025-07-25 19:49:43,650 - INFO -   找到 60 个AVI文件
2025-07-25 19:49:43,650 - INFO -   处理进度: 1/60 - sunyan_1.avi
2025-07-25 19:49:43,650 - INFO - 开始转换: sunyan_1.avi
2025-07-25 19:51:43,351 - INFO - 转换成功: sunyan_1.avi
2025-07-25 19:51:43,351 - INFO -   原始大小: 202.2 MB
2025-07-25 19:51:43,351 - INFO -   转换后大小: 24.8 MB
2025-07-25 19:51:43,351 - INFO -   压缩率: 87.7%
2025-07-25 19:51:43,351 - INFO -   处理进度: 2/60 - sunyan_10.avi
2025-07-25 19:51:43,351 - INFO - 开始转换: sunyan_10.avi
2025-07-25 19:53:31,841 - INFO - 转换成功: sunyan_10.avi
2025-07-25 19:53:31,841 - INFO -   原始大小: 192.4 MB
2025-07-25 19:53:31,841 - INFO -   转换后大小: 27.8 MB
2025-07-25 19:53:31,841 - INFO -   压缩率: 85.6%
2025-07-25 19:53:31,841 - INFO -   处理进度: 3/60 - sunyan_11.avi
2025-07-25 19:53:31,841 - INFO - 开始转换: sunyan_11.avi
2025-07-25 19:55:05,863 - INFO - 转换成功: sunyan_11.avi
2025-07-25 19:55:05,863 - INFO -   原始大小: 171.8 MB
2025-07-25 19:55:05,863 - INFO -   转换后大小: 20.6 MB
2025-07-25 19:55:05,863 - INFO -   压缩率: 88.0%
2025-07-25 19:55:05,863 - INFO -   处理进度: 4/60 - sunyan_12.avi
2025-07-25 19:55:05,863 - INFO - 开始转换: sunyan_12.avi
2025-07-25 19:56:47,941 - INFO - 转换成功: sunyan_12.avi
2025-07-25 19:56:47,941 - INFO -   原始大小: 180.8 MB
2025-07-25 19:56:47,941 - INFO -   转换后大小: 21.9 MB
2025-07-25 19:56:47,941 - INFO -   压缩率: 87.9%
2025-07-25 19:56:47,941 - INFO -   处理进度: 5/60 - sunyan_13.avi
2025-07-25 19:56:47,941 - INFO - 开始转换: sunyan_13.avi
2025-07-25 19:58:44,472 - INFO - 转换成功: sunyan_13.avi
2025-07-25 19:58:44,472 - INFO -   原始大小: 213.0 MB
2025-07-25 19:58:44,472 - INFO -   转换后大小: 26.2 MB
2025-07-25 19:58:44,472 - INFO -   压缩率: 87.7%
2025-07-25 19:58:44,472 - INFO -   处理进度: 6/60 - sunyan_14.avi
2025-07-25 19:58:44,472 - INFO - 开始转换: sunyan_14.avi
2025-07-25 20:00:03,623 - INFO - 转换成功: sunyan_14.avi
2025-07-25 20:00:03,623 - INFO -   原始大小: 174.7 MB
2025-07-25 20:00:03,623 - INFO -   转换后大小: 22.3 MB
2025-07-25 20:00:03,623 - INFO -   压缩率: 87.2%
2025-07-25 20:00:03,623 - INFO -   处理进度: 7/60 - sunyan_15.avi
2025-07-25 20:00:03,623 - INFO - 开始转换: sunyan_15.avi
2025-07-25 20:01:10,202 - INFO - 转换成功: sunyan_15.avi
2025-07-25 20:01:10,202 - INFO -   原始大小: 161.2 MB
2025-07-25 20:01:10,202 - INFO -   转换后大小: 20.2 MB
2025-07-25 20:01:10,202 - INFO -   压缩率: 87.4%
2025-07-25 20:01:10,202 - INFO -   处理进度: 8/60 - sunyan_16.avi
2025-07-25 20:01:10,202 - INFO - 开始转换: sunyan_16.avi
2025-07-25 20:02:24,715 - INFO - 转换成功: sunyan_16.avi
2025-07-25 20:02:24,716 - INFO -   原始大小: 188.5 MB
2025-07-25 20:02:24,716 - INFO -   转换后大小: 23.0 MB
2025-07-25 20:02:24,716 - INFO -   压缩率: 87.8%
2025-07-25 20:02:24,716 - INFO -   处理进度: 9/60 - sunyan_17.avi
2025-07-25 20:02:24,716 - INFO - 开始转换: sunyan_17.avi
2025-07-25 20:03:30,059 - INFO - 转换成功: sunyan_17.avi
2025-07-25 20:03:30,059 - INFO -   原始大小: 173.2 MB
2025-07-25 20:03:30,059 - INFO -   转换后大小: 20.6 MB
2025-07-25 20:03:30,059 - INFO -   压缩率: 88.1%
2025-07-25 20:03:30,059 - INFO -   处理进度: 10/60 - sunyan_18.avi
2025-07-25 20:03:30,059 - INFO - 开始转换: sunyan_18.avi
2025-07-25 20:04:38,824 - INFO - 转换成功: sunyan_18.avi
2025-07-25 20:04:38,824 - INFO -   原始大小: 185.5 MB
2025-07-25 20:04:38,824 - INFO -   转换后大小: 21.5 MB
2025-07-25 20:04:38,824 - INFO -   压缩率: 88.4%
2025-07-25 20:04:38,824 - INFO -   处理进度: 11/60 - sunyan_19.avi
2025-07-25 20:04:38,824 - INFO - 开始转换: sunyan_19.avi
2025-07-25 20:05:48,261 - INFO - 转换成功: sunyan_19.avi
2025-07-25 20:05:48,261 - INFO -   原始大小: 184.2 MB
2025-07-25 20:05:48,261 - INFO -   转换后大小: 21.6 MB
2025-07-25 20:05:48,261 - INFO -   压缩率: 88.3%
2025-07-25 20:05:48,261 - INFO -   处理进度: 12/60 - sunyan_2.avi
2025-07-25 20:05:48,261 - INFO - 开始转换: sunyan_2.avi
2025-07-25 20:06:55,322 - INFO - 转换成功: sunyan_2.avi
2025-07-25 20:06:55,322 - INFO -   原始大小: 173.6 MB
2025-07-25 20:06:55,322 - INFO -   转换后大小: 21.0 MB
2025-07-25 20:06:55,322 - INFO -   压缩率: 87.9%
2025-07-25 20:06:55,322 - INFO -   处理进度: 13/60 - sunyan_20.avi
2025-07-25 20:06:55,322 - INFO - 开始转换: sunyan_20.avi
2025-07-25 20:08:06,487 - INFO - 转换成功: sunyan_20.avi
2025-07-25 20:08:06,487 - INFO -   原始大小: 175.3 MB
2025-07-25 20:08:06,487 - INFO -   转换后大小: 21.8 MB
2025-07-25 20:08:06,487 - INFO -   压缩率: 87.6%
2025-07-25 20:08:06,487 - INFO -   处理进度: 14/60 - sunyan_21.avi
2025-07-25 20:08:06,487 - INFO - 开始转换: sunyan_21.avi
2025-07-25 20:09:15,435 - INFO - 转换成功: sunyan_21.avi
2025-07-25 20:09:15,435 - INFO -   原始大小: 178.0 MB
2025-07-25 20:09:15,450 - INFO -   转换后大小: 21.3 MB
2025-07-25 20:09:15,450 - INFO -   压缩率: 88.1%
2025-07-25 20:09:15,450 - INFO -   处理进度: 15/60 - sunyan_22.avi
2025-07-25 20:09:15,450 - INFO - 开始转换: sunyan_22.avi
2025-07-25 20:10:29,243 - INFO - 转换成功: sunyan_22.avi
2025-07-25 20:10:29,243 - INFO -   原始大小: 190.1 MB
2025-07-25 20:10:29,243 - INFO -   转换后大小: 23.0 MB
2025-07-25 20:10:29,243 - INFO -   压缩率: 87.9%
2025-07-25 20:10:29,243 - INFO -   处理进度: 16/60 - sunyan_23.avi
2025-07-25 20:10:29,243 - INFO - 开始转换: sunyan_23.avi
2025-07-25 20:11:37,246 - INFO - 转换成功: sunyan_23.avi
2025-07-25 20:11:37,246 - INFO -   原始大小: 174.8 MB
2025-07-25 20:11:37,246 - INFO -   转换后大小: 21.7 MB
2025-07-25 20:11:37,246 - INFO -   压缩率: 87.6%
2025-07-25 20:11:37,246 - INFO -   处理进度: 17/60 - sunyan_24.avi
2025-07-25 20:11:37,246 - INFO - 开始转换: sunyan_24.avi
2025-07-25 20:12:43,868 - INFO - 转换成功: sunyan_24.avi
2025-07-25 20:12:43,868 - INFO -   原始大小: 167.6 MB
2025-07-25 20:12:43,868 - INFO -   转换后大小: 19.9 MB
2025-07-25 20:12:43,868 - INFO -   压缩率: 88.1%
2025-07-25 20:12:43,868 - INFO -   处理进度: 18/60 - sunyan_25.avi
2025-07-25 20:12:43,868 - INFO - 开始转换: sunyan_25.avi
2025-07-25 20:13:51,814 - INFO - 转换成功: sunyan_25.avi
2025-07-25 20:13:51,814 - INFO -   原始大小: 174.0 MB
2025-07-25 20:13:51,814 - INFO -   转换后大小: 20.9 MB
2025-07-25 20:13:51,814 - INFO -   压缩率: 88.0%
2025-07-25 20:13:51,814 - INFO -   处理进度: 19/60 - sunyan_26.avi
2025-07-25 20:13:51,814 - INFO - 开始转换: sunyan_26.avi
2025-07-25 20:14:50,305 - INFO - 转换成功: sunyan_26.avi
2025-07-25 20:14:50,305 - INFO -   原始大小: 150.5 MB
2025-07-25 20:14:50,305 - INFO -   转换后大小: 17.4 MB
2025-07-25 20:14:50,305 - INFO -   压缩率: 88.4%
2025-07-25 20:14:50,305 - INFO -   处理进度: 20/60 - sunyan_27.avi
2025-07-25 20:14:50,305 - INFO - 开始转换: sunyan_27.avi
2025-07-25 20:16:04,295 - INFO - 转换成功: sunyan_27.avi
2025-07-25 20:16:04,295 - INFO -   原始大小: 171.0 MB
2025-07-25 20:16:04,295 - INFO -   转换后大小: 20.3 MB
2025-07-25 20:16:04,295 - INFO -   压缩率: 88.1%
2025-07-25 20:16:04,295 - INFO -   处理进度: 21/60 - sunyan_28.avi
2025-07-25 20:16:04,295 - INFO - 开始转换: sunyan_28.avi
2025-07-25 20:17:13,506 - INFO - 转换成功: sunyan_28.avi
2025-07-25 20:17:13,506 - INFO -   原始大小: 169.2 MB
2025-07-25 20:17:13,506 - INFO -   转换后大小: 21.3 MB
2025-07-25 20:17:13,506 - INFO -   压缩率: 87.4%
2025-07-25 20:17:13,506 - INFO -   处理进度: 22/60 - sunyan_29.avi
2025-07-25 20:17:13,506 - INFO - 开始转换: sunyan_29.avi
2025-07-25 20:18:30,407 - INFO - 转换成功: sunyan_29.avi
2025-07-25 20:18:30,407 - INFO -   原始大小: 191.2 MB
2025-07-25 20:18:30,407 - INFO -   转换后大小: 22.8 MB
2025-07-25 20:18:30,407 - INFO -   压缩率: 88.1%
2025-07-25 20:18:30,407 - INFO -   处理进度: 23/60 - sunyan_3.avi
2025-07-25 20:18:30,407 - INFO - 开始转换: sunyan_3.avi
2025-07-25 20:19:33,794 - INFO - 转换成功: sunyan_3.avi
2025-07-25 20:19:33,794 - INFO -   原始大小: 165.4 MB
2025-07-25 20:19:33,794 - INFO -   转换后大小: 20.1 MB
2025-07-25 20:19:33,794 - INFO -   压缩率: 87.8%
2025-07-25 20:19:33,794 - INFO -   处理进度: 24/60 - sunyan_30.avi
2025-07-25 20:19:33,794 - INFO - 开始转换: sunyan_30.avi
2025-07-25 20:20:42,609 - INFO - 转换成功: sunyan_30.avi
2025-07-25 20:20:42,609 - INFO -   原始大小: 169.9 MB
2025-07-25 20:20:42,609 - INFO -   转换后大小: 21.5 MB
2025-07-25 20:20:42,609 - INFO -   压缩率: 87.4%
2025-07-25 20:20:42,609 - INFO -   处理进度: 25/60 - sunyan_31.avi
2025-07-25 20:20:42,609 - INFO - 开始转换: sunyan_31.avi
2025-07-25 20:22:08,698 - INFO - 转换成功: sunyan_31.avi
2025-07-25 20:22:08,698 - INFO -   原始大小: 217.3 MB
2025-07-25 20:22:08,698 - INFO -   转换后大小: 25.7 MB
2025-07-25 20:22:08,698 - INFO -   压缩率: 88.2%
2025-07-25 20:22:08,698 - INFO -   处理进度: 26/60 - sunyan_32.avi
2025-07-25 20:22:08,698 - INFO - 开始转换: sunyan_32.avi
2025-07-25 20:23:32,740 - INFO - 转换成功: sunyan_32.avi
2025-07-25 20:23:32,740 - INFO -   原始大小: 209.6 MB
2025-07-25 20:23:32,740 - INFO -   转换后大小: 24.8 MB
2025-07-25 20:23:32,740 - INFO -   压缩率: 88.1%
2025-07-25 20:23:32,740 - INFO -   处理进度: 27/60 - sunyan_33.avi
2025-07-25 20:23:32,740 - INFO - 开始转换: sunyan_33.avi
2025-07-25 20:24:33,672 - INFO - 转换成功: sunyan_33.avi
2025-07-25 20:24:33,672 - INFO -   原始大小: 151.0 MB
2025-07-25 20:24:33,672 - INFO -   转换后大小: 17.5 MB
2025-07-25 20:24:33,672 - INFO -   压缩率: 88.4%
2025-07-25 20:24:33,672 - INFO -   处理进度: 28/60 - sunyan_34.avi
2025-07-25 20:24:33,672 - INFO - 开始转换: sunyan_34.avi
2025-07-25 20:25:53,508 - INFO - 转换成功: sunyan_34.avi
2025-07-25 20:25:53,508 - INFO -   原始大小: 204.8 MB
2025-07-25 20:25:53,508 - INFO -   转换后大小: 23.7 MB
2025-07-25 20:25:53,508 - INFO -   压缩率: 88.4%
2025-07-25 20:25:53,508 - INFO -   处理进度: 29/60 - sunyan_35.avi
2025-07-25 20:25:53,508 - INFO - 开始转换: sunyan_35.avi
2025-07-25 20:27:04,118 - INFO - 转换成功: sunyan_35.avi
2025-07-25 20:27:04,118 - INFO -   原始大小: 178.2 MB
2025-07-25 20:27:04,118 - INFO -   转换后大小: 20.9 MB
2025-07-25 20:27:04,118 - INFO -   压缩率: 88.3%
2025-07-25 20:27:04,118 - INFO -   处理进度: 30/60 - sunyan_36.avi
2025-07-25 20:27:04,118 - INFO - 开始转换: sunyan_36.avi
2025-07-25 20:28:22,246 - INFO - 转换成功: sunyan_36.avi
2025-07-25 20:28:22,246 - INFO -   原始大小: 191.3 MB
2025-07-25 20:28:22,246 - INFO -   转换后大小: 22.5 MB
2025-07-25 20:28:22,247 - INFO -   压缩率: 88.2%
2025-07-25 20:28:22,247 - INFO -   处理进度: 31/60 - sunyan_37.avi
2025-07-25 20:28:22,247 - INFO - 开始转换: sunyan_37.avi
2025-07-25 20:29:42,018 - INFO - 转换成功: sunyan_37.avi
2025-07-25 20:29:42,018 - INFO -   原始大小: 201.2 MB
2025-07-25 20:29:42,018 - INFO -   转换后大小: 23.2 MB
2025-07-25 20:29:42,018 - INFO -   压缩率: 88.5%
2025-07-25 20:29:42,018 - INFO -   处理进度: 32/60 - sunyan_38.avi
2025-07-25 20:29:42,018 - INFO - 开始转换: sunyan_38.avi
2025-07-25 20:30:50,969 - INFO - 转换成功: sunyan_38.avi
2025-07-25 20:30:50,969 - INFO -   原始大小: 156.1 MB
2025-07-25 20:30:50,969 - INFO -   转换后大小: 18.3 MB
2025-07-25 20:30:50,969 - INFO -   压缩率: 88.3%
2025-07-25 20:30:50,969 - INFO -   处理进度: 33/60 - sunyan_39.avi
2025-07-25 20:30:50,969 - INFO - 开始转换: sunyan_39.avi
2025-07-25 20:32:06,925 - INFO - 转换成功: sunyan_39.avi
2025-07-25 20:32:06,925 - INFO -   原始大小: 195.6 MB
2025-07-25 20:32:06,925 - INFO -   转换后大小: 23.4 MB
2025-07-25 20:32:06,925 - INFO -   压缩率: 88.0%
2025-07-25 20:32:06,925 - INFO -   处理进度: 34/60 - sunyan_4.avi
2025-07-25 20:32:06,925 - INFO - 开始转换: sunyan_4.avi
2025-07-25 20:33:14,781 - INFO - 转换成功: sunyan_4.avi
2025-07-25 20:33:14,781 - INFO -   原始大小: 170.6 MB
2025-07-25 20:33:14,781 - INFO -   转换后大小: 21.1 MB
2025-07-25 20:33:14,782 - INFO -   压缩率: 87.6%
2025-07-25 20:33:14,782 - INFO -   处理进度: 35/60 - sunyan_40.avi
2025-07-25 20:33:14,782 - INFO - 开始转换: sunyan_40.avi
2025-07-25 20:34:22,910 - INFO - 转换成功: sunyan_40.avi
2025-07-25 20:34:22,910 - INFO -   原始大小: 170.8 MB
2025-07-25 20:34:22,910 - INFO -   转换后大小: 20.3 MB
2025-07-25 20:34:22,910 - INFO -   压缩率: 88.1%
2025-07-25 20:34:22,910 - INFO -   处理进度: 36/60 - sunyan_41.avi
2025-07-25 20:34:22,910 - INFO - 开始转换: sunyan_41.avi
2025-07-25 20:35:28,436 - INFO - 转换成功: sunyan_41.avi
2025-07-25 20:35:28,436 - INFO -   原始大小: 169.6 MB
2025-07-25 20:35:28,436 - INFO -   转换后大小: 19.8 MB
2025-07-25 20:35:28,436 - INFO -   压缩率: 88.3%
2025-07-25 20:35:28,436 - INFO -   处理进度: 37/60 - sunyan_42.avi
2025-07-25 20:35:28,436 - INFO - 开始转换: sunyan_42.avi
2025-07-25 20:36:30,135 - INFO - 转换成功: sunyan_42.avi
2025-07-25 20:36:30,135 - INFO -   原始大小: 157.7 MB
2025-07-25 20:36:30,135 - INFO -   转换后大小: 17.8 MB
2025-07-25 20:36:30,135 - INFO -   压缩率: 88.7%
2025-07-25 20:36:30,135 - INFO -   处理进度: 38/60 - sunyan_43.avi
2025-07-25 20:36:30,135 - INFO - 开始转换: sunyan_43.avi
2025-07-25 20:37:26,279 - INFO - 转换成功: sunyan_43.avi
2025-07-25 20:37:26,279 - INFO -   原始大小: 142.3 MB
2025-07-25 20:37:26,279 - INFO -   转换后大小: 16.3 MB
2025-07-25 20:37:26,279 - INFO -   压缩率: 88.6%
2025-07-25 20:37:26,279 - INFO -   处理进度: 39/60 - sunyan_44.avi
2025-07-25 20:37:26,279 - INFO - 开始转换: sunyan_44.avi
2025-07-25 20:38:31,068 - INFO - 转换成功: sunyan_44.avi
2025-07-25 20:38:31,068 - INFO -   原始大小: 164.6 MB
2025-07-25 20:38:31,068 - INFO -   转换后大小: 19.7 MB
2025-07-25 20:38:31,082 - INFO -   压缩率: 88.0%
2025-07-25 20:38:31,082 - INFO -   处理进度: 40/60 - sunyan_45.avi
2025-07-25 20:38:31,082 - INFO - 开始转换: sunyan_45.avi
2025-07-25 20:39:56,404 - INFO - 转换成功: sunyan_45.avi
2025-07-25 20:39:56,404 - INFO -   原始大小: 215.4 MB
2025-07-25 20:39:56,404 - INFO -   转换后大小: 26.4 MB
2025-07-25 20:39:56,404 - INFO -   压缩率: 87.7%
2025-07-25 20:39:56,404 - INFO -   处理进度: 41/60 - sunyan_46.avi
2025-07-25 20:39:56,404 - INFO - 开始转换: sunyan_46.avi
2025-07-25 20:41:02,939 - INFO - 转换成功: sunyan_46.avi
2025-07-25 20:41:02,954 - INFO -   原始大小: 161.8 MB
2025-07-25 20:41:02,954 - INFO -   转换后大小: 19.1 MB
2025-07-25 20:41:02,954 - INFO -   压缩率: 88.2%
2025-07-25 20:41:02,954 - INFO -   处理进度: 42/60 - sunyan_47.avi
2025-07-25 20:41:02,954 - INFO - 开始转换: sunyan_47.avi
2025-07-25 20:42:17,503 - INFO - 转换成功: sunyan_47.avi
2025-07-25 20:42:17,503 - INFO -   原始大小: 169.2 MB
2025-07-25 20:42:17,503 - INFO -   转换后大小: 20.0 MB
2025-07-25 20:42:17,503 - INFO -   压缩率: 88.2%
2025-07-25 20:42:17,503 - INFO -   处理进度: 43/60 - sunyan_48.avi
2025-07-25 20:42:17,503 - INFO - 开始转换: sunyan_48.avi
2025-07-25 20:43:33,400 - INFO - 转换成功: sunyan_48.avi
2025-07-25 20:43:33,400 - INFO -   原始大小: 182.5 MB
2025-07-25 20:43:33,400 - INFO -   转换后大小: 21.9 MB
2025-07-25 20:43:33,400 - INFO -   压缩率: 88.0%
2025-07-25 20:43:33,400 - INFO -   处理进度: 44/60 - sunyan_49.avi
2025-07-25 20:43:33,400 - INFO - 开始转换: sunyan_49.avi
2025-07-25 20:45:28,163 - INFO - 转换成功: sunyan_49.avi
2025-07-25 20:45:28,163 - INFO -   原始大小: 231.9 MB
2025-07-25 20:45:28,163 - INFO -   转换后大小: 28.0 MB
2025-07-25 20:45:28,163 - INFO -   压缩率: 87.9%
2025-07-25 20:45:28,163 - INFO -   处理进度: 45/60 - sunyan_5.avi
2025-07-25 20:45:28,163 - INFO - 开始转换: sunyan_5.avi
2025-07-25 20:46:28,770 - INFO - 转换成功: sunyan_5.avi
2025-07-25 20:46:28,770 - INFO -   原始大小: 156.3 MB
2025-07-25 20:46:28,770 - INFO -   转换后大小: 19.7 MB
2025-07-25 20:46:28,770 - INFO -   压缩率: 87.4%
2025-07-25 20:46:28,770 - INFO -   处理进度: 46/60 - sunyan_50.avi
2025-07-25 20:46:28,770 - INFO - 开始转换: sunyan_50.avi
2025-07-25 20:47:31,944 - INFO - 转换成功: sunyan_50.avi
2025-07-25 20:47:31,944 - INFO -   原始大小: 144.7 MB
2025-07-25 20:47:31,944 - INFO -   转换后大小: 17.7 MB
2025-07-25 20:47:31,944 - INFO -   压缩率: 87.8%
2025-07-25 20:47:31,944 - INFO -   处理进度: 47/60 - sunyan_51.avi
2025-07-25 20:47:31,944 - INFO - 开始转换: sunyan_51.avi
2025-07-25 20:48:40,662 - INFO - 转换成功: sunyan_51.avi
2025-07-25 20:48:40,662 - INFO -   原始大小: 170.9 MB
2025-07-25 20:48:40,662 - INFO -   转换后大小: 19.2 MB
2025-07-25 20:48:40,662 - INFO -   压缩率: 88.7%
2025-07-25 20:48:40,662 - INFO -   处理进度: 48/60 - sunyan_52.avi
2025-07-25 20:48:40,662 - INFO - 开始转换: sunyan_52.avi
2025-07-25 20:50:05,174 - INFO - 转换成功: sunyan_52.avi
2025-07-25 20:50:05,174 - INFO -   原始大小: 202.7 MB
2025-07-25 20:50:05,174 - INFO -   转换后大小: 23.5 MB
2025-07-25 20:50:05,174 - INFO -   压缩率: 88.4%
2025-07-25 20:50:05,174 - INFO -   处理进度: 49/60 - sunyan_53.avi
2025-07-25 20:50:05,174 - INFO - 开始转换: sunyan_53.avi
2025-07-25 20:51:20,522 - INFO - 转换成功: sunyan_53.avi
2025-07-25 20:51:20,522 - INFO -   原始大小: 166.0 MB
2025-07-25 20:51:20,522 - INFO -   转换后大小: 20.5 MB
2025-07-25 20:51:20,522 - INFO -   压缩率: 87.7%
2025-07-25 20:51:20,532 - INFO -   处理进度: 50/60 - sunyan_54.avi
2025-07-25 20:51:20,532 - INFO - 开始转换: sunyan_54.avi
2025-07-25 20:52:37,982 - INFO - 转换成功: sunyan_54.avi
2025-07-25 20:52:37,982 - INFO -   原始大小: 181.6 MB
2025-07-25 20:52:37,982 - INFO -   转换后大小: 21.2 MB
2025-07-25 20:52:37,982 - INFO -   压缩率: 88.3%
2025-07-25 20:52:37,982 - INFO -   处理进度: 51/60 - sunyan_55.avi
2025-07-25 20:52:37,982 - INFO - 开始转换: sunyan_55.avi
2025-07-25 20:53:41,453 - INFO - 转换成功: sunyan_55.avi
2025-07-25 20:53:41,453 - INFO -   原始大小: 151.5 MB
2025-07-25 20:53:41,453 - INFO -   转换后大小: 17.9 MB
2025-07-25 20:53:41,453 - INFO -   压缩率: 88.2%
2025-07-25 20:53:41,453 - INFO -   处理进度: 52/60 - sunyan_56.avi
2025-07-25 20:53:41,453 - INFO - 开始转换: sunyan_56.avi
2025-07-25 20:55:07,271 - INFO - 转换成功: sunyan_56.avi
2025-07-25 20:55:07,271 - INFO -   原始大小: 204.9 MB
2025-07-25 20:55:07,271 - INFO -   转换后大小: 24.2 MB
2025-07-25 20:55:07,271 - INFO -   压缩率: 88.2%
2025-07-25 20:55:07,271 - INFO -   处理进度: 53/60 - sunyan_57.avi
2025-07-25 20:55:07,271 - INFO - 开始转换: sunyan_57.avi
2025-07-25 20:56:14,533 - INFO - 转换成功: sunyan_57.avi
2025-07-25 20:56:14,533 - INFO -   原始大小: 161.4 MB
2025-07-25 20:56:14,533 - INFO -   转换后大小: 17.8 MB
2025-07-25 20:56:14,533 - INFO -   压缩率: 89.0%
2025-07-25 20:56:14,533 - INFO -   处理进度: 54/60 - sunyan_58.avi
2025-07-25 20:56:14,533 - INFO - 开始转换: sunyan_58.avi
2025-07-25 20:57:27,699 - INFO - 转换成功: sunyan_58.avi
2025-07-25 20:57:27,699 - INFO -   原始大小: 176.6 MB
2025-07-25 20:57:27,699 - INFO -   转换后大小: 20.1 MB
2025-07-25 20:57:27,699 - INFO -   压缩率: 88.6%
2025-07-25 20:57:27,699 - INFO -   处理进度: 55/60 - sunyan_59.avi
2025-07-25 20:57:27,699 - INFO - 开始转换: sunyan_59.avi
2025-07-25 20:58:38,121 - INFO - 转换成功: sunyan_59.avi
2025-07-25 20:58:38,121 - INFO -   原始大小: 175.2 MB
2025-07-25 20:58:38,121 - INFO -   转换后大小: 19.6 MB
2025-07-25 20:58:38,121 - INFO -   压缩率: 88.8%
2025-07-25 20:58:38,121 - INFO -   处理进度: 56/60 - sunyan_6.avi
2025-07-25 20:58:38,121 - INFO - 开始转换: sunyan_6.avi
2025-07-25 20:59:49,555 - INFO - 转换成功: sunyan_6.avi
2025-07-25 20:59:49,555 - INFO -   原始大小: 181.2 MB
2025-07-25 20:59:49,555 - INFO -   转换后大小: 23.8 MB
2025-07-25 20:59:49,555 - INFO -   压缩率: 86.9%
2025-07-25 20:59:49,555 - INFO -   处理进度: 57/60 - sunyan_60.avi
2025-07-25 20:59:49,555 - INFO - 开始转换: sunyan_60.avi
2025-07-25 21:01:04,500 - INFO - 转换成功: sunyan_60.avi
2025-07-25 21:01:04,500 - INFO -   原始大小: 165.6 MB
2025-07-25 21:01:04,500 - INFO -   转换后大小: 18.5 MB
2025-07-25 21:01:04,500 - INFO -   压缩率: 88.8%
2025-07-25 21:01:04,500 - INFO -   处理进度: 58/60 - sunyan_7.avi
2025-07-25 21:01:04,500 - INFO - 开始转换: sunyan_7.avi
2025-07-25 21:02:10,502 - INFO - 转换成功: sunyan_7.avi
2025-07-25 21:02:10,502 - INFO -   原始大小: 171.3 MB
2025-07-25 21:02:10,502 - INFO -   转换后大小: 23.3 MB
2025-07-25 21:02:10,502 - INFO -   压缩率: 86.4%
2025-07-25 21:02:10,502 - INFO -   处理进度: 59/60 - sunyan_8.avi
2025-07-25 21:02:10,502 - INFO - 开始转换: sunyan_8.avi
2025-07-25 21:03:14,466 - INFO - 转换成功: sunyan_8.avi
2025-07-25 21:03:14,466 - INFO -   原始大小: 171.7 MB
2025-07-25 21:03:14,466 - INFO -   转换后大小: 22.7 MB
2025-07-25 21:03:14,466 - INFO -   压缩率: 86.8%
2025-07-25 21:03:14,466 - INFO -   处理进度: 60/60 - sunyan_9.avi
2025-07-25 21:03:14,466 - INFO - 开始转换: sunyan_9.avi
2025-07-25 21:04:42,340 - INFO - 转换成功: sunyan_9.avi
2025-07-25 21:04:42,340 - INFO -   原始大小: 219.7 MB
2025-07-25 21:04:42,340 - INFO -   转换后大小: 32.1 MB
2025-07-25 21:04:42,355 - INFO -   压缩率: 85.4%
2025-07-25 21:04:42,355 - INFO -   该目录转换完成: 60/60 个文件
2025-07-25 21:04:42,355 - INFO - 处理video目录: C:\Users\<USER>\Desktop\curiosity_pupil\data\20250725_160047_sunyan\video
2025-07-25 21:04:42,355 - INFO -   找到 39 个AVI文件
2025-07-25 21:04:42,355 - INFO -   处理进度: 1/39 - sunyan_1.avi
2025-07-25 21:04:42,361 - INFO - 开始转换: sunyan_1.avi
2025-07-25 21:05:55,591 - INFO - 转换成功: sunyan_1.avi
2025-07-25 21:05:55,591 - INFO -   原始大小: 178.0 MB
2025-07-25 21:05:55,591 - INFO -   转换后大小: 21.2 MB
2025-07-25 21:05:55,591 - INFO -   压缩率: 88.1%
2025-07-25 21:05:55,591 - INFO -   处理进度: 2/39 - sunyan_10.avi
2025-07-25 21:05:55,591 - INFO - 开始转换: sunyan_10.avi
2025-07-25 21:07:13,224 - INFO - 转换成功: sunyan_10.avi
2025-07-25 21:07:13,224 - INFO -   原始大小: 162.3 MB
2025-07-25 21:07:13,224 - INFO -   转换后大小: 19.6 MB
2025-07-25 21:07:13,224 - INFO -   压缩率: 87.9%
2025-07-25 21:07:13,224 - INFO -   处理进度: 3/39 - sunyan_11.avi
2025-07-25 21:07:13,224 - INFO - 开始转换: sunyan_11.avi
2025-07-25 21:08:20,632 - INFO - 转换成功: sunyan_11.avi
2025-07-25 21:08:20,632 - INFO -   原始大小: 161.2 MB
2025-07-25 21:08:20,632 - INFO -   转换后大小: 19.4 MB
2025-07-25 21:08:20,632 - INFO -   压缩率: 88.0%
2025-07-25 21:08:20,632 - INFO -   处理进度: 4/39 - sunyan_12.avi
2025-07-25 21:08:20,632 - INFO - 开始转换: sunyan_12.avi
2025-07-25 21:09:32,991 - INFO - 转换成功: sunyan_12.avi
2025-07-25 21:09:32,991 - INFO -   原始大小: 186.0 MB
2025-07-25 21:09:32,991 - INFO -   转换后大小: 22.7 MB
2025-07-25 21:09:32,991 - INFO -   压缩率: 87.8%
2025-07-25 21:09:32,991 - INFO -   处理进度: 5/39 - sunyan_13.avi
2025-07-25 21:09:32,991 - INFO - 开始转换: sunyan_13.avi
2025-07-25 21:10:37,169 - INFO - 转换成功: sunyan_13.avi
2025-07-25 21:10:37,169 - INFO -   原始大小: 164.4 MB
2025-07-25 21:10:37,169 - INFO -   转换后大小: 20.4 MB
2025-07-25 21:10:37,169 - INFO -   压缩率: 87.6%
2025-07-25 21:10:37,169 - INFO -   处理进度: 6/39 - sunyan_14.avi
2025-07-25 21:10:37,169 - INFO - 开始转换: sunyan_14.avi
2025-07-25 21:11:56,131 - INFO - 转换成功: sunyan_14.avi
2025-07-25 21:11:56,131 - INFO -   原始大小: 188.5 MB
2025-07-25 21:11:56,132 - INFO -   转换后大小: 23.5 MB
2025-07-25 21:11:56,132 - INFO -   压缩率: 87.5%
2025-07-25 21:11:56,133 - INFO -   处理进度: 7/39 - sunyan_15.avi
2025-07-25 21:11:56,134 - INFO - 开始转换: sunyan_15.avi
2025-07-25 21:12:56,330 - INFO - 转换成功: sunyan_15.avi
2025-07-25 21:12:56,330 - INFO -   原始大小: 156.0 MB
2025-07-25 21:12:56,330 - INFO -   转换后大小: 18.5 MB
2025-07-25 21:12:56,330 - INFO -   压缩率: 88.1%
2025-07-25 21:12:56,330 - INFO -   处理进度: 8/39 - sunyan_16.avi
2025-07-25 21:12:56,330 - INFO - 开始转换: sunyan_16.avi
2025-07-25 21:14:11,940 - INFO - 转换成功: sunyan_16.avi
2025-07-25 21:14:11,940 - INFO -   原始大小: 192.4 MB
2025-07-25 21:14:11,940 - INFO -   转换后大小: 22.5 MB
2025-07-25 21:14:11,940 - INFO -   压缩率: 88.3%
2025-07-25 21:14:11,940 - INFO -   处理进度: 9/39 - sunyan_17.avi
2025-07-25 21:14:11,940 - INFO - 开始转换: sunyan_17.avi
2025-07-25 21:15:25,816 - INFO - 转换成功: sunyan_17.avi
2025-07-25 21:15:25,816 - INFO -   原始大小: 170.4 MB
2025-07-25 21:15:25,816 - INFO -   转换后大小: 20.5 MB
2025-07-25 21:15:25,816 - INFO -   压缩率: 88.0%
2025-07-25 21:15:25,816 - INFO -   处理进度: 10/39 - sunyan_18.avi
2025-07-25 21:15:25,816 - INFO - 开始转换: sunyan_18.avi
2025-07-25 21:16:26,147 - INFO - 转换成功: sunyan_18.avi
2025-07-25 21:16:26,147 - INFO -   原始大小: 156.3 MB
2025-07-25 21:16:26,147 - INFO -   转换后大小: 18.3 MB
2025-07-25 21:16:26,147 - INFO -   压缩率: 88.3%
2025-07-25 21:16:26,147 - INFO -   处理进度: 11/39 - sunyan_19.avi
2025-07-25 21:16:26,147 - INFO - 开始转换: sunyan_19.avi
2025-07-25 21:17:36,857 - INFO - 转换成功: sunyan_19.avi
2025-07-25 21:17:36,857 - INFO -   原始大小: 179.8 MB
2025-07-25 21:17:36,857 - INFO -   转换后大小: 20.5 MB
2025-07-25 21:17:36,857 - INFO -   压缩率: 88.6%
2025-07-25 21:17:36,857 - INFO -   处理进度: 12/39 - sunyan_2.avi
2025-07-25 21:17:36,857 - INFO - 开始转换: sunyan_2.avi
2025-07-25 21:18:52,300 - INFO - 转换成功: sunyan_2.avi
2025-07-25 21:18:52,300 - INFO -   原始大小: 195.6 MB
2025-07-25 21:18:52,300 - INFO -   转换后大小: 23.0 MB
2025-07-25 21:18:52,300 - INFO -   压缩率: 88.2%
2025-07-25 21:18:52,300 - INFO -   处理进度: 13/39 - sunyan_20.avi
2025-07-25 21:18:52,300 - INFO - 开始转换: sunyan_20.avi
2025-07-25 21:20:06,624 - INFO - 转换成功: sunyan_20.avi
2025-07-25 21:20:06,624 - INFO -   原始大小: 194.9 MB
2025-07-25 21:20:06,624 - INFO -   转换后大小: 22.5 MB
2025-07-25 21:20:06,624 - INFO -   压缩率: 88.5%
2025-07-25 21:20:06,624 - INFO -   处理进度: 14/39 - sunyan_21.avi
2025-07-25 21:20:06,624 - INFO - 开始转换: sunyan_21.avi
2025-07-25 21:21:19,618 - INFO - 转换成功: sunyan_21.avi
2025-07-25 21:21:19,618 - INFO -   原始大小: 170.7 MB
2025-07-25 21:21:19,618 - INFO -   转换后大小: 21.8 MB
2025-07-25 21:21:19,618 - INFO -   压缩率: 87.2%
2025-07-25 21:21:19,618 - INFO -   处理进度: 15/39 - sunyan_22.avi
2025-07-25 21:21:19,618 - INFO - 开始转换: sunyan_22.avi
2025-07-25 21:22:18,072 - INFO - 转换成功: sunyan_22.avi
2025-07-25 21:22:18,072 - INFO -   原始大小: 146.8 MB
2025-07-25 21:22:18,072 - INFO -   转换后大小: 17.5 MB
2025-07-25 21:22:18,072 - INFO -   压缩率: 88.1%
2025-07-25 21:22:18,072 - INFO -   处理进度: 16/39 - sunyan_23.avi
2025-07-25 21:22:18,072 - INFO - 开始转换: sunyan_23.avi
2025-07-25 21:23:17,973 - INFO - 转换成功: sunyan_23.avi
2025-07-25 21:23:17,973 - INFO -   原始大小: 150.3 MB
2025-07-25 21:23:17,973 - INFO -   转换后大小: 18.0 MB
2025-07-25 21:23:17,973 - INFO -   压缩率: 88.0%
2025-07-25 21:23:17,973 - INFO -   处理进度: 17/39 - sunyan_24.avi
2025-07-25 21:23:17,973 - INFO - 开始转换: sunyan_24.avi
2025-07-25 21:24:27,566 - INFO - 转换成功: sunyan_24.avi
2025-07-25 21:24:27,566 - INFO -   原始大小: 171.1 MB
2025-07-25 21:24:27,566 - INFO -   转换后大小: 20.8 MB
2025-07-25 21:24:27,566 - INFO -   压缩率: 87.9%
2025-07-25 21:24:27,566 - INFO -   处理进度: 18/39 - sunyan_25.avi
2025-07-25 21:24:27,566 - INFO - 开始转换: sunyan_25.avi
2025-07-25 21:25:37,423 - INFO - 转换成功: sunyan_25.avi
2025-07-25 21:25:37,423 - INFO -   原始大小: 175.5 MB
2025-07-25 21:25:37,438 - INFO -   转换后大小: 21.6 MB
2025-07-25 21:25:37,438 - INFO -   压缩率: 87.7%
2025-07-25 21:25:37,438 - INFO -   处理进度: 19/39 - sunyan_26.avi
2025-07-25 21:25:37,438 - INFO - 开始转换: sunyan_26.avi
2025-07-25 21:26:44,289 - INFO - 转换成功: sunyan_26.avi
2025-07-25 21:26:44,289 - INFO -   原始大小: 163.6 MB
2025-07-25 21:26:44,289 - INFO -   转换后大小: 20.0 MB
2025-07-25 21:26:44,289 - INFO -   压缩率: 87.8%
2025-07-25 21:26:44,289 - INFO -   处理进度: 20/39 - sunyan_27.avi
2025-07-25 21:26:44,289 - INFO - 开始转换: sunyan_27.avi
2025-07-25 21:27:56,723 - INFO - 转换成功: sunyan_27.avi
2025-07-25 21:27:56,723 - INFO -   原始大小: 185.1 MB
2025-07-25 21:27:56,723 - INFO -   转换后大小: 21.9 MB
2025-07-25 21:27:56,723 - INFO -   压缩率: 88.2%
2025-07-25 21:27:56,723 - INFO -   处理进度: 21/39 - sunyan_28.avi
2025-07-25 21:27:56,723 - INFO - 开始转换: sunyan_28.avi
2025-07-25 21:29:03,893 - INFO - 转换成功: sunyan_28.avi
2025-07-25 21:29:03,893 - INFO -   原始大小: 167.9 MB
2025-07-25 21:29:03,893 - INFO -   转换后大小: 20.4 MB
2025-07-25 21:29:03,893 - INFO -   压缩率: 87.8%
2025-07-25 21:29:03,893 - INFO -   处理进度: 22/39 - sunyan_29.avi
2025-07-25 21:29:03,893 - INFO - 开始转换: sunyan_29.avi
2025-07-25 21:30:29,334 - INFO - 转换成功: sunyan_29.avi
2025-07-25 21:30:29,334 - INFO -   原始大小: 194.6 MB
2025-07-25 21:30:29,334 - INFO -   转换后大小: 23.0 MB
2025-07-25 21:30:29,334 - INFO -   压缩率: 88.2%
2025-07-25 21:30:29,334 - INFO -   处理进度: 23/39 - sunyan_3.avi
2025-07-25 21:30:29,334 - INFO - 开始转换: sunyan_3.avi
2025-07-25 21:31:40,201 - INFO - 转换成功: sunyan_3.avi
2025-07-25 21:31:40,201 - INFO -   原始大小: 182.2 MB
2025-07-25 21:31:40,201 - INFO -   转换后大小: 21.8 MB
2025-07-25 21:31:40,201 - INFO -   压缩率: 88.0%
2025-07-25 21:31:40,201 - INFO -   处理进度: 24/39 - sunyan_30.avi
2025-07-25 21:31:40,201 - INFO - 开始转换: sunyan_30.avi
2025-07-25 21:33:00,582 - INFO - 转换成功: sunyan_30.avi
2025-07-25 21:33:00,582 - INFO -   原始大小: 203.1 MB
2025-07-25 21:33:00,582 - INFO -   转换后大小: 24.5 MB
2025-07-25 21:33:00,582 - INFO -   压缩率: 87.9%
2025-07-25 21:33:00,582 - INFO -   处理进度: 25/39 - sunyan_31.avi
2025-07-25 21:33:00,582 - INFO - 开始转换: sunyan_31.avi
2025-07-25 21:34:32,387 - INFO - 转换成功: sunyan_31.avi
2025-07-25 21:34:32,387 - INFO -   原始大小: 183.9 MB
2025-07-25 21:34:32,387 - INFO -   转换后大小: 22.2 MB
2025-07-25 21:34:32,387 - INFO -   压缩率: 87.9%
2025-07-25 21:34:32,387 - INFO -   处理进度: 26/39 - sunyan_32.avi
2025-07-25 21:34:32,387 - INFO - 开始转换: sunyan_32.avi
2025-07-25 21:35:53,012 - INFO - 转换成功: sunyan_32.avi
2025-07-25 21:35:53,012 - INFO -   原始大小: 186.2 MB
2025-07-25 21:35:53,012 - INFO -   转换后大小: 23.3 MB
2025-07-25 21:35:53,012 - INFO -   压缩率: 87.5%
2025-07-25 21:35:53,016 - INFO -   处理进度: 27/39 - sunyan_33.avi
2025-07-25 21:35:53,016 - INFO - 开始转换: sunyan_33.avi
2025-07-25 21:37:04,201 - INFO - 转换成功: sunyan_33.avi
2025-07-25 21:37:04,201 - INFO -   原始大小: 166.0 MB
2025-07-25 21:37:04,201 - INFO -   转换后大小: 21.0 MB
2025-07-25 21:37:04,201 - INFO -   压缩率: 87.4%
2025-07-25 21:37:04,201 - INFO -   处理进度: 28/39 - sunyan_34.avi
2025-07-25 21:37:04,201 - INFO - 开始转换: sunyan_34.avi
2025-07-25 21:38:23,337 - INFO - 转换成功: sunyan_34.avi
2025-07-25 21:38:23,337 - INFO -   原始大小: 182.2 MB
2025-07-25 21:38:23,337 - INFO -   转换后大小: 22.5 MB
2025-07-25 21:38:23,337 - INFO -   压缩率: 87.6%
2025-07-25 21:38:23,337 - INFO -   处理进度: 29/39 - sunyan_35.avi
2025-07-25 21:38:23,337 - INFO - 开始转换: sunyan_35.avi
2025-07-25 21:39:38,949 - INFO - 转换成功: sunyan_35.avi
2025-07-25 21:39:38,949 - INFO -   原始大小: 173.9 MB
2025-07-25 21:39:38,949 - INFO -   转换后大小: 21.6 MB
2025-07-25 21:39:38,949 - INFO -   压缩率: 87.6%
2025-07-25 21:39:38,949 - INFO -   处理进度: 30/39 - sunyan_36.avi
2025-07-25 21:39:38,949 - INFO - 开始转换: sunyan_36.avi
2025-07-25 21:40:48,317 - INFO - 转换成功: sunyan_36.avi
2025-07-25 21:40:48,317 - INFO -   原始大小: 160.3 MB
2025-07-25 21:40:48,317 - INFO -   转换后大小: 18.8 MB
2025-07-25 21:40:48,317 - INFO -   压缩率: 88.3%
2025-07-25 21:40:48,317 - INFO -   处理进度: 31/39 - sunyan_37.avi
2025-07-25 21:40:48,317 - INFO - 开始转换: sunyan_37.avi
2025-07-25 21:42:04,502 - INFO - 转换成功: sunyan_37.avi
2025-07-25 21:42:04,502 - INFO -   原始大小: 156.5 MB
2025-07-25 21:42:04,517 - INFO -   转换后大小: 17.9 MB
2025-07-25 21:42:04,517 - INFO -   压缩率: 88.5%
2025-07-25 21:42:04,517 - INFO -   处理进度: 32/39 - sunyan_38.avi
2025-07-25 21:42:04,517 - INFO - 开始转换: sunyan_38.avi
2025-07-25 21:43:16,855 - INFO - 转换成功: sunyan_38.avi
2025-07-25 21:43:16,855 - INFO -   原始大小: 172.6 MB
2025-07-25 21:43:16,855 - INFO -   转换后大小: 20.1 MB
2025-07-25 21:43:16,855 - INFO -   压缩率: 88.4%
2025-07-25 21:43:16,855 - INFO -   处理进度: 33/39 - sunyan_39.avi
2025-07-25 21:43:16,855 - INFO - 开始转换: sunyan_39.avi
2025-07-25 21:44:32,798 - INFO - 转换成功: sunyan_39.avi
2025-07-25 21:44:32,798 - INFO -   原始大小: 183.8 MB
2025-07-25 21:44:32,798 - INFO -   转换后大小: 21.1 MB
2025-07-25 21:44:32,798 - INFO -   压缩率: 88.5%
2025-07-25 21:44:32,798 - INFO -   处理进度: 34/39 - sunyan_4.avi
2025-07-25 21:44:32,798 - INFO - 开始转换: sunyan_4.avi
2025-07-25 21:45:41,366 - INFO - 转换成功: sunyan_4.avi
2025-07-25 21:45:41,366 - INFO -   原始大小: 156.8 MB
2025-07-25 21:45:41,366 - INFO -   转换后大小: 19.2 MB
2025-07-25 21:45:41,366 - INFO -   压缩率: 87.8%
2025-07-25 21:45:41,366 - INFO -   处理进度: 35/39 - sunyan_5.avi
2025-07-25 21:45:41,366 - INFO - 开始转换: sunyan_5.avi
2025-07-25 21:47:14,743 - INFO - 转换成功: sunyan_5.avi
2025-07-25 21:47:14,743 - INFO -   原始大小: 228.0 MB
2025-07-25 21:47:14,743 - INFO -   转换后大小: 28.6 MB
2025-07-25 21:47:14,743 - INFO -   压缩率: 87.5%
2025-07-25 21:47:14,743 - INFO -   处理进度: 36/39 - sunyan_6.avi
2025-07-25 21:47:14,743 - INFO - 开始转换: sunyan_6.avi
2025-07-25 21:48:16,884 - INFO - 转换成功: sunyan_6.avi
2025-07-25 21:48:16,884 - INFO -   原始大小: 159.4 MB
2025-07-25 21:48:16,884 - INFO -   转换后大小: 20.3 MB
2025-07-25 21:48:16,884 - INFO -   压缩率: 87.3%
2025-07-25 21:48:16,884 - INFO -   处理进度: 37/39 - sunyan_7.avi
2025-07-25 21:48:16,884 - INFO - 开始转换: sunyan_7.avi
2025-07-25 21:49:30,637 - INFO - 转换成功: sunyan_7.avi
2025-07-25 21:49:30,637 - INFO -   原始大小: 176.3 MB
2025-07-25 21:49:30,637 - INFO -   转换后大小: 23.9 MB
2025-07-25 21:49:30,637 - INFO -   压缩率: 86.4%
2025-07-25 21:49:30,637 - INFO -   处理进度: 38/39 - sunyan_8.avi
2025-07-25 21:49:30,637 - INFO - 开始转换: sunyan_8.avi
2025-07-25 21:50:49,463 - INFO - 转换成功: sunyan_8.avi
2025-07-25 21:50:49,463 - INFO -   原始大小: 196.0 MB
2025-07-25 21:50:49,478 - INFO -   转换后大小: 23.0 MB
2025-07-25 21:50:49,478 - INFO -   压缩率: 88.3%
2025-07-25 21:50:49,478 - INFO -   处理进度: 39/39 - sunyan_9.avi
2025-07-25 21:50:49,478 - INFO - 开始转换: sunyan_9.avi
2025-07-25 21:52:01,775 - INFO - 转换成功: sunyan_9.avi
2025-07-25 21:52:01,775 - INFO -   原始大小: 176.4 MB
2025-07-25 21:52:01,775 - INFO -   转换后大小: 21.5 MB
2025-07-25 21:52:01,775 - INFO -   压缩率: 87.8%
2025-07-25 21:52:01,775 - INFO -   该目录转换完成: 39/39 个文件
2025-07-25 21:52:01,790 - INFO - 处理video目录: C:\Users\<USER>\Desktop\curiosity_pupil\data\20250725_162711_sunyan_continuous_reading\video
2025-07-25 21:52:01,790 - INFO -   找到 1 个AVI文件
2025-07-25 21:52:01,790 - INFO -   处理进度: 1/1 - sunyan_0.avi
2025-07-25 21:52:01,793 - INFO - 开始转换: sunyan_0.avi
2025-07-25 22:45:43,259 - INFO - 转换成功: sunyan_0.avi
2025-07-25 22:45:43,259 - INFO -   原始大小: 7782.5 MB
2025-07-25 22:45:43,259 - INFO -   转换后大小: 916.0 MB
2025-07-25 22:45:43,259 - INFO -   压缩率: 88.2%
2025-07-25 22:45:43,259 - INFO -   该目录转换完成: 1/1 个文件
2025-07-25 22:45:43,259 - INFO - 处理video目录: C:\Users\<USER>\Desktop\curiosity_pupil\data\20250725_165456_sunyan_continuous_reading\video
2025-07-25 22:45:43,259 - INFO -   找到 1 个AVI文件
2025-07-25 22:45:43,259 - INFO -   处理进度: 1/1 - sunyan_0.avi
2025-07-25 22:45:43,259 - INFO - 开始转换: sunyan_0.avi
2025-07-25 22:51:37,377 - INFO - 转换成功: sunyan_0.avi
2025-07-25 22:51:37,377 - INFO -   原始大小: 875.5 MB
2025-07-25 22:51:37,377 - INFO -   转换后大小: 99.9 MB
2025-07-25 22:51:37,377 - INFO -   压缩率: 88.6%
2025-07-25 22:51:37,377 - INFO -   该目录转换完成: 1/1 个文件
2025-07-25 22:51:37,377 - INFO - 处理video目录: C:\Users\<USER>\Desktop\curiosity_pupil\data\20250725_170121_sunyan_continuous_reading\video
2025-07-25 22:51:37,377 - INFO -   找到 1 个AVI文件
2025-07-25 22:51:37,377 - INFO -   处理进度: 1/1 - sunyan_0.avi
2025-07-25 22:51:37,377 - INFO - 开始转换: sunyan_0.avi
2025-07-25 23:21:38,283 - INFO - 转换成功: sunyan_0.avi
2025-07-25 23:21:38,283 - INFO -   原始大小: 4626.5 MB
2025-07-25 23:21:38,283 - INFO -   转换后大小: 546.4 MB
2025-07-25 23:21:38,283 - INFO -   压缩率: 88.2%
2025-07-25 23:21:38,283 - INFO -   该目录转换完成: 1/1 个文件
2025-07-25 23:21:38,283 - INFO - 处理video目录: C:\Users\<USER>\Desktop\curiosity_pupil\data\20250725_171927_sunyan_continuous_reading\video
2025-07-25 23:21:38,283 - INFO -   找到 1 个AVI文件
2025-07-25 23:21:38,283 - INFO -   处理进度: 1/1 - sunyan_0.avi
2025-07-25 23:21:38,283 - INFO - 开始转换: sunyan_0.avi
2025-07-25 23:46:08,722 - INFO - 转换成功: sunyan_0.avi
2025-07-25 23:46:08,722 - INFO -   原始大小: 3726.6 MB
2025-07-25 23:46:08,722 - INFO -   转换后大小: 452.3 MB
2025-07-25 23:46:08,722 - INFO -   压缩率: 87.9%
2025-07-25 23:46:08,722 - INFO -   该目录转换完成: 1/1 个文件
2025-07-25 23:46:08,722 - INFO - 所有video目录转换完成!
2025-07-25 23:46:08,737 - INFO - 总计成功转换: 193/193 个文件
2025-08-05 12:37:21,972 - INFO - 搜索所有video子目录: C:/Users/<USER>/Desktop/curiosity_pupil/data
2025-08-05 12:37:21,994 - INFO - 找到 15 个video目录
2025-08-05 12:37:21,994 - INFO - 处理video目录: C:\Users\<USER>\Desktop\curiosity_pupil\data\20250729_151323_test_151323_continuous_reading_aaatest\video
2025-08-05 12:37:21,995 - INFO -   找到 9 个AVI文件
2025-08-05 12:37:21,996 - INFO -   处理进度: 1/9 - test_151323_1.avi
2025-08-05 12:37:21,996 - INFO - 开始转换: test_151323_1.avi
2025-08-05 12:38:26,975 - INFO - 转换成功: test_151323_1.avi
2025-08-05 12:38:26,975 - INFO -   原始大小: 111.6 MB
2025-08-05 12:38:26,975 - INFO -   转换后大小: 12.2 MB
2025-08-05 12:38:26,975 - INFO -   压缩率: 89.1%
2025-08-05 12:38:26,975 - INFO -   处理进度: 2/9 - test_151323_2.avi
2025-08-05 12:38:26,975 - INFO - 开始转换: test_151323_2.avi
2025-08-05 12:39:54,135 - INFO - 转换成功: test_151323_2.avi
2025-08-05 12:39:54,135 - INFO -   原始大小: 112.5 MB
2025-08-05 12:39:54,135 - INFO -   转换后大小: 12.3 MB
2025-08-05 12:39:54,135 - INFO -   压缩率: 89.0%
2025-08-05 12:39:54,135 - INFO -   处理进度: 3/9 - test_151323_3.avi
2025-08-05 12:39:54,135 - INFO - 开始转换: test_151323_3.avi
2025-08-05 12:41:25,920 - INFO - 转换成功: test_151323_3.avi
2025-08-05 12:41:25,932 - INFO -   原始大小: 103.2 MB
2025-08-05 12:41:25,932 - INFO -   转换后大小: 11.3 MB
2025-08-05 12:41:25,932 - INFO -   压缩率: 89.1%
2025-08-05 12:41:25,932 - INFO -   处理进度: 4/9 - test_151323_4.avi
2025-08-05 12:41:25,932 - INFO - 开始转换: test_151323_4.avi
2025-08-05 12:42:26,699 - INFO - 转换成功: test_151323_4.avi
2025-08-05 12:42:26,699 - INFO -   原始大小: 91.3 MB
2025-08-05 12:42:26,699 - INFO -   转换后大小: 10.2 MB
2025-08-05 12:42:26,699 - INFO -   压缩率: 88.8%
2025-08-05 12:42:26,714 - INFO -   处理进度: 5/9 - test_151323_5.avi
2025-08-05 12:42:26,714 - INFO - 开始转换: test_151323_5.avi
2025-08-05 12:44:05,335 - INFO - 转换成功: test_151323_5.avi
2025-08-05 12:44:05,335 - INFO -   原始大小: 120.8 MB
2025-08-05 12:44:05,335 - INFO -   转换后大小: 13.5 MB
2025-08-05 12:44:05,335 - INFO -   压缩率: 88.8%
2025-08-05 12:44:05,335 - INFO -   处理进度: 6/9 - test_151323_6.avi
2025-08-05 12:44:05,335 - INFO - 开始转换: test_151323_6.avi
2025-08-05 12:46:51,161 - INFO - 转换成功: test_151323_6.avi
2025-08-05 12:46:51,161 - INFO -   原始大小: 141.4 MB
2025-08-05 12:46:51,161 - INFO -   转换后大小: 15.8 MB
2025-08-05 12:46:51,161 - INFO -   压缩率: 88.8%
2025-08-05 12:46:51,163 - INFO -   处理进度: 7/9 - test_151323_7.avi
2025-08-05 12:46:51,163 - INFO - 开始转换: test_151323_7.avi
2025-08-05 12:49:19,326 - INFO - 转换成功: test_151323_7.avi
2025-08-05 12:49:19,329 - INFO -   原始大小: 146.1 MB
2025-08-05 12:49:19,331 - INFO -   转换后大小: 16.5 MB
2025-08-05 12:49:19,331 - INFO -   压缩率: 88.7%
2025-08-05 12:49:19,331 - INFO -   处理进度: 8/9 - test_151323_8.avi
2025-08-05 12:49:19,331 - INFO - 开始转换: test_151323_8.avi
2025-08-05 12:51:37,829 - INFO - 转换成功: test_151323_8.avi
2025-08-05 12:51:37,830 - INFO -   原始大小: 133.5 MB
2025-08-05 12:51:37,831 - INFO -   转换后大小: 15.1 MB
2025-08-05 12:51:37,831 - INFO -   压缩率: 88.7%
2025-08-05 12:51:37,832 - INFO -   处理进度: 9/9 - test_151323_9.avi
2025-08-05 12:51:37,832 - INFO - 开始转换: test_151323_9.avi
2025-08-05 12:51:45,540 - INFO - 转换成功: test_151323_9.avi
2025-08-05 12:51:45,540 - INFO -   原始大小: 8.1 MB
2025-08-05 12:51:45,540 - INFO -   转换后大小: 1.1 MB
2025-08-05 12:51:45,540 - INFO -   压缩率: 86.5%
2025-08-05 12:51:45,540 - INFO -   该目录转换完成: 9/9 个文件
2025-08-05 12:51:45,540 - INFO - 处理video目录: C:\Users\<USER>\Desktop\curiosity_pupil\data\20250729_154532_dark_env_aaa_test_continuous_reading\video
2025-08-05 12:51:45,540 - INFO -   找到 4 个AVI文件
2025-08-05 12:51:45,540 - INFO -   处理进度: 1/4 - dark_env_aaa_test_1.avi
2025-08-05 12:51:45,540 - INFO - 开始转换: dark_env_aaa_test_1.avi
2025-08-05 12:55:33,987 - INFO - 转换成功: dark_env_aaa_test_1.avi
2025-08-05 12:55:33,987 - INFO -   原始大小: 198.9 MB
2025-08-05 12:55:33,987 - INFO -   转换后大小: 53.4 MB
2025-08-05 12:55:33,987 - INFO -   压缩率: 73.1%
2025-08-05 12:55:33,987 - INFO -   处理进度: 2/4 - dark_env_aaa_test_2.avi
2025-08-05 12:55:33,987 - INFO - 开始转换: dark_env_aaa_test_2.avi
