# 实验程序修改总结

## 修改概述

根据您的要求，我对好奇心瞳孔冷知识实验程序进行了以下两个主要修改：

1. **动态答案显示时间控制**：答案显示时间不再固定，而是通过眼动检测来动态控制
2. **十字准星漂移校正**：在显示十字准星时加入EyeLink漂移校正功能

## 详细修改内容

### 1. 动态答案显示时间控制

#### 修改文件：`experiment_display.py`
- **修改方法**：`show_answer()`
- **新增参数**：
  - `eyelink_manager`: EyeLink管理器对象
  - `gaze_duration_threshold`: 注视时间阈值（默认1.0秒）

#### 功能特点：
- 在答案显示屏幕的中下方绘制一个蓝色长方形按钮
- 按钮中显示"读完看我哦"文字
- 实时监测被试的眼动位置
- 当眼动持续注视按钮区域达到设定时间后，自动切换到下一屏幕
- 支持Esc键手动跳过
- 防止无限等待（最多60秒超时）

#### 按钮规格：
- 尺寸：200×60像素
- 位置：屏幕中央下方150像素处
- 颜色：深蓝色背景，白色边框和文字
- 字体：SimHei，24号，加粗

### 2. 十字准星漂移校正

#### 修改文件：`experiment_display.py`
- **修改方法**：`show_fixation_cross()`
- **新增参数**：
  - `eyelink_manager`: EyeLink管理器对象
  - `perform_drift_correction`: 是否执行漂移校正（默认False）

#### 功能特点：
- 显示十字准星并提示被试注视
- 调用EyeLink的`doDriftCorrect()`函数
- 自动处理坐标系转换（PsychoPy坐标系 → 像素坐标系）
- 显示校正结果反馈
- 支持按ESC键进入EyeLink设置菜单

#### 校正流程：
1. 显示十字准星和指示文本3秒
2. 执行EyeLink漂移校正
3. 显示校正结果（成功/失败）
4. 继续正常的十字准星显示

### 3. EyeLink管理器增强

#### 修改文件：`eyelink_manager.py`
- **新增方法**：`get_current_gaze_position()`

#### 功能特点：
- 获取最新的眼动样本数据
- 支持左眼和右眼数据
- 返回(x, y)坐标或None
- 异常处理和错误日志

### 4. 实验流程集成

#### 修改文件：`experiment_flow.py`
- **修改内容**：
  - 更新`timing`参数配置
  - 修改基线校准阶段，加入漂移校正
  - 修改答案显示阶段，使用动态时间控制

#### 新增参数：
- `gaze_duration_threshold`: 1.0秒（可调节）
- `answer_display`: None（表示动态控制）

#### 流程变化：
- **基线校准阶段**：现在会自动执行漂移校正
- **答案显示阶段**：使用眼动检测控制显示时间

## 参数配置

### 可调节参数

1. **注视时间阈值**：
   ```python
   # 在experiment_flow.py中修改
   'gaze_duration_threshold': 1.0  # 单位：秒
   ```

2. **按钮位置和尺寸**：
   ```python
   # 在experiment_display.py的show_answer方法中修改
   button_width = 200      # 按钮宽度
   button_height = 60      # 按钮高度
   button_y = -win_height//2 + 150  # 按钮Y位置
   ```

3. **漂移校正设置**：
   ```python
   # 在show_fixation_cross调用时设置
   perform_drift_correction=True  # 是否执行漂移校正
   ```

## 使用方法

### 在实际实验中使用：

1. **启用EyeLink**：
   ```python
   experiment = CuriosityExperiment(
       participant_id="被试ID",
       use_eyelink=True,  # 启用EyeLink
       fullscreen=True
   )
   ```

2. **调整参数**：
   ```python
   # 修改注视时间阈值
   experiment.timing['gaze_duration_threshold'] = 1.5  # 1.5秒
   ```

### 测试和验证：

运行以下测试程序验证功能：
- `test_modifications.py`：测试所有修改是否正确
- `demo_new_features.py`：演示新功能效果

## 技术实现细节

### 眼动位置检测算法：
1. 获取EyeLink最新样本数据
2. 检查左眼或右眼数据可用性
3. 提取注视点坐标
4. 判断是否在按钮区域内
5. 计算持续注视时间

### 坐标系转换：
- PsychoPy坐标系：中心(0,0)，Y轴向上为正
- EyeLink像素坐标系：左上角(0,0)，Y轴向下为正
- 转换公式：
  ```python
  pixel_x = int(win_width // 2 + psychopy_x)
  pixel_y = int(win_height // 2 - psychopy_y)
  ```

### 异常处理：
- 网络连接异常
- 眼动数据获取失败
- 坐标转换错误
- 超时保护机制

## 兼容性说明

- **向后兼容**：原有功能保持不变
- **可选功能**：新功能通过参数控制，默认关闭
- **虚拟模式**：支持无EyeLink设备的测试运行
- **错误恢复**：异常情况下自动降级到原有模式

## 测试结果

✅ 所有模块导入正常  
✅ 新方法参数配置正确  
✅ EyeLink集成功能正常  
✅ 实验流程集成成功  
✅ 虚拟模式测试通过  

## 后续建议

1. **实际设备测试**：在真实EyeLink设备上测试功能
2. **参数优化**：根据实际使用情况调整注视时间阈值
3. **用户界面**：可考虑添加实时注视状态指示
4. **数据记录**：记录注视时间和按钮交互数据用于分析

---

**修改完成时间**：2025年7月8日  
**测试状态**：✅ 通过  
**兼容性**：✅ 向后兼容
