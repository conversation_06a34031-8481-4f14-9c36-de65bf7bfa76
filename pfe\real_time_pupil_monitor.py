#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
实时瞳孔监测优化程序
基于PFE校正的实时瞳孔大小监测和显示

功能：
1. 读取之前的PFE标定文件
2. 在屏幕中央显示标定点作为瞳孔参照
3. 随机显示不同位置的点
4. 以不同高度从左往右慢速移动光点
5. 实时显示校正后的瞳孔大小数值
6. 显示最大最小值的差值范围
"""

import os
import sys
import time
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
from datetime import datetime
from typing import List, Tuple, Dict, Optional
import logging
import random
import threading
import queue

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

try:
    from psychopy import visual, core, event, monitors
    PSYCHOPY_AVAILABLE = True
except ImportError:
    PSYCHOPY_AVAILABLE = False
    print("警告：PsychoPy未安装，将使用模拟模式")

from eyelink_manager import EyeLinkManager
from pfe.config import *
from pfe.pfe_correction import PFECorrector

# 配置日志
logging.basicConfig(
    level=getattr(logging, LOG_LEVEL),
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger('实时瞳孔监测')

# 实时监测配置
MONITOR_CONFIG = {
    'update_rate': 30,  # 更新频率 (Hz)
    'display_duration': 60,  # 显示时长 (秒)
    'center_point_duration': 4,  # 中央点显示时长 (秒)
    'random_point_duration': 20,  # 随机点显示时长 (秒)
    'moving_point_duration': 30,  # 移动点显示时长 (秒)
    'moving_speed': 3,  # 移动速度 (像素/秒)
    'point_size': 15,  # 点的大小
    'text_size': 24,  # 文字大小
    'text_color': 'white',
    'point_color': 'red',
    'background_color': 'gray'
}

class RealTimePupilMonitor:
    """实时瞳孔监测器"""
    
    def __init__(self, calibration_data_path: str,
                 screen_width: int = DEFAULT_SCREEN_WIDTH,
                 screen_height: int = DEFAULT_SCREEN_HEIGHT,
                 dummy_mode: bool = DUMMY_MODE):
        """
        初始化实时瞳孔监测器
        
        Args:
            calibration_data_path: PFE标定数据文件路径
            screen_width: 屏幕宽度
            screen_height: 屏幕高度
            dummy_mode: 是否使用虚拟模式
        """
        self.calibration_data_path = calibration_data_path
        self.screen_width = screen_width
        self.screen_height = screen_height
        self.dummy_mode = dummy_mode
        
        # 初始化组件
        self.window = None
        self.eyelink = None
        self.pfe_corrector = None
        
        # 监测数据
        self.pupil_data_queue = queue.Queue()
        self.current_pupil_data = {
            'raw_left': 0,
            'corrected_left': 0,
            'center_baseline': 0,
            # 原始瞳孔统计
            'raw_min_value': float('inf'),
            'raw_max_value': float('-inf'),
            'raw_range_value': 0,
            # 校正后瞳孔统计
            'corrected_min_value': float('inf'),
            'corrected_max_value': float('-inf'),
            'corrected_range_value': 0,
            # 校正后瞳孔与基线差值的绝对值统计
            'baseline_diff_min': float('inf'),
            'baseline_diff_max': float('-inf'),
            'baseline_diff_range': 0
        }
        
        # 监测状态
        self.is_monitoring = False
        self.monitor_thread = None
        
        # 显示元素
        self.text_elements = {}
        self.point_element = None
        
        logger.info(f"实时瞳孔监测器初始化完成")
        logger.info(f"标定数据: {calibration_data_path}")
        logger.info(f"屏幕尺寸: {screen_width}×{screen_height}")
    
    def setup_display(self) -> bool:
        """设置显示窗口"""
        if not PSYCHOPY_AVAILABLE:
            logger.warning("PsychoPy不可用，跳过显示设置")
            return True
            
        try:
            # 创建显示窗口
            self.window = visual.Window(
                size=(self.screen_width, self.screen_height),
                fullscr=True,
                color=MONITOR_CONFIG['background_color'],
                units='pix',
                allowGUI=False
            )
            
            # 创建文本显示元素
            self._create_text_elements()
            
            # 创建点显示元素
            self.point_element = visual.Circle(
                self.window,
                radius=MONITOR_CONFIG['point_size']/2,
                fillColor=MONITOR_CONFIG['point_color'],
                lineColor=MONITOR_CONFIG['point_color']
            )
            
            logger.info("显示窗口创建成功")
            return True
            
        except Exception as e:
            logger.error(f"显示窗口创建失败: {e}")
            return False
    
    def _create_text_elements(self):
        """创建文本显示元素"""
        try:
            # 标题
            self.text_elements['title'] = visual.TextStim(
                self.window,
                text="实时瞳孔监测",
                font='SimHei',
                height=MONITOR_CONFIG['text_size'] + 10,
                color=MONITOR_CONFIG['text_color'],
                pos=(0, self.screen_height/2 - 50)
            )
            
            # 原始瞳孔大小
            self.text_elements['raw_pupil'] = visual.TextStim(
                self.window,
                text="原始瞳孔: 0",
                font='SimHei',
                height=MONITOR_CONFIG['text_size'],
                color=MONITOR_CONFIG['text_color'],
                pos=(0, 100)
            )
            
            # 校正后瞳孔大小
            self.text_elements['corrected_pupil'] = visual.TextStim(
                self.window,
                text="校正瞳孔: 0",
                font='SimHei',
                height=MONITOR_CONFIG['text_size'],
                color=MONITOR_CONFIG['text_color'],
                pos=(0, 80)
            )

            # 中央基线
            self.text_elements['baseline'] = visual.TextStim(
                self.window,
                text="中央基线: 0",
                font='SimHei',
                height=MONITOR_CONFIG['text_size'],
                color=MONITOR_CONFIG['text_color'],
                pos=(0, 40)
            )

            # 原始瞳孔范围信息
            self.text_elements['raw_range_info'] = visual.TextStim(
                self.window,
                text="原始范围: 0 (最小: 0, 最大: 0)",
                font='SimHei',
                height=MONITOR_CONFIG['text_size'] - 2,
                color='lightblue',
                pos=(0, 0)
            )

            # 校正后瞳孔范围信息
            self.text_elements['corrected_range_info'] = visual.TextStim(
                self.window,
                text="校正范围: 0 (最小: 0, 最大: 0)",
                font='SimHei',
                height=MONITOR_CONFIG['text_size'] - 2,
                color='lightgreen',
                pos=(0, -30)
            )

            # 与基线差值范围信息
            self.text_elements['baseline_diff_range_info'] = visual.TextStim(
                self.window,
                text="基线差值范围: 0 (最小: 0, 最大: 0)",
                font='SimHei',
                height=MONITOR_CONFIG['text_size'] - 2,
                color='yellow',
                pos=(0, -60)
            )
            
            # 状态信息
            self.text_elements['status'] = visual.TextStim(
                self.window,
                text="状态: 准备中",
                font='SimHei',
                height=MONITOR_CONFIG['text_size'] - 4,
                color='orange',
                pos=(0, -120)
            )

            # 指导语
            self.text_elements['instruction'] = visual.TextStim(
                self.window,
                text="按ESC键退出",
                font='SimHei',
                height=MONITOR_CONFIG['text_size'] - 6,
                color='lightgray',
                pos=(0, -self.screen_height/2 + 30)
            )
            
        except Exception as e:
            logger.error(f"创建文本元素失败: {e}")
    
    def setup_eyelink(self) -> bool:
        """设置EyeLink连接"""
        try:
            # 创建EyeLink管理器
            self.eyelink = EyeLinkManager(
                participant_id="pupil_monitor",
                data_dir="./temp",
                screen_size=(self.screen_width, self.screen_height),
                dummy_mode=self.dummy_mode,
                display_window=self.window,
                enable_camera=False
            )

            # 连接EyeLink
            if not self.eyelink.connect():
                logger.error("EyeLink连接失败")
                return False

            # 配置EyeLink
            if not self.eyelink.setup_tracker():
                logger.error("EyeLink配置失败")
                return False

            # 校准EyeLink（关键步骤！）
            if not self.dummy_mode:
                logger.info("开始EyeLink校准...")
                if not self.eyelink.calibrate():
                    logger.error("EyeLink校准失败")
                    return False
                logger.info("EyeLink校准完成")

            logger.info("EyeLink设置完成")
            return True

        except Exception as e:
            logger.error(f"EyeLink设置失败: {e}")
            return False
    
    def setup_pfe_corrector(self) -> bool:
        """设置PFE校正器"""
        try:
            # 创建PFE校正器
            self.pfe_corrector = PFECorrector(
                calibration_data_path=self.calibration_data_path,
                screen_width=self.screen_width,
                screen_height=self.screen_height
            )
            
            if self.pfe_corrector.calibration_data is None:
                logger.error("PFE标定数据加载失败")
                return False
            
            logger.info("PFE校正器设置完成")
            return True
            
        except Exception as e:
            logger.error(f"PFE校正器设置失败: {e}")
            return False

    def start_monitoring(self):
        """开始实时监测"""
        if self.is_monitoring:
            logger.warning("监测已在进行中")
            return

        self.is_monitoring = True
        self.monitor_thread = threading.Thread(target=self._monitor_loop, daemon=True)
        self.monitor_thread.start()
        logger.info("开始实时瞳孔监测")

    def stop_monitoring(self):
        """停止实时监测"""
        self.is_monitoring = False
        if self.monitor_thread:
            self.monitor_thread.join(timeout=1.0)
        logger.info("停止实时瞳孔监测")

    def _monitor_loop(self):
        """监测循环"""
        try:
            update_interval = 1.0 / MONITOR_CONFIG['update_rate']

            while self.is_monitoring:
                # 获取当前瞳孔数据
                pupil_data = self._get_current_pupil_data()

                if pupil_data:
                    # 更新监测数据
                    self._update_pupil_data(pupil_data)

                time.sleep(update_interval)

        except Exception as e:
            logger.error(f"监测循环出错: {e}")

    def _get_current_pupil_data(self) -> Optional[Dict]:
        """获取当前瞳孔数据"""
        try:
            if self.dummy_mode:
                # 虚拟模式：生成模拟数据
                return {
                    'pupil_left': np.random.normal(4000, 100),
                    'gaze_x_left': np.random.normal(self.screen_width/2, 50),
                    'gaze_y_left': np.random.normal(self.screen_height/2, 50)
                }

            if self.eyelink:
                # 获取实时数据
                pupil_data = self.eyelink.get_current_pupil_data()

                # 调试信息：每10次获取打印一次状态
                if hasattr(self, '_debug_counter'):
                    self._debug_counter += 1
                else:
                    self._debug_counter = 1

                if self._debug_counter % 300 == 0:  # 每10秒打印一次（30Hz * 10s = 300）
                    if pupil_data:
                        logger.info(f"✓ 获取到实时数据: {pupil_data}")
                    else:
                        logger.warning("⚠️ 未获取到实时数据 - 检查EyeLink连接和记录状态")
                        # 检查EyeLink状态
                        if hasattr(self.eyelink, 'tracker') and self.eyelink.tracker:
                            logger.info("EyeLink tracker已连接")
                        else:
                            logger.error("EyeLink tracker未连接")

                return pupil_data

            return None

        except Exception as e:
            logger.error(f"获取瞳孔数据失败: {e}")
            return None

    def _update_pupil_data(self, pupil_data: Dict):
        """更新瞳孔数据"""
        try:
            raw_pupil = pupil_data.get('pupil_left', 0)
            gaze_x = pupil_data.get('gaze_x_left', self.screen_width/2)
            gaze_y = pupil_data.get('gaze_y_left', self.screen_height/2)

            # 应用PFE校正
            corrected_pupil = self._apply_pfe_correction(raw_pupil, gaze_x, gaze_y)

            # 更新当前数据
            self.current_pupil_data['raw_left'] = raw_pupil
            self.current_pupil_data['corrected_left'] = corrected_pupil

            # 更新原始瞳孔统计
            if raw_pupil > 0:
                self.current_pupil_data['raw_min_value'] = min(
                    self.current_pupil_data['raw_min_value'], raw_pupil
                )
                self.current_pupil_data['raw_max_value'] = max(
                    self.current_pupil_data['raw_max_value'], raw_pupil
                )
                self.current_pupil_data['raw_range_value'] = (
                    self.current_pupil_data['raw_max_value'] - self.current_pupil_data['raw_min_value']
                )

            # 更新校正后瞳孔统计
            if corrected_pupil > 0:
                self.current_pupil_data['corrected_min_value'] = min(
                    self.current_pupil_data['corrected_min_value'], corrected_pupil
                )
                self.current_pupil_data['corrected_max_value'] = max(
                    self.current_pupil_data['corrected_max_value'], corrected_pupil
                )
                self.current_pupil_data['corrected_range_value'] = (
                    self.current_pupil_data['corrected_max_value'] - self.current_pupil_data['corrected_min_value']
                )

                # 更新与基线差值的绝对值统计
                if self.current_pupil_data['center_baseline'] > 0:
                    baseline_diff = abs(corrected_pupil - self.current_pupil_data['center_baseline'])
                    self.current_pupil_data['baseline_diff_min'] = min(
                        self.current_pupil_data['baseline_diff_min'], baseline_diff
                    )
                    self.current_pupil_data['baseline_diff_max'] = max(
                        self.current_pupil_data['baseline_diff_max'], baseline_diff
                    )
                    self.current_pupil_data['baseline_diff_range'] = (
                        self.current_pupil_data['baseline_diff_max'] - self.current_pupil_data['baseline_diff_min']
                    )

        except Exception as e:
            logger.error(f"更新瞳孔数据失败: {e}")

    def _apply_pfe_correction(self, raw_pupil: float, gaze_x: float, gaze_y: float) -> float:
        """应用PFE校正"""
        try:
            if not self.pfe_corrector or raw_pupil <= 0:
                return raw_pupil

            # 创建临时DataFrame进行校正
            temp_df = pd.DataFrame({
                'gx_left': [gaze_x],
                'gy_left': [gaze_y],
                'pa_left': [raw_pupil]
            })

            # 应用校正
            corrected_df = self.pfe_corrector.correct_pupil_data(temp_df, method='auto')

            if 'pa_left_corrected' in corrected_df.columns:
                return corrected_df['pa_left_corrected'].iloc[0]
            else:
                return raw_pupil

        except Exception as e:
            logger.error(f"PFE校正失败: {e}")
            return raw_pupil

    def update_display(self, current_phase: str, point_pos: Optional[Tuple[float, float]] = None):
        """更新显示"""
        if not PSYCHOPY_AVAILABLE or not self.window:
            return

        try:
            # 清空屏幕
            self.window.clearBuffer()

            # 更新文本内容
            self._update_text_content(current_phase)

            # 绘制所有文本
            for text_elem in self.text_elements.values():
                text_elem.draw()

            # 绘制点（如果有）
            if point_pos and self.point_element:
                # 转换坐标系（PsychoPy使用中心为原点）
                psychopy_x = point_pos[0] - self.screen_width/2
                psychopy_y = self.screen_height/2 - point_pos[1]
                self.point_element.pos = (psychopy_x, psychopy_y)
                self.point_element.draw()

            # 刷新显示
            self.window.flip()

        except Exception as e:
            logger.error(f"更新显示失败: {e}")

    def _update_text_content(self, current_phase: str):
        """更新文本内容"""
        try:
            # 更新瞳孔数据显示
            self.text_elements['raw_pupil'].text = f"原始瞳孔: {self.current_pupil_data['raw_left']:.1f}"
            self.text_elements['corrected_pupil'].text = f"校正瞳孔: {self.current_pupil_data['corrected_left']:.1f}"
            self.text_elements['baseline'].text = f"中央基线: {self.current_pupil_data['center_baseline']:.1f}"

            # 更新原始瞳孔范围信息
            raw_min = self.current_pupil_data['raw_min_value']
            raw_max = self.current_pupil_data['raw_max_value']
            raw_range = self.current_pupil_data['raw_range_value']

            if raw_min != float('inf') and raw_max != float('-inf'):
                self.text_elements['raw_range_info'].text = (
                    f"原始范围: {raw_range:.1f} (最小: {raw_min:.1f}, 最大: {raw_max:.1f})"
                )
            else:
                self.text_elements['raw_range_info'].text = "原始范围: 等待数据..."

            # 更新校正后瞳孔范围信息
            corrected_min = self.current_pupil_data['corrected_min_value']
            corrected_max = self.current_pupil_data['corrected_max_value']
            corrected_range = self.current_pupil_data['corrected_range_value']

            if corrected_min != float('inf') and corrected_max != float('-inf'):
                self.text_elements['corrected_range_info'].text = (
                    f"校正范围: {corrected_range:.1f} (最小: {corrected_min:.1f}, 最大: {corrected_max:.1f})"
                )
            else:
                self.text_elements['corrected_range_info'].text = "校正范围: 等待数据..."

            # 更新与基线差值范围信息
            diff_min = self.current_pupil_data['baseline_diff_min']
            diff_max = self.current_pupil_data['baseline_diff_max']
            diff_range = self.current_pupil_data['baseline_diff_range']

            if (diff_min != float('inf') and diff_max != float('-inf') and
                self.current_pupil_data['center_baseline'] > 0):
                self.text_elements['baseline_diff_range_info'].text = (
                    f"基线差值范围: {diff_range:.1f} (最小: {diff_min:.1f}, 最大: {diff_max:.1f})"
                )
            else:
                self.text_elements['baseline_diff_range_info'].text = "基线差值范围: 等待基线数据..."

            # 更新状态
            self.text_elements['status'].text = f"状态: {current_phase}"

        except Exception as e:
            logger.error(f"更新文本内容失败: {e}")

    def run_monitoring_sequence(self):
        """运行完整的监测序列"""
        try:
            logger.info("开始监测序列")

            # 开始EyeLink记录（关键步骤！）
            if self.eyelink and not self.dummy_mode:
                logger.info("开始EyeLink记录...")
                if not self.eyelink.start_recording(trial_id=1):
                    logger.error("EyeLink记录启动失败")
                    return
                self.eyelink.send_message("PUPIL_MONITOR_START")
                logger.info("EyeLink记录已启动")

            # 开始实时监测
            self.start_monitoring()

            # 阶段1：中央标定点
            logger.info("阶段1：中央标定点")
            if self.eyelink:
                self.eyelink.send_message("CENTER_POINT_PHASE_START")
            self._run_center_point_phase()

            # 阶段2：随机位置点
            logger.info("阶段2：随机位置点")
            if self.eyelink:
                self.eyelink.send_message("RANDOM_POINTS_PHASE_START")
            self._run_random_points_phase()

            # 阶段3：移动光点
            logger.info("阶段3：移动光点")
            if self.eyelink:
                self.eyelink.send_message("MOVING_POINT_PHASE_START")
            self._run_moving_point_phase()

            # 停止记录和监测
            if self.eyelink and not self.dummy_mode:
                self.eyelink.send_message("PUPIL_MONITOR_END")
                self.eyelink.stop_recording()
                logger.info("EyeLink记录已停止")

            self.stop_monitoring()

            logger.info("监测序列完成")

        except Exception as e:
            logger.error(f"监测序列失败: {e}")
            # 确保停止记录
            if self.eyelink and not self.dummy_mode:
                try:
                    self.eyelink.stop_recording()
                except:
                    pass
            self.stop_monitoring()

    def _run_center_point_phase(self):
        """运行中央标定点阶段"""
        try:
            center_x = self.screen_width / 2
            center_y = self.screen_height / 2

            start_time = time.time()
            baseline_values = []

            while time.time() - start_time < MONITOR_CONFIG['center_point_duration']:
                # 检查退出键
                if self._check_exit():
                    return

                # 更新显示
                self.update_display("中央标定点", (center_x, center_y))

                # 收集基线数据
                if self.current_pupil_data['corrected_left'] > 0:
                    baseline_values.append(self.current_pupil_data['corrected_left'])

                time.sleep(1.0 / 60)  # 60 FPS

            # 计算中央基线
            if baseline_values:
                self.current_pupil_data['center_baseline'] = np.mean(baseline_values)
                logger.info(f"中央基线: {self.current_pupil_data['center_baseline']:.1f}")

        except Exception as e:
            logger.error(f"中央标定点阶段失败: {e}")

    def _run_random_points_phase(self):
        """运行随机位置点阶段"""
        try:
            start_time = time.time()
            point_duration = 2.0  # 每个点显示2秒
            last_point_time = start_time
            current_point = None

            while time.time() - start_time < MONITOR_CONFIG['random_point_duration']:
                # 检查退出键
                if self._check_exit():
                    return

                # 生成新的随机点
                if time.time() - last_point_time >= point_duration:
                    current_point = self._generate_random_point()
                    last_point_time = time.time()

                # 更新显示
                self.update_display("随机位置点", current_point)

                time.sleep(1.0 / 60)  # 60 FPS

        except Exception as e:
            logger.error(f"随机位置点阶段失败: {e}")

    def _run_moving_point_phase(self):
        """运行移动光点阶段"""
        try:
            start_time = time.time()

            # 移动参数
            move_height_levels = [0.3, 0.5, 0.7]  # 屏幕高度的比例
            current_level = 0
            level_duration = MONITOR_CONFIG['moving_point_duration'] / len(move_height_levels)

            while time.time() - start_time < MONITOR_CONFIG['moving_point_duration']:
                # 检查退出键
                if self._check_exit():
                    return

                # 计算当前高度级别
                elapsed = time.time() - start_time
                current_level = int(elapsed / level_duration)
                if current_level >= len(move_height_levels):
                    current_level = len(move_height_levels) - 1

                # 计算移动点位置
                level_start_time = start_time + current_level * level_duration
                level_elapsed = time.time() - level_start_time

                # 水平位置（从左到右循环）
                cycle_duration = 10.0  # 4秒一个周期
                cycle_progress = (level_elapsed % cycle_duration) / cycle_duration
                x = cycle_progress * self.screen_width

                # 垂直位置
                y = move_height_levels[current_level] * self.screen_height

                current_point = (x, y)

                # 更新显示
                self.update_display(f"移动光点 (高度{current_level+1})", current_point)

                time.sleep(1.0 / 60)  # 60 FPS

        except Exception as e:
            logger.error(f"移动光点阶段失败: {e}")

    def _generate_random_point(self) -> Tuple[float, float]:
        """生成随机点位置"""
        # 在屏幕范围内生成随机位置，避免边缘
        margin = 100
        x = random.uniform(margin, self.screen_width - margin)
        y = random.uniform(margin, self.screen_height - margin)
        return (x, y)

    def _check_exit(self) -> bool:
        """检查是否需要退出"""
        if not PSYCHOPY_AVAILABLE:
            return False

        try:
            keys = event.getKeys(['escape'])
            return len(keys) > 0
        except:
            return False

    def cleanup(self):
        """清理资源"""
        try:
            # 停止监测
            self.stop_monitoring()

            # 关闭EyeLink
            if self.eyelink:
                self.eyelink.close()

            # 关闭显示窗口
            if PSYCHOPY_AVAILABLE and self.window:
                self.window.close()
                core.quit()

            logger.info("资源清理完成")

        except Exception as e:
            logger.error(f"资源清理失败: {e}")

def run_real_time_pupil_monitor(calibration_data_path: str,
                               screen_width: int = DEFAULT_SCREEN_WIDTH,
                               screen_height: int = DEFAULT_SCREEN_HEIGHT,
                               dummy_mode: bool = DUMMY_MODE) -> bool:
    """
    运行实时瞳孔监测的便捷函数

    Args:
        calibration_data_path: PFE标定数据文件路径
        screen_width: 屏幕宽度
        screen_height: 屏幕高度
        dummy_mode: 是否使用虚拟模式

    Returns:
        bool: 是否成功完成监测
    """
    monitor = None
    try:
        # 检查标定文件
        if not os.path.exists(calibration_data_path):
            logger.error(f"标定文件不存在: {calibration_data_path}")
            return False

        # 创建监测器
        monitor = RealTimePupilMonitor(
            calibration_data_path=calibration_data_path,
            screen_width=screen_width,
            screen_height=screen_height,
            dummy_mode=dummy_mode
        )

        # 设置显示
        if not monitor.setup_display():
            logger.error("显示设置失败")
            return False

        # 设置EyeLink
        if not monitor.setup_eyelink():
            logger.error("EyeLink设置失败")
            return False

        # 检查EyeLink状态
        if not dummy_mode and monitor.eyelink:
            logger.info("检查EyeLink连接状态...")
            if hasattr(monitor.eyelink, 'tracker') and monitor.eyelink.tracker:
                logger.info("✓ EyeLink tracker已连接")
            else:
                logger.error("✗ EyeLink tracker未连接")
                return False

        # 设置PFE校正器
        if not monitor.setup_pfe_corrector():
            logger.error("PFE校正器设置失败")
            return False

        # 显示开始提示
        if PSYCHOPY_AVAILABLE and monitor.window:
            start_text = visual.TextStim(
                monitor.window,
                text="实时瞳孔监测即将开始\n\n按空格键开始监测\n按ESC键随时退出",
                font='SimHei',
                height=30,
                color='white',
                pos=(0, 0)
            )
            start_text.draw()
            monitor.window.flip()

            # 等待空格键
            event.waitKeys(keyList=['space'])

        # 运行监测序列
        monitor.run_monitoring_sequence()

        # 显示完成提示
        if PSYCHOPY_AVAILABLE and monitor.window:
            end_text = visual.TextStim(
                monitor.window,
                text="监测完成！\n\n按任意键退出",
                font='SimHei',
                height=30,
                color='white',
                pos=(0, 0)
            )
            end_text.draw()
            monitor.window.flip()
            event.waitKeys()

        return True

    except Exception as e:
        logger.error(f"实时瞳孔监测失败: {e}")
        return False

    finally:
        if monitor:
            monitor.cleanup()

def find_latest_calibration_file(calibration_dir: Optional[str] = None) -> Optional[str]:
    """
    查找最新的PFE标定文件

    Args:
        calibration_dir: 标定数据目录，默认为pfe/calibration_data

    Returns:
        str: 最新标定文件路径，如果没有找到则返回None
    """
    try:
        if calibration_dir is None:
            calibration_dir = os.path.join(os.path.dirname(__file__), "calibration_data")

        if not os.path.exists(calibration_dir):
            logger.warning(f"标定数据目录不存在: {calibration_dir}")
            return None

        # 查找所有CSV标定文件
        calibration_files = []
        for filename in os.listdir(calibration_dir):
            # 匹配包含calibration的CSV文件
            if filename.endswith('.csv') and 'calibration' in filename:
                filepath = os.path.join(calibration_dir, filename)
                if os.path.isfile(filepath):
                    calibration_files.append(filepath)

        if not calibration_files:
            logger.warning(f"在目录 {calibration_dir} 中未找到标定文件")
            return None

        # 按修改时间排序，返回最新的
        calibration_files.sort(key=os.path.getmtime, reverse=True)
        latest_file = calibration_files[0]

        logger.info(f"找到最新标定文件: {latest_file}")
        return latest_file

    except Exception as e:
        logger.error(f"查找标定文件失败: {e}")
        return None

if __name__ == "__main__":
    # 测试实时瞳孔监测
    import argparse

    parser = argparse.ArgumentParser(description='实时瞳孔监测程序')
    parser.add_argument('--calibration', '-c', help='PFE标定数据文件路径')
    parser.add_argument('--dummy', action='store_true', help='使用虚拟模式')
    parser.add_argument('--width', type=int, default=DEFAULT_SCREEN_WIDTH, help='屏幕宽度')
    parser.add_argument('--height', type=int, default=DEFAULT_SCREEN_HEIGHT, help='屏幕高度')
    parser.add_argument('--auto', action='store_true', help='自动查找最新标定文件')

    args = parser.parse_args()

    # 确定标定文件路径
    calibration_path = args.calibration
    if args.auto or not calibration_path:
        calibration_path = find_latest_calibration_file()
        if not calibration_path:
            print("错误：未找到标定文件，请使用 --calibration 参数指定文件路径")
            sys.exit(1)

    print(f"开始实时瞳孔监测...")
    print(f"标定文件: {calibration_path}")
    print(f"屏幕尺寸: {args.width}×{args.height}")
    print(f"虚拟模式: {args.dummy}")

    # 运行监测
    success = run_real_time_pupil_monitor(
        calibration_data_path=calibration_path,
        screen_width=args.width,
        screen_height=args.height,
        dummy_mode=args.dummy
    )

    if success:
        print("✓ 实时瞳孔监测完成")
    else:
        print("✗ 实时瞳孔监测失败")
        sys.exit(1)
