#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
测试真实EDF文件的LHIPA计算
使用用户的预处理模块和PyPil LHIPA算法
"""

import sys
import os
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
from typing import Optional, Tuple, List
import warnings

# 添加模块路径
pypil_path = os.path.join(os.getcwd(), 'Pupillometric-measures-of-cognitive-load-main', 'Maintained Code')
sys.path.insert(0, pypil_path)

analysis_path = os.path.join(os.getcwd(), 'analysis')
sys.path.insert(0, analysis_path)

# 导入LHIPA计算器
from cognitive_load.lhipa_calculator import convert_to_pypil_format, IPA, LHIPA, calculate_sliding_lhipa

def test_edf_lhipa(edf_path: str):
    """
    测试真实EDF文件的LHIPA计算
    
    Args:
        edf_path: EDF文件路径
    """
    try:
        # 导入用户的预处理模块
        from preprocess_edf import preprocess_edf_file
        
        print(f"🔄 开始处理EDF文件: {edf_path}")
        print(f"文件是否存在: {os.path.exists(edf_path)}")
        
        if not os.path.exists(edf_path):
            print(f"❌ 文件不存在: {edf_path}")
            return
        
        # 预处理EDF文件
        print(f"\n🔄 步骤1: 预处理EDF文件...")
        processed_data = preprocess_edf_file(
            edf_path,
            smooth_window=5,
            blink_interpolation_window=40,
            interpolation_method='quadratic',
            velocity_threshold=500.0,
            eye_selection='binocular'
        )
        
        if processed_data.empty:
            print(f"❌ 预处理后的数据为空")
            return
        
        print(f"✅ 预处理完成")
        print(f"数据形状: {processed_data.shape}")
        print(f"数据列: {list(processed_data.columns)}")
        print(f"时间范围: {processed_data['time'].min():.1f} - {processed_data['time'].max():.1f} ms")
        
        if 'pupil_avg' in processed_data.columns:
            valid_pupil = processed_data['pupil_avg'].dropna()
            print(f"有效瞳孔数据点: {len(valid_pupil)}/{len(processed_data)}")
            print(f"瞳孔直径范围: {valid_pupil.min():.1f} - {valid_pupil.max():.1f}")
        
        # 转换数据格式
        print(f"\n🔄 步骤2: 转换数据格式...")
        pypil_data = convert_to_pypil_format(processed_data)
        
        if len(pypil_data) < 100:
            print(f"❌ 数据点太少（{len(pypil_data)}），无法进行可靠的LHIPA计算")
            return
        
        # 计算整体IPA和LHIPA
        print(f"\n🔄 步骤3: 计算整体IPA和LHIPA...")
        
        with warnings.catch_warnings():
            warnings.simplefilter("ignore")
            ipa_calculator = IPA(pypil_data)
            lhipa_calculator = LHIPA(pypil_data)
        
        ipa_value = ipa_calculator.ipa
        lhipa_value = lhipa_calculator.lhipa
        
        print(f"✅ 整体计算完成")
        print(f"IPA值: {ipa_value:.6f}")
        print(f"LHIPA值: {lhipa_value:.6f}")
        
        # 计算滑动窗口LHIPA（如果数据足够长）
        if len(processed_data) > 7000:
            print(f"\n🔄 步骤4: 计算滑动窗口LHIPA...")
            
            # 使用较小的窗口进行测试
            window_size = min(7000, len(processed_data) // 5)
            step_size = window_size // 4
            
            time_points, ipa_series, lhipa_series = calculate_sliding_lhipa(
                processed_data, 
                window_size=window_size, 
                step_size=step_size
            )
            
            # 绘制结果
            print(f"\n🔄 步骤5: 绘制结果...")
            plot_lhipa_results(processed_data, time_points, ipa_series, lhipa_series, 
                             ipa_value, lhipa_value, edf_path)
        else:
            print(f"\n⚠️ 数据长度不足（{len(processed_data)}），跳过滑动窗口分析")
            # 只绘制基础结果
            plot_basic_results(processed_data, ipa_value, lhipa_value, edf_path)
        
        print(f"\n🎉 LHIPA计算完成！")
        
    except ImportError as e:
        print(f"❌ 导入预处理模块失败: {e}")
        print("请确保 analysis/preprocess_edf.py 文件存在且可导入")
    except Exception as e:
        print(f"❌ 处理过程中出现错误: {e}")
        import traceback
        traceback.print_exc()

def plot_lhipa_results(processed_data, time_points, ipa_series, lhipa_series, 
                      overall_ipa, overall_lhipa, edf_path):
    """绘制LHIPA分析结果"""
    
    # 设置中文字体
    plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'DejaVu Sans']
    plt.rcParams['axes.unicode_minus'] = False
    
    fig, axes = plt.subplots(3, 1, figsize=(15, 12))
    
    # 绘制原始瞳孔数据
    time_sec = processed_data['time'] / 1000.0
    axes[0].plot(time_sec, processed_data['pupil_avg'], 'b-', alpha=0.7, linewidth=0.8)
    axes[0].set_title(f'Pupil Data - {os.path.basename(edf_path)}', fontsize=14)
    axes[0].set_xlabel('Time (seconds)')
    axes[0].set_ylabel('Pupil Diameter (pixels)')
    axes[0].grid(True, alpha=0.3)
    
    # 绘制IPA时间序列
    valid_ipa = ~np.isnan(ipa_series)
    if np.any(valid_ipa):
        axes[1].plot(np.array(time_points)[valid_ipa], np.array(ipa_series)[valid_ipa], 
                    'g-', linewidth=2, label=f'Sliding IPA')
        axes[1].axhline(y=overall_ipa, color='g', linestyle='--', alpha=0.7, 
                       label=f'Overall IPA: {overall_ipa:.4f}')
    axes[1].set_title('Index of Pupillary Activity (IPA)', fontsize=14)
    axes[1].set_xlabel('Time (seconds)')
    axes[1].set_ylabel('IPA Value')
    axes[1].legend()
    axes[1].grid(True, alpha=0.3)
    
    # 绘制LHIPA时间序列
    valid_lhipa = ~np.isnan(lhipa_series)
    if np.any(valid_lhipa):
        axes[2].plot(np.array(time_points)[valid_lhipa], np.array(lhipa_series)[valid_lhipa], 
                    'r-', linewidth=2, label=f'Sliding LHIPA')
        axes[2].axhline(y=overall_lhipa, color='r', linestyle='--', alpha=0.7, 
                       label=f'Overall LHIPA: {overall_lhipa:.4f}')
    axes[2].set_title('Low/High Index of Pupillary Activity (LHIPA)', fontsize=14)
    axes[2].set_xlabel('Time (seconds)')
    axes[2].set_ylabel('LHIPA Value')
    axes[2].legend()
    axes[2].grid(True, alpha=0.3)
    
    plt.tight_layout()
    
    # 保存图片
    output_name = f"lhipa_analysis_{os.path.splitext(os.path.basename(edf_path))[0]}.png"
    plt.savefig(output_name, dpi=300, bbox_inches='tight')
    print(f"✅ 分析结果已保存: {output_name}")
    
    # 显示统计信息
    print(f"\n📊 统计信息:")
    print(f"整体IPA: {overall_ipa:.6f}")
    print(f"整体LHIPA: {overall_lhipa:.6f}")
    
    if np.any(valid_ipa):
        ipa_mean = np.nanmean(ipa_series)
        ipa_std = np.nanstd(ipa_series)
        print(f"滑动IPA: {ipa_mean:.6f} ± {ipa_std:.6f}")
    
    if np.any(valid_lhipa):
        lhipa_mean = np.nanmean(lhipa_series)
        lhipa_std = np.nanstd(lhipa_series)
        print(f"滑动LHIPA: {lhipa_mean:.6f} ± {lhipa_std:.6f}")

def plot_basic_results(processed_data, ipa_value, lhipa_value, edf_path):
    """绘制基础结果（当数据不足以进行滑动窗口分析时）"""
    
    plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'DejaVu Sans']
    plt.rcParams['axes.unicode_minus'] = False
    
    fig, axes = plt.subplots(2, 1, figsize=(12, 8))
    
    # 绘制瞳孔数据
    time_sec = processed_data['time'] / 1000.0
    axes[0].plot(time_sec, processed_data['pupil_avg'], 'b-', alpha=0.7, linewidth=1)
    axes[0].set_title(f'Pupil Data - {os.path.basename(edf_path)}', fontsize=14)
    axes[0].set_xlabel('Time (seconds)')
    axes[0].set_ylabel('Pupil Diameter (pixels)')
    axes[0].grid(True, alpha=0.3)
    
    # 绘制IPA和LHIPA值
    axes[1].bar(['IPA', 'LHIPA'], [ipa_value, lhipa_value], 
               color=['skyblue', 'lightcoral'], alpha=0.8)
    axes[1].set_title('IPA and LHIPA Values', fontsize=14)
    axes[1].set_ylabel('Index Value')
    axes[1].grid(True, alpha=0.3)
    
    # 添加数值标签
    axes[1].text(0, ipa_value + ipa_value*0.05, f'{ipa_value:.4f}', 
                ha='center', va='bottom', fontweight='bold')
    axes[1].text(1, lhipa_value + lhipa_value*0.05, f'{lhipa_value:.4f}', 
                ha='center', va='bottom', fontweight='bold')
    
    plt.tight_layout()
    
    # 保存图片
    output_name = f"lhipa_basic_{os.path.splitext(os.path.basename(edf_path))[0]}.png"
    plt.savefig(output_name, dpi=300, bbox_inches='tight')
    print(f"✅ 基础分析结果已保存: {output_name}")

if __name__ == "__main__":
    # 测试用的EDF文件路径（请根据实际情况修改）
    test_edf_files = [
        "C:/Users/<USER>/Desktop/curiosity_pupil/data/20250805_131104_test_131104_circular_motion/test_131104.edf",
        "C:/Users/<USER>/Desktop/curiosity_pupil/data/20250801_154631_mzx_continuous_reading/mzx.edf",
        "C:/Users/<USER>/Desktop/curiosity_pupil/data/20250729_161339_test_161339_continuous_reading/test_161339.edf"
    ]
    
    print("🧪 测试真实EDF文件的LHIPA计算")
    print("="*60)
    
    # 找到第一个存在的EDF文件进行测试
    for edf_path in test_edf_files:
        if os.path.exists(edf_path):
            print(f"找到测试文件: {edf_path}")
            test_edf_lhipa(edf_path)
            break
    else:
        print("❌ 未找到可用的测试EDF文件")
        print("请修改 test_edf_files 列表中的路径，或提供有效的EDF文件路径")
        print("\n可以手动运行:")
        print("python test_real_edf_lhipa.py")
        print("然后在代码中修改edf_path变量")
