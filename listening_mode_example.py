#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
连续阅读实验听力模式使用示例
演示如何启用和使用听力模式功能
"""

import os
import sys

# 添加项目根目录到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, current_dir)

def demonstrate_listening_mode():
    """演示听力模式功能"""
    print("连续阅读实验听力模式使用示例")
    print("=" * 50)
    
    print("\n1. 听力模式功能说明:")
    print("   - 文本内容通过TTS语音播放，而非屏幕显示")
    print("   - 播放期间屏幕中央显示白点")
    print("   - 播放完成后自动进入评分环节")
    print("   - 评分环节保持不变")
    
    print("\n2. 配置说明:")
    print("   在 continuous_reading_config.py 中添加了以下配置:")
    print("   - EXPERIMENT_CONFIG['listening_mode']: 是否启用听力模式")
    print("   - LISTENING_CONFIG: 听力模式的详细设置")
    
    print("\n3. 配置参数:")
    from continuous_reading.continuous_reading_config import LISTENING_CONFIG
    for key, value in LISTENING_CONFIG.items():
        print(f"   - {key}: {value}")
    
    print("\n4. 使用方法:")
    print("   方法1: 在配置文件中设置")
    print("   EXPERIMENT_CONFIG['listening_mode'] = True")
    
    print("\n   方法2: 运行时选择")
    print("   运行 continuous_reading_experiment.py 时会询问是否使用听力模式")
    
    print("\n5. 技术实现:")
    print("   - 使用 edge-tts 进行文本转语音")
    print("   - 使用 mpv 播放器进行音频播放")
    print("   - 在每段文本前添加 '11.' 前缀防止声音被吞掉")
    print("   - 异步播放确保不阻塞界面")
    
    print("\n6. 依赖要求:")
    print("   - pip install edge-tts")
    print("   - 系统需要安装 mpv 播放器")
    
    print("\n7. 注意事项:")
    print("   - 确保系统音量适中")
    print("   - 确保网络连接正常（TTS需要联网）")
    print("   - 播放期间请保持安静的环境")

def show_config_example():
    """显示配置示例"""
    print("\n" + "=" * 50)
    print("配置文件示例 (continuous_reading_config.py)")
    print("=" * 50)
    
    config_example = '''
# 在 EXPERIMENT_CONFIG 中添加:
EXPERIMENT_CONFIG = {
    # ... 其他配置 ...
    'listening_mode': False,  # 是否启用听力模式
}

# 新增 LISTENING_CONFIG:
LISTENING_CONFIG = {
    'voice': 'zh-CN-YunxiNeural',        # TTS语音
    'rate': '-10%',                      # 语速调整
    'prefix_text': '11.',                # 朗读前缀
    'dot_size': 20,                      # 白点大小（像素）
    'dot_color': 'white',                # 白点颜色
    'background_color': 'black',         # 背景颜色
    'auto_advance_to_rating': True,      # 朗读完成后自动进入评分
}
'''
    print(config_example)

def show_usage_example():
    """显示使用示例"""
    print("\n" + "=" * 50)
    print("代码使用示例")
    print("=" * 50)
    
    usage_example = '''
# 1. 导入必要模块
from continuous_reading.continuous_reading_experiment import ContinuousReadingExperiment

# 2. 创建实验对象
experiment = ContinuousReadingExperiment(
    participant_id="test_listening",
    use_eyelink=True,
    fullscreen=True
)

# 3. 启用听力模式（如果需要）
from continuous_reading.continuous_reading_config import EXPERIMENT_CONFIG
EXPERIMENT_CONFIG['listening_mode'] = True

# 4. 运行实验
try:
    success = experiment.run_experiment()
    if success:
        print("实验完成！")
    else:
        print("实验被中断")
finally:
    experiment.cleanup()
'''
    print(usage_example)

def main():
    """主函数"""
    try:
        demonstrate_listening_mode()
        show_config_example()
        show_usage_example()
        
        print("\n" + "=" * 50)
        print("示例完成！")
        print("现在可以运行 continuous_reading_experiment.py 并选择听力模式进行测试")
        
    except Exception as e:
        print(f"示例运行出错: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
