#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
PFE (Pupil Foreshortening Error) 标定程序
基于论文: Hayes & Petrov (2016) Behav Res

功能：
1. 显示8×6网格点进行瞳孔前缩误差标定
2. 每个点注视4秒，记录瞳孔数据
3. 分析后3秒的瞳孔平均值
4. 保存标定数据用于后续矫正
"""

import os
import sys
import time
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
from datetime import datetime
from typing import List, Tuple, Dict, Optional
import logging
import random

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

try:
    from psychopy import visual, core, event, monitors
    PSYCHOPY_AVAILABLE = True
except ImportError:
    PSYCHOPY_AVAILABLE = False
    print("警告：PsychoPy未安装，将使用模拟模式")

from eyelink_manager import EyeLinkManager
from pfe.config import *

# 配置日志
logging.basicConfig(
    level=getattr(logging, LOG_LEVEL),
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger('PFE标定')

class PFECalibrator:
    """PFE瞳孔前缩误差标定器"""
    
    def __init__(self, participant_id: str, data_dir: str, 
                 screen_width: int = DEFAULT_SCREEN_WIDTH,
                 screen_height: int = DEFAULT_SCREEN_HEIGHT,
                 dummy_mode: bool = DUMMY_MODE):
        """
        初始化PFE标定器
        
        Args:
            participant_id: 被试ID
            data_dir: 数据保存目录
            screen_width: 屏幕宽度
            screen_height: 屏幕高度
            dummy_mode: 是否使用虚拟模式
        """
        self.participant_id = participant_id
        self.data_dir = data_dir
        self.screen_width = screen_width
        self.screen_height = screen_height
        self.dummy_mode = dummy_mode
        
        # 创建保存目录 - 直接保存到pfe文件夹下的calibration_data子目录
        pfe_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))  # 项目根目录
        self.pfe_data_dir = os.path.join(pfe_dir, "pfe", "calibration_data")
        os.makedirs(self.pfe_data_dir, exist_ok=True)
        
        # 初始化组件
        self.window = None
        self.eyelink = None
        self.grid_positions = []
        self.calibration_data = []
        self.calibration_sequence = []  # 标定序列（包含重复）

        # 生成网格位置
        self.grid_positions = get_grid_positions(screen_width, screen_height)

        # 生成标定序列（包含重复和随机化）
        self._generate_calibration_sequence()
        
        logger.info(f"PFE标定器初始化完成")
        logger.info(f"被试ID: {participant_id}")
        logger.info(f"屏幕尺寸: {screen_width}×{screen_height}")
        logger.info(f"网格点数: {len(self.grid_positions)}")
        logger.info(f"重复次数: {CALIBRATION_REPETITIONS}")
        logger.info(f"总标定次数: {len(self.calibration_sequence)}")
        logger.info(f"预计时间: {len(self.calibration_sequence) * FIXATION_DURATION / 60:.1f}分钟")

    def _generate_calibration_sequence(self):
        """生成标定序列（包含重复和随机化）"""
        # 为每个网格点创建重复序列
        sequence = []
        for repetition in range(CALIBRATION_REPETITIONS):
            for point_idx in range(len(self.grid_positions)):
                sequence.append({
                    'point_index': point_idx,
                    'repetition': repetition,
                    'position': self.grid_positions[point_idx]
                })

        # 随机化顺序
        if RANDOMIZE_ORDER:
            random.shuffle(sequence)

        self.calibration_sequence = sequence
        logger.info(f"生成标定序列: {len(sequence)}个标定点")
    
    def setup_display(self) -> bool:
        """设置显示窗口"""
        if not PSYCHOPY_AVAILABLE:
            logger.warning("PsychoPy不可用，跳过显示设置")
            return True
            
        try:
            # 创建显示窗口
            self.window = visual.Window(
                size=(self.screen_width, self.screen_height),
                fullscr=True,
                color=BACKGROUND_COLOR,
                units='pix',
                allowGUI=False
            )
            
            logger.info("显示窗口创建成功")
            return True
            
        except Exception as e:
            logger.error(f"显示窗口创建失败: {e}")
            return False
    
    def setup_eyelink(self) -> bool:
        """设置EyeLink连接"""
        try:
            # 创建EyeLink管理器
            self.eyelink = EyeLinkManager(
                participant_id=f"{self.participant_id}_pfe",
                data_dir=self.pfe_data_dir,
                screen_size=(self.screen_width, self.screen_height),
                dummy_mode=self.dummy_mode,
                display_window=self.window,
                enable_camera=False  # PFE标定不需要摄像头
            )
            
            # 连接EyeLink
            if not self.eyelink.connect():
                logger.error("EyeLink连接失败")
                return False
            
            # 配置EyeLink
            if not self.eyelink.setup_tracker():
                logger.error("EyeLink配置失败")
                return False
            
            # 校准EyeLink
            if not self.eyelink.calibrate():
                logger.error("EyeLink校准失败")
                return False
            
            logger.info("EyeLink设置完成")
            return True
            
        except Exception as e:
            logger.error(f"EyeLink设置失败: {e}")
            return False
    
    def show_instructions(self):
        """显示指导语"""
        if not PSYCHOPY_AVAILABLE or not self.window:
            print(INSTRUCTION_TEXT)
            input("按Enter键继续...")
            return
            
        try:
            # 创建指导语文本
            instruction_stim = visual.TextStim(
                self.window,
                text=INSTRUCTION_TEXT,
                font='SimHei',
                height=30,
                color='white',
                pos=(0, 0),
                wrapWidth=self.screen_width * 0.8
            )
            
            # 显示指导语
            instruction_stim.draw()
            self.window.flip()
            
            # 等待空格键
            event.waitKeys(keyList=['space'])
            
        except Exception as e:
            logger.error(f"显示指导语失败: {e}")
    
    def run_calibration(self) -> bool:
        """运行PFE标定"""
        logger.info("开始PFE标定...")

        # 记录实验开始
        if self.eyelink:
            self.eyelink.log_experiment_start()
            self.eyelink.send_message("PFE_CALIBRATION_START")

        try:
            for i, trial_info in enumerate(self.calibration_sequence):
                point_idx = trial_info['point_index']
                repetition = trial_info['repetition']
                x, y = trial_info['position']

                logger.info(f"标定 {i+1}/{len(self.calibration_sequence)}: 点{point_idx+1} 重复{repetition+1} ({x:.1f}, {y:.1f})")

                # 检查是否需要休息
                if i > 0 and i % BREAK_INTERVAL == 0:
                    self._show_break_message(i, len(self.calibration_sequence))

                # 显示注视点并记录数据
                point_data = self._calibrate_single_point(i, x, y, point_idx, repetition)
                if point_data:
                    self.calibration_data.append(point_data)
                    logger.info(f"标定 {i+1} 完成: 瞳孔大小 = {point_data.get('pupil_left', 'N/A')}")
                else:
                    logger.warning(f"标定 {i+1} 失败")

            # 记录实验结束
            if self.eyelink:
                self.eyelink.send_message("PFE_CALIBRATION_END")
                self.eyelink.log_experiment_end()

            # 后处理：分析EyeLink数据
            if not self.dummy_mode and self.eyelink:
                self._post_process_eyelink_data()

            logger.info(f"PFE标定完成，成功标定 {len(self.calibration_data)}/{len(self.calibration_sequence)} 次")
            return len(self.calibration_data) >= MIN_VALID_POINTS
            
        except Exception as e:
            logger.error(f"PFE标定过程出错: {e}")
            return False
    
    def _calibrate_single_point(self, trial_index: int, x: float, y: float, point_index: Optional[int] = None, repetition: Optional[int] = None) -> Optional[Dict]:
        """标定单个网格点"""
        try:
            # 使用trial_index作为记录ID，如果point_index为None则使用trial_index
            record_id = point_index if point_index is not None else trial_index

            # 开始记录
            if self.eyelink:
                self.eyelink.start_recording(trial_id=record_id)
                self.eyelink.send_message(f"POINT_START {record_id} {x} {y} rep_{repetition}")

            # 显示注视点
            if PSYCHOPY_AVAILABLE and self.window:
                dot = visual.Circle(
                    self.window,
                    radius=DOT_SIZE/2,
                    pos=(x - self.screen_width/2, self.screen_height/2 - y),  # 转换坐标系
                    fillColor=DOT_COLOR,
                    lineColor=DOT_COLOR
                )

                dot.draw()
                self.window.flip()

            # 记录开始时间
            start_time = time.time()

            # 分析瞳孔数据（在注视期间收集）
            point_data = self._analyze_point_data(record_id, x, y, start_time)

            # 添加额外信息
            if point_data:
                point_data['original_point_index'] = point_index
                point_data['repetition'] = repetition
                point_data['trial_index'] = trial_index

            # 停止记录
            if self.eyelink:
                self.eyelink.send_message(f"POINT_END {record_id}")
                self.eyelink.stop_recording()

            # 清空屏幕
            if PSYCHOPY_AVAILABLE and self.window:
                self.window.flip()

            if not point_data:
                logger.warning(f"试次 {trial_index} 数据分析失败")
                return None

            return point_data

        except Exception as e:
            logger.error(f"标定试次 {trial_index} 失败: {e}")
            return None

    def _analyze_point_data(self, point_index: int, x: float, y: float, start_time: float) -> Optional[Dict]:
        """分析单个标定点的瞳孔数据"""
        try:
            if self.dummy_mode:
                # 虚拟模式：生成模拟数据
                point_data = {
                    'point_index': point_index,
                    'screen_x': x,
                    'screen_y': y,
                    'pupil_left': np.random.normal(4000, 200),
                    'pupil_right': np.random.normal(4000, 200),
                    'gaze_x': x + np.random.normal(0, 10),
                    'gaze_y': y + np.random.normal(0, 10),
                    'timestamp': start_time,
                    'valid': True
                }
                return point_data

            # 真实模式：收集瞳孔数据样本
            # 等待1秒基线期，然后收集后3秒的数据
            time.sleep(BASELINE_DURATION)

            # 收集分析窗口内的瞳孔数据
            analysis_duration_ms = int(ANALYSIS_WINDOW * 1000)

            if not self.eyelink or not hasattr(self.eyelink, 'collect_pupil_samples'):
                logger.warning(f"点 {point_index} EyeLink不可用或方法不存在")
                return None

            samples = self.eyelink.collect_pupil_samples(
                duration_ms=analysis_duration_ms,
                sample_rate=20  # 每秒20个样本
            )

            if not samples:
                logger.warning(f"点 {point_index} 未收集到瞳孔数据")
                return None

            # 分析收集到的样本
            pupil_left_values = []
            pupil_right_values = []
            gaze_x_values = []
            gaze_y_values = []

            for sample in samples:
                if 'pupil_left' in sample and sample['pupil_left'] is not None:
                    if MIN_PUPIL_SIZE <= sample['pupil_left'] <= MAX_PUPIL_SIZE:
                        pupil_left_values.append(sample['pupil_left'])
                        if 'gaze_x_left' in sample:
                            gaze_x_values.append(sample['gaze_x_left'])
                        if 'gaze_y_left' in sample:
                            gaze_y_values.append(sample['gaze_y_left'])

                if 'pupil_right' in sample and sample['pupil_right'] is not None:
                    if MIN_PUPIL_SIZE <= sample['pupil_right'] <= MAX_PUPIL_SIZE:
                        pupil_right_values.append(sample['pupil_right'])
                        if 'gaze_x_right' in sample:
                            gaze_x_values.append(sample['gaze_x_right'])
                        if 'gaze_y_right' in sample:
                            gaze_y_values.append(sample['gaze_y_right'])

            # 计算平均值
            point_data = {
                'point_index': point_index,
                'screen_x': x,
                'screen_y': y,
                'pupil_left': np.mean(pupil_left_values) if pupil_left_values else None,
                'pupil_right': np.mean(pupil_right_values) if pupil_right_values else None,
                'gaze_x': np.mean(gaze_x_values) if gaze_x_values else None,
                'gaze_y': np.mean(gaze_y_values) if gaze_y_values else None,
                'timestamp': start_time,
                'valid': bool(pupil_left_values or pupil_right_values),
                'sample_count': len(samples),
                'valid_left_samples': len(pupil_left_values),
                'valid_right_samples': len(pupil_right_values)
            }

            return point_data

        except Exception as e:
            logger.error(f"分析点 {point_index} 数据失败: {e}")
            return None

    def _post_process_eyelink_data(self):
        """后处理EyeLink数据，提取每个标定点的瞳孔信息"""
        try:
            logger.info("瞳孔数据已在实时收集过程中获取")

            # 统计有效数据点
            valid_points = sum(1 for point in self.calibration_data if point.get('valid', False))
            total_points = len(self.calibration_data)

            logger.info(f"有效数据点: {valid_points}/{total_points}")

            # 检查数据质量
            for i, point_data in enumerate(self.calibration_data):
                if not point_data.get('valid', False):
                    logger.warning(f"点 {i+1} 数据无效")
                elif point_data.get('pupil_left') is None and point_data.get('pupil_right') is None:
                    logger.warning(f"点 {i+1} 无瞳孔数据")

        except Exception as e:
            logger.error(f"后处理EyeLink数据失败: {e}")
    
    def _show_break_message(self, completed: int, total: int):
        """显示休息提示"""
        break_text = BREAK_TEXT.format(completed=completed, total=total)
        
        if not PSYCHOPY_AVAILABLE or not self.window:
            print(break_text)
            input("按Enter键继续...")
            return
            
        try:
            break_stim = visual.TextStim(
                self.window,
                text=break_text,
                font='SimHei',
                height=30,
                color='white',
                pos=(0, 0),
                wrapWidth=self.screen_width * 0.8
            )
            
            break_stim.draw()
            self.window.flip()
            
            # 等待空格键
            event.waitKeys(keyList=['space'])
            
        except Exception as e:
            logger.error(f"显示休息提示失败: {e}")
    
    def save_calibration_data(self) -> str:
        """保存标定数据"""
        try:
            # 创建DataFrame
            df = pd.DataFrame(self.calibration_data)

            # 生成文件名
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"{self.participant_id}_pfe_calibration_{timestamp}.csv"
            filepath = os.path.join(self.pfe_data_dir, filename)

            # 保存CSV文件
            df.to_csv(filepath, index=False, encoding='utf-8-sig')

            logger.info(f"标定数据已保存: {filepath}")

            # 显示校准结果可视化
            if SHOW_CALIBRATION_RESULT:
                self._show_calibration_result(df, filepath)

            return filepath

        except Exception as e:
            logger.error(f"保存标定数据失败: {e}")
            return ""

    def _show_calibration_result(self, df: pd.DataFrame, filepath: str):
        """显示校准结果可视化"""
        try:
            # 设置中文字体
            plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'DejaVu Sans']
            plt.rcParams['axes.unicode_minus'] = False

            # 计算每个网格点的平均瞳孔大小
            grid_summary = self._calculate_grid_summary(df)

            if grid_summary.empty:
                logger.warning("没有有效的瞳孔数据用于可视化")
                return

            # 创建图形
            fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(16, 8))

            # 左图：显示网格点和瞳孔大小（圆点半径表示瞳孔大小）
            self._plot_pupil_size_grid(ax1, grid_summary)

            # 右图：显示瞳孔大小分布
            self._plot_pupil_distribution(ax2, grid_summary)

            # 设置总标题
            fig.suptitle(f'PFE校准结果 - {self.participant_id}', fontsize=16, fontweight='bold')

            # 调整布局
            plt.tight_layout()

            # 保存图片
            if SAVE_CALIBRATION_PLOT:
                plot_filename = filepath.replace('.csv', '_result.png')
                plt.savefig(plot_filename, dpi=PLOT_DPI, bbox_inches='tight')
                logger.info(f"校准结果图已保存: {plot_filename}")

            # 显示图片
            plt.show()

        except Exception as e:
            logger.error(f"显示校准结果失败: {e}")

    def _calculate_grid_summary(self, df: pd.DataFrame) -> pd.DataFrame:
        """计算每个网格点的汇总统计"""
        try:
            # 使用双眼平均瞳孔大小
            df['pupil_avg'] = df[['pupil_left', 'pupil_right']].mean(axis=1, skipna=True)

            # 按原始网格点分组计算统计
            summary = df.groupby(['original_point_index', 'screen_x', 'screen_y']).agg({
                'pupil_avg': ['mean', 'std', 'count'],
                'pupil_left': ['mean', 'std'],
                'pupil_right': ['mean', 'std']
            }).reset_index()

            # 简化列名
            summary.columns = ['point_index', 'screen_x', 'screen_y',
                             'pupil_mean', 'pupil_std', 'sample_count',
                             'left_mean', 'left_std', 'right_mean', 'right_std']

            # 过滤有效数据
            summary = summary[summary['sample_count'] > 0]
            summary = summary.dropna(subset=['pupil_mean'])

            logger.info(f"计算了 {len(summary)} 个网格点的汇总统计")
            return summary

        except Exception as e:
            logger.error(f"计算网格汇总失败: {e}")
            return pd.DataFrame()

    def _plot_pupil_size_grid(self, ax, grid_summary: pd.DataFrame):
        """绘制网格点瞳孔大小图（圆点半径表示瞳孔大小）"""
        try:
            # 计算圆点半径
            pupil_sizes = np.array(grid_summary['pupil_mean'].tolist())
            min_pupil = np.min(pupil_sizes)
            max_pupil = np.max(pupil_sizes)

            # 归一化瞳孔大小到半径范围
            if max_pupil > min_pupil:
                normalized_sizes = (pupil_sizes - min_pupil) / (max_pupil - min_pupil)
                radii = MIN_DOT_RADIUS + normalized_sizes * (MAX_DOT_RADIUS - MIN_DOT_RADIUS)
            else:
                radii = np.full(len(pupil_sizes), (MIN_DOT_RADIUS + MAX_DOT_RADIUS) / 2)

            # 绘制圆点
            for i, (_, row) in enumerate(grid_summary.iterrows()):
                x, y = row['screen_x'], row['screen_y']
                radius = radii[i]
                pupil_size = row['pupil_mean']
                sample_count = int(row['sample_count'])

                # 绘制圆点
                circle = plt.Circle((x, y), radius,
                                  color='blue', alpha=0.6,
                                  label=f'瞳孔大小: {pupil_size:.1f}' if i == 0 else "")
                ax.add_patch(circle)

                # 添加文本标注
                ax.text(x, y, f'{pupil_size:.0f}\n(n={sample_count})',
                       ha='center', va='center', fontsize=8,
                       color='black', fontweight='bold')

            # 设置坐标轴
            ax.set_xlim(0, self.screen_width)
            ax.set_ylim(0, self.screen_height)
            ax.set_aspect('equal')
            ax.invert_yaxis()  # 翻转Y轴使其与屏幕坐标一致
            ax.set_xlabel('屏幕X坐标 (像素)')
            ax.set_ylabel('屏幕Y坐标 (像素)')
            ax.set_title('网格点瞳孔大小分布\n(圆点大小 ∝ 瞳孔大小)')
            ax.grid(True, alpha=0.3)

            # 添加颜色条说明
            ax.text(0.02, 0.98, f'瞳孔大小范围: {min_pupil:.1f} - {max_pupil:.1f}',
                   transform=ax.transAxes, va='top', ha='left',
                   bbox=dict(boxstyle='round', facecolor='white', alpha=0.8))

        except Exception as e:
            logger.error(f"绘制瞳孔大小网格失败: {e}")

    def _plot_pupil_distribution(self, ax, grid_summary: pd.DataFrame):
        """绘制瞳孔大小分布图"""
        try:
            pupil_sizes = np.array(grid_summary['pupil_mean'].tolist())
            sample_counts = np.array(grid_summary['sample_count'].tolist())

            # 绘制直方图
            ax.hist(pupil_sizes, bins=20, alpha=0.7, color='skyblue', edgecolor='black')

            # 添加统计信息
            mean_pupil = np.mean(pupil_sizes)
            std_pupil = np.std(pupil_sizes)

            ax.axvline(mean_pupil, color='red', linestyle='--', linewidth=2,
                      label=f'均值: {mean_pupil:.1f}')
            ax.axvline(mean_pupil - std_pupil, color='orange', linestyle=':',
                      label=f'±1σ: {std_pupil:.1f}')
            ax.axvline(mean_pupil + std_pupil, color='orange', linestyle=':')

            ax.set_xlabel('平均瞳孔大小')
            ax.set_ylabel('网格点数量')
            ax.set_title('瞳孔大小分布统计')
            ax.legend()
            ax.grid(True, alpha=0.3)

            # 添加统计文本
            stats_text = f'总网格点: {len(pupil_sizes)}\n'
            stats_text += f'总测量次数: {int(np.sum(sample_counts))}\n'
            stats_text += f'平均重复次数: {np.mean(sample_counts):.1f}\n'
            stats_text += f'瞳孔大小 CV: {(std_pupil/mean_pupil)*100:.1f}%'

            ax.text(0.98, 0.98, stats_text, transform=ax.transAxes,
                   va='top', ha='right',
                   bbox=dict(boxstyle='round', facecolor='white', alpha=0.8))

        except Exception as e:
            logger.error(f"绘制瞳孔分布失败: {e}")

    def cleanup(self):
        """清理资源"""
        try:
            if self.eyelink:
                self.eyelink.close()
            
            if PSYCHOPY_AVAILABLE and self.window:
                self.window.close()
                core.quit()
            
            logger.info("资源清理完成")
            
        except Exception as e:
            logger.error(f"资源清理失败: {e}")

def run_pfe_calibration(participant_id: str, data_dir: str, 
                       screen_width: int = DEFAULT_SCREEN_WIDTH,
                       screen_height: int = DEFAULT_SCREEN_HEIGHT,
                       dummy_mode: bool = DUMMY_MODE) -> str:
    """
    运行PFE标定的便捷函数
    
    Args:
        participant_id: 被试ID
        data_dir: 数据保存目录
        screen_width: 屏幕宽度
        screen_height: 屏幕高度
        dummy_mode: 是否使用虚拟模式
        
    Returns:
        str: 标定数据文件路径
    """
    calibrator = None
    try:
        # 创建标定器
        calibrator = PFECalibrator(participant_id, data_dir, screen_width, screen_height, dummy_mode)
        
        # 设置显示
        if not calibrator.setup_display():
            logger.error("显示设置失败")
            return ""
        
        # 设置EyeLink
        if not calibrator.setup_eyelink():
            logger.error("EyeLink设置失败")
            return ""
        
        # 显示指导语
        calibrator.show_instructions()
        
        # 运行标定
        if not calibrator.run_calibration():
            logger.error("PFE标定失败")
            return ""
        
        # 保存数据
        filepath = calibrator.save_calibration_data()

        # 显示完成信息
        if PSYCHOPY_AVAILABLE and calibrator.window:
            completion_text = COMPLETION_TEXT + "\n\n校准结果可视化已生成！"
            completion_stim = visual.TextStim(
                calibrator.window,
                text=completion_text,
                font='SimHei',
                height=30,
                color='#202020',
                pos=(0, 0)
            )
            completion_stim.draw()
            calibrator.window.flip()
            time.sleep(3)
        
        return filepath
        
    except Exception as e:
        logger.error(f"PFE标定运行失败: {e}")
        return ""
        
    finally:
        if calibrator:
            calibrator.cleanup()

if __name__ == "__main__":
    # 测试PFE标定
    import argparse
    
    parser = argparse.ArgumentParser(description='PFE瞳孔前缩误差标定')
    parser.add_argument('--participant', '-p', default='test_pfe', help='被试ID')
    parser.add_argument('--data_dir', '-d', default='./data', help='数据保存目录')
    parser.add_argument('--dummy', action='store_true', help='使用虚拟模式')
    parser.add_argument('--width', type=int, default=DEFAULT_SCREEN_WIDTH, help='屏幕宽度')
    parser.add_argument('--height', type=int, default=DEFAULT_SCREEN_HEIGHT, help='屏幕高度')
    
    args = parser.parse_args()
    
    print(f"开始PFE标定...")
    print(f"被试ID: {args.participant}")
    print(f"屏幕尺寸: {args.width}×{args.height}")
    print(f"虚拟模式: {args.dummy}")
    
    # 运行标定
    result_file = run_pfe_calibration(
        participant_id=args.participant,
        data_dir=args.data_dir,
        screen_width=args.width,
        screen_height=args.height,
        dummy_mode=args.dummy
    )
    
    if result_file:
        print(f"✓ PFE标定完成，数据保存至: {result_file}")
    else:
        print("✗ PFE标定失败")
