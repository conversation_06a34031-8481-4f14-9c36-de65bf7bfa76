#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
PFE (Pupil Foreshortening Error) 标定程序
基于论文: Hayes & Petrov (2016) Behav Res

功能：
1. 显示8×6网格点进行瞳孔前缩误差标定
2. 每个点注视4秒，记录瞳孔数据
3. 分析后3秒的瞳孔平均值
4. 保存标定数据用于后续矫正
"""

import os
import sys
import time
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
from datetime import datetime
from typing import List, Tuple, Dict, Optional
import logging

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

try:
    from psychopy import visual, core, event, monitors
    PSYCHOPY_AVAILABLE = True
except ImportError:
    PSYCHOPY_AVAILABLE = False
    print("警告：PsychoPy未安装，将使用模拟模式")

from eyelink_manager import EyeLinkManager
from pfe.config import *

# 配置日志
logging.basicConfig(
    level=getattr(logging, LOG_LEVEL),
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger('PFE标定')

class PFECalibrator:
    """PFE瞳孔前缩误差标定器"""
    
    def __init__(self, participant_id: str, data_dir: str, 
                 screen_width: int = DEFAULT_SCREEN_WIDTH,
                 screen_height: int = DEFAULT_SCREEN_HEIGHT,
                 dummy_mode: bool = DUMMY_MODE):
        """
        初始化PFE标定器
        
        Args:
            participant_id: 被试ID
            data_dir: 数据保存目录
            screen_width: 屏幕宽度
            screen_height: 屏幕高度
            dummy_mode: 是否使用虚拟模式
        """
        self.participant_id = participant_id
        self.data_dir = data_dir
        self.screen_width = screen_width
        self.screen_height = screen_height
        self.dummy_mode = dummy_mode
        
        # 创建保存目录 - 直接保存到pfe文件夹下的calibration_data子目录
        pfe_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))  # 项目根目录
        self.pfe_data_dir = os.path.join(pfe_dir, "pfe", "calibration_data")
        os.makedirs(self.pfe_data_dir, exist_ok=True)
        
        # 初始化组件
        self.window = None
        self.eyelink = None
        self.grid_positions = []
        self.calibration_data = []
        
        # 生成网格位置
        self.grid_positions = get_grid_positions(screen_width, screen_height)
        
        logger.info(f"PFE标定器初始化完成")
        logger.info(f"被试ID: {participant_id}")
        logger.info(f"屏幕尺寸: {screen_width}×{screen_height}")
        logger.info(f"网格点数: {len(self.grid_positions)}")
        logger.info(f"预计时间: {len(self.grid_positions) * FIXATION_DURATION / 60:.1f}分钟")
    
    def setup_display(self) -> bool:
        """设置显示窗口"""
        if not PSYCHOPY_AVAILABLE:
            logger.warning("PsychoPy不可用，跳过显示设置")
            return True
            
        try:
            # 创建显示窗口
            self.window = visual.Window(
                size=(self.screen_width, self.screen_height),
                fullscr=True,
                color=BACKGROUND_COLOR,
                units='pix',
                allowGUI=False
            )
            
            logger.info("显示窗口创建成功")
            return True
            
        except Exception as e:
            logger.error(f"显示窗口创建失败: {e}")
            return False
    
    def setup_eyelink(self) -> bool:
        """设置EyeLink连接"""
        try:
            # 创建EyeLink管理器
            self.eyelink = EyeLinkManager(
                participant_id=f"{self.participant_id}_pfe",
                data_dir=self.pfe_data_dir,
                screen_size=(self.screen_width, self.screen_height),
                dummy_mode=self.dummy_mode,
                display_window=self.window,
                enable_camera=False  # PFE标定不需要摄像头
            )
            
            # 连接EyeLink
            if not self.eyelink.connect():
                logger.error("EyeLink连接失败")
                return False
            
            # 配置EyeLink
            if not self.eyelink.setup_tracker():
                logger.error("EyeLink配置失败")
                return False
            
            # 校准EyeLink
            if not self.eyelink.calibrate():
                logger.error("EyeLink校准失败")
                return False
            
            logger.info("EyeLink设置完成")
            return True
            
        except Exception as e:
            logger.error(f"EyeLink设置失败: {e}")
            return False
    
    def show_instructions(self):
        """显示指导语"""
        if not PSYCHOPY_AVAILABLE or not self.window:
            print(INSTRUCTION_TEXT)
            input("按Enter键继续...")
            return
            
        try:
            # 创建指导语文本
            instruction_stim = visual.TextStim(
                self.window,
                text=INSTRUCTION_TEXT,
                font='SimHei',
                height=30,
                color='white',
                pos=(0, 0),
                wrapWidth=self.screen_width * 0.8
            )
            
            # 显示指导语
            instruction_stim.draw()
            self.window.flip()
            
            # 等待空格键
            event.waitKeys(keyList=['space'])
            
        except Exception as e:
            logger.error(f"显示指导语失败: {e}")
    
    def run_calibration(self) -> bool:
        """运行PFE标定"""
        logger.info("开始PFE标定...")
        
        # 记录实验开始
        if self.eyelink:
            self.eyelink.log_experiment_start()
            self.eyelink.send_message("PFE_CALIBRATION_START")
        
        try:
            for i, (x, y) in enumerate(self.grid_positions):
                logger.info(f"标定点 {i+1}/{len(self.grid_positions)}: ({x:.1f}, {y:.1f})")
                
                # 检查是否需要休息
                if i > 0 and i % BREAK_INTERVAL == 0:
                    self._show_break_message(i, len(self.grid_positions))
                
                # 显示注视点并记录数据
                point_data = self._calibrate_single_point(i, x, y)
                if point_data:
                    self.calibration_data.append(point_data)
                    logger.info(f"点 {i+1} 标定完成: 瞳孔大小 = {point_data.get('pupil_left', 'N/A')}")
                else:
                    logger.warning(f"点 {i+1} 标定失败")
            
            # 记录实验结束
            if self.eyelink:
                self.eyelink.send_message("PFE_CALIBRATION_END")
                self.eyelink.log_experiment_end()
            
            # 后处理：分析EyeLink数据
            if not self.dummy_mode and self.eyelink:
                self._post_process_eyelink_data()

            logger.info(f"PFE标定完成，成功标定 {len(self.calibration_data)}/{len(self.grid_positions)} 个点")
            return len(self.calibration_data) >= MIN_VALID_POINTS
            
        except Exception as e:
            logger.error(f"PFE标定过程出错: {e}")
            return False
    
    def _calibrate_single_point(self, point_index: int, x: float, y: float) -> Optional[Dict]:
        """标定单个网格点"""
        try:
            # 开始记录
            if self.eyelink:
                self.eyelink.start_recording(trial_id=point_index)
                self.eyelink.send_message(f"POINT_START {point_index} {x} {y}")
            
            # 显示注视点
            if PSYCHOPY_AVAILABLE and self.window:
                dot = visual.Circle(
                    self.window,
                    radius=DOT_SIZE/2,
                    pos=(x - self.screen_width/2, self.screen_height/2 - y),  # 转换坐标系
                    fillColor=DOT_COLOR,
                    lineColor=DOT_COLOR
                )
                
                dot.draw()
                self.window.flip()
            
            # 记录开始时间
            start_time = time.time()

            # 分析瞳孔数据（在注视期间收集）
            point_data = self._analyze_point_data(point_index, x, y, start_time)

            # 停止记录
            if self.eyelink:
                self.eyelink.send_message(f"POINT_END {point_index}")
                self.eyelink.stop_recording()

            # 清空屏幕
            if PSYCHOPY_AVAILABLE and self.window:
                self.window.flip()

            if not point_data:
                logger.warning(f"点 {point_index} 数据分析失败")
                return None
            
            return point_data
            
        except Exception as e:
            logger.error(f"标定点 {point_index} 失败: {e}")
            return None

    def _analyze_point_data(self, point_index: int, x: float, y: float, start_time: float) -> Optional[Dict]:
        """分析单个标定点的瞳孔数据"""
        try:
            if self.dummy_mode:
                # 虚拟模式：生成模拟数据
                point_data = {
                    'point_index': point_index,
                    'screen_x': x,
                    'screen_y': y,
                    'pupil_left': np.random.normal(4000, 200),
                    'pupil_right': np.random.normal(4000, 200),
                    'gaze_x': x + np.random.normal(0, 10),
                    'gaze_y': y + np.random.normal(0, 10),
                    'timestamp': start_time,
                    'valid': True
                }
                return point_data

            # 真实模式：收集瞳孔数据样本
            # 等待1秒基线期，然后收集后3秒的数据
            time.sleep(BASELINE_DURATION)

            # 收集分析窗口内的瞳孔数据
            analysis_duration_ms = int(ANALYSIS_WINDOW * 1000)

            if not self.eyelink or not hasattr(self.eyelink, 'collect_pupil_samples'):
                logger.warning(f"点 {point_index} EyeLink不可用或方法不存在")
                return None

            samples = self.eyelink.collect_pupil_samples(
                duration_ms=analysis_duration_ms,
                sample_rate=20  # 每秒20个样本
            )

            if not samples:
                logger.warning(f"点 {point_index} 未收集到瞳孔数据")
                return None

            # 分析收集到的样本
            pupil_left_values = []
            pupil_right_values = []
            gaze_x_values = []
            gaze_y_values = []

            for sample in samples:
                if 'pupil_left' in sample and sample['pupil_left'] is not None:
                    if MIN_PUPIL_SIZE <= sample['pupil_left'] <= MAX_PUPIL_SIZE:
                        pupil_left_values.append(sample['pupil_left'])
                        if 'gaze_x_left' in sample:
                            gaze_x_values.append(sample['gaze_x_left'])
                        if 'gaze_y_left' in sample:
                            gaze_y_values.append(sample['gaze_y_left'])

                if 'pupil_right' in sample and sample['pupil_right'] is not None:
                    if MIN_PUPIL_SIZE <= sample['pupil_right'] <= MAX_PUPIL_SIZE:
                        pupil_right_values.append(sample['pupil_right'])
                        if 'gaze_x_right' in sample:
                            gaze_x_values.append(sample['gaze_x_right'])
                        if 'gaze_y_right' in sample:
                            gaze_y_values.append(sample['gaze_y_right'])

            # 计算平均值
            point_data = {
                'point_index': point_index,
                'screen_x': x,
                'screen_y': y,
                'pupil_left': np.mean(pupil_left_values) if pupil_left_values else None,
                'pupil_right': np.mean(pupil_right_values) if pupil_right_values else None,
                'gaze_x': np.mean(gaze_x_values) if gaze_x_values else None,
                'gaze_y': np.mean(gaze_y_values) if gaze_y_values else None,
                'timestamp': start_time,
                'valid': bool(pupil_left_values or pupil_right_values),
                'sample_count': len(samples),
                'valid_left_samples': len(pupil_left_values),
                'valid_right_samples': len(pupil_right_values)
            }

            return point_data

        except Exception as e:
            logger.error(f"分析点 {point_index} 数据失败: {e}")
            return None

    def _post_process_eyelink_data(self):
        """后处理EyeLink数据，提取每个标定点的瞳孔信息"""
        try:
            logger.info("瞳孔数据已在实时收集过程中获取")

            # 统计有效数据点
            valid_points = sum(1 for point in self.calibration_data if point.get('valid', False))
            total_points = len(self.calibration_data)

            logger.info(f"有效数据点: {valid_points}/{total_points}")

            # 检查数据质量
            for i, point_data in enumerate(self.calibration_data):
                if not point_data.get('valid', False):
                    logger.warning(f"点 {i+1} 数据无效")
                elif point_data.get('pupil_left') is None and point_data.get('pupil_right') is None:
                    logger.warning(f"点 {i+1} 无瞳孔数据")

        except Exception as e:
            logger.error(f"后处理EyeLink数据失败: {e}")
    
    def _show_break_message(self, completed: int, total: int):
        """显示休息提示"""
        break_text = BREAK_TEXT.format(completed=completed, total=total)
        
        if not PSYCHOPY_AVAILABLE or not self.window:
            print(break_text)
            input("按Enter键继续...")
            return
            
        try:
            break_stim = visual.TextStim(
                self.window,
                text=break_text,
                font='SimHei',
                height=30,
                color='white',
                pos=(0, 0),
                wrapWidth=self.screen_width * 0.8
            )
            
            break_stim.draw()
            self.window.flip()
            
            # 等待空格键
            event.waitKeys(keyList=['space'])
            
        except Exception as e:
            logger.error(f"显示休息提示失败: {e}")
    
    def save_calibration_data(self) -> str:
        """保存标定数据"""
        try:
            # 创建DataFrame
            df = pd.DataFrame(self.calibration_data)
            
            # 生成文件名
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"{self.participant_id}_pfe_calibration_{timestamp}.csv"
            filepath = os.path.join(self.pfe_data_dir, filename)
            
            # 保存CSV文件
            df.to_csv(filepath, index=False, encoding='utf-8-sig')
            
            logger.info(f"标定数据已保存: {filepath}")
            return filepath
            
        except Exception as e:
            logger.error(f"保存标定数据失败: {e}")
            return ""
    
    def cleanup(self):
        """清理资源"""
        try:
            if self.eyelink:
                self.eyelink.close()
            
            if PSYCHOPY_AVAILABLE and self.window:
                self.window.close()
                core.quit()
            
            logger.info("资源清理完成")
            
        except Exception as e:
            logger.error(f"资源清理失败: {e}")

def run_pfe_calibration(participant_id: str, data_dir: str, 
                       screen_width: int = DEFAULT_SCREEN_WIDTH,
                       screen_height: int = DEFAULT_SCREEN_HEIGHT,
                       dummy_mode: bool = DUMMY_MODE) -> str:
    """
    运行PFE标定的便捷函数
    
    Args:
        participant_id: 被试ID
        data_dir: 数据保存目录
        screen_width: 屏幕宽度
        screen_height: 屏幕高度
        dummy_mode: 是否使用虚拟模式
        
    Returns:
        str: 标定数据文件路径
    """
    calibrator = None
    try:
        # 创建标定器
        calibrator = PFECalibrator(participant_id, data_dir, screen_width, screen_height, dummy_mode)
        
        # 设置显示
        if not calibrator.setup_display():
            logger.error("显示设置失败")
            return ""
        
        # 设置EyeLink
        if not calibrator.setup_eyelink():
            logger.error("EyeLink设置失败")
            return ""
        
        # 显示指导语
        calibrator.show_instructions()
        
        # 运行标定
        if not calibrator.run_calibration():
            logger.error("PFE标定失败")
            return ""
        
        # 保存数据
        filepath = calibrator.save_calibration_data()
        
        # 显示完成信息
        if PSYCHOPY_AVAILABLE and calibrator.window:
            completion_stim = visual.TextStim(
                calibrator.window,
                text=COMPLETION_TEXT,
                font='SimHei',
                height=30,
                color='white',
                pos=(0, 0)
            )
            completion_stim.draw()
            calibrator.window.flip()
            time.sleep(3)
        
        return filepath
        
    except Exception as e:
        logger.error(f"PFE标定运行失败: {e}")
        return ""
        
    finally:
        if calibrator:
            calibrator.cleanup()

if __name__ == "__main__":
    # 测试PFE标定
    import argparse
    
    parser = argparse.ArgumentParser(description='PFE瞳孔前缩误差标定')
    parser.add_argument('--participant', '-p', default='test_pfe', help='被试ID')
    parser.add_argument('--data_dir', '-d', default='./data', help='数据保存目录')
    parser.add_argument('--dummy', action='store_true', help='使用虚拟模式')
    parser.add_argument('--width', type=int, default=DEFAULT_SCREEN_WIDTH, help='屏幕宽度')
    parser.add_argument('--height', type=int, default=DEFAULT_SCREEN_HEIGHT, help='屏幕高度')
    
    args = parser.parse_args()
    
    print(f"开始PFE标定...")
    print(f"被试ID: {args.participant}")
    print(f"屏幕尺寸: {args.width}×{args.height}")
    print(f"虚拟模式: {args.dummy}")
    
    # 运行标定
    result_file = run_pfe_calibration(
        participant_id=args.participant,
        data_dir=args.data_dir,
        screen_width=args.width,
        screen_height=args.height,
        dummy_mode=args.dummy
    )
    
    if result_file:
        print(f"✓ PFE标定完成，数据保存至: {result_file}")
    else:
        print("✗ PFE标定失败")
