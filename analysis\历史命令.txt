# EyeLink数据分析历史命令记录

## 2025-07-11 执行的命令
python test_eyelink_messages.py  # 测试EyeLink消息发送，确保没有中文字符
python test_key_buffer_clear.py  # 测试按键缓存清空功能
找到我代码里头等待校正获取屏幕刷新率的等待时间的那行代码在哪里？查找我的用户使用手册和我的代码。
conda activate et
python .\analysis\main.py
conda activate et
python main.py
我如何在data viewer的回放中可以看到我当时在屏幕上显示的文字。而不用整张背景截图。找一些example中的示例代码：能否实时在主试机上看到我在被试机上展示的文字。阅读example中的txt 介绍

## 2025-07-12 执行的命令
conda activate et
python check_blinks.py  # 检查EDF文件中的眨眼标记
python check_messages.py  # 检查EDF文件中的消息格式
python test_preprocessing.py  # 测试数据预处理模块并生成第10个试次的瞳孔数据图
python test_preprocessing.py  # 测试添加速度阈值过滤功能后的预处理模块
python test_velocity_filter.py  # 测试速度阈值过滤功能的效果
在analysis中，参考draw_pupil_line程序如何用pyedfread库读取瞳孔数据，先上网查如何使用pyedfread把edf文件转换成data frame，然后之后的main.py中的步骤三和其他步骤都用这个库来获取数据，分析数据。
创建了pyedfread_analyzer.py - 基于pyedfread的瞳孔数据分析器
修改了main.py - 在步骤3中集成pyedfread分析器，优先使用EDF文件，回退到ASC文件
更新了config.py - 添加pyedfread相关配置参数
创建了test_pyedfread.py - 测试pyedfread功能的脚本
创建了pyedfread_example.py - pyedfread使用示例和教程
创建了pyedfread使用指南.md - 详细的使用文档和说明
修复了pyedfread_analyzer.py中的编码问题（移除了✓符号）
成功测试了main.py --skip-conversion，pyedfread分析器正常工作
创建了pyedfread集成总结.md - 完整的集成工作总结
修改了config.py - 添加viridis配色和双图模式配置
修改了visualizer.py - 实现viridis配色和双图生成功能
创建了test_viridis_plots.py - 测试viridis配色和双图功能
修改了main.py - 改进智能分析器选择逻辑，确保回退机制正常工作
成功测试了新的可视化功能，生成了viridis配色的双图
对于analysis中的画好奇心和有趣程度的曲线，让所有的起始点都从0开始
修改了visualizer.py - 添加基线校正功能，让所有曲线从0开始
修改了config.py - 添加baseline_correct配置选项
创建了test_baseline_correction.py - 测试基线校正功能
成功测试了基线校正功能，所有曲线现在都从0开始
在主程序中添加题目屏蔽功能，可以配置屏蔽特定题目ID
python test_key_buffer_detailed.py  # 详细测试按键缓存清空功能
python analysis\draw_pupil_line.py  # 使用pyedfread绘制瞳孔直径曲线
python temp_read_edf.py  # 读取EDF文件并显示所有消息类型
更新了draw_pupil_line.py中的parse_messages函数 - 添加了更多消息类型的解析功能，包括EVENT_XXX消息、评分事件、基线事件等
修复了find_current_trial函数的逻辑错误，现在可以正确解析所有试次的事件
更新了DATA_SEGMENTS配置，使用正确的事件名称：BASELINE_START_FIXATION、BASELINE_START_PUPIL等
成功生成了所有20个试次的瞳孔直径曲线图，包括4种不同的时间段分析
在eyelink_manager.py中添加外置摄像头录像功能 - 支持UVC标准摄像头，1080p 30fps录制，与EyeLink同步开始/停止
pip install opencv-python  # 安装OpenCV用于摄像头录像
python check_camera.py  # 检查摄像头可用性，发现3个可用摄像头，测试录像功能成功
python test_camera_recording.py  # 测试EyeLink Manager摄像头集成功能，所有测试通过
python camera_usage_example.py  # 运行完整实验示例，3个试次，生成EDF和视频文件
创建了摄像头录像功能说明.md - 详细的使用文档和配置说明
创建了check_camera.py - 摄像头可用性检查工具
创建了test_camera_recording.py - 摄像头录像功能测试脚本
创建了camera_usage_example.py - 完整实验使用示例
创建了摄像头录像功能集成总结.md - 完整的功能集成总结和技术文档
用户修改摄像头索引为2，并尝试使用hvc1/hev1格式但出现掉帧问题
创建了test_video_codecs.py - 测试不同视频编码格式的性能和压缩效率
python test_video_codecs.py  # 测试结果：X264编码0%掉帧率，H264编码0.7%掉帧率，hvc1编码1.3%掉帧率
更新eyelink_manager.py使用X264编码格式 - 实现无掉帧录像且保持高压缩率
python test_camera_recording.py  # 验证X264编码工作正常，生成38MB视频文件（5秒录像）
优化摄像头设置：缓冲区大小设为1，采集格式使用MJPG提高速度

## 2025-07-15 执行的命令
创建了continuous_reading文件夹 - 连续文本阅读实验模块
创建了continuous_reading_config.py - 连续阅读实验配置文件，包含文本、基线、评分、显示等所有配置
创建了text_materials.py - 文本材料管理模块，支持自动解析句子、统计信息、预览功能
创建了continuous_reading_experiment.py - 连续阅读实验主程序，复用现有代码架构
创建了reading_text.txt - 示例阅读文本，科幻主题连续故事
创建了run_experiment.py - 实验启动脚本，包含依赖检查和参数配置
创建了README.md - 详细的使用说明文档，包含配置、运行、数据格式等说明

## 2025-07-10 执行的命令
cd analysis; python main.py  # 成功运行完整分析流程，处理gyd的最新数据
cd analysis; python view_results.py  # 查看分析结果摘要

## 创建分析系统
python main.py  # 运行完整分析流程
python main.py --skip-conversion  # 跳过EDF转换，直接分析现有ASC文件

## 单独运行各个模块
python edf_converter.py  # 只转换EDF文件
python data_processor.py  # 只处理实验数据
python pupil_analyzer.py  # 只分析瞳孔数据
python visualizer.py  # 只进行可视化

## 配置修改
# 修改config.py中的参数来调整分析设置

## 输出文件位置
# ./output/ - 数据处理结果
# ./figures/ - 生成的图形
# ./output/*.log - 日志文件

所有默认相机改成2，无论是根目录的main_exp还是continuous reading里的相机都改成2。尽量少改动代码.


使用pytrack-ntu这个库,从多个指定edf文件目录获取edf文件，然后
在preprocess_edf.py的main函数中添加数据处理前后瞳孔图对比功能 - 添加plot_pupil_comparison函数，支持左眼、右眼、双眼平均的对比显示，包含数据质量统计
conda activate et
python preprocess_edf.py  # 修复图表空白和中文字体问题，添加智能字体检测，修复时间范围过滤，成功生成对比图
添加二次曲线插值功能 - 新增quadratic_fit_points参数(默认10个点)，支持quadratic插值方法，使用numpy.polyfit进行二次多项式拟合，MSE最小化，包含回退机制
conda activate et
python preprocess_edf.py  # 成功测试二次曲线插值功能，检测到480个眨眼时间段，自动回退机制正常工作
创建了avi2mp4.py - AVI视频文件转换为H.265编码MP4的程序，支持批量转换、目录结构保持、质量参数配置
更新了avi2mp4.py - 添加搜索所有video子目录功能，支持两种模式：单个目录转换和批量video目录转换
创建了delete_video_folders.py - 删除指定根目录下所有名为"video"的子文件夹内容的工具，支持递归搜索、预览模式、强制删除等功能