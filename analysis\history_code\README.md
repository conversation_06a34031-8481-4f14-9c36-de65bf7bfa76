# EyeLink数据分析系统

这是一个完整的EyeLink眼动数据分析系统，用于处理好奇心实验的瞳孔数据。

## 功能特性

- **EDF文件转换**: 自动将EyeLink的EDF文件转换为ASC文件
- **数据处理**: 解析实验数据，按照不同评分类型分组试次
- **瞳孔数据分析**: 提取和清理瞳孔数据，支持双眼平均
- **数据可视化**: 生成CV领域标准的时间曲线图，包含均值和标准差

## 文件结构

```
analysis/
├── config.py              # 配置文件
├── main.py                # 主程序
├── edf_converter.py       # EDF文件转换器
├── data_processor.py      # 数据处理器
├── pupil_analyzer.py      # 瞳孔数据分析器
├── visualizer.py          # 数据可视化器
├── output/                # 输出目录
│   ├── *.json            # 数据处理结果
│   └── *.log             # 日志文件
├── figures/               # 图形输出目录
│   └── *.png             # 生成的图形
├── 历史命令.txt           # 历史命令记录
└── README.md             # 说明文档
```

## 配置说明

在 `config.py` 中可以配置以下参数：

### 路径配置
- `EDF2ASC_PATH`: edf2asc.exe的路径
- `DATA_DIR`: 数据目录
- `TARGET_PARTICIPANT`: 目标参与者ID

### 评分配置
- `RATING_TYPES`: 评分类型定义
- `PLOT_RATINGS`: 要绘制的评分类型
- `RATING_VALUES_TO_PLOT`: 要绘制的评分值

### 瞳孔数据配置
- `PUPIL_CONFIG`: 瞳孔数据分析参数
  - `event_start/end`: 分析时间段的开始/结束事件
  - `eyes_to_use`: 使用的眼睛 ('left', 'right', 'both')
  - `baseline_duration`: 基线时长
  - `filter_blinks`: 是否过滤眨眼
  - `smooth_data`: 是否平滑数据

### 图形配置
- `PLOT_CONFIG`: 图形显示参数
  - `figure_size`: 图形大小
  - `colors`: 颜色配置
  - `show_std`: 是否显示标准差
  - `chinese_font`: 中文字体

## 使用方法

### 1. 运行完整分析流程
```bash
cd analysis
python main.py
```

### 2. 跳过EDF转换（如果ASC文件已存在）
```bash
python main.py --skip-conversion
```

### 3. 单独运行各个模块
```bash
python edf_converter.py      # 只转换EDF文件
python data_processor.py     # 只处理实验数据
python pupil_analyzer.py     # 只分析瞳孔数据
python visualizer.py         # 只进行可视化
```

## 输出结果

### 数据文件
- `output/grouped_trial_data.json`: 按评分分组的试次数据
- `output/summary_statistics.json`: 汇总统计信息
- `output/*.log`: 各模块的日志文件

### 图形文件
- `figures/*_pupil_timecourse.png`: 瞳孔时间曲线图

## 分析结果示例

根据最新的gyd数据分析结果：

### 好奇心评分分布
- 评分2: 4个试次
- 评分3: 4个试次  
- 评分4: 6个试次
- 评分5: 1个试次
- 平均评分: 3.27 (标准差: 0.93)

### 有趣度评分分布
- 评分1: 2个试次
- 评分2: 3个试次
- 评分3: 3个试次
- 评分4: 7个试次
- 平均评分: 3.00 (标准差: 1.10)

### 惊讶度评分分布
- 评分1: 11个试次
- 评分2: 4个试次
- 平均评分: 1.27 (标准差: 0.44)

## 注意事项

1. **编码问题**: 系统会自动尝试多种编码格式读取ASC文件
2. **事件数据**: 如果ASC文件中没有事件标记，系统会自动估计试次时间范围
3. **中文显示**: 确保系统安装了SimHei字体以正确显示中文
4. **数据质量**: 系统会自动过滤异常值和插值缺失数据

## 扩展功能

系统设计为模块化，可以轻松扩展：

1. **添加新的评分类型**: 在`config.py`中修改`RATING_TYPES`
2. **修改分析时间段**: 调整`PUPIL_CONFIG`中的事件配置
3. **自定义可视化**: 修改`PLOT_CONFIG`中的图形参数
4. **添加新的分析方法**: 在相应模块中添加新的分析函数

## 依赖库

- pandas: 数据处理
- numpy: 数值计算
- matplotlib: 图形绘制
- scipy: 科学计算
- seaborn: 统计图形
