# ParseEyeLinkAscFiles

Read EyeLink .asc data files info into Python as Pandas dataframes.
Write them to more readable .csv files if desired.

Dependencies: <PERSON><PERSON><PERSON>, Pandas

ParseEyeLinkAsc_script is an iPython notebook containing a typical use case.

If the code doesn't work for you, it may be that you had different settings on your tracker or EDF2ASC.
For more information on how the asc files are formatted, see the help menu on your EDF2ASC program.

For a parser written in C, see http://download.sr-support.com/dispdoc/page25.html.  
