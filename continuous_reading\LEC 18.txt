好的，欢迎来到 CS285 第 18 讲。在今天的讲座中，我们将做一些与以往不同的事情。我们不会涉及任何新的强化学习算法，而是将讨论变分推断和生成模型。这与我们通常的材料有所不同，因为今天我们不会涵盖任何强化学习算法。但我希望在这次课程中用一整节课来讲解变分推断，因为变分推断的概念在各种强化学习主题中反复出现，包括基于模型的强化学习、逆向强化学习、探索等等。更广泛地说，变分推断与强化学习和基于学习的控制有着非常深刻的联系，我们将在下周学习这一点。

在今天的讲座中，我们将讨论概率潜变量模型，它们是什么以及它们的用途。我们将讨论变分推断如何让我们能够有效地近似训练概率潜变量模型。然后，我们将讨论一种叫做摊销变分推断的方法，这是一个非常有用且强大的工具，可以将变分推断与深度神经网络等函数逼近器结合使用。最后，我们将讨论一些可以用摊销 VI 训练的示例模型，包括变分自编码器和在基于模型的强化学习中有用的各种序列级模型。所以，今天讲座的目标是理解深度学习中的潜变量模型，并理解如何使用摊销变分推断来训练它们。

好的，让我们从一个非常基本的概述开始。那些已经熟悉这部分材料的同学可能想直接跳过，但我想确保从头开始，以确保大家在符号、术语等方面都处于同一水平。那么，什么是概率模型？概率模型是一个非常通用的术语，指的是表示概率分布的模型。所以，如果你有一些随机变量 x，那么 p(x) 就可以用一个概率模型来表示。花点时间想一想，我们在本课程中已经遇到过哪些概率模型？

我们已经见过哪些例子？如果你只有一个随机变量 x，并且想要建模 p(x)，你可能有一些数据，那些橙色的点代表你实际观察到的 x。建模 p(x) 意味着为它们拟合一个分布。例如，你可能会拟合一个多元正态分布来表示这些数据。概率模型也可以是条件模型。例如，你可以有一个模型 p(y|x)。在这种情况下，你可能不关心对 x 的分布建模，但你关心对给定 x 的 y 的条件分布建模。所以，如果你在 x 轴上有一些输入 x，在 y 轴上有一些输出 y，你可能会拟合一个条件高斯模型，一个将 y 表示为 x 的线性函数并带有加性高斯噪声的模型。我们以前肯定见过这样的模型，花点时间回想一下，在这门课的什么时候我们见过条件概率模型？其中一个例子当然是策略。策略是条件概率模型，它们为我们提供了给定 s 的 a 的条件分布。

好的，今天讲座的主要主题实际上是一种叫做潜变量模型的东西。潜变量模型是一种特殊类型的概率模型。形式上，潜变量模型就是一个模型，其中除证据或查询变量外还有其他变量。在 p(x) 中，没有证据，查询是 x。在 p(y|x) 中，证据是 x，查询是 y。如果你有一个潜变量模型，这意味着模型中还有一些既不是证据也不是查询的其他变量，因此需要被积分掉才能评估你想要的概率。

一个非常经典的用来表示 p(x) 的潜变量模型是混合模型。在这张图片中，我们的数据被组织成三个非常清晰的簇。但我们事先并不知道这些簇是什么，所以这里的簇是按颜色编码的，但数据实际上没有颜色编码，数据只是一堆点。你想要表示一个能够准确拟合这些数据的分布。事实证明，使用一个包含三个多元正态分布的混合模型来表示这个分布非常方便。这是一种潜变量模型。花点时间想一想，这里的潜变量是什么？在这种情况下，潜变量实际上是一个可以取三个值之一的分类离散变量，对应于三个簇的身份。我们可以将这个潜变量模型表示为对潜变量所有可能值的求和，即我们建模的变量 x 在给定潜变量 z 的条件分布乘以该 z 的概率。所以，这里的 p(x) 由 p(x|z) * p(z) 对 z 的求和给出。z 是一个取三个值之一的分类变量，对应于簇的身份，而 x 是一个二维连续变量，对应于点的实际位置。

我们可以对条件模型做完全相同的事情。我们可以说 p(y|x) 是通过对我们的潜变量 z 求和 p(y|x, z) * p(z) 得到的。现在，还有其他方法可以创建这种分解。例如，你可以说 p(z) 也依赖于 x，所以你可以有 p(y|x, z) * p(z|x)。你甚至可以有 y 的条件不依赖于 x，所以你可以有 p(y|z) * p(z|x)。这些都是有效的设计选择。如果我们坚持使用离散的分类变量 z，我们之前已经见过的一个模型例子是混合密度网络，这是我们在讨论模仿学习以及如何进行多模态模仿学习以处理像绕树行驶这样的多模态情况时讨论过的模型。在课程的第二讲中，我们学习了如何让神经网络输出由高斯混合表示的分布。神经网络为每个混合元素输出多个均值 μ、方差 σ 和权重 w。假设网络的输入是 x，输出是 y，潜变量同样是簇的身份。花点时间想一想，幻灯片右侧这张图对应的概率模型到底是什么？它将 p(y|x) 表示为对 z 的求和，形式为 p(y|给定某项) * p(z|给定某项)。这个和是什么？在这种情况下，我们的神经网络实际上输出了高斯分布的均值和协方差，并且还输出了每个混合元素的权重 w。所以，事实上，这个模型是由 p(y|x, z) * p(z|x) 对 z 求和给出的。这实际上与我这里写的方程有点不同。在这张图的右边，p(z) 实际上取决于 x，所以这是一个我们做出的设计选择。

总的来说，如果你有一个潜变量模型，你可以这样想：你有一些复杂的 x 分布，由这张图表示，所以 p(x) 是一个复杂的东西。你有一些关于 z 的先验 p(z)，我们通常会选择这个先验为一个更简单的分布。也许 z 是分类的，所以 p(z) 是一个离散分布；或者 z 是连续的，但 p(z) 是一些非常简单的分布类别，比如高斯分布。然后，我们可能会将从 z 到 x 的映射，即 p(x|z)，表示为一些简单的条件分布。所以 p(x|z) 可能是一个神经网络，其中均值是 z 的神经网络函数，方差也是 z 的神经网络函数。这些函数可能非常复杂，但实际的分布 p(x|z) 可能非常简单，例如，一个高斯分布，一个正态分布。所以 p(z) 是一个简单的分布，p(x|z) 也是一个简单的分布。这个简单分布的参数可能很复杂，但分布本身是简单的，例如，可以用显式参数化的东西。理解这一点非常重要，尤其是我所说的“简单”这个词的含义。p(x) 不简单，因为很难找到一个单一的参数化，比如高斯分布或贝塔分布，来完美地捕捉 p(x)。p(z) 是简单的，因为像高斯分布这样的简单分布可以完美地捕捉它。p(x|z) 也是简单的，因为你可以用高斯分布来表示它，尽管那个高斯分布的参数可能非常复杂。当然，我这里用高斯分布作为例子，这些可以是不同种类的分布，不同的参数化，它们可以是离散的或连续的。所以这只是一个例子。

但总的来说，p(x) 将由对 c 的所有可能值的某个求和或积分给出，即 p(x|z) * p(z)。所以这里发生的是，p(z) 和 p(x|z) 都是简单的，但它们的乘积在积分掉 z 后，可能会成为一个非常复杂的分布。这是一个非常强大的想法，因为它允许我们将复杂的分布表示为我们可以学习和参数化的简单分布的乘积。好的，所以我们有两个简单的分布，将它们相乘并积分掉 z。完全相同的事情也可能发生在条件情况下。在我之前的例子中，用于多模态策略的条件潜变量模型，你可以在输出上有一个高斯混合，或者更一般地，你可以有一些潜变量，我们称之为 z，作为模型的额外输入。你有一些先验 p(z)，还有你的条件 p(y|x, z)，与前一张幻灯片完全相同的逻辑也适用。所以 p(z) 会很简单，p(y|x, z) 也会很简单，但积分掉 z 的结果，也就是最终的分布 p(y|x)，可能会极其复杂。

这类东西出现的另一个领域是基于模型的强化学习。你可以在基于模型的强化学习中使用潜变量模型。我们之前在讨论带图像的基于模型的强化学习时已经看到了一个例子。我们看到了这些潜变量状态模型的例子，你观察图像 o，并希望学习一个依赖于动作 u 的潜变量状态 x。这里我们实际上有一个更复杂的潜变量空间。我们有我们的观测分布 p(o|x) 和我们的先验 p(x)，实际上它建模了动力学，它实际上建模了 p(xt+1|xt) 和 p(x1)。所以 x 的先验结构更复杂。这些模型的潜变量空间是有结构的，我们将在讲座结束时重新讨论这一点。所以，如果这部分不完全清楚，别担心，我们会回来的。

好的，现在我们已经了解了潜变量模型是什么，它们是用来做什么的，以及我们为什么需要它们。我们将在其他地方看到潜变量模型。下周我们还将讨论如何将强化学习与变分推断结合起来，以实际建模人类行为。所以，我们不是说给定一个奖励函数，什么是最佳的行动方式，而是可以说，给定一个人的行为数据，我们能否反向工程出这个人在尝试做什么？我们能否推断出他们的目标函数，推断出一些关于他们思维过程的东西？这在模仿学习领域和神经科学及运动控制中研究人类行为时都很常见。我们还在探索中看到了潜变量模型和生成模型。当我们讨论探索时，我们实际上简要地提到了这一点。我们讨论了如何使用变分推断技术来进行信息增益计算等，以及如何使用生成模型和密度模型来分配伪计数和基于计数的奖励。所以，这类生成模型和潜变量模型在强化学习的研究中经常出现。顺便说一句，当我使用“生成模型”这个术语时，为了澄清一下术语，生成模型是生成 x 的模型。所以 p(x) 是一个生成模型。潜变量模型是具有潜变量的模型。并非所有生成模型都是潜变量模型，也并非所有潜变量模型都是生成模型。然而，通常将生成模型表示为潜变量模型要容易得多，因为生成模型通常需要表示一个非常复杂的概率分布，而将一个复杂的概率分布表示为多个简单概率分布的乘积要容易得多。因此，虽然生成模型不一定是潜变量模型，但当我们有一个复杂的生成模型时，将其建模为潜变量模型通常非常方便。

好的，现在让我们进入讲座的核心部分，我们来谈谈如何训练潜变量模型，以及为什么这很困难。假设我们有我们的模型 pθ(x)，这里的 θ 代表模型的参数，我们有数据 x1, x2, x3, ..., xn。当我们想要拟合数据时，我们通常想要一个最大似然拟合。所以，最自然的通用建模目标是将 θ 设置为 argmax (1/m) * Σ log pθ(xi)，其中求和遍及所有数据点。所以，如果你找到最大化所有数据点对数概率的 θ，你就找到了所谓的最大似然拟合，这在某种意义上是你能为你的数据拥有的最好的模型。而你的 p(x) 是由 p(x|z) * p(z) 对 z 的积分给出的。所以，如果我将 p(x) 的这个方程代入最大似然拟合中，我得到了这个训练目标。当然，你可能注意到的第一件事是，如果 z 是一个连续变量，这个训练目标是相当难以计算的。每次你想进行一个梯度步骤时，计算这个积分会变得非常棘手。所以我们不能直接这样做。在一些非常简单的情况下，我们可以，例如，如果我们有一个高斯混合模型，我们实际上可以对所有混合元素求和，但事实证明该算法仍然不是很好，因为它最终的数值属性很差。所以即使在我们可以估计这个积分的情况下，我们通常也不想这样做，因为最终的优化景观非常糟糕。但对于连续变量，我们甚至可能没有那个选择，我们可能无法准确地估计那个积分，即使我们想这样做。

好的，我们如何以一种易于处理的方式来估计对数似然及其梯度？这确实是训练这些潜变量模型的关键。一个替代方案是使用一个叫做期望对数似然的目标。我将在这里陈述这个目标，我不会证明它，但当我们稍后讨论变分推断时，我们会看到为什么这个目标是合理的。所以现在，就姑且接受它，这是我们将要使用的目标，但稍后我们会看到为什么这个目标是一个有原则的选择的理由。期望对数似然直观上相当于猜测潜变量模型是什么。所以你可以把潜变量看作是数据的部分观测。所以数据实际上由 x 和 z 组成，但你观察到了 x 而不是 z。所以你可以做的是，你基本上可以猜测 z 是什么。你可以说，鉴于数据点在这里，它可能属于这个簇。然后构造一个假的标签，说这个 xi 实际上有这个 z 值，然后对那个 x 和 z 的值做最大似然。当然，在现实中，你并不知道与特定 xi 对应的确切 z，但你可能有一个关于它的分布。所以，你不是只取最可能的那个 z 值，而是取整个 z 的分布，并按那个 z 的概率加权平均似然。

这就给了我们期望对数似然的计算。所以，我们将要使用的目标是，对我们所有数据点求和，在给定 xi 的 z 的期望下，求 log pθ(xi, z) 的值。所以直觉是，如果你猜测给定 x 最可能的 z，并假装它是正确的——当然，在现实中，你实际上不只猜测一个 z，你实际上是对所有 z 求和，并按它们是正确的概率加权。所以有很多可能的 z 值，所以你使用分布 p(z|xi)。

好的，首先，为什么这个目标更容易处理？因为期望值可以用样本来估计，对吧？所以那个期望值，如果你想得到期望值的无偏估计，你不需要实际考虑所有的 z，你可以简单地从后验 p(z|xi) 中采样，然后将这些样本的对数概率平均起来。你在上一张幻灯片上不能用那个技巧，如果你有积分的对数，你不能用那个技巧，因为一个积分或和的对数不能线性分解，但和可以。所以你可以用样本来估计它。所以，我们做这个的可行算法，就像我们用策略梯度看到的那样，就是从 z 给定 xi 中采样，然后将对数概率平均起来，当然你也可以对梯度做同样的事情。所以，如果你能得到这个后验 p(z|xi)，那么你就能以一种可处理的方式计算期望对数似然，并以一种可处理的方式计算它的梯度。但那时最大的挑战就变成了，我们如何计算 p(z|xi)？如果我们能计算那个量，那么我们就可以估计期望对数似然。这将是讲座下一部分的主题。所以，当你想估计 p(z|xi) 时，你实际上是在说，给定 x 中的某个点，将它映射回一个关于 z 的分布，这可能是一个相当复杂的分布，然后在那个分布下计算期望对数似然。好的，这就是我们将在讲座的下一部分讨论的内容。

---

好的，现在让我们进入今天讲座的主要技术部分，即讨论变分推断框架。这个框架基本上关注这个问题：我们如何计算 p(z|xi)？但在推导这个的过程中，我们也会看到为什么期望对数似然实际上是一个合理的目标。让我们考虑做一些粗略的近似。p(z|xi) 通常是一个相当复杂的分布，因为单个点 x 可能来自 z 空间中的许多不同地方。但让我们做一个非常简化的近似，我们说，我们将用某个分布 qi(z) 来近似 p(z|xi)，这个分布是一个高斯分布，或者通常是某个非常简单的、可参数化的分布类别。注意我把它叫做 qi(z)，所以它是关于 z 的一个分布，它将是特定于这个点 xi 的一个分布。好的，所以我们不使用这个复杂的东西，而是尝试用一个单一的峰来近似它。我特意选择了这张图，只是为了清楚地表明，这个近似不一定会很好，但我们将尝试在这个简单的分布类别，即高斯分布类别中找到最佳的拟合。

事实证明，如果你用任何 qi(z) 来近似 p(z|xi)，你实际上可以构造一个关于 xi 的对数概率的下界。这是一个非常强大的想法，因为如果你能构造一个关于 xi 的对数概率的下界，那么最大化那个下界就会推高 xi 的对数概率。现在，通常来说，最大化下界并不会增加你关心的量，但如果下界足够紧，那么它就会。我们稍后会看到，在某些条件下，这个下界确实是紧的。但现在，我们先不担心紧度，我们只看看如何通过使用 qi(z) 来得到一个下界。

所以我们可以写出 xi 的对数概率，即 log(∫ p(xi|z) * p(z) dz)。你知道，如果你想引入一个当前不在方程中的量，通常的技巧是用那个量除以它自己，所以 qi(z) / qi(z) = 1，所以我们可以在任何时候乘上它。现在，我们可以注意到，我们有一个量乘以 qi(z)，所以我们可以把那个量写成在 qi(z) 下的期望值。所以我们基本上取那个比率中的分子，它变成了一个期望，然后其他所有东西都留下了。所以我们有 log(E_qi(z) [p(xi|z) * p(z) / qi(z)])。好的，到目前为止我们还没有做任何近似，这些只是一些代数操作。

接下来，我们将使用詹森不等式。詹森不等式是一种关联应用于线性组合的凸函数或凹函数的方法。我在幻灯片上写出的是詹森不等式对于对数函数这个凹函数的一个特例，但总的来说，这个不等式对于任何凹函数都成立。如果你有一个凸函数，那么它也成立，但不等式的方向相反。对于对数函数的情况，詹森不等式说，某个变量 y 的期望值的对数大于或等于该变量对数的期望值。如果这让你觉得有点反直觉，你可以考虑画一张图。对数函数是一个凹函数，所以它大概是那样的。如果你想象函数和的对数，因为对数增加的速率总是减小的，那么函数和的对数将大于或等于对数的和，因为速率的减小。所以如果这对你有点不清楚，试着画一张关于多个对数函数相加的图。

我们可以直接将詹森不等式应用于上一张幻灯片的结果。我们这样做的方法是，注意到我们有一个量的期望值的对数。所以应用詹森不等式，只是将期望值移到对数外面，并将等号替换为大于等于号。这意味着我们之前的结果由 E_qi(z) [log(p(xi|z) * p(z) / qi(z))] 所下界。但现在，我们当然知道，乘积的对数可以写成对数的和。所以我们可以等价地写成 E_qi(z) [log p(xi|z) + log p(z)] - E_qi(z) [log qi(z)]。我这样写的原因是，我想把所有依赖于 p 的项收集在第一部分，所有依赖于 q 的项收集在第二部分。

现在，这个方程的好处是，一切都是可处理的。这对于任何 qi(z) 都成立。所以我们可以只选择一些随机的 qi(z)，我们就得到了一个下界，尽管当然不是所有的 qi 都会导致最好的下界。但我们可以选择一些 qi(z)，从中采样来评估第一个期望，然后第二个期望，你会注意到，实际上是 qi(z) 的熵的方程，对于许多简单的分布，比如高斯分布，它有一个闭合形式的解。好的，所以我们可以用 qi 的熵来替换第二项。所以，最大化这个可以最大化 log p(xi)，虽然正如我之前提到的，你需要证明这个界限不会太松。

现在，让我做一个简短的题外话，来回顾一下我们在这里遇到的一些信息论量。我们已经看到了很多这些，例如，我们在探索讲座中已经讨论过熵，但我只想简要回顾一下，因为这些东西对于获得变分推断实际在做什么的良好直觉非常重要。熵，某个分布的熵，是该分布对数概率的负期望值。这里有一个伯努利分布的熵的方程，也就是一个二元事件的概率。你可以看到，当该事件的概率接近 0.5 时，熵增加，如果事件保证发生（概率等于 1）或保证不发生（概率等于 0），熵下降到零。所以，对熵的一个直觉是，随机变量有多随机。这在伯努利变量的情况下很有意义，当它是 0.5 时，变量在某种意义上是最随机的，最不可预测的，并且它具有最高的熵。第二个直觉是，在自身期望下，对数概率有多大。所以，如果你主要看到在自身期望下较低的对数概率，那意味着有很多地方你分配了大致相等的概率。如果你主要看到非常高的对数概率，那意味着你真的集中在少数几个你分配了一半概率的点上。所以顶部的例子熵很高，因为对数概率通常在各处都较低，而底部的熵较低，因为对数概率只在少数地方非常高，那是一个低熵分布。

那么我们可以问一个问题，对于我们在前一张幻灯片上看到的变分下界，我们期望它实际上做什么？它是某个量的期望值加上 qi 的熵。所以如果这张图显示的是 p(xi, z)，也就是第一个期望内的东西，你可以想象这个函数的期望值可以通过在最高的峰上放置大量的概率质量来最大化。对吧？如果我们只最大化第一部分，我们就会得到这个结果，我们只想找到一个 z 的分布，在这个分布内我们有 p(xi, z) 的最大值。但我们也在尝试最大化这个分布的熵，所以我们不想让它太窄。如果我们也在尝试最大化熵，那么我们想尽可能地把它铺开，同时仍然保持在 p(xi, z) 很大的区域内。所以，因为我们有第二项，我们得到的东西有点铺开，直觉是，因此，最大化这个量的 qi(z) 会有点覆盖 p(xi, z) 的分布。

我想在这里回顾的另一个概念是 KL 散度。两个分布 q 和 p 之间的 KL 散度，是在第一个分布下的期望值，即第一个分布的概率除以第二个分布的概率的比值的对数。同样，通过利用乘积的对数是对数的和这一事实，我们可以将其写成 E_q[log q(x)] - E_q[log p(x)]。我们可以用一种看起来很像前一张幻灯片上方程的方式重写它，如果我们只是交换位置，并认识到在 q 下的 log q 的期望值只是负熵。所以 KL 散度是在 q 下的 log p(x) 的负期望值和 q 的熵。

KL 散度衡量的一个直觉是两个分布有多么不同。你会注意到当 q 和 p 相等时，KL 散度为零。很容易看出为什么它是零，因为你有 q/p = 1，log(1) = 0。第二个直觉是，一个分布在另一个分布下的期望对数概率减去熵有多小。为什么要减去熵？原因和我们之前看到的一样，因为如果你没有熵项，那么 q 只会想停留在 p 下最可能的点上。但如果我们有熵，它就想覆盖它。

所以变分近似说，log p(xi) 大于或等于在 qi(z) 下的期望值 E[log p(xi|z) + log p(z)] + H(qi)。我们称之为证据下界或变分下界，我将用 L_i(p, qi) 来表示它。正如我们在前一张幻灯片中看到的，它也是负的 KL 散度。那么，什么才是一个好的 qi(z) 呢？直觉上，一个好的 qi(z) 应该近似于 p(z|xi)，因为那样你就能得到最紧的界。在什么意义上近似呢？嗯，你可以用 KL 散度来比较它们。所以你可以说，KL 散度衡量了两个分布之间的差异，当 KL 散度为零时，这两个分布完全相等。所以，我们选择 qi 来最小化 qi(z) 和后验 p(z|xi) 之间的 KL 散度。为什么？因为如果我们用之前的定义写出这个 KL 散度，我们会发现它等于在 qi(z) 下的期望值 E[log(qi(z) / p(z|xi))]。现在 p(z|xi) 可以写成 p(xi, z) / p(xi)。因为我们是求倒数，所以我们翻转比例，得到这里的这个方程。再次应用乘积的对数是对数的和的性质，我们得到旁边的这个方程。

所以我们有第一项，在 q 下的负期望值 E[-log p(xi|z) - log p(z)]，然后我们有熵项，然后我们有这个先验项。代入熵的方程，我们得到这个。这意味着这两个量之间的 KL 散度等于负的变分下界加上 xi 的对数概率。然而，注意到 xi 的对数概率实际上不依赖于 qi。所以我们可以稍微重新排列一下项，我们可以将 log p(xi) 表示为 q_i 和 p 之间的 KL 散度加上证据下界。这不是一个不等式，这都是精确的。现在我们知道 KL 散度总是正的，所以这实际上是推导证据下界的另一种方式。对吧？因为你知道 log p(x) 等于某个正量加上 L，这意味着 L 是 log p(xi) 的一个下界。但更进一步，这个方程表明，如果我们将 KL 散度降到零，那么证据下界实际上等于 log p(xi)，这意味着最小化那个 KL 散度是收紧界限的有效方法。这证明了为什么我们想选择 qi(z) 来近似 p(z|xi)，也证明了为什么我们想使用期望对数似然，因为当我们使用期望对数似然时，这就像在 p(z|x) 下取期望，这是界限最紧的点。

好的，这是我们之前的方程，我们用它来推导这个界，而 KL 散度我们可以这样写出来，这是我们在前一张幻灯片上看到的。所以这意味着 KL 散度由负的变分下界加上这个 log p(xi) 项给出。现在 log p(xi) 不依赖于 qi，所以如果我们想优化 qi(x) over z 来最小化 KL 散度，我们可以等价地优化相同的证据下界。这很有吸引力，相对于 q 最大化相同的证据下界可以最小化 KL 散度并收紧我们的界限，相对于 p 最大化则可以增加对数似然。

所以现在这直接提出了一个实际的学习算法：取你的变分下界，你的证据下界，相对于 qi 最大化它以获得最紧的界，然后相对于 p 最大化它以改进你的模型，改进你的对数似然，然后交替这两个步骤。好的，简单回顾一下，我们的目标是最大化 log pθ(xi)，但这很难处理，所以我们改为最大化证据下界。对于每个 xi，我们将通过从我们的 qi(xi) 中采样 z 来计算相对于模型参数的梯度，然后使用这些样本来估计梯度。这是一个单一样本版本，所以你从 qi(xi) 中采样一个 z，然后假设你的先验 p(z) 不依赖于 θ，那么梯度就是 log pθ(xi|z) 的梯度。然后你改进你的 θ，然后你更新 qi 以最大化相同的证据下界。

这是变分推断的随机梯度下降版本。为了让我们都明白，我再说一遍。为了估计梯度 grad_θ L_i(p, qi)，从近似后验 q_i 中采样一个 z，计算梯度 grad_θ L_i(p, qi) 为 grad_θ log pθ(xi|z)，然后进行那个梯度步骤，然后更新你的 qi 以最大化 L_i(p, qi)。好的，这里的一切都很直接，除了最后一行，你实际上如何改进你的 qi？假设 qi 由一个均值为 μi 和方差为 σi 的高斯分布给出。嗯，你实际上可以计算证据下界相对于均值和方差的梯度，然后对 μi 和 σi 进行梯度上升。

那么，这有什么问题呢？我们有多少个参数？记住，我们对每个数据点 xi 都有一个单独的 qi，它们每个都有不同的 μ 和不同的 σ。如果你有几千个数据点，这不是什么大问题，但如果你有数百万个数据点，这就成了一个大问题。在深度学习的背景下，我们通常会有非常大量的数据点。所以，如果我们有这个高斯分布，总参数数量是模型中的参数数量 θ，加上均值的维度，加上方差的维度，再乘以数据点的数量 n。好的，这可能有点太大了。n 可能是一个非常大的数字，这可能是难以处理的。但请记住，我们的直觉是 qi(z) 需要以某种方式近似后验 p(z|xi)。那么，如果我们不为每个数据点学习一个单独的 qi(z)，一个单独的均值和方差，而是训练一个单独的神经网络模型来近似 qi(z) 呢？所以，我们不是为每个数据点 xi 都有一个单独的 qi(z)，而是有一个网络 q(z|xi)，旨在近似后验。那么，在我们的生成模型中，我们会有一个从 z 映射到 x 的神经网络，和另一个从 x 映射到 z 的神经网络。那个神经网络为我们提供了一个后验估计，其均值和方差由 x 的神经网络函数给出。这就是摊销变分推断背后的思想，我将在讲座的下一部分讨论它。

---

好的，现在让我们来谈谈摊销变分推断如何让我们能够学习这个 q(z|x)，从而使变分推断成为一个适用的工具，即使在数据集大小非常大的情况下也是如此。回顾一下我们之前的内容，我们训练模型参数 θ 的变分推断过程大概是这样的：对于我们数据集中的每个图像 xi 或数据点 x，或者更一般地，对于每个小批量，你会估计你的变分下界 Li(p, q) 相对于 θ 的梯度。你这样做的方法是从近似后验 qi(z) 中采样一个 z，然后估计梯度，这只是 log pθ(xi|z) 相对于 θ 的梯度，也就是使用你刚从 qi 得到的一个样本的单样本估计器。然后你用这个估计的梯度 grad_θ Li 进行一个梯度步骤，然后你更新 qi 以最大化界限 Li，从而收紧它。当然，问题在于你如何最大化这个界限。

我们在前一部分讲座中看到的是，如果你将 qi 表示为每个数据点 xi 的一个单独的高斯分布，那么你就可以简单地计算 Li 相对于 θ 的导数，但现在总参数数量随着 n 的增加而增加。所以，在摊销变分推断中的想法是，通过使用一个单一的模型来摊销推断这个近似后验 qi(z) 的成本，这个模型将为任何 x 提供后验。那个单一的模型可以是一个神经网络模型。在这种情况下，我们仍然会对每个 x 有一个高斯后验，但我们不是为每个数据点存储那个后验的均值和方差，而是会有一个神经网络，输入那个数据点，并输出 μ_φ(x) 和 σ_φ(x)，即数据点 x 的高斯后验的参数。

好的，这就是摊销变分推断的基本思想。你有两个网络，你想要学习的生成模型 pθ(x|z)，以及我们称之为你的推断网络 q_φ(z|x)。我们从之前的讨论中知道，你可以用任何 q 来构建 log p(xi) 的下界，但当这个 q 接近后验时，这个下界最紧。所以现在，就像我们之前有 qi(z) 一样，我们现在有 q_φ(z|xi)。现在我们的变分下界是两个分布的函数：pθ(xi|z) 和 q_φ(z|xi)。所以我们的训练过程，只是对前一张幻灯片中的过程的修改，将是这样的：首先计算变分下界相对于 θ 的梯度，这基本上和之前的方式一样，从 q_φ(z|xi) 中采样 z，然后计算相对于 θ 的梯度，这近似于 log pθ(xi|z) 相对于 θ 的梯度，其中 z 是你刚刚采样的 z。然后你可以在你的生成模型的参数 θ 上进行梯度上升步骤。

然后你需要做的是在 φ 上进行一个梯度步骤，而在 φ 上的梯度步骤也是在变分下界 L 上进行梯度上升。所以我们为了完成这个算法必须回答的问题是，我们如何计算 L 相对于 φ 的梯度？这就是我们现在关心的问题。

这是 L 的表达式，你可以看到 φ 出现在两个地方。首先，φ 是你取期望值的分布，其次，φ 出现在熵项中。那么，请大家思考一个问题：我们来看第一项，它是在由 φ 参数化的分布下的期望值，而期望的内容与 φ 无关。我们之前在哪里见过这个？我们实际上已经讨论过一个可以计算这部分梯度的算法，我们在课程的早期讨论过这个算法。花点时间思考一下，并尝试想一想，根据我们已经在课堂上学到的知识，这一项的梯度会是什么样子？我们的 q_φ(z|x) 将由一个高斯分布给出，其中均值和方差是 x 的神经网络函数。高斯分布的熵可以用闭合形式表示，它是一个涉及 μ_φ 和 σ_φ 的闭合形式方程。你可以在教科书或维基百科上查到这个，这是一个非常标准的方程。所以那个其实很容易做，你只需用 μ 和 σ 写下方程，然后就可以求它的导数。真正有问题的是第一项。

所以我们可以有启发性地将那一项重写为 J(φ)，它等于在 z ~ q_φ(z|xi) 的期望下，某个我称之为 R 的量，它是 x_i 和 z 的函数。重要的是 R 不依赖于 φ。那么我们如何计算这个关于 φ 的导数呢？嗯，我们可以直接使用策略梯度。我在这里特意使用了 J 和 R，只是为了清楚地表明这与我们之前使用策略梯度时的方程形式完全相同。这意味着我们可以通过从 q_φ 中采样 z，然后在这些样本上平均来估计导数 grad J(φ)，而我们平均的量是 grad_φ log q_φ 乘以 R。现在，与策略梯度不同，这些样本不需要与真实世界进行实际交互。这些样本只需要从你的 q 模型中采样，然后在 p 模型下评估对数似然。所以你可以廉价地生成这些样本，这是一个非常合理的方法来计算第一项的梯度。

好的，这个梯度有什么问题呢？花点时间回想一下策略梯度的讲座，并思考一下为什么我们可能想要稍微改进一下这个梯度。我现在告诉你，你可以使用这个策略梯度，使用策略梯度来优化推断网络的参数是完全合理的摊销变分推断实现方式。你可能需要抽取多个样本才能得到一个准确的梯度，但这是一个完全可行的方法，但不是最好的方法。就像我们在策略梯度讲座中学到的那样，这种梯度估计器往往有很高的方差。高方差意味着你的梯度会很嘈杂，或者你需要抽取更多的样本才能得到一个同样准确的梯度，这可能会有点不方便。幸运的是，有一个特殊的技巧可以用于摊销变分推断，这在常规强化学习中通常是不可用的，那就是所谓的重参数化技巧。总的来说，在强化学习中，我们使用策略梯度是因为我们无法通过动力学计算导数，但在摊销变-分推断中，没有未知的动力学，只有 q，而通过 q 的均值和方差计算导数实际上是相当可行的。

所以，这里是我们可以利用这个的技巧。这是 J(φ) 的方程，记住困难的部分是计算这一项的梯度。熵的梯度很容易得到，因为熵是一个用 μ 和 σ 表示的闭合形式方程，你可以使用任何自动微分软件来计算它的导数。困难的部分是计算 J(φ) 的梯度，其中 q_φ 又是这个高斯分布。所以，如果你有一个变量 z 服从高斯分布，你总可以把这个变量重写为一个确定性项 μ_φ(x) 和一个随机项 ε * σ_φ(x) 的和。如果 ε 服从均值为零、方差为一的高斯分布，那么代入这个公式将使 z 对应于均值为 μ、方差为 σ 的高斯分布的样本。所以你实际上是将一个来自零均值单位方差高斯分布的样本，转换成一个均值为 μ、方差为 σ 的样本。

关于这个方程需要注意的一点是，ε 不依赖于 φ。所以，这种写法将 z 表示为一个由 φ 参数化的确定性函数，其随机变量 ε 独立于 φ。这就是为什么我们称之为重参数化技巧，因为我们正在重新参数化随机变量 z，使其成为另一个独立于 φ 的随机变量 ε 的确定性函数。当我们这样做时，我们可以得到一个更好的梯度估计器。所以我们可以做的是，我们可以将我们关于 z 的期望写成关于 ε 的期望，我只是简单地将用 ε 表示的 z 的方程代入 R 中。这是一个严格的等式，而不是一个近似。在 z ~ N(μ, σ^2) 下的期望值等于在 ε ~ N(0, 1) 下的期望值，其中你在 x_i, μ(x_i) + εσ(x_i) 处评估你的 R。

现在我们非常接近能够为 J(φ) 计算一个更好的梯度了，因为这里的 φ 现在只参数化了确定性的量。所以花一分钟想一想，我们如何基于这个关于 ε 的期望写出一个更好的梯度估计器。好的，这里是我们如何估计 J(φ) 相对于 φ 的梯度。首先，从一个零均值单位方差高斯分布中采样 ε，采样 ε1 到 εm。实际上一个样本效果就很好，所以你可以为你的小批量中的每个数据点只生成一个样本，这实际上是我们通常会做的。然后，计算相对于 φ 的梯度，就是对所有样本求平均，平均 grad_φ R(xi, μ_φ(xi) + ε_j σ_φ(xi))。这要求 R 相对于 z 是可微的，当然也要求 μ_φ 和 σ_φ 相对于 φ 是可微的，它们是可微的因为它们是神经网络。我们通常不能在强化学习中使用这个，因为在强化学习中我们不假设我们知道可以计算你的回报的导数，但在这里我们可以，因为 R 只是这个三角模型下的对数概率，它是另一个我们知道的神经网络。所以我们可以计算它相对于 φ 的导数。这个梯度估计器的方差更低，因为我们实际上使用了 R 的导数。我们之前的策略梯度没有使用 R 的导数。所以这是一个更好的梯度估计器，而且大多数自动微分软件，比如 TensorFlow 或 PyTorch，会为你计算这个。你甚至不需要自己去微分 pθ(xi, z)，你只需在自动微分软件中实现它，让自动微分软件处理一切。

所以这实际上是计算导数的一种非常简单的方法。唯一不寻常的是你必须采样这些 ε。否则，它看起来就像任何其他神经网络一样。这是你可以看待它的另一种方式。你可以取你最初的变分下界，即 E_z[log pθ(xi|z) + log p(z)] + H(q)。把它写成三项，所以在 z 下有一个解码器 pθ(xi|z) 的期望，加上一个在 z 下的先验 log p(z) 的期望，再加上熵。第二项本质上是 q_φ(z|x) 和 p(z) 之间 KL 散度的方程。对吧？因为 KL 散度有一个熵项和一个期望值项。如果我们的先验 p(z) 也是高斯分布，那么两个高斯分布之间的 KL 散度有一个方便的解析形式。所以如果你查找两个高斯分布之间的 KL 散度，你会找到一个用它们的均值和方差表示的方程，就像你会找到熵的方程一样。这意味着你不需要任何花哨的梯度估计器来计算这个，你只需写下那个方程，在 TensorFlow 或 PyTorch 中实现它，然后对它调用梯度。所以计算这一项不需要任何随机采样。

所以我们剩下这个，现在我们将对第一项使用重参数化技巧，将其表示为在 ε 下的期望。现在这个方程中的所有东西都是确定性的，除了 ε。所以我们将用单个高斯样本 ε 来近似第一个期望。我们剩下的方程就是这个。现在，这个方程中的所有东西，你都可以直接在你的自动微分软件中编码。log pθ(xi | q_φ(xi) + εσ_φ(xi)) 只是调用 pθ 的神经网络表示，其输入依赖于 μ_φ 和 σ_φ 的神经网络表示。所以这里的所有东西都可以反向传播，而 ε 只是被当作一个常数。第二项，KL 散度，有一个用 q 和 p(z) 的均值和方差表示的闭合形式方程。

所以你可以这样想，你有你的由 φ 参数化的推断网络，它输入 xi，并上传两个量，一个均值 μ_φ(xi) 和一个 σ_φ(xi)。然后你从你的零均值单位方差高斯分布中采样你的 ε，并将其与 μ 和 σ 结合起来得到你的 z。然后 z 被输入到 pθ(xi|z) 中，以产生一个关于 x 的分布。这是一个完整的计算图，这里的所有东西都有已知的导数。所以你可以对整个东西相对于 φ 和 θ 进行反向传播。这意味着你实际上可以在你的自动微分软件中编写这个，并对整个东西调用梯度，这将给你 L 相对于 θ 和 φ 的导数。

那么这个重参数化技巧与策略梯度相比如何呢？这是变分下界第一项（不是熵项）导数的策略梯度估计器。这可以处理离散和连续的潜变量。这实际上不关心 z 是连续数还是离散数。所以 q 不必是高斯的，q 可以是任何分布，只要它有明确定义的对数概率。但它方差很高，通常需要为每个 x 抽取多个样本和较小的学习率。重参数化技巧由这个方程给出，只适用于连续潜变量，因为你必须能够取 R 相对于 z 的导数。如果 z 是离散的，那个导数没有明确定义。然而，它实现起来非常简单，通常方差很低，每个数据点只需要一个样本就能很好地工作。所以，如果你想知道在你的摊销变分推断实现中使用哪种梯度估计器，如果你有连续变量，使用重参数化技巧可能是个好主意。如果你有离散变量，那么你可能需要使用策略梯度风格的估计器。

---

好的，在今天讲座的最后一部分，我们将讨论如何基于我们在其他部分讨论的原则来实例化实际的深度学习模型。我们还将简要讨论这些类型的模型如何在深度学习中应用的一些例子。我们将在后续的讲座中更广泛地讨论变分推断在深度学习中的作用，但今天我们将只关注用摊销变分推断训练的生成模型的直接应用。让我们首先从可以说是最基本的摊销变分推断模型开始，那就是变分自编码器。

在变分自编码器中，我们将使用一个潜变量或潜向量 Z 来建模某种输入 X，这通常是一张图像。我们将像以前一样有一个编码器和一个解码器。这将是我们所概述原则的最直接的实例化。我们的编码器是一个深度神经网络，它接收 X 并产生 Z 上的均值和方差。所以编码器定义了一个高斯分布 q(Z|X)，其中均值和方差由神经网络的输出给出。解码器是一个神经网络，它将接收 Z 并产生观测变量 X 上的均值和方差。所以，想法是，如果我们想要采样，我们会从先验分布中生成一个 z，这个先验分布通常固定为零均值单位方差高斯分布，然后我们会使用解码器 pθ(X|Z) 来解码它，这基本上意味着通过解码器神经网络运行它，然后从得到的关于输入的高斯分布中采样。例如，这可以用来构建一个关于图像的生成模型。在这里你可以看到一些从在人脸图片上训练的变分自编码器中抽取的样本例子。这里的 x 是像素图像，所以它们是像素数组，而 z 将是维度为 64 或 128 的潜向量。

变分自编码器的架构基本上遵循我们在前面章节中讨论的内容。所以，如果我们想把它设置成一个用于训练的计算图，我们会有一个带参数 φ 的编码器 Q，它接收图像 xi，那个神经网络会输出均值 μ_φ(xi) 和方差 σ_φ(xi)。然后我们会从一个零均值单位方差分布中采样一些噪声，然后我们会通过取均值并加上噪声 ε 乘以标准差 σ 来形成最终的 Z。然后我们可以将 Z 通过解码器 pθ(x|Z)，那会产生图像。训练过程使用重参数化技巧来训练整个东西，它被训练来最大化变分下界，同时优化解码器的参数 θ 和编码器的参数 φ。

所以目标最终只是整个数据集上解码器输出图像的对数概率的平均值，其中 Z 的均值和标准差来自编码器。所以我们使用重参数化技巧来将这个第一个对数概率项的误差从解码器一直反向传播到编码器，从而同时训练 θ 和 φ。当然，我们还必须减去 KL 散度正则化项，这基本上考虑了先验。这里的 KL 散度是在编码器分布 q_φ(Z|xi) 和先验 p(Z) 之间。由于 q_φ(Z|xi) 是高斯的，而先验 p(Z) 按照惯例是零均值单位方差高斯分布，这个 KL 散度实际上可以用两个高斯分布之间 KL 散度的解析公式以闭合形式计算。所以，这做起来非常直接。

好的，这就是变分自编码器。变分自编码器允许我们做的是，它允许我们训练一个表示某些输入的潜变量模型，这些输入通常被认为是图像。那么我们如何使用变分自编码器呢？我们可以通过在一堆图像上训练来使用变分自编码器，并得到一个潜变量表示，我们可以从中采样，例如，通过从先验 p(z) 中生成一个样本，然后使用解码器 p(x|z) 来解码那个样本。

那么，这为什么能起作用呢？直觉上，数学上的东西都来自前面的章节。这之所以能起作用的直觉是，证据下界，也就是变分下界，会尝试使数据集中图像的可能性尽可能高，给定我们从编码器为那些图像获得的 Z。但编码器也被训练得要接近先验，这意味着如果编码器产生的 Z 与你从零均值单位方差先验分布中采样得到的 Z 类型相差太大，那么编码器将为此付出沉重的代价。所以编码器有非常强的动机去产生看起来像从先验中采样的 Z，因为如果它不这样做，那么 KL 散度项会非常大，那会带来很大的惩罚。

这解释了为什么从编码器中采样的样本会落在那个单位方差先验内。它本身并没有解释为什么任何从单位方差先验中采样的样本都会接近被编码的东西。对此的论点与效率有关。问题是，编码器也希望有一个相当高的方差，希望方差接近于一，因为那是先验所具有的。所以编码器希望在潜空间的使用上非常节俭，这意味着它真的想使用潜空间的每一部分。如果潜空间的某一部分未被使用，编码器最好扩展到那些空间并增加其方差，以便其方差可以更接近于一。结果，你最终在这些 Z 和 X 之间建立了一个映射，其中几乎你从单位方差先验中采样的每个 Z 都会映射到某个有效的 X。这意味着你既可以拿图像进行编码，得到它们的表示 Z，也可以从先验中采样，解码并得到合理的采样图像，这实际上就是这张幻灯片上的动画所示。

好的，让我们谈谈这个在深度学习中的一些应用。这类变分自编码器通常在深度学习中用于表示学习的目的。现在，这不要与处理部分可观测性混淆，我们稍后会更多地讨论部分可观测性。现在我们只讨论 Z 作为单个状态的表示。所以我们仍然假设一切都是完全可观测的，即状态包含推断动作所需的所有信息，都是马尔可夫的，以及所有那些好东西。但状态观测在某种程度上是复杂的，例如，它们可能对应于 Atari 游戏中的图像。在这种情况下，使用变分自编码器可能实际上是有益的，不是为了采样额外的图像，而只是为了获得那些图像的更好表示。所以现在我们的解码器将被训练来生成给定 Z 的状态，但如果状态是图像，它基本上与我们之前看到的模型类型完全相同。

例如，我们可以做的是，我们可以对我们重放缓冲区中的所有状态训练一个 VAE，比如对于一个 Atari 游戏，然后当我们运行 RL 时，我们会用 Z 代替原始状态 S 作为 RL 的状态表示。然后我们就重复这个过程。现在，为什么这是个好主意？为什么我们期望这些 Z 比状态本身是更好的状态表示？想法是，变分自编码器因为它学习了这些满足独立高斯先验的 Z 表示，意味着 Z 的每个维度都与其他每个维度独立，应该比图像本身更能导致底层变异因素的更好解耦。想象一下这张来自 Montezuma's Revenge 右上角的图像。构成那个角色，也就是玩家角色的单个像素，彼此之间非常相关，因为虽然玩家角色由许多像素组成，但那些像素作为一个整体移动。所以对于下游的 RL 来说，玩家每个像素的颜色是什么并不那么重要，重要的是那团像素移动的整体位置和可能的速度。所以有一些构成图像的底层变异因素，在这种情况下，是玩家角色、骷髅和钥匙的位置、方向和速度，它们代表了比图像像素本身更简洁、更有用的图像表示。这些是底层的变异因素，我们直观上想做的是，取这张图像并解耦底层的变异因素，使得每个相当独立的变异因素，比如玩家角色和骷髅的位置，构成新学习的状态表示的不同维度。这或多或少是变分自编码器试图做的事情。

我在左下角展示的这些图片，来自 Higgins 等人 2017 年的一篇著作，展示了当不同类型的变分自编码器在具有已知底层变异因素的数据上训练时会发生什么。在左边，你可以看到家具物品的例子，它们在形状、大小和方向上有所不同。在中间，你可以看到在光照方向、面部表情和面部方向上有所不同的面孔。在右边是来自 CelebA 数据集的自然人脸图像，它们在年龄、种族、发型等方面有所不同。作者在这些图片中做的是，在每一行中，他们都在左侧和右侧的图像之间进行插值。现在，如果你在像素空间中插值图像，它们实际上不会沿着自然的变异因素进行插值。例如，左上角第一行的椅子将其方向从朝右的椅子插值为朝左的椅子，这与你实际插值像素颜色本身得到的结果不同。这意味着底层的表示 Z 实际上捕捉了环境中的变异因素，这就是我们期望好的 VAE 能做到的。现在我要说，在实践中，VAE 在多大程度上实际捕捉了这些图像中真实的变异因素当然是有争议的，但那是它们试图做的事情。

这个想法的一个实际例子，例如，在像作业三中的 Q-learning 算法的背景下，可能看起来是这样的：就像在常规 Q-learning 中一样，你使用你的探索策略从你的环境中收集一个转换，并将其添加到你的重放缓冲区。然后你使用从重放缓冲区中采样的一个批次，使用变分下界来更新你的解码器和编码器。这改善了表示 Z。然后你用重放缓冲区中的一个批次来更新你的 Q 函数，但 Q 函数现在接收由编码器产生的潜表示 Z 作为输入，而不是原始图像。我们期望这是一个更容易的过程，因为现在输入到 Q 函数的表示比原始图像是更好的表示。然后我们重复这个过程。值得注意的是，这为我们提供了一种使用先验数据的好方法。如果我们有合理的 Atari 试验的先验图像，我们可以用它来预训练变分自编码器，这将为我们提供一个可以用于 RL 的良好表示，或者当然我们可以在进行中动态学习它。

好的，我将要讨论的下一类模型是条件模型。条件模型就像之前的 VAE 一样，只是现在我们的目标不是建模图像的分布 p(X)，而是建模某个条件分布 p(Y|X)。想法是，可能是 p(Y|X) 复杂且多模态，所以我们实际上不关心 X 是如何分布的，我们只关心给定 X 的 Y 是如何分布的。但我们希望那个分布 p(Y|X) 非常具有表达力。为了处理这个问题，我们只需将条件信息 X 放在编码器和解码器的条件栏的右边。我们也可以选择性地将其放在先验本身的条件栏的右边，尽管我们不必这样做。而且条件模型通常只使用一个无条件的先验。所以实际上的改变只是我们仍然有一个编码器网络，我们仍然有一个解码器网络，但现在它们都将这个 X，也就是条件信息，作为输入。

当然，一个非常经典的用法是用于策略。所以 y 可能是动作，x 可能是观测。现在你可以把这看作是一个策略 p(y|x)，它额外接收一个噪声样本作为输入，使其成为 p(y|x, z)。那么，现在给大家一个小测验，这个 p(y|x, z) 是编码器还是解码器？花点时间想一想。答案当然是，它是解码器，对吧？因为解码器是接收 z 并产生变量的东西。所以我们这里建模的变量是 y，所以 p(y|x, z) 是解码器。基本上一切都和以前一样，只是我们现在生成 Y，而且编码器和解码器都接收 X 作为输入。先验可以选择性地依赖于 X，但实际上没有必要这样做。所以现在的架构是，你有你的带参数 f 的编码器，它接收 X 和 Y，并产生 Z 的均值和标准差 μ 和 σ。我们仍然有噪声，我们仍然将 μ 加上 ε 乘以 σ 来得到我们的 Z，但现在我们的解码器接收 Z 和 X 作为输入，并产生 pθ(y|x, z)。就像以前一样，整个东西都用幻灯片顶部显示的变分下界进行训练。所以即使模型是条件的，实际上改变的很少。

然后在测试时，我们可以简单地从先验中采样 Z，然后使用 p(y|x, z) 来解码它。这些类型的条件变分自编码器最常用于表示多模态策略。这些多模态策略可以与强化学习一起使用，尽管它们更常用于模仿学习。为什么会这样？我们实际上在之前的一些讲座中讨论过这个原因。在 RL 中，我们的目标通常是学习一个接近最优的策略，我们知道完全可观测的 MDP 通常会有确定性的最优策略。但在模仿学习中，我们可能需要模仿多模态和非马尔可夫的人类行为，在这种情况下，拥有一个多模态策略可能非常重要，就像课程开始时那个树的例子，你可以从左边或右边绕过树，但你真的不想折中，这意味着如果人类有时向左走，有时向右走，你真的希望有一个多模态策略来表示这个分布，这样你就不会无意中走到中间去。

这里有几篇论文的例子，它们使用了这类条件变分自编码器来表示多模态策略。在《从游戏中学习潜变量计划》（Learning Latent Plans from Play）这篇我们之前讨论过的论文中，该方法包含一个相当复杂的变分自编码器，它会建模自由形式的人类行为数据，人类基本上是在这个机器人环境中玩耍，其中条件 VAE 实际上甚至不表示单个动作，而是表示动作序列，论文称之为计划。想法是，人类可能会执行许多不同的动作组合来在任意两点之间移动，这里的潜变量解释了即使对于相同的起点和终点，这些选择之间的差异。

这是另一篇使用条件变分自编码器的论文的视频。这是一个真实的机器人系统，一个双臂操纵器，正在学习相当复杂的任务，例如给脚穿鞋。这里的变分自编码器是条件的，在这种情况下，编码器和解码器实际上都是用 Transformer 表示的。所以架构有点复杂，这里的“编码器”和“解码器”这个词有点被重载了。左边的橙色部分是 VAE 编码器，它接收动作，实际上它接收动作序列以提高建模精度，但它也可以做单个动作并将它们编码成一个潜变量 Z，使用一个 Transformer。然后解码器实际上由一个 Transformer 编码器组成，它接收来自机器人上多个摄像头的输入和潜变量 Z，然后将它们解码成未来动作的序列。所以这是一个更复杂的模型，它将条件 VAE 与 Transformer 结合起来。长话短说，条件变分自编码器在模仿学习中为表示比常规高斯分布可能表示的复杂得多的策略找到了很多应用。

好的，我们将讨论的最后一类模型实际上是我们之前在基于模型的 RL 讲座中讨论过的模型，尽管那时我们还不知道变分自编码器，所以我们不得不在一个非常高的层次上描述这些模型，现在我将更详细地介绍这些模型。这里的目标将是处理部分可观测的系统。在部分可观测的系统中，我们不知道状态，我们只有观测序列，我们将学习状态空间模型，其中状态 Z 实际上将是变分自编码器中的潜状态。假设我们有这些图像观测，我们如何将序列级问题表述为变分自编码器？

我们处于部分可观测的设置中，这就是为什么我使用 o 而不是 s，我希望以某种方式将序列建模问题（我有 Z 和 O）强制转换为一个我有一个单一潜向量 Z 和一个单一观测向量 X 的模型。我必须做出的选择是，Z 是什么，X 是什么，先验的形式是什么，解码器的形式是什么，以及编码器的形式是什么。所以我必须基本上把这些部分连接起来，这实际上非常不平凡。这个模型将比我们之前描述的模型复杂得多，因为现在潜变量本身就是一个序列。不是每个时间步 Z1, Z2, Z3 都是 VAE 中的一个不同的潜变量，而是整个 Z 的序列将是潜变量。所以 X，即观测，是 O 的序列，一个完整的轨迹。Z 是在单个时间步 Z1, Z2, ..., ZT 的 Z 的序列。所以变分自编码器现在是一个序列级的变分自编码器。它有时被称为序列 VAE。

事实上，这个序列 VAE 实际上是条件的，它是一个序列级的条件 VAE。它以什么为条件？花点时间想一想。答案当然是，它以我们没有建模的唯一部分为条件，那就是动作。所以它实际上是一个条件 VAE，其中条件信息，即条件栏右边的东西，是动作序列。观测 X 是观测时间步的序列，而潜变量 Z 是在单个时间步的 Z 的序列。我们的先验是什么？嗯，我们的先验现在将更有结构，因为我们在 Z 上有这些动力学。所以我们不希望 Z 的不同维度是独立的，就像在常规 VAE 中那样。我们希望 Z 的不同维度彼此相关，我们希望考虑到动力学，这些将是先验的一部分。所以注意，我们的先验现在实际上是条件的，先验 p(Z) 由 p(Z1) 的乘积给出，p(Z1) 可以是一个零均值单位方差高斯分布，乘以 p(ZT+1|ZT, at) 的乘积。而动力学部分通常也是为序列 VAE 学习的。所以它是先验的一部分，但是是学习的。所以第一步是高斯分布，但其他步骤不是。你可以想象 Z 在潜空间中形成一个轨迹，但记住，除了 Z1，Z2, Z3, Z4 等通常不服从单位高斯分布，它们的分布取决于之前的 z。

我们的解码器将把 Z 解码成 O，解码器通常是每个时间步独立的。原因是我们希望 Z 总结那个时间步所需的所有信息，我们希望 Z 构成一个马尔可夫状态空间。所以我们的解码器将独立地解码每个时间步。所以它只是所有时间步上 p(ot|zt) 的乘积。那么我们的编码器呢？我们的编码器也能独立吗？总的来说，答案当然是否定的，因为如果我们处于部分可观测的设置中，关键在于 O2 没有关于底层状态 Z2 的足够信息。所以，在序列 VAE 中，我们的编码器通常是实际上最复杂的部分。编码器将为我们提供给定所有先前 O 的 ZT 的分布。

表示编码器有很多不同的方法。编码器也可以考虑之前的 Z，它实际上可以以许多不同的方式构建。我们在基于模型的 RL 讲座中讨论了一些构建编码器的方法，我接下来要提到的论文实际上都有不同的编码器架构。我不会详细介绍所有这些编码器如何构建，最简单的想像是这里的独立编码器。但请注意，这个独立编码器将所有疾病视为彼此独立，但将它们视为依赖于整个动作序列。你可以想象，产生单个 Z 的过程对应于查看 O 的历史并推断 Z 现在应该是什么。如果我们想要一个部分可观测环境中的状态估计器，这实际上非常自然，我们可能会根据到目前为止的观测历史来推断状态的分布。当然，如果我们也考虑 ZT-1，我们可以有一个更好的编码器。所以我们可以有一个编码器 q(ZT|ZT-1, o1..T)，那会是一个更好的编码器。

不同的工作探索了各种不同的编码器，而使用变分推断框架的全部意义在于，我们实际上在使用何种编码器方面有很大的灵活性。一些编码器会比其他编码器更好，因为它们能导致更准确的后验，但所有这些都构成了有效的变分下界。在这个例子中，解码器将是独立的，意味着每个 z 将被解码成那个时间步的图像，并且给定 z，图像彼此独立。但当然，由于先验，z 都是紧密耦合的。编码器将接收图像的历史并产生当前 z 的分布，我们可能会用某种序列模型（如 LSTM 或 Transformer）来表示它。你可以看到，一旦我们将编码器表示为序列模型，也很容易输入之前的 z。

好的，这里是这些类型的序列模型在深度学习中的一些应用。一个应用领域是学习状态空间模型，然后在状态空间中进行规划。已经有很多论文这样做了。最早的一篇是这篇名为《嵌入以控制》（Embed to Control）的论文，其想法是学习各种简单系统（如倒立摆、质点等）的潜空间嵌入。所以在这里，他们正在可视化状态空间，这是一个倒立摆的状态空间，你可以在右侧看到，他们在训练过程中可视化了其几何形状。他们提出的观点是，算法实际上是从像素中推断出真实的自由度，而这些自由度对算法来说是未知的。

这是一个更复杂的任务，这里的潜空间是三维的，你可以看到它有点将图像展开到这个潜空间中，这类似于系统真实状态空间的几何形状。这里有一些可视化，这是一个简单的摆锤环境，右边他们可视化了生成结果，左边是真实图像。这是一个更复杂的倒立摆平衡任务。当然，这是一项更早的工作，它使用了非常原始的图像，从那时起，事情已经发展了很长一段路。这项工作大约是 7 年前的，当时这类模型被用来控制真实机器人。所以这里的序列 VAE 实际上是在预测机器人的图像，然后策略控制它堆叠这些乐高积木。后来，这些东西也被应用到许多基于像素的基准测试任务中，并且与各种不同类型的编码器以及各种规划算法（从 LQR 到随机采样以及其他种类的轨迹优化器）配合得很好。

另一类序列模型方法使用状态空间模型来推断一个状态空间，然后实际上在该状态空间中运行 RL。这里有一个例子，叫做随机潜变量行动者-评论家（Stochastic Latent Actor-Critic）。它使用了一个 Q 函数行动者-评论家算法，即软行动者-评论家（Soft Actor-Critic），使其能够处理图像观测。所以这里的序列 VAE 被用来为行动者-评论家算法提取一个表示。你可以在这里看到一些来自系统的真实 rollout，然后是一些来自 VAE 的样本，表明 VAE 实际上正在学习生成看起来非常像真实系统的视频。

这里是另一篇论文，它做了一件非常类似的事情，使用了一个行动者-评论家算法和实际的短视界 rollout。它实际上是规划和 RL 的结合，使用序列 VAE 来表示潜状态。好的，非常感谢大家。