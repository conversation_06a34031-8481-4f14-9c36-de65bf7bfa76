#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
数据可视化器 - 创建瞳孔数据的时间曲线图
"""

import os
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import matplotlib.font_manager as fm
import matplotlib.colors as mcolors
import seaborn as sns
import logging
from analysis.history_code.config import FIGURES_DIR, PLOT_CONFIG, RATING_TYPES, PLOT_RATINGS, RATING_VALUES_TO_PLOT

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger('visualizer')

# 设置中文字体
plt.rcParams['font.sans-serif'] = [PLOT_CONFIG['chinese_font'], 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

class PupilVisualizer:
    def __init__(self):
        self.colors = PLOT_CONFIG['colors']
        self.colormap = PLOT_CONFIG['colormap']
        
    def prepare_time_series_data(self, trial_pupil_results, grouped_data, rating_type):
        """准备时间序列数据"""
        logger.info(f"准备{rating_type}的时间序列数据")
        
        time_series_data = {}
        
        # 获取该评分类型的分组
        rating_groups = grouped_data.get(rating_type, {})
        
        for rating_value in RATING_VALUES_TO_PLOT:
            if rating_value not in rating_groups:
                continue
                
            trials_for_rating = rating_groups[rating_value]
            all_pupil_data = []
            
            for trial in trials_for_rating:
                trial_num = trial['trial_num']
                if trial_num in trial_pupil_results:
                    pupil_data = trial_pupil_results[trial_num]['pupil_data']
                    if not pupil_data.empty and 'pupil_size' in pupil_data.columns:
                        # 标准化时间轴（转换为秒）
                        time_data = pupil_data['relative_time'].values / 1000.0  # 转换为秒
                        pupil_values = pupil_data['pupil_size'].values
                        
                        # 过滤有效数据
                        valid_mask = ~np.isnan(pupil_values)
                        if np.sum(valid_mask) > 0:
                            all_pupil_data.append({
                                'time': time_data[valid_mask],
                                'pupil': pupil_values[valid_mask],
                                'trial_num': trial_num
                            })
            
            if all_pupil_data:
                time_series_data[rating_value] = all_pupil_data
                logger.info(f"评分{rating_value}: {len(all_pupil_data)}个有效试次")
        
        return time_series_data
    
    def interpolate_to_common_timebase(self, time_series_data, time_resolution=0.01):
        """将所有数据插值到共同的时间基准"""
        logger.info("插值到共同时间基准")
        
        # 找到所有数据的时间范围
        all_times = []
        for rating_value, trials_data in time_series_data.items():
            for trial_data in trials_data:
                all_times.extend(trial_data['time'])
        
        if not all_times:
            return {}
        
        min_time = min(all_times)
        max_time = max(all_times)
        
        # 创建共同的时间轴
        common_time = np.arange(min_time, max_time + time_resolution, time_resolution)
        
        interpolated_data = {}
        
        for rating_value, trials_data in time_series_data.items():
            interpolated_trials = []
            
            for trial_data in trials_data:
                try:
                    # 插值到共同时间轴
                    from scipy.interpolate import interp1d
                    f = interp1d(trial_data['time'], trial_data['pupil'], 
                               kind='linear', bounds_error=False, fill_value=np.nan)
                    interpolated_pupil = f(common_time)
                    
                    interpolated_trials.append({
                        'time': common_time,
                        'pupil': interpolated_pupil,
                        'trial_num': trial_data['trial_num']
                    })
                except Exception as e:
                    logger.warning(f"插值失败 - 试次{trial_data['trial_num']}: {e}")
                    continue
            
            if interpolated_trials:
                interpolated_data[rating_value] = {
                    'time': common_time,
                    'trials': interpolated_trials
                }
        
        return interpolated_data
    
    def calculate_mean_and_std(self, interpolated_data, baseline_correct=True):
        """计算均值和标准差，支持基线校正"""
        logger.info("计算均值和标准差")

        stats_data = {}

        for rating_value, data in interpolated_data.items():
            common_time = data['time']
            trials = data['trials']

            # 收集所有试次的瞳孔数据
            all_pupil_data = []
            for trial in trials:
                pupil_data = np.array(trial['pupil'])

                # 基线校正：让每条曲线从0开始
                if baseline_correct and len(pupil_data) > 0:
                    # 找到第一个非NaN值作为基线
                    baseline_idx = 0
                    while baseline_idx < len(pupil_data) and np.isnan(pupil_data[baseline_idx]):
                        baseline_idx += 1

                    if baseline_idx < len(pupil_data):
                        baseline_value = pupil_data[baseline_idx]
                        pupil_data = pupil_data - baseline_value
                        logger.debug(f"评分{rating_value}试次基线校正: 基线值={baseline_value:.2f}")

                all_pupil_data.append(pupil_data)

            if all_pupil_data:
                # 转换为numpy数组
                pupil_matrix = np.array(all_pupil_data)

                # 计算均值和标准差（忽略NaN值）
                mean_pupil = np.nanmean(pupil_matrix, axis=0)
                std_pupil = np.nanstd(pupil_matrix, axis=0)

                stats_data[rating_value] = {
                    'time': common_time,
                    'mean': mean_pupil,
                    'std': std_pupil,
                    'n_trials': len(trials),
                    'baseline_corrected': baseline_correct
                }

                if baseline_correct:
                    logger.info(f"评分{rating_value}: 基线校正后均值范围 {np.nanmin(mean_pupil):.2f} 到 {np.nanmax(mean_pupil):.2f}")

        return stats_data
    
    def get_colors_for_ratings(self, rating_values):
        """根据评分值获取viridis配色"""
        import matplotlib.pyplot as plt

        # 获取评分值的数量
        n_ratings = len(rating_values)
        if n_ratings == 0:
            return []

        # 使用viridis配色映射
        cmap = plt.get_cmap(self.colormap)

        # 为每个评分值分配颜色
        colors = []
        for i, rating in enumerate(sorted(rating_values)):
            # 将评分值映射到0-1范围
            color_position = i / max(1, n_ratings - 1) if n_ratings > 1 else 0.5
            colors.append(cmap(color_position))

        return colors

    def create_pupil_time_plot(self, stats_data, rating_type, show_std=True, save_path=None):
        """创建瞳孔时间曲线图"""
        plot_type = "均值+标准差" if show_std else "仅均值"
        logger.info(f"创建{rating_type}的瞳孔时间曲线图 ({plot_type})")

        fig, ax = plt.subplots(figsize=PLOT_CONFIG['figure_size'], dpi=PLOT_CONFIG['dpi'])

        # 获取所有评分值并排序
        rating_values = sorted(stats_data.keys())
        colors = self.get_colors_for_ratings(rating_values)

        for i, rating_value in enumerate(rating_values):
            data = stats_data[rating_value]
            color = colors[i]

            # 绘制均值线
            ax.plot(data['time'], data['mean'],
                   color=color, linewidth=PLOT_CONFIG['line_width'],
                   alpha=PLOT_CONFIG['alpha'],
                   label=f'{RATING_TYPES.get(rating_type, rating_type)} = {rating_value} (n={data["n_trials"]})')

            # 绘制标准差区域（如果需要）
            if show_std and PLOT_CONFIG['show_std']:
                ax.fill_between(data['time'],
                              data['mean'] - data['std'],
                              data['mean'] + data['std'],
                              color=color, alpha=PLOT_CONFIG['std_alpha'])

        # 设置图形属性
        ax.set_xlabel('时间 (秒)', fontsize=PLOT_CONFIG['font_size'])

        # 根据基线校正状态设置Y轴标签
        if PLOT_CONFIG['baseline_correct']:
            ax.set_ylabel('瞳孔大小变化 (像素)', fontsize=PLOT_CONFIG['font_size'])
        else:
            ax.set_ylabel('瞳孔大小 (像素)', fontsize=PLOT_CONFIG['font_size'])

        # 设置标题
        title_suffix = " (均值±标准差)" if show_std else " (均值)"
        baseline_suffix = " - 基线校正" if PLOT_CONFIG['baseline_correct'] else ""
        ax.set_title(f'{RATING_TYPES.get(rating_type, rating_type)}与瞳孔变化时间曲线{title_suffix}{baseline_suffix}',
                    fontsize=PLOT_CONFIG['font_size'] + 2)

        ax.legend(fontsize=PLOT_CONFIG['font_size'] - 1)
        ax.grid(True, alpha=0.3)

        # 设置坐标轴
        ax.tick_params(labelsize=PLOT_CONFIG['font_size'] - 1)

        plt.tight_layout()

        # 保存图形
        if save_path is None:
            suffix = PLOT_CONFIG['mean_std_suffix'] if show_std else PLOT_CONFIG['mean_only_suffix']
            save_path = os.path.join(FIGURES_DIR, f'{rating_type}_pupil_timecourse{suffix}.{PLOT_CONFIG["save_format"]}')

        plt.savefig(save_path, dpi=PLOT_CONFIG['dpi'], bbox_inches='tight')
        logger.info(f"图形已保存到: {save_path}")

        return fig, ax
    
    def create_all_plots(self, trial_pupil_results, grouped_data):
        """创建所有评分类型的图形"""
        logger.info("创建所有图形")
        
        created_plots = []
        
        for rating_type in PLOT_RATINGS:
            if rating_type not in grouped_data:
                logger.warning(f"未找到{rating_type}的分组数据")
                continue
            
            try:
                # 准备时间序列数据
                time_series_data = self.prepare_time_series_data(
                    trial_pupil_results, grouped_data, rating_type)
                
                if not time_series_data:
                    logger.warning(f"{rating_type}没有有效的时间序列数据")
                    continue
                
                # 插值到共同时间基准
                interpolated_data = self.interpolate_to_common_timebase(time_series_data)
                
                if not interpolated_data:
                    logger.warning(f"{rating_type}插值后没有有效数据")
                    continue
                
                # 计算统计量（使用基线校正）
                stats_data = self.calculate_mean_and_std(interpolated_data,
                                                       baseline_correct=PLOT_CONFIG['baseline_correct'])
                
                if not stats_data:
                    logger.warning(f"{rating_type}没有有效的统计数据")
                    continue
                
                # 创建图形
                if PLOT_CONFIG['create_both_plots']:
                    # 创建仅均值图
                    fig_mean, ax_mean = self.create_pupil_time_plot(stats_data, rating_type, show_std=False)
                    created_plots.append({
                        'rating_type': rating_type,
                        'plot_type': 'mean_only',
                        'figure': fig_mean,
                        'axes': ax_mean,
                        'stats_data': stats_data
                    })

                    # 创建均值+标准差图
                    fig_std, ax_std = self.create_pupil_time_plot(stats_data, rating_type, show_std=True)
                    created_plots.append({
                        'rating_type': rating_type,
                        'plot_type': 'mean_std',
                        'figure': fig_std,
                        'axes': ax_std,
                        'stats_data': stats_data
                    })

                    plt.show()
                    plt.show()
                else:
                    # 只创建一种图（根据配置决定是否显示标准差）
                    fig, ax = self.create_pupil_time_plot(stats_data, rating_type, show_std=PLOT_CONFIG['show_std'])
                    created_plots.append({
                        'rating_type': rating_type,
                        'plot_type': 'single',
                        'figure': fig,
                        'axes': ax,
                        'stats_data': stats_data
                    })

                    plt.show()
                
            except Exception as e:
                logger.error(f"创建{rating_type}图形时出错: {e}")
                continue
        
        logger.info(f"成功创建了{len(created_plots)}个图形")
        return created_plots

if __name__ == "__main__":
    visualizer = PupilVisualizer()
    # 测试代码可以在这里添加
