#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
PFE标定数据分析工具
用于分析EDF文件中的PFE标定数据，提取每个网格点的瞳孔信息
"""

import os
import sys
import numpy as np
import pandas as pd
import logging
from typing import Dict, List, Tuple, Optional

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from analysis.preprocess_edf import EyeDataPreprocessor
from pfe.config import *

# 配置日志
logging.basicConfig(
    level=getattr(logging, LOG_LEVEL),
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger('PFE数据分析')

class PFEDataAnalyzer:
    """PFE标定数据分析器"""
    
    def __init__(self):
        """初始化分析器"""
        self.preprocessor = EyeDataPreprocessor(
            smooth_window=1,  # 不进行平滑，保持原始数据
            blink_interpolation_window=0,  # 不进行眨眼插值
            interpolation_method='linear',
            min_pupil_size=MIN_PUPIL_SIZE,
            max_pupil_size=MAX_PUPIL_SIZE,
            sampling_rate=SAMPLING_RATE,
            velocity_threshold=VELOCITY_THRESHOLD,
            eye_selection='binocular'
        )
    
    def analyze_pfe_calibration_data(self, calibration_dir: str) -> str:
        """
        分析PFE标定数据

        Args:
            calibration_dir: 标定数据目录路径，包含EDF文件和CSV文件

        Returns:
            str: 更新后的标定数据文件路径
        """
        try:
            logger.info(f"开始分析PFE标定数据...")
            logger.info(f"标定目录: {calibration_dir}")

            # 1. 自动查找EDF和CSV文件
            edf_files = [f for f in os.listdir(calibration_dir) if f.endswith('.edf')]
            csv_files = [f for f in os.listdir(calibration_dir) if f.endswith('.csv') and 'calibration' in f and 'analyzed' not in f]

            if not edf_files:
                logger.error(f"在目录 {calibration_dir} 中未找到EDF文件")
                return ""

            if not csv_files:
                logger.error(f"在目录 {calibration_dir} 中未找到标定CSV文件")
                return ""

            edf_path = os.path.join(calibration_dir, edf_files[0])
            calibration_csv_path = os.path.join(calibration_dir, csv_files[0])

            logger.info(f"EDF文件: {edf_path}")
            logger.info(f"标定文件: {calibration_csv_path}")

            # 2. 加载标定配置数据
            calibration_df = pd.read_csv(calibration_csv_path, encoding='utf-8-sig')
            logger.info(f"加载标定配置: {len(calibration_df)} 个点")

            # 3. 加载EDF数据
            samples, events, messages = self.preprocessor.load_edf_data(edf_path)
            if samples is None:
                logger.error("无法加载EDF数据")
                return ""
            
            # 4. 提取PFE标定消息
            if messages is None:
                logger.error("无法加载消息数据")
                return ""

            pfe_messages = self._extract_pfe_messages(messages)
            logger.info(f"提取PFE消息: {len(pfe_messages)} 条")
            
            # 4. 分析每个标定点的瞳孔数据
            updated_calibration_data = []
            for _, row in calibration_df.iterrows():
                point_index = row['point_index']
                screen_x = row['screen_x']
                screen_y = row['screen_y']
                
                # 提取该点的瞳孔数据
                point_pupil_data = self._extract_point_pupil_data(
                    samples, pfe_messages, point_index, screen_x, screen_y
                )
                
                if point_pupil_data:
                    # 更新标定数据
                    updated_row = row.copy()
                    updated_row.update(point_pupil_data)
                    updated_calibration_data.append(updated_row)
                    
                    logger.info(f"点 {point_index}: 瞳孔左眼={point_pupil_data.get('pupil_left', 'N/A'):.1f}, "
                              f"右眼={point_pupil_data.get('pupil_right', 'N/A'):.1f}")
                else:
                    logger.warning(f"点 {point_index} 数据提取失败")
            
            # 5. 保存更新后的标定数据
            if updated_calibration_data:
                updated_df = pd.DataFrame(updated_calibration_data)
                
                # 生成新的文件名
                base_name = os.path.splitext(calibration_csv_path)[0]
                updated_path = f"{base_name}_analyzed.csv"
                
                # 保存文件
                updated_df.to_csv(updated_path, index=False, encoding='utf-8-sig')
                logger.info(f"更新后的标定数据已保存: {updated_path}")
                
                # 生成分析报告
                self._generate_analysis_report(updated_df, updated_path)
                
                return updated_path
            else:
                logger.error("没有成功分析的标定点")
                return ""
                
        except Exception as e:
            logger.error(f"分析PFE标定数据失败: {e}")
            return ""
    
    def _extract_pfe_messages(self, messages: pd.DataFrame) -> pd.DataFrame:
        """提取PFE相关的消息"""
        try:
            # 检查消息字段名称
            message_col = 'message' if 'message' in messages.columns else 'text'
            if message_col not in messages.columns:
                logger.error(f"消息DataFrame中未找到消息字段，可用字段: {list(messages.columns)}")
                return pd.DataFrame()

            # 筛选PFE标定相关的消息
            pfe_mask = messages[message_col].str.contains('POINT_START|POINT_END|PFE_CALIBRATION', na=False)
            pfe_messages = messages[pfe_mask].copy()

            # 解析消息内容，考虑EVENT_xxx前缀
            pfe_messages['message_type'] = pfe_messages[message_col].str.extract(r'(?:EVENT_\d+\s+)?(POINT_START|POINT_END|PFE_CALIBRATION_?\w*)')

            # 保存原始消息文本用于后续解析
            pfe_messages['text'] = pfe_messages[message_col]

            return pfe_messages.sort_values('time')

        except Exception as e:
            logger.error(f"提取PFE消息失败: {e}")
            return pd.DataFrame()
    
    def _extract_point_pupil_data(self, samples: pd.DataFrame, pfe_messages: pd.DataFrame,
                                 point_index: int, screen_x: float, screen_y: float) -> Optional[Dict]:
        """提取单个标定点的瞳孔数据"""
        try:
            # 查找该点的开始和结束消息，考虑EVENT_xxx前缀
            start_msg = pfe_messages[
                (pfe_messages['message_type'] == 'POINT_START') &
                (pfe_messages['text'].str.contains(f'POINT_START {point_index}\\b'))
            ]

            end_msg = pfe_messages[
                (pfe_messages['message_type'] == 'POINT_END') &
                (pfe_messages['text'].str.contains(f'POINT_END {point_index}\\b'))
            ]

            if start_msg.empty or end_msg.empty:
                logger.warning(f"找不到点 {point_index} 的开始或结束消息")
                logger.debug(f"可用的POINT_START消息: {pfe_messages[pfe_messages['message_type'] == 'POINT_START']['text'].tolist()}")
                logger.debug(f"可用的POINT_END消息: {pfe_messages[pfe_messages['message_type'] == 'POINT_END']['text'].tolist()}")
                return None

            start_time = start_msg.iloc[0]['time']
            end_time = end_msg.iloc[0]['time']
            
            # 计算分析窗口（后3秒）
            analysis_start = end_time - ANALYSIS_WINDOW * 1000  # 转换为毫秒
            analysis_end = end_time
            
            # 提取分析窗口内的样本数据
            analysis_mask = (samples['time'] >= analysis_start) & (samples['time'] <= analysis_end)
            analysis_samples = samples[analysis_mask]
            
            if len(analysis_samples) == 0:
                logger.warning(f"点 {point_index} 分析窗口内无数据")
                return None
            
            # 计算瞳孔数据统计
            pupil_stats = self._calculate_pupil_statistics(analysis_samples)
            
            # 计算注视位置统计
            gaze_stats = self._calculate_gaze_statistics(analysis_samples, screen_x, screen_y)
            
            # 合并结果
            result = {
                'pupil_left': pupil_stats.get('pupil_left_mean'),
                'pupil_right': pupil_stats.get('pupil_right_mean'),
                'pupil_left_std': pupil_stats.get('pupil_left_std'),
                'pupil_right_std': pupil_stats.get('pupil_right_std'),
                'gaze_x': gaze_stats.get('gaze_x_mean'),
                'gaze_y': gaze_stats.get('gaze_y_mean'),
                'gaze_accuracy': gaze_stats.get('gaze_accuracy'),
                'valid_samples': len(analysis_samples),
                'analysis_duration': (analysis_end - analysis_start) / 1000,
                'data_quality': pupil_stats.get('data_quality', 0.0)
            }
            
            return result
            
        except Exception as e:
            logger.error(f"提取点 {point_index} 瞳孔数据失败: {e}")
            return None
    
    def _calculate_pupil_statistics(self, samples: pd.DataFrame) -> Dict:
        """计算瞳孔数据统计"""
        try:
            stats = {}
            
            # 左眼瞳孔统计
            if 'pa_left' in samples.columns:
                left_valid = samples['pa_left'].dropna()
                if len(left_valid) > 0:
                    stats['pupil_left_mean'] = left_valid.mean()
                    stats['pupil_left_std'] = left_valid.std()
                    stats['pupil_left_valid_ratio'] = len(left_valid) / len(samples)
                else:
                    stats['pupil_left_mean'] = np.nan
                    stats['pupil_left_std'] = np.nan
                    stats['pupil_left_valid_ratio'] = 0.0
            
            # 右眼瞳孔统计
            if 'pa_right' in samples.columns:
                right_valid = samples['pa_right'].dropna()
                if len(right_valid) > 0:
                    stats['pupil_right_mean'] = right_valid.mean()
                    stats['pupil_right_std'] = right_valid.std()
                    stats['pupil_right_valid_ratio'] = len(right_valid) / len(samples)
                else:
                    stats['pupil_right_mean'] = np.nan
                    stats['pupil_right_std'] = np.nan
                    stats['pupil_right_valid_ratio'] = 0.0
            
            # 计算数据质量分数
            left_ratio = stats.get('pupil_left_valid_ratio', 0)
            right_ratio = stats.get('pupil_right_valid_ratio', 0)
            stats['data_quality'] = (left_ratio + right_ratio) / 2
            
            return stats
            
        except Exception as e:
            logger.error(f"计算瞳孔统计失败: {e}")
            return {}
    
    def _calculate_gaze_statistics(self, samples: pd.DataFrame, target_x: float, target_y: float) -> Dict:
        """计算注视位置统计"""
        try:
            stats = {}
            
            # 提取注视位置数据
            if 'gx_left' in samples.columns and 'gy_left' in samples.columns:
                gaze_x_valid = samples['gx_left'].dropna()
                gaze_y_valid = samples['gy_left'].dropna()

                if len(gaze_x_valid) > 0 and len(gaze_y_valid) > 0:
                    stats['gaze_x_mean'] = gaze_x_valid.mean()
                    stats['gaze_y_mean'] = gaze_y_valid.mean()

                    # 计算注视精度（与目标点的距离）
                    distances = np.sqrt(
                        (gaze_x_valid - target_x)**2 +
                        (gaze_y_valid - target_y)**2
                    )
                    stats['gaze_accuracy'] = distances.mean()
                else:
                    stats['gaze_x_mean'] = np.nan
                    stats['gaze_y_mean'] = np.nan
                    stats['gaze_accuracy'] = np.nan
            
            return stats
            
        except Exception as e:
            logger.error(f"计算注视统计失败: {e}")
            return {}
    
    def _generate_analysis_report(self, calibration_df: pd.DataFrame, output_path: str):
        """生成分析报告"""
        try:
            report_path = output_path.replace('.csv', '_report.txt')
            
            with open(report_path, 'w', encoding='utf-8') as f:
                f.write("PFE标定数据分析报告\n")
                f.write("=" * 50 + "\n\n")
                
                # 基本统计
                total_points = len(calibration_df)
                valid_left = calibration_df['pupil_left'].notna().sum()
                valid_right = calibration_df['pupil_right'].notna().sum()
                
                f.write(f"总标定点数: {total_points}\n")
                f.write(f"有效左眼数据: {valid_left} ({valid_left/total_points:.1%})\n")
                f.write(f"有效右眼数据: {valid_right} ({valid_right/total_points:.1%})\n\n")
                
                # 瞳孔大小统计
                if valid_left > 0:
                    left_mean = calibration_df['pupil_left'].mean()
                    left_std = calibration_df['pupil_left'].std()
                    f.write(f"左眼瞳孔大小: {left_mean:.1f} ± {left_std:.1f}\n")
                
                if valid_right > 0:
                    right_mean = calibration_df['pupil_right'].mean()
                    right_std = calibration_df['pupil_right'].std()
                    f.write(f"右眼瞳孔大小: {right_mean:.1f} ± {right_std:.1f}\n")
                
                # 注视精度统计
                if 'gaze_accuracy' in calibration_df.columns:
                    accuracy_valid = calibration_df['gaze_accuracy'].dropna()
                    if len(accuracy_valid) > 0:
                        f.write(f"\n注视精度: {accuracy_valid.mean():.1f} ± {accuracy_valid.std():.1f} 像素\n")
                
                # 数据质量统计
                if 'data_quality' in calibration_df.columns:
                    quality_mean = calibration_df['data_quality'].mean()
                    f.write(f"平均数据质量: {quality_mean:.1%}\n")
            
            logger.info(f"分析报告已保存: {report_path}")
            
        except Exception as e:
            logger.error(f"生成分析报告失败: {e}")

def analyze_pfe_calibration(calibration_dir: str) -> str:
    """
    分析PFE标定数据的便捷函数

    Args:
        calibration_dir: 标定数据目录路径

    Returns:
        str: 分析后的数据文件路径
    """
    analyzer = PFEDataAnalyzer()
    return analyzer.analyze_pfe_calibration_data(calibration_dir)

if __name__ == "__main__":
    # 测试数据分析
    import argparse

    parser = argparse.ArgumentParser(description='PFE标定数据分析')
    parser.add_argument('--dir', required=True, help='标定数据目录路径')

    args = parser.parse_args()

    print(f"分析PFE标定数据...")
    print(f"标定目录: {args.dir}")

    result_file = analyze_pfe_calibration(args.dir)

    if result_file:
        print(f"✓ 分析完成，结果保存至: {result_file}")
    else:
        print("✗ 分析失败")
