AEON库时间序列分类文档摘要

=== AEON库概述 ===
aeon是一个scikit-learn兼容的时间序列机器学习工具包，支持分类、回归、聚类、异常检测、分割和相似性搜索等任务。

=== 数据格式要求 ===
1. 时间序列数据存储格式：3D numpy数组，形状为 (n_cases, n_channels, n_timepoints)
   - n_cases: 样本数量
   - n_channels: 通道数（单变量为1，多变量为多个）
   - n_timepoints: 时间点数量

2. 标签格式：1D numpy数组，形状为 (n_cases,)

3. 不等长时间序列：存储为2D numpy数组的列表

=== HIVECOTE2算法详细信息 ===
HIVECOTE2 (HC2) 是最先进的混合时间序列分类算法：

1. 算法组成：
   - DrCIF (Diverse Representation Canonical Interval Forest)
   - TDE (Temporal Dictionary Ensemble) 
   - Arsenal (ROCKET分类器集成)
   - STC (Shapelet Transform Classifier)

2. 主要特点：
   - 支持单变量和多变量时间序列
   - 可配置时间限制 (time_limit_in_minutes参数)
   - 使用out-of-bag误差估计替代交叉验证
   - 在UCR和UEA数据集上表现最佳

3. 使用示例：
```python
from aeon.classification.hybrid import HIVECOTEV2
hc2 = HIVECOTEV2(time_limit_in_minutes=0.2)
hc2.fit(X_train, y_train)
y_pred = hc2.predict(X_test)
```

=== 其他重要分类算法 ===
1. RocketClassifier - 基于卷积的快速算法
2. DrCIFClassifier - 基于区间的算法
3. LITETimeClassifier - 轻量级深度学习算法
4. KNeighborsTimeSeriesClassifier - 基于距离的算法

=== 算法分类 ===
- Convolution-based: Arsenal, RocketClassifier, MiniRocketClassifier
- Deep learning: TimeCNNClassifier, FCNClassifier, InceptionTimeClassifier
- Dictionary-based: BOSSEnsemble, WEASEL, TDE
- Distance-based: ElasticEnsemble, KNeighborsTimeSeriesClassifier
- Feature-based: Catch22Classifier, TSFreshClassifier
- Interval-based: DrCIFClassifier, TimeSeriesForestClassifier
- Shapelet-based: ShapeletTransformClassifier
- Hybrid: HIVECOTEV1, HIVECOTEV2

=== 兼容性 ===
- 与scikit-learn完全兼容
- 支持GridSearchCV参数调优
- 支持cross_val_score交叉验证
- 支持CalibratedClassifierCV概率校准

=== 多变量分类支持 ===
可通过以下方式查询支持多变量的分类器：
```python
from aeon.utils.discovery import all_estimators
all_estimators(
    tag_filter={"capability:multivariate": True},
    type_filter="classifier",
)
```

=== 性能评估 ===
HIVECOTE2在112个UCR数据集上的表现最佳，是当前最准确的时间序列分类算法。
