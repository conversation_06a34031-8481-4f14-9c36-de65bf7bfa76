"""
眼动数据到AEON格式转换器
将EyeLink DataFrame数据转换为aeon库所需的3D numpy数组格式
"""

import numpy as np
import pandas as pd
import json
import os
import sys
from pathlib import Path
from typing import List, Tuple, Dict, Optional, Union
import logging

# 添加项目根目录到路径
project_root = Path(__file__).parent.parent
sys.path.append(str(project_root))

from analysis.preprocess_edf import EyeDataPreprocessor

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class EyeTrackingToAeonConverter:
    """眼动数据到AEON格式转换器"""
    
    def __init__(self, 
                 channels: List[str] = None,
                 window_duration_ms: int = 5000,
                 sampling_rate: int = 1000,
                 label_strategy: str = 'ternary'):
        """
        初始化转换器
        
        Args:
            channels: 要提取的通道列表
            window_duration_ms: 时间窗口长度(毫秒)
            sampling_rate: 采样率(Hz)
            label_strategy: 标签编码策略 ('binary', 'ternary', 'original')
        """
        # 默认通道配置
        if channels is None:
            self.channels = [
                'pa_left',      # 左眼瞳孔面积
                'pa_right',     # 右眼瞳孔面积
                'gx_left',      # 左眼X坐标
                'gy_left',      # 左眼Y坐标
                'gx_right',     # 右眼X坐标
                'gy_right'      # 右眼Y坐标
            ]
        else:
            self.channels = channels
        
        self.window_duration_ms = window_duration_ms
        self.sampling_rate = sampling_rate
        self.window_samples = window_duration_ms  # 1000Hz采样率下，毫秒数=样本数
        self.label_strategy = label_strategy
        
        logger.info(f"转换器初始化完成")
        logger.info(f"  通道数: {len(self.channels)}")
        logger.info(f"  通道列表: {self.channels}")
        logger.info(f"  时间窗口: {window_duration_ms}ms ({self.window_samples}个样本)")
        logger.info(f"  标签策略: {label_strategy}")
    
    def load_rating_data(self, data_dir: str) -> List[Dict]:
        """
        加载评分数据
        
        Args:
            data_dir: 数据目录路径
            
        Returns:
            评分数据列表
        """
        rating_files = []
        
        # 查找所有rating_group_*.json文件
        for file in os.listdir(data_dir):
            if file.startswith('rating_group_') and file.endswith('.json'):
                file_path = os.path.join(data_dir, file)
                try:
                    with open(file_path, 'r', encoding='utf-8') as f:
                        rating_data = json.load(f)
                        rating_files.append(rating_data)
                except Exception as e:
                    logger.warning(f"无法读取评分文件 {file}: {e}")
        
        # 按时间戳排序
        rating_files.sort(key=lambda x: x.get('timestamp', 0))
        
        logger.info(f"加载了 {len(rating_files)} 个评分数据")
        return rating_files
    
    def encode_labels(self, ratings: List[int]) -> Tuple[np.ndarray, List[str]]:
        """
        编码兴趣评分标签
        
        Args:
            ratings: 原始评分列表 (1-5)
            
        Returns:
            encoded_labels: 编码后的标签数组
            class_names: 类别名称列表
        """
        ratings = np.array(ratings)
        
        if self.label_strategy == 'binary':
            # 二分类: 1-3 -> 0, 4-5 -> 1
            labels = (ratings >= 4).astype(int)
            class_names = ['低兴趣', '高兴趣']
            
        elif self.label_strategy == 'ternary':
            # 三分类: 1-2 -> 0, 3 -> 1, 4-5 -> 2
            labels = np.zeros_like(ratings)
            labels[ratings == 3] = 1
            labels[ratings >= 4] = 2
            class_names = ['低兴趣', '中等兴趣', '高兴趣']
            
        elif self.label_strategy == 'original':
            # 五分类: 1-5 -> 0-4
            labels = ratings - 1
            class_names = ['非常不感兴趣', '不感兴趣', '一般', '感兴趣', '非常感兴趣']
            
        else:
            raise ValueError(f"未知的标签策略: {self.label_strategy}")
        
        return labels, class_names
    
    def extract_time_window(self, 
                           samples: pd.DataFrame, 
                           center_time: float, 
                           window_duration_ms: int = None) -> np.ndarray:
        """
        提取指定时间点周围的时间窗口数据
        
        Args:
            samples: 眼动样本数据
            center_time: 中心时间点(毫秒)
            window_duration_ms: 窗口长度(毫秒)，默认使用初始化时的值
            
        Returns:
            window_data: (n_channels, n_timepoints) 的窗口数据
        """
        if window_duration_ms is None:
            window_duration_ms = self.window_duration_ms
        
        # 计算窗口边界
        half_window = window_duration_ms // 2
        start_time = center_time - half_window
        end_time = center_time + half_window
        
        # 筛选时间范围内的数据
        time_mask = (samples['time'] >= start_time) & (samples['time'] <= end_time)
        window_samples = samples[time_mask].copy()
        
        # 提取指定通道的数据
        window_data = np.zeros((len(self.channels), window_duration_ms))
        
        for i, channel in enumerate(self.channels):
            if channel in window_samples.columns:
                # 获取通道数据
                channel_data = window_samples[channel].values
                
                # 处理数据长度
                if len(channel_data) >= window_duration_ms:
                    # 截断到指定长度
                    window_data[i, :] = channel_data[:window_duration_ms]
                else:
                    # 填充到指定长度
                    window_data[i, :len(channel_data)] = channel_data
                    # 剩余部分用最后一个有效值填充
                    if len(channel_data) > 0:
                        window_data[i, len(channel_data):] = channel_data[-1]
            else:
                logger.warning(f"通道 {channel} 不存在于数据中")
        
        return window_data
    
    def convert_single_experiment(self, 
                                 edf_path: str, 
                                 data_dir: str) -> Tuple[np.ndarray, np.ndarray, Dict]:
        """
        转换单个实验的数据
        
        Args:
            edf_path: EDF文件路径
            data_dir: 包含评分数据的目录路径
            
        Returns:
            X: (n_trials, n_channels, n_timepoints) 特征数据
            y: (n_trials,) 标签数据
            metadata: 元数据信息
        """
        logger.info(f"处理实验: {os.path.basename(edf_path)}")
        
        # 1. 加载眼动数据
        preprocessor = EyeDataPreprocessor()
        samples, events, messages = preprocessor.load_edf_data(edf_path)
        
        if samples is None:
            raise ValueError(f"无法加载EDF数据: {edf_path}")
        
        # 2. 加载评分数据
        rating_data = self.load_rating_data(data_dir)
        
        if not rating_data:
            raise ValueError(f"未找到评分数据: {data_dir}")
        
        # 3. 提取时间窗口和标签
        X_list = []
        y_list = []
        metadata_list = []
        
        for rating_item in rating_data:
            try:
                # 获取评分时间戳和评分值
                timestamp = rating_item.get('timestamp', 0) * 1000  # 转换为毫秒
                interest_rating = rating_item.get('ratings', {}).get('interest_rating')
                
                if interest_rating is None:
                    logger.warning(f"评分数据缺失，跳过时间戳: {timestamp}")
                    continue
                
                # 提取时间窗口
                window_data = self.extract_time_window(samples, timestamp)
                
                # 检查数据有效性
                if np.isnan(window_data).all():
                    logger.warning(f"时间窗口数据全为NaN，跳过时间戳: {timestamp}")
                    continue
                
                X_list.append(window_data)
                y_list.append(interest_rating)
                
                # 保存元数据
                metadata_list.append({
                    'timestamp': timestamp,
                    'rating': interest_rating,
                    'sentence_count': rating_item.get('sentence_count', 0),
                    'sentence_group_ids': rating_item.get('sentence_group_ids', [])
                })
                
            except Exception as e:
                logger.error(f"处理评分数据时出错: {e}")
                continue
        
        if not X_list:
            raise ValueError("没有有效的数据可以转换")
        
        # 4. 转换为numpy数组
        X = np.array(X_list)  # (n_trials, n_channels, n_timepoints)
        y_raw = np.array(y_list)
        
        # 5. 编码标签
        y, class_names = self.encode_labels(y_raw)
        
        # 6. 创建元数据
        metadata = {
            'experiment_path': edf_path,
            'data_dir': data_dir,
            'n_trials': len(X),
            'n_channels': len(self.channels),
            'n_timepoints': self.window_samples,
            'channels': self.channels,
            'class_names': class_names,
            'label_strategy': self.label_strategy,
            'window_duration_ms': self.window_duration_ms,
            'sampling_rate': self.sampling_rate,
            'trial_metadata': metadata_list,
            'raw_ratings': y_raw.tolist(),
            'encoded_labels': y.tolist()
        }
        
        logger.info(f"转换完成: {len(X)} 个试次")
        logger.info(f"  数据形状: {X.shape}")
        logger.info(f"  标签分布: {np.bincount(y)}")
        
        return X, y, metadata
    
    def validate_aeon_format(self, X: np.ndarray, y: np.ndarray) -> bool:
        """
        验证数据格式是否符合aeon要求
        
        Args:
            X: 特征数据
            y: 标签数据
            
        Returns:
            是否通过验证
        """
        try:
            # 基本格式检查
            assert isinstance(X, np.ndarray), "X必须是numpy数组"
            assert isinstance(y, np.ndarray), "y必须是numpy数组"
            assert X.ndim == 3, f"X必须是3D数组，当前维度: {X.ndim}"
            assert y.ndim == 1, f"y必须是1D数组，当前维度: {y.ndim}"
            assert X.shape[0] == y.shape[0], f"样本数量不匹配: X={X.shape[0]}, y={y.shape[0]}"
            
            # 数据类型检查
            assert np.issubdtype(X.dtype, np.number), "X必须是数值类型"
            assert np.issubdtype(y.dtype, np.integer), "y必须是整数类型"
            
            # 数值有效性检查
            assert np.isfinite(X).any(), "X包含过多无效值"
            assert np.isfinite(y).all(), "y包含无效值"
            
            logger.info("✓ AEON格式验证通过")
            logger.info(f"  样本数: {X.shape[0]}")
            logger.info(f"  通道数: {X.shape[1]}")
            logger.info(f"  时间点数: {X.shape[2]}")
            logger.info(f"  标签类别: {np.unique(y)}")
            logger.info(f"  数据类型: X={X.dtype}, y={y.dtype}")
            
            return True
            
        except AssertionError as e:
            logger.error(f"✗ AEON格式验证失败: {e}")
            return False
        except Exception as e:
            logger.error(f"✗ 验证过程出错: {e}")
            return False

def main():
    """测试转换器功能"""
    # 创建转换器
    converter = EyeTrackingToAeonConverter(
        window_duration_ms=3000,  # 3秒窗口
        label_strategy='ternary'   # 三分类
    )
    
    # 查找测试数据
    data_root = "data"
    test_dirs = [
        "20250812_180658_cs285_lec19_continuous_reading",
        "20250729_155706_dark_test_continuous_reading"
    ]
    
    for test_dir in test_dirs:
        data_dir = os.path.join(data_root, test_dir)
        if not os.path.exists(data_dir):
            continue
            
        # 查找EDF文件
        edf_files = [f for f in os.listdir(data_dir) if f.endswith('.edf')]
        if not edf_files:
            continue
            
        edf_path = os.path.join(data_dir, edf_files[0])
        
        try:
            logger.info(f"测试转换: {test_dir}")
            X, y, metadata = converter.convert_single_experiment(edf_path, data_dir)
            
            # 验证格式
            if converter.validate_aeon_format(X, y):
                logger.info(f"✓ {test_dir} 转换成功")
            else:
                logger.error(f"✗ {test_dir} 格式验证失败")
                
        except Exception as e:
            logger.error(f"✗ {test_dir} 转换失败: {e}")

if __name__ == "__main__":
    main()
