#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
EDF文件转换器 - 将EyeLink的EDF文件转换为ASC文件
"""

import os
import glob
import subprocess
import logging
from datetime import datetime
from analysis.history_code.config import EDF2ASC_PATH, DATA_DIR, OUTPUT_DIR, EDF_CONVERSION_CONFIG, TARGET_PARTICIPANT, LATEST_DATA_ONLY

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(os.path.join(OUTPUT_DIR, 'edf_conversion.log')),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger('edf_converter')

def find_edf_files(data_dir=DATA_DIR, target_participant=TARGET_PARTICIPANT, latest_only=LATEST_DATA_ONLY):
    """
    查找需要转换的EDF文件
    
    Args:
        data_dir: 数据目录
        target_participant: 目标参与者ID，None表示所有参与者
        latest_only: 是否只处理最新数据
        
    Returns:
        edf_files: 找到的EDF文件列表
    """
    logger.info(f"查找EDF文件: 目标参与者={target_participant}, 仅最新={latest_only}")
    
    # 查找所有包含EDF文件的文件夹
    if target_participant:
        participant_dirs = glob.glob(os.path.join(data_dir, f"*_{target_participant}*"))
    else:
        participant_dirs = glob.glob(os.path.join(data_dir, "*"))
    
    participant_dirs = [d for d in participant_dirs if os.path.isdir(d)]
    logger.info(f"找到{len(participant_dirs)}个参与者文件夹")
    
    if latest_only and participant_dirs:
        # 按照文件夹名称排序（通常包含日期时间）
        participant_dirs.sort(reverse=True)
        # 如果指定了参与者，找到该参与者的最新文件夹
        if target_participant:
            target_dirs = [d for d in participant_dirs if target_participant in os.path.basename(d)]
            if target_dirs:
                participant_dirs = [target_dirs[0]]  # 只保留最新的
                logger.info(f"选择最新的目标参与者文件夹: {participant_dirs[0]}")
            else:
                logger.warning(f"未找到目标参与者 {target_participant} 的文件夹")
                return []
        else:
            # 只保留最新的文件夹
            participant_dirs = [participant_dirs[0]]
            logger.info(f"选择最新的文件夹: {participant_dirs[0]}")
    
    # 查找所有EDF文件
    edf_files = []
    for participant_dir in participant_dirs:
        files = glob.glob(os.path.join(participant_dir, "*.edf"))
        edf_files.extend(files)
    
    logger.info(f"找到{len(edf_files)}个EDF文件")
    return edf_files

def convert_edf_to_asc(edf_file, options=None):
    """
    将EDF文件转换为ASC文件
    
    Args:
        edf_file: EDF文件路径
        options: 转换选项
        
    Returns:
        success: 是否成功
        asc_file: 生成的ASC文件路径
    """
    if options is None:
        options = []
    
    # 构建输出文件路径（与EDF文件在同一目录）
    asc_file = os.path.splitext(edf_file)[0] + '.asc'
    
    # 检查ASC文件是否已存在
    if os.path.exists(asc_file):
        logger.info(f"ASC文件已存在: {asc_file}")
        return True, asc_file
    
    # 构建命令
    cmd = [EDF2ASC_PATH]
    
    # 添加选项
    # if EDF_CONVERSION_CONFIG['include_samples']:
    #     cmd.append('-s')
    # if EDF_CONVERSION_CONFIG['include_events']:
    #     cmd.append('-e')
    # if EDF_CONVERSION_CONFIG['include_input']:
    #     cmd.append('-i')
    # if EDF_CONVERSION_CONFIG['time_format'] == 'ELAPSED':
    #     cmd.append('-t')
    
    # 添加分辨率选项
    # cmd.extend(['-res', str(EDF_CONVERSION_CONFIG['resolution'])])
    
    # 添加自定义选项
    # cmd.extend(options)
    
    # 添加输入文件
    cmd.append(edf_file)
    
    logger.info(f"执行命令: {' '.join(cmd)}")
    
    # try:
    #     # 执行命令
    #     result = subprocess.run(cmd, check=True, capture_output=True, text=True)
    #     logger.info(f"转换成功: {edf_file} -> {asc_file}")
    #     logger.debug(f"命令输出: {result.stdout}")
    #     return True, asc_file
    # except subprocess.CalledProcessError as e:
    #     logger.error(f"转换失败: {edf_file}")
    #     logger.error(f"错误信息: {e}")
    #     logger.error(f"标准输出: {e.stdout}")
    #     logger.error(f"标准错误: {e.stderr}")
    #     return False, None
    
    result = subprocess.run(cmd, check=True, capture_output=True, text=True)
    return True, asc_file

def convert_all_edf_files():
    """转换所有找到的EDF文件"""
    edf_files = find_edf_files()
    
    if not edf_files:
        logger.warning("未找到EDF文件")
        return []
    
    converted_files = []
    for edf_file in edf_files:
        success, asc_file = convert_edf_to_asc(edf_file)
        if success:
            converted_files.append(asc_file)
    
    logger.info(f"共转换{len(converted_files)}个文件")
    return converted_files

if __name__ == "__main__":
    logger.info("开始EDF文件转换")
    convert_all_edf_files()
    logger.info("EDF文件转换完成")
