#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
测试PFE随机标定功能
"""

import os
import sys
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from pfe.pfe_calibration import run_pfe_calibration
from pfe.config import *

def test_random_calibration():
    """测试随机标定功能"""
    print("=" * 60)
    print("测试PFE随机标定功能")
    print("=" * 60)
    
    # 测试参数
    participant_id = "test_random"
    data_dir = "./data"
    dummy_mode = True  # 使用虚拟模式进行测试
    
    print(f"被试ID: {participant_id}")
    print(f"网格配置: {GRID_COLS}×{GRID_ROWS} = {TOTAL_GRID_POINTS}个点")
    print(f"重复次数: {CALIBRATION_REPETITIONS}")
    print(f"随机顺序: {RANDOMIZE_ORDER}")
    print(f"总标定次数: {TOTAL_GRID_POINTS * CALIBRATION_REPETITIONS}")
    print(f"预计时间: {TOTAL_GRID_POINTS * CALIBRATION_REPETITIONS * FIXATION_DURATION / 60:.1f}分钟")
    print(f"虚拟模式: {dummy_mode}")
    print()
    
    # 运行标定
    print("开始PFE标定...")
    result_file = run_pfe_calibration(
        participant_id=participant_id,
        data_dir=data_dir,
        dummy_mode=dummy_mode
    )
    
    if result_file:
        print(f"✓ PFE标定完成！")
        print(f"数据文件: {result_file}")
        
        # 分析结果
        analyze_calibration_result(result_file)
        
    else:
        print("✗ PFE标定失败")

def analyze_calibration_result(filepath: str):
    """分析标定结果"""
    try:
        print("\n" + "=" * 40)
        print("分析标定结果")
        print("=" * 40)
        
        # 读取数据
        df = pd.read_csv(filepath)
        print(f"读取数据: {len(df)} 条记录")
        
        # 基本统计
        print("\n基本统计:")
        print(f"- 总标定次数: {len(df)}")
        print(f"- 有效记录: {df['valid'].sum()}")
        print(f"- 有效率: {df['valid'].mean()*100:.1f}%")
        
        # 网格点统计
        if 'original_point_index' in df.columns:
            unique_points = df['original_point_index'].nunique()
            print(f"- 标定的网格点数: {unique_points}")
            
            # 每个点的重复次数
            point_counts = df.groupby('original_point_index').size()
            print(f"- 每点平均重复次数: {point_counts.mean():.1f}")
            print(f"- 重复次数范围: {point_counts.min()} - {point_counts.max()}")
        
        # 瞳孔数据统计
        print("\n瞳孔数据统计:")
        for eye in ['left', 'right']:
            col = f'pupil_{eye}'
            if col in df.columns:
                valid_data = df[df[col].notna()][col]
                if len(valid_data) > 0:
                    print(f"- {eye.upper()}眼瞳孔大小: {valid_data.mean():.1f} ± {valid_data.std():.1f}")
                    print(f"  范围: {valid_data.min():.1f} - {valid_data.max():.1f}")
                    print(f"  变异系数: {(valid_data.std()/valid_data.mean())*100:.1f}%")
        
        # 双眼平均
        if 'pupil_left' in df.columns and 'pupil_right' in df.columns:
            df['pupil_avg'] = df[['pupil_left', 'pupil_right']].mean(axis=1, skipna=True)
            valid_avg = df[df['pupil_avg'].notna()]['pupil_avg']
            if len(valid_avg) > 0:
                print(f"- 双眼平均瞳孔大小: {valid_avg.mean():.1f} ± {valid_avg.std():.1f}")
                print(f"  变异系数: {(valid_avg.std()/valid_avg.mean())*100:.1f}%")
        
        # 检查随机化效果
        if 'original_point_index' in df.columns and 'repetition' in df.columns:
            print("\n随机化效果检查:")
            
            # 检查前几个标定的点是否分布均匀
            first_10 = df.head(10)['original_point_index'].tolist()
            unique_in_first_10 = len(set(first_10))
            print(f"- 前10次标定涉及 {unique_in_first_10} 个不同网格点")
            print(f"- 前10次标定的点序列: {first_10}")
            
            # 检查重复分布
            rep_order = []
            for point_idx in range(TOTAL_GRID_POINTS):
                point_data = df[df['original_point_index'] == point_idx]
                if len(point_data) > 0:
                    reps = point_data['repetition'].tolist()
                    rep_order.extend(reps)
            
            if rep_order:
                print(f"- 重复次数分布均匀性检查:")
                for rep in range(CALIBRATION_REPETITIONS):
                    count = rep_order.count(rep)
                    print(f"  重复{rep+1}: {count}次")
        
        print("\n✓ 结果分析完成")
        
    except Exception as e:
        print(f"✗ 分析结果失败: {e}")

if __name__ == "__main__":
    test_random_calibration()
