#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
主分析脚本 (Main Analysis Script)

目的 (Purpose):
本脚本用于分析预处理后的EDF眼动数据。主要功能包括：
1. 读取多个EDF文件并使用 preprocess_edf.py进行预处理。
2. 从眼动仪消息中解析试次信息（如评分、事件时间戳）。
3. 根据指定的事件窗口（如问题呈现期间）和基线进行数据分段和基线校正。
4. 按不同的评分类型（如好奇心、难度）对瞳孔数据进行分组和统计（计算均值和标准差）。
5. 绘制结果图表，展示不同评分下瞳孔大小的变化趋势，并支持标准差阴影显示。

基线校正功能 (Baseline Correction):
- 支持灵活的基线时间窗口定义
- 可指定基线参考事件（如 QUESTION_DISPLAY_START）
- 可设置基线开始时间相对于参考事件的偏移（如 -2000ms 表示事件前2秒）
- 可设置基线持续时间（如 200ms）
- 基线校正使用指定时间窗口内瞳孔数据的均值

实现方式 (Implementation):
1. 配置驱动：所有关键参数（文件路径、事件名、分析窗口等）都在一个`CONFIG`字典中定义，方便修改。
2. 模块化函数：脚本被分解为多个函数，分别负责消息解析、数据分段、统计聚合和绘图。
3. 数据处理流程：
   - `main_analysis`作为主函数，协调整个流程。
   - 循环处理每个EDF文件，加载数据、预处理、解析试次信息。
   - `extract_all_trial_segments`函数根据试次信息从连续的瞳孔数据中提取有效的数据段，并进行可选的基线校正。
   - 所有数据段被合并到一个大的Pandas DataFrame中。
   - 针对每种评分类别，数据被分组并计算均值和标准差。
   - `plot_analysis_results`函数使用matplotlib生成两类图表：纯均值对比图和均值+标准差范围图。
4. 依赖 `preprocess_edf.py` 模块进行核心的瞳孔数据预处理。
"""

import os
import re
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import matplotlib.cm as cm
import logging
from typing import Dict, List, Any, Optional

# 假设 preprocess_edf 在同一个分析目录下
from preprocess_edf import EyeDataPreprocessor, setup_chinese_font

# --- 全局配置 ---
# 请在此处修改您的分析参数
CONFIG = {
    # 需要处理的EDF文件列表，请使用您自己的文件路径
    'data_sources': [
        #dulu
        # "C:/Users/<USER>/Desktop/curiosity_pupil/data/20250718_142425_dulu/dulu.edf",
        # "C:/Users/<USER>/Desktop/curiosity_pupil/data/20250718_145240_dulu/dulu.edf",
        # "C:/Users/<USER>/Desktop/curiosity_pupil/data/20250718_151616_dulu/dulu.edf",
        # "C:/Users/<USER>/Desktop/curiosity_pupil/data/20250718_153841_dulu/dulu.edf",
        # "C:/Users/<USER>/Desktop/curiosity_pupil/data/20250718_160729_dulu/dulu.edf",
        #conitnuous reading
        # "C:/Users/<USER>/Desktop/curiosity_pupil/data/20250718_163043_dulu_continuous/dulu_continuous.edf",
        
        #zhaojingyi
        # "C:/Users/<USER>/Desktop/curiosity_pupil/data/20250716_141650_zhaojingyi/zhaojingyi.edf",
        # "C:/Users/<USER>/Desktop/curiosity_pupil/data/20250716_143501_zhaojingyi/zhaojingyi.edf",
        # "C:/Users/<USER>/Desktop/curiosity_pupil/data/20250716_145428_zhaojingyi/zhaojingyi.edf",
        # "C:/Users/<USER>/Desktop/curiosity_pupil/data/20250716_150840_zhaojingyi/zhaojingyi.edf",
        # "C:/Users/<USER>/Desktop/curiosity_pupil/data/20250716_153224_zhaojingyi/zhaojingyi.edf",
        # "C:/Users/<USER>/Desktop/curiosity_pupil/data/20250716_154752_zhaojingyi/zhaojingyi.edf",
        #continuous reading
        # "C:/Users/<USER>/Desktop/curiosity_pupil/data/20250716_160737_zhaojingyi_continuousReading/zhaojingyi.edf",
        
        
        #sunyan
        # "C:/Users/<USER>/Desktop/curiosity_pupil/data/20250725_140627_sunyan/sunyan.edf",
        # "C:/Users/<USER>/Desktop/curiosity_pupil/data/20250725_142821_sunyan/sunyan.edf",
        # "C:/Users/<USER>/Desktop/curiosity_pupil/data/20250725_151950_sunyan/sunyan.edf",
        # "C:/Users/<USER>/Desktop/curiosity_pupil/data/20250725_160047_sunyan/sunyan.edf",
        
        #sunyan continuous reading
        # "C:/Users/<USER>/Desktop/curiosity_pupil/data/20250725_162711_sunyan_continuous_reading/sunyan.edf",#半生缘
        # "C:/Users/<USER>/Desktop/curiosity_pupil/data/20250725_170121_sunyan_continuous_reading/sunyan.edf",#心理学书
        # "C:/Users/<USER>/Desktop/curiosity_pupil/data/20250725_171927_sunyan_continuous_reading/sunyan.edf",#乱序各种有趣程度文件测试
        
        #zhuqingchao
        # "E:/眼动 被试/zhuqingchao/20250724_122823_zhuqingchao_continuous_reading/zhuqingchao.edf",
        # "E:/眼动 被试/zhuqingchao/20250724_130214_zhuqingchao_continuous_reading/zhuqingchao.edf",

        
        #test
        # "C:/Users/<USER>/Desktop/curiosity_pupil/data/20250729_151323_test_151323_continuous_reading_aaatest/test_151323.edf",
        # "C:/Users/<USER>/Desktop/curiosity_pupil/data/20250729_154532_dark_env_aaa_test_continuous_reading/dark_env_aaa_test.edf",
        # "C:/Users/<USER>/Desktop/curiosity_pupil/data/20250729_155706_dark_test_continuous_reading/dark_test.edf",
        # "C:/Users/<USER>/Desktop/curiosity_pupil/data/20250729_161339_test_161339_continuous_reading/test_161339.edf",
        # "C:/Users/<USER>/Desktop/curiosity_pupil/data/20250729_162025_test_162025_continuous_reading/test_162025.edf",
        # "C:/Users/<USER>/Desktop/curiosity_pupil/data/20250803_154304_9pt_test/test_154258.edf",
        # "C:/Users/<USER>/Desktop/curiosity_pupil/data/20250804_162547_9ptnew_continuous_reading/9ptnew.edf",
        # "C:/Users/<USER>/Desktop/curiosity_pupil/data/20250805_122729_9pt_vertical_continuous_reading/9pt_vertical.edf",
        
        #听力
        "C:/Users/<USER>/Desktop/curiosity_pupil/data/20250812_180658_cs285_lec19_continuous_reading/test_180654.edf",
        
        # "path/to/your/second.edf", # 可以添加更多文件
    ],
    # 定义分析窗口
    'analysis_window': {
        'event_start': 'sentence,start', # 分析开始的事件
        'event_end': 'sentence,end',     # 分析结束的事件
    },
    # 'analysis_window': {
    #     'event_start': 'ANSWER_DISPLAY_START', # 分析开始的事件
    #     'event_end': 'ANSWER_DISPLAY_END',     # 分析结束的事件
    # },
    # 'analysis_window': {
    #     'event_start': 'BASELINE_START PUPIL', # 分析开始的事件
    #     'event_end': 'ANSWER_DISPLAY_END',     # 分析结束的事件
    # },
    # 基线校正配置
    'baseline': {
        'enabled': False,                         # 是否启用基线校正
        'reference_event': 'BASELINE_START PUPIL',  # 基线参考事件
        'start_offset_ms': 0,               # 基线开始时间相对于参考事件的偏移（毫秒，负数表示事件前）
        'duration_ms': 20,                     # 基线期时长（毫秒）
    },
    # 需要从消息中解析的评分类别
    # 脚本会查找包含 "RATING_VALUE" 和以下变量名的消息, e.g., "RATING_VALUE CURIOSITY 4"
    # 'rating_variables': ['CURIOSITY', 'PLEASURE', 'SURPRISE'], INTEREST
    'rating_variables': ['INTEREST'],
    # 定义试次的开始和结束标志（支持多字符串匹配，用逗号分隔）
    'trial_markers': {
        'start': 'sentence,start',
        'end': 'INTEREST,end'
    },
    # 输出配置
    'output_dir': './analysis/figures/analysis_results',
    'plotting': {
        'colormap': 'viridis',
        'show_plots': True,
        'save_plots': True,
    },
    # 评分聚合配置（可选）
    # 如果需要将某些评分聚合在一起，可以在这里定义映射
    # 格式: {目标值: [原始值列表]}
    # 例如: 将1,2聚合为1，3保持为3，4,5聚合为5
    'rating_aggregation': {
        # 'PLEASURE': {1: [1, 2], 3: [3], 5: [4, 5]},  # 示例：将PLEASURE评分聚合
        # 'CURIOSITY': {1: [1, 2], 3: [3], 5: [4, 5]}, # 示例：将CURIOSITY评分聚合
    },

    'preprocessing_params': {  # smooth_window150,  # 之后可以加入汉宁窗50~150ms滤波，或者是4hz的截止频率获取以下的滤波值
        # 'smooth_window': 5,  # 之后可以加入汉宁窗50~150ms滤波，或者是4hz的截止频率获取以下的滤波值
        'smooth_window': 150,
        'blink_interpolation_window': 40,  # 启用眨眼插值以便看到处理效果
        'interpolation_method': 'cubic',  # 使用二次曲线插值 cubic quadratic
        # 'min_pupil_size': 3750.0,
        # 'max_pupil_size': 6000.0,
        # 'velocity_threshold': 20.0,
        'min_pupil_size': 600.0,
        'max_pupil_size': 6000.0,
        'sampling_rate': 1000,
        'velocity_threshold': 10.0,
        'quadratic_fit_points': 300,  # 使用前后x个点进行拟合
        'min_valid_segment_length': 200,  # 最小有效区间长度，设为50个数据点
        'eye_selection': 'left',  # 眼睛选择: 'left'=仅左眼, 'right'=仅右眼, 'binocular'=双眼平均
        'enable_pfe_correction': False,  # 是否启用PFE矫正
        'pfe_angle_threshold_degrees': 45.0,  # PFE角度阈值（度）
    },
    # 指定时间段瞳孔均值统计配置
    'time_window_analysis': {
        'enabled': True,                    # 是否启用指定时间段分析
        'reference_event': 'sentence,end',  # 参考事件
        'time_before_event_ms': 5000,        # 参考事件前多少毫秒开始统计
        'window_duration_ms': 5000,          # 统计窗口持续时间（毫秒）
        'save_plots': True,                  # 是否保存图表
        'show_plots': True,                  # 是否显示图表
    },
    # LHIPA分析配置
    'lhipa_analysis': {
        'enabled': False,                    # 是否启用LHIPA分析
        'reference_event': 'sentence,end',  # 参考事件
        'time_before_event_ms': 5000,        # 参考事件前多少毫秒开始统计
        'window_duration_ms': 5000,          # 统计窗口持续时间（毫秒）
        'save_plots': True,                  # 是否保存图表
        'show_plots': True,                  # 是否显示图表
    },

}

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger('主分析')

# 导入LHIPA计算器
import sys
import os
# 添加cognitive_load目录到路径
cognitive_load_path = os.path.join(os.path.dirname(os.path.dirname(__file__)), 'cognitive_load')
sys.path.insert(0, cognitive_load_path)
try:
    from lhipa_calculator import convert_to_pypil_format, IPA, LHIPA
    LHIPA_AVAILABLE = True
    logger.info("LHIPA模块导入成功")
except ImportError as e:
    logger.warning(f"无法导入LHIPA模块: {e}，LHIPA分析功能将被禁用")
    LHIPA_AVAILABLE = False


def parse_trial_messages(messages: pd.DataFrame, config: Dict) -> List[Dict[str, Any]]:
    """
    从EDF消息中解析每个试次的详细信息。
    """
    # --- 调试代码 ---
    # 检查 messages DataFrame 的列名
    if 'text' not in messages.columns:
        logger.error(f"错误：'messages' DataFrame 中缺少 'text' 列。可用列: {messages.columns.tolist()}")
        # 在 pyedfread 的某些版本中，列名可能是 'trialid', 'time', 'message'
        # 我们尝试重命名以兼容
        if 'message' in messages.columns:
            logger.info("检测到 'message' 列，将其重命名为 'text' 以继续。")
            messages = messages.rename(columns={'message': 'text'})
        else:
            return [] # 如果没有可用的文本列，则返回空列表

    trials = []
    current_trial = None
    trial_start_marker = config['trial_markers']['start']
    trial_end_marker = config['trial_markers']['end']
    rating_vars = config['rating_variables']

    for _, msg in messages.iterrows():
        msg_text = msg['text'].strip()
        msg_time = msg['time']

        # 使用多字符串匹配检查试次开始标记
        if match_event_with_keywords(msg_text, trial_start_marker):
            if current_trial is not None:
                trials.append(current_trial)
            current_trial = {'events': {}, 'ratings': {}}
        
        if current_trial is None:
            continue

        # 检查是否为事件消息 (更灵活的匹配)
        # 移除 'EVENT_XXX ' 前缀
        cleaned_msg_text = re.sub(r'^EVENT_\d+\s+', '', msg_text)
        if '_START' in cleaned_msg_text or '_END' in cleaned_msg_text:
            current_trial['events'][cleaned_msg_text] = msg_time
        
        # 检查是否为评级消息
        for var in rating_vars:
            # 兼容性修改：检查消息中是否包含 "RATING" 和评分变量名
            if "rating" in msg_text.lower() and var.lower() in msg_text.lower():
                try:
                    # 使用更精确的正则表达式，只匹配字符串末尾的数字
                    match = re.search(r'(\d+)$', msg_text)
                    if match:
                        rating_value = int(match.group(1))
                    else:
                        continue
                    
                    # --- 新增：验证评分范围 ---
                    is_valid = True
                    if var in ['CURIOSITY', 'PLEASURE', 'INTEREST']:
                        if not (1 <= rating_value <= 5):
                            logger.warning(f"发现无效的 '{var}' 评分 '{rating_value}' (不在1-5范围内)，已忽略。消息: '{msg_text}'")
                            is_valid = False
                    
                    if is_valid:
                        current_trial['ratings'][var] = rating_value

                    break # 找到后就跳出循环，避免重复匹配
                except (AttributeError, ValueError):
                    logger.warning(f"无法从消息 '{msg_text}' 中解析评分 '{var}' 的数值")

        # 使用多字符串匹配检查试次结束标记
        if match_event_with_keywords(msg_text, trial_end_marker):
            if current_trial is not None:
                #print(current_trial)
                # print("当前trial id",current_trial['events'])
                # print("有趣度",current_trial['ratings']['INTEREST'])
                # print("------------------------")
                
                current_trial['events'][trial_end_marker] = msg_time
                trials.append(current_trial)
                current_trial = None

    if current_trial is not None:
        trials.append(current_trial)
    
    logger.info(f"成功解析出 {len(trials)} 个试次")
    return trials


def match_event_with_keywords(event_text: str, keywords_str: str) -> bool:
    """
    检查事件文本是否包含所有指定的关键词（支持逗号分割的多个关键词）。

    参数:
    - event_text: 事件文本（如 "sentence_GROUP_78_79_END"）
    - keywords_str: 关键词字符串，可以是单个关键词或逗号分割的多个关键词（如 "end,sentence_group"）

    返回:
    - bool: 如果事件文本包含所有关键词则返回True，否则返回False
    """
    if not keywords_str:
        return False

    # 将关键词字符串按逗号分割，并去除空格，转换为小写
    keywords = [kw.strip().lower() for kw in keywords_str.split(',') if kw.strip()]

    if not keywords:
        return False

    # 将事件文本转换为小写进行匹配
    event_text_lower = event_text.lower()

    # 检查是否所有关键词都在事件文本中
    return all(keyword in event_text_lower for keyword in keywords)


def extract_all_trial_segments(subject_id: str, processed_data: pd.DataFrame, trial_info: List[Dict[str, Any]], config: Dict) -> Optional[pd.DataFrame]:
    """
    从预处理后的数据中提取所有试次的分析片段。
    """
    all_segments = []
    win_start_event = config['analysis_window']['event_start']
    win_end_event = config['analysis_window']['event_end']

    for i, trial in enumerate(trial_info):
        # 使用新的关键词匹配方式，支持逗号分割的多个关键词
        found_start_key = next((key for key in trial['events'] if match_event_with_keywords(key, win_start_event)), None)
        found_end_key = next((key for key in trial['events'] if match_event_with_keywords(key, win_end_event)), None)

        if found_start_key and found_end_key:
            start_time = trial['events'][found_start_key]
            end_time = trial['events'][found_end_key]

            # 提取分析窗口数据
            segment_df = processed_data[(processed_data['time'] >= start_time) & (processed_data['time'] <= end_time)].copy()
            if segment_df.empty:
                logger.warning(f"受试者 {subject_id}, 试次 {i+1}: 在时间范围 [{start_time}, {end_time}] 内没有找到数据")
                continue
            
            # 基线校正
            pupil_col = 'pupil_avg'
            if config['baseline']['enabled']:
                reference_event = config['baseline']['reference_event']
                start_offset_ms = config['baseline']['start_offset_ms']
                duration_ms = config['baseline']['duration_ms']

                found_reference_key = next((key for key in trial['events'] if match_event_with_keywords(key, reference_event)), None)

                if found_reference_key:
                    reference_time = trial['events'][found_reference_key]
                    baseline_start_time = reference_time + start_offset_ms
                    baseline_end_time = baseline_start_time + duration_ms

                    baseline_df = processed_data[(processed_data['time'] >= baseline_start_time) & (processed_data['time'] <= baseline_end_time)]

                    if not baseline_df.empty and baseline_df[pupil_col].notna().any():
                        baseline_pupil = baseline_df[pupil_col].mean()
                        segment_df['pupil_corrected'] = segment_df[pupil_col] - baseline_pupil
                        # logger.info(f"受试者 {subject_id}, 试次 {i+1}: 基线校正完成，基线时间窗口: [{baseline_start_time:.1f}, {baseline_end_time:.1f}] ms，基线均值: {baseline_pupil:.2f}")
                    else:
                        logger.warning(f"受试者 {subject_id}, 试次 {i+1}: 基线数据不足或无效，跳过基线校正")
                        segment_df['pupil_corrected'] = segment_df[pupil_col]
                else:
                    logger.warning(f"受试者 {subject_id}, 试次 {i+1}: 未找到基线参考事件 '{reference_event}'，跳过基线校正")
                    segment_df['pupil_corrected'] = segment_df[pupil_col]
            else:
                segment_df['pupil_corrected'] = segment_df[pupil_col]
                
            # 添加试次信息
            segment_df['subject_id'] = subject_id
            segment_df['trial_id'] = i + 1
            segment_df['relative_time'] = segment_df['time'] - start_time
            for rating_var, rating_val in trial['ratings'].items():
                segment_df[rating_var] = rating_val
            
            all_segments.append(segment_df)

    if not all_segments:
        logger.warning(f"受试者 {subject_id}: 未能提取任何有效的数据段")
        return None
        
    return pd.concat(all_segments, ignore_index=True)


def aggregate_ratings(data: pd.DataFrame, rating_variable: str, aggregation_mapping: Dict[int, List[int]]) -> pd.DataFrame:
    """
    自定义评分聚合函数

    参数:
    - data: 包含评分数据的DataFrame
    - rating_variable: 要聚合的评分变量名称
    - aggregation_mapping: 聚合映射字典，格式为 {目标值: [原始值列表]}
                          例如: {1: [1, 2], 3: [3], 5: [4, 5]}

    返回:
    - 聚合后的DataFrame副本

    示例:
    >>> # 将评分1,2聚合为1，评分3保持为3，评分4,5聚合为5
    >>> mapping = {1: [1, 2], 3: [3], 5: [4, 5]}
    >>> aggregated_data = aggregate_ratings(data, 'PLEASURE', mapping)
    """
    if rating_variable not in data.columns:
        logger.error(f"评分变量 '{rating_variable}' 不存在于数据中")
        return data.copy()

    # 创建数据副本以避免修改原始数据
    aggregated_data = data.copy()

    # 创建反向映射：原始值 -> 目标值
    reverse_mapping = {}
    for target_value, original_values in aggregation_mapping.items():
        for original_value in original_values:
            reverse_mapping[original_value] = target_value

    # 应用聚合映射
    aggregated_data[f'{rating_variable}_aggregated'] = aggregated_data[rating_variable].map(reverse_mapping)

    # 检查是否有未映射的值
    unmapped_mask = aggregated_data[f'{rating_variable}_aggregated'].isnull() & aggregated_data[rating_variable].notnull()
    if unmapped_mask.any():
        unmapped_values = aggregated_data.loc[unmapped_mask, rating_variable].unique()
        logger.warning(f"发现未在聚合映射中定义的评分值: {unmapped_values}，这些值将被保留为原始值")
        # 对于未映射的值，保持原始值
        aggregated_data.loc[unmapped_mask, f'{rating_variable}_aggregated'] = aggregated_data.loc[unmapped_mask, rating_variable]

    logger.info(f"评分聚合完成。原始评分 '{rating_variable}' -> 聚合评分 '{rating_variable}_aggregated'")
    logger.info(f"聚合映射: {aggregation_mapping}")

    return aggregated_data


def plot_analysis_results(data: pd.DataFrame, rating_variable: str, config: Dict):
    """
    绘制分析结果图表：分别生成均值图和均值+标准差图，并标注最值差和标准差信息。
    """
    if data[rating_variable].isnull().all():
        logger.warning(f"评分类别 '{rating_variable}' 数据全部为空，跳过绘图。")
        return

    # 按评级和相对时间分组，计算均值和标准差
    agg_data = data.groupby([rating_variable, 'relative_time'])['pupil_corrected'].agg(['mean', 'std']).reset_index()

    # 计算每个评分等级的样本量（试次数量）
    sample_counts = data.groupby(rating_variable)['trial_id'].nunique().to_dict()

    # 计算每个rating的统计信息
    rating_stats = {}
    overall_mean = agg_data['mean'].mean()  # 全局均值

    for rating in sorted(agg_data[rating_variable].unique()):
        subset = agg_data[agg_data[rating_variable] == rating]
        rating_mean = subset['mean'].mean()
        rating_min = subset['mean'].min()
        rating_max = subset['mean'].max()
        rating_range = rating_max - rating_min  # 最值差
        rating_std_from_mean = np.sqrt(np.mean((subset['mean'] - rating_mean) ** 2))  # 相对于该rating均值的标准差
        rating_std_from_overall = np.sqrt(np.mean((subset['mean'] - overall_mean) ** 2))  # 相对于全局均值的标准差

        rating_stats[rating] = {
            'mean': rating_mean,
            'min': rating_min,
            'max': rating_max,
            'range': rating_range,
            'std_from_rating_mean': rating_std_from_mean,
            'std_from_overall_mean': rating_std_from_overall
        }

    # 准备绘图
    setup_chinese_font()
    ratings = sorted(agg_data[rating_variable].unique())
    colors = cm.get_cmap(config['plotting']['colormap'], len(ratings))
    output_dir = config['output_dir']
    os.makedirs(output_dir, exist_ok=True)

    # 图1: 仅均值（带统计信息标注）
    fig1, ax1 = plt.subplots(1, 1, figsize=(14, 10))

    # 绘制曲线
    for i, rating in enumerate(ratings):
        subset = agg_data[agg_data[rating_variable] == rating]
        n_samples = sample_counts.get(rating, 0)
        stats = rating_stats[rating]

        # 绘制主曲线
        ax1.plot(subset['relative_time'], subset['mean'],
                label=f'Rating {int(rating)} (n={n_samples})',
                color=colors(i), linewidth=2)

        # 标注最大值和最小值点
        max_idx = subset['mean'].idxmax()
        min_idx = subset['mean'].idxmin()
        max_time = subset.loc[max_idx, 'relative_time']
        min_time = subset.loc[min_idx, 'relative_time']
        max_val = subset.loc[max_idx, 'mean']
        min_val = subset.loc[min_idx, 'mean']

        # 标注最大值点
        ax1.scatter(max_time, max_val, color=colors(i), s=80, marker='^', zorder=5)
        ax1.annotate(f'Max: {max_val:.1f}',
                    xy=(max_time, max_val),
                    xytext=(10, 10), textcoords='offset points',
                    fontsize=9, color=colors(i), fontweight='bold',
                    bbox=dict(boxstyle='round,pad=0.3', facecolor='white', alpha=0.8))

        # 标注最小值点
        ax1.scatter(min_time, min_val, color=colors(i), s=80, marker='v', zorder=5)
        ax1.annotate(f'Min: {min_val:.1f}',
                    xy=(min_time, min_val),
                    xytext=(10, -20), textcoords='offset points',
                    fontsize=9, color=colors(i), fontweight='bold',
                    bbox=dict(boxstyle='round,pad=0.3', facecolor='white', alpha=0.8))

    # 添加统计信息文本框
    stats_text = "统计信息:\n"
    for rating in ratings:
        stats = rating_stats[rating]
        stats_text += f"Rating {int(rating)}:\n"
        stats_text += f"  最值差: {stats['range']:.2f}\n"
        stats_text += f"  相对自身均值标准差: {stats['std_from_rating_mean']:.2f}\n"
        stats_text += f"  相对全局均值标准差: {stats['std_from_overall_mean']:.2f}\n\n"

    # 在图表右上角添加统计信息
    ax1.text(0.98, 0.98, stats_text, transform=ax1.transAxes,
            fontsize=10, verticalalignment='top', horizontalalignment='right',
            bbox=dict(boxstyle='round,pad=0.5', facecolor='lightgray', alpha=0.8))

    ax1.set_title(f'Mean Pupil Response by {rating_variable} (with Statistics)', fontsize=16, fontweight='bold')
    ax1.set_xlabel('Time from Question Start (ms)')
    ax1.set_ylabel('Pupil Diameter Change (pixels, baseline corrected)')
    ax1.legend(loc='upper left')
    ax1.grid(True, alpha=0.3)
    plt.tight_layout()

    # 保存和显示图1
    if config['plotting']['save_plots']:
        save_path1 = os.path.join(output_dir, f'mean_plot_with_stats_{rating_variable}.png')
        plt.savefig(save_path1, dpi=300, bbox_inches='tight')
        logger.info(f"均值图表（含统计信息）已保存到: {save_path1}")

    if config['plotting']['show_plots']:
        plt.show()

    plt.close()

    # 图2: 均值 + 标准差阴影（带统计信息标注）
    fig2, ax2 = plt.subplots(1, 1, figsize=(14, 10))

    # 绘制曲线和标准差阴影
    for i, rating in enumerate(ratings):
        subset = agg_data[agg_data[rating_variable] == rating]
        n_samples = sample_counts.get(rating, 0)
        stats = rating_stats[rating]

        # 绘制主曲线
        ax2.plot(subset['relative_time'], subset['mean'],
                label=f'Rating {int(rating)} (n={n_samples})',
                color=colors(i), linewidth=2)

        # 绘制标准差阴影
        ax2.fill_between(subset['relative_time'],
                        subset['mean'] - subset['std'],
                        subset['mean'] + subset['std'],
                        color=colors(i), alpha=0.2)

        # 标注最大值和最小值点
        max_idx = subset['mean'].idxmax()
        min_idx = subset['mean'].idxmin()
        max_time = subset.loc[max_idx, 'relative_time']
        min_time = subset.loc[min_idx, 'relative_time']
        max_val = subset.loc[max_idx, 'mean']
        min_val = subset.loc[min_idx, 'mean']

        # 标注最大值点
        ax2.scatter(max_time, max_val, color=colors(i), s=80, marker='^', zorder=5)
        ax2.annotate(f'Max: {max_val:.1f}',
                    xy=(max_time, max_val),
                    xytext=(10, 10), textcoords='offset points',
                    fontsize=9, color=colors(i), fontweight='bold',
                    bbox=dict(boxstyle='round,pad=0.3', facecolor='white', alpha=0.8))

        # 标注最小值点
        ax2.scatter(min_time, min_val, color=colors(i), s=80, marker='v', zorder=5)
        ax2.annotate(f'Min: {min_val:.1f}',
                    xy=(min_time, min_val),
                    xytext=(10, -20), textcoords='offset points',
                    fontsize=9, color=colors(i), fontweight='bold',
                    bbox=dict(boxstyle='round,pad=0.3', facecolor='white', alpha=0.8))

    # 添加统计信息文本框
    stats_text = "统计信息:\n"
    for rating in ratings:
        stats = rating_stats[rating]
        stats_text += f"Rating {int(rating)}:\n"
        stats_text += f"  最值差: {stats['range']:.2f}\n"
        stats_text += f"  相对自身均值标准差: {stats['std_from_rating_mean']:.2f}\n"
        stats_text += f"  相对全局均值标准差: {stats['std_from_overall_mean']:.2f}\n\n"

    # 在图表右上角添加统计信息
    ax2.text(0.98, 0.98, stats_text, transform=ax2.transAxes,
            fontsize=10, verticalalignment='top', horizontalalignment='right',
            bbox=dict(boxstyle='round,pad=0.5', facecolor='lightgray', alpha=0.8))

    ax2.set_title(f'Mean Pupil Response with Standard Deviation by {rating_variable} (with Statistics)', fontsize=16, fontweight='bold')
    ax2.set_xlabel('Time from Question Start (ms)')
    ax2.set_ylabel('Pupil Diameter Change (pixels, baseline corrected)')
    ax2.legend(loc='upper left')
    ax2.grid(True, alpha=0.3)
    plt.tight_layout()

    # 保存和显示图2
    if config['plotting']['save_plots']:
        save_path2 = os.path.join(output_dir, f'mean_std_plot_with_stats_{rating_variable}.png')
        plt.savefig(save_path2, dpi=300, bbox_inches='tight')
        logger.info(f"均值+标准差图表（含统计信息）已保存到: {save_path2}")

    if config['plotting']['show_plots']:
        plt.show()

    plt.close()


def analyze_time_window_pupil_means(data: pd.DataFrame, trial_info: List[Dict[str, Any]], rating_variable: str, config: Dict):
    """
    分析指定时间段内不同评分等级的瞳孔均值。

    参数:
    - data: 包含瞳孔数据的DataFrame
    - trial_info: 试次信息列表
    - rating_variable: 评分变量名称
    - config: 配置字典
    """
    if not config['time_window_analysis']['enabled']:
        return

    logger.info(f"--- 开始指定时间段瞳孔均值分析: {rating_variable} ---")

    # 获取配置参数
    reference_event = config['time_window_analysis']['reference_event']
    time_before_event_ms = config['time_window_analysis']['time_before_event_ms']
    window_duration_ms = config['time_window_analysis']['window_duration_ms']

    # 存储每个试次在指定时间窗口内的瞳孔均值
    window_data = []

    # 获取所有唯一的试次ID和对应的受试者ID
    unique_trials = data[['trial_id', 'subject_id']].drop_duplicates()

    # 遍历每个试次
    for _, trial_row in unique_trials.iterrows():
        trial_id = trial_row['trial_id']
        subject_id = trial_row['subject_id']

        # 找到对应的试次信息（需要考虑可能有多个受试者的情况）
        # 这里假设trial_info是按顺序排列的，需要根据实际情况调整
        if trial_id - 1 < len(trial_info):
            trial = trial_info[trial_id - 1]
        else:
            logger.warning(f"试次 {trial_id}: 在trial_info中未找到对应信息，跳过")
            continue

        # 查找参考事件时间
        found_reference_key = next((key for key in trial['events'] if match_event_with_keywords(key, reference_event)), None)

        if not found_reference_key:
            logger.warning(f"试次 {trial_id}: 未找到参考事件 '{reference_event}'，跳过")
            continue

        reference_time = trial['events'][found_reference_key]
        window_start_time = reference_time - time_before_event_ms
        window_end_time = window_start_time + window_duration_ms

        # 获取该试次的数据
        trial_data = data[(data['trial_id'] == trial_id) & (data['subject_id'] == subject_id)]
        if trial_data.empty:
            continue

        # 提取时间窗口内的数据（使用相对时间计算绝对时间）
        # 需要将相对时间转换回绝对时间
        trial_start_time = trial_data['time'].min() - trial_data['relative_time'].min()
        absolute_window_start = window_start_time
        absolute_window_end = window_end_time

        window_segment = trial_data[
            (trial_data['time'] >= absolute_window_start) &
            (trial_data['time'] <= absolute_window_end)
        ]

        if window_segment.empty or window_segment['pupil_corrected'].isna().all():
            logger.warning(f"试次 {trial_id}: 时间窗口 [{absolute_window_start:.1f}, {absolute_window_end:.1f}] 内无有效瞳孔数据")
            continue

        # 计算该时间窗口内的瞳孔均值
        pupil_mean = window_segment['pupil_corrected'].mean()

        # 获取该试次的评分
        rating_col = rating_variable if rating_variable in trial_data.columns else rating_variable.replace('_aggregated', '')
        if rating_col in trial_data.columns:
            rating_value = trial_data[rating_col].iloc[0]

            window_data.append({
                'trial_id': trial_id,
                'subject_id': subject_id,
                'rating': rating_value,
                'pupil_mean': pupil_mean,
                'window_start': absolute_window_start,
                'window_end': absolute_window_end
            })

    if not window_data:
        logger.warning(f"未找到任何有效的时间窗口数据用于分析 {rating_variable}")
        return

    # 转换为DataFrame
    window_df = pd.DataFrame(window_data)

    # 按评分等级分组计算统计量
    rating_stats = window_df.groupby('rating')['pupil_mean'].agg(['mean', 'std', 'count']).reset_index()

    logger.info(f"时间窗口分析结果 ({rating_variable}):")
    logger.info(f"分析窗口: {reference_event} 前 {time_before_event_ms}ms 到前 {time_before_event_ms - window_duration_ms}ms")
    for _, row in rating_stats.iterrows():
        logger.info(f"评分 {int(row['rating'])}: 均值={row['mean']:.2f}, 标准差={row['std']:.2f}, 样本数={int(row['count'])}")

    # 绘制直方图
    plot_time_window_results(rating_stats, rating_variable, config)

    return rating_stats


def plot_time_window_results(rating_stats: pd.DataFrame, rating_variable: str, config: Dict):
    """
    绘制指定时间段瞳孔均值的直方图。

    参数:
    - rating_stats: 包含各评分等级统计量的DataFrame
    - rating_variable: 评分变量名称
    - config: 配置字典
    """
    setup_chinese_font()

    # 准备数据
    ratings = rating_stats['rating'].values.astype(float)
    means = rating_stats['mean'].values.astype(float)
    stds = rating_stats['std'].values.astype(float)
    counts = rating_stats['count'].values.astype(int)

    # 创建图表
    fig, ax = plt.subplots(1, 1, figsize=(10, 6))

    # 绘制直方图（均值）和误差棒（标准差）
    colors = cm.get_cmap(config['plotting']['colormap'], len(ratings))
    bar_colors = [colors(i) for i in range(len(ratings))]

    bars = ax.bar(ratings, means, yerr=stds, capsize=5, color=bar_colors, alpha=0.7, edgecolor='black')

    # 设置图表属性
    time_before = config['time_window_analysis']['time_before_event_ms']
    window_duration = config['time_window_analysis']['window_duration_ms']
    reference_event = config['time_window_analysis']['reference_event']

    ax.set_title(f'{rating_variable} 评分在指定时间段的瞳孔均值\n'
                f'({reference_event} 前 {time_before}ms 到前 {time_before - window_duration}ms)',
                fontsize=14, fontweight='bold')
    ax.set_xlabel(f'{rating_variable} 评分等级')
    ax.set_ylabel('瞳孔直径变化均值 (像素, 基线校正)')
    ax.set_xticks(ratings)
    ax.grid(True, alpha=0.3, axis='y')
    ax.set_ylim(4000, 5600)  # 设置y轴的范围从-50到200

    # 在每个柱子上显示数值和样本量
    for i, (rating, mean_val, std_val, count) in enumerate(zip(ratings, means, stds, counts)):
        ax.text(rating, mean_val + std_val + (max(means) * 0.02),
                f'{mean_val:.1f}±{std_val:.1f}\n(n={count})',
                ha='center', va='bottom', fontsize=10, fontweight='bold')

    # 添加图例显示总体样本量信息
    total_samples = sum(counts)
    legend_text = f'总样本量: n={total_samples}'
    ax.text(0.02, 0.98, legend_text, transform=ax.transAxes,
            fontsize=12, fontweight='bold',
            bbox=dict(boxstyle='round', facecolor='white', alpha=0.8),
            verticalalignment='top')

    plt.tight_layout()

    # 保存图表
    if config['time_window_analysis']['save_plots']:
        output_dir = config['output_dir']
        os.makedirs(output_dir, exist_ok=True)
        save_path = os.path.join(output_dir, f'time_window_analysis_{rating_variable}.png')
        plt.savefig(save_path, dpi=300, bbox_inches='tight')
        logger.info(f"时间窗口分析图表已保存到: {save_path}")

    # 显示图表
    if config['time_window_analysis']['show_plots']:
        plt.show()

    plt.close()


def analyze_lhipa_by_rating(data: pd.DataFrame, trial_info: List[Dict[str, Any]], rating_variable: str, config: Dict):
    """
    分析指定时间段内不同评分等级的LHIPA值。

    参数:
    - data: 包含瞳孔数据的DataFrame
    - trial_info: 试次信息列表
    - rating_variable: 评分变量名称
    - config: 配置字典
    """
    if not config['lhipa_analysis']['enabled']:
        return

    if not LHIPA_AVAILABLE:
        logger.warning("LHIPA模块不可用，跳过LHIPA分析")
        return

    logger.info(f"--- 开始LHIPA分析: {rating_variable} ---")

    # 获取配置参数
    reference_event = config['lhipa_analysis']['reference_event']
    time_before_event_ms = config['lhipa_analysis']['time_before_event_ms']
    window_duration_ms = config['lhipa_analysis']['window_duration_ms']

    # 存储每个试次在指定时间窗口内的LHIPA值
    lhipa_data = []

    # 获取所有唯一的试次ID和对应的受试者ID
    unique_trials = data[['trial_id', 'subject_id']].drop_duplicates()

    # 遍历每个试次
    for _, trial_row in unique_trials.iterrows():
        trial_id = trial_row['trial_id']
        subject_id = trial_row['subject_id']

        # 找到对应的试次信息
        if trial_id - 1 < len(trial_info):
            trial = trial_info[trial_id - 1]
        else:
            logger.warning(f"试次 {trial_id}: 在trial_info中未找到对应信息，跳过")
            continue

        # 查找参考事件时间
        found_reference_key = next((key for key in trial['events'] if match_event_with_keywords(key, reference_event)), None)

        if not found_reference_key:
            logger.warning(f"试次 {trial_id}: 未找到参考事件 '{reference_event}'，跳过")
            continue

        reference_time = trial['events'][found_reference_key]
        window_start_time = reference_time - time_before_event_ms
        window_end_time = window_start_time + window_duration_ms

        # 获取该试次的数据
        trial_data = data[(data['trial_id'] == trial_id) & (data['subject_id'] == subject_id)]
        if trial_data.empty:
            continue

        # 提取时间窗口内的数据
        window_segment = trial_data[
            (trial_data['time'] >= window_start_time) &
            (trial_data['time'] <= window_end_time)
        ]

        if window_segment.empty or window_segment['pupil_corrected'].isna().all():
            logger.warning(f"试次 {trial_id}: 时间窗口 [{window_start_time:.1f}, {window_end_time:.1f}] 内无有效瞳孔数据")
            continue

        # 检查数据点数量是否足够进行LHIPA计算
        if len(window_segment) < 100:
            logger.warning(f"试次 {trial_id}: 数据点太少({len(window_segment)})，跳过LHIPA计算")
            continue

        try:
            # 转换数据格式为PyPil格式
            pypil_data = convert_to_pypil_format(window_segment, time_col='time', pupil_col='pupil_corrected')

            if len(pypil_data) < 100:
                logger.warning(f"试次 {trial_id}: 转换后数据点太少({len(pypil_data)})，跳过LHIPA计算")
                continue

            # 计算IPA和LHIPA
            ipa_calc = IPA(pypil_data)
            lhipa_calc = LHIPA(pypil_data)

            ipa_value = ipa_calc.ipa
            lhipa_value = lhipa_calc.lhipa

            # 获取该试次的评分
            rating_col = rating_variable if rating_variable in trial_data.columns else rating_variable.replace('_aggregated', '')
            if rating_col in trial_data.columns:
                rating_value = trial_data[rating_col].iloc[0]

                lhipa_data.append({
                    'trial_id': trial_id,
                    'subject_id': subject_id,
                    'rating': rating_value,
                    'ipa_value': ipa_value,
                    'lhipa_value': lhipa_value,
                    'window_start': window_start_time,
                    'window_end': window_end_time
                })

                logger.debug(f"试次 {trial_id}: IPA={ipa_value:.6f}, LHIPA={lhipa_value:.6f}, Rating={rating_value}")

        except Exception as e:
            logger.warning(f"试次 {trial_id}: LHIPA计算失败: {e}")
            continue

    if not lhipa_data:
        logger.warning(f"未找到任何有效的LHIPA数据用于分析 {rating_variable}")
        return

    # 转换为DataFrame
    lhipa_df = pd.DataFrame(lhipa_data)

    # 按评分等级分组计算统计量
    ipa_stats = lhipa_df.groupby('rating')['ipa_value'].agg(['mean', 'std', 'count']).reset_index()
    lhipa_stats = lhipa_df.groupby('rating')['lhipa_value'].agg(['mean', 'std', 'count']).reset_index()

    logger.info(f"LHIPA分析结果 ({rating_variable}):")
    logger.info(f"分析窗口: {reference_event} 前 {time_before_event_ms}ms 到前 {time_before_event_ms - window_duration_ms}ms")

    for _, row in ipa_stats.iterrows():
        logger.info(f"评分 {int(row['rating'])}: IPA均值={row['mean']:.6f}, IPA标准差={row['std']:.6f}, 样本数={int(row['count'])}")

    for _, row in lhipa_stats.iterrows():
        logger.info(f"评分 {int(row['rating'])}: LHIPA均值={row['mean']:.6f}, LHIPA标准差={row['std']:.6f}, 样本数={int(row['count'])}")

    # 绘制LHIPA结果图表
    plot_lhipa_results(ipa_stats, lhipa_stats, rating_variable, config)

    return ipa_stats, lhipa_stats


def plot_lhipa_results(ipa_stats: pd.DataFrame, lhipa_stats: pd.DataFrame, rating_variable: str, config: Dict):
    """
    绘制LHIPA分析结果的直方图。

    参数:
    - ipa_stats: 包含各评分等级IPA统计量的DataFrame
    - lhipa_stats: 包含各评分等级LHIPA统计量的DataFrame
    - rating_variable: 评分变量名称
    - config: 配置字典
    """
    setup_chinese_font()

    # 准备数据
    ratings = ipa_stats['rating'].values.astype(float)
    ipa_means = ipa_stats['mean'].values.astype(float)
    ipa_stds = ipa_stats['std'].values.astype(float)
    ipa_counts = ipa_stats['count'].values.astype(int)

    lhipa_means = lhipa_stats['mean'].values.astype(float)
    lhipa_stds = lhipa_stats['std'].values.astype(float)
    lhipa_counts = lhipa_stats['count'].values.astype(int)

    # 创建子图
    fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(16, 6))

    # 颜色设置
    colors = cm.get_cmap(config['plotting']['colormap'], len(ratings))
    bar_colors = [colors(i) for i in range(len(ratings))]

    # 绘制IPA直方图
    bars1 = ax1.bar(ratings, ipa_means, yerr=ipa_stds, capsize=5, color=bar_colors, alpha=0.7, edgecolor='black')

    time_before = config['lhipa_analysis']['time_before_event_ms']
    window_duration = config['lhipa_analysis']['window_duration_ms']
    reference_event = config['lhipa_analysis']['reference_event']

    ax1.set_title(f'{rating_variable} 评分的IPA值\n'
                 f'({reference_event} 前 {time_before}ms 到前 {time_before - window_duration}ms)',
                 fontsize=14, fontweight='bold')
    ax1.set_xlabel(f'{rating_variable} 评分等级')
    ax1.set_ylabel('IPA值 (瞳孔活动指数)')
    ax1.set_xticks(ratings)
    ax1.grid(True, alpha=0.3, axis='y')

    # 在每个柱子上显示数值和样本量
    for i, (rating, mean_val, std_val, count) in enumerate(zip(ratings, ipa_means, ipa_stds, ipa_counts)):
        ax1.text(rating, mean_val + std_val + (max(ipa_means) * 0.02),
                f'{mean_val:.4f}±{std_val:.4f}\n(n={count})',
                ha='center', va='bottom', fontsize=10, fontweight='bold')

    # 绘制LHIPA直方图
    bars2 = ax2.bar(ratings, lhipa_means, yerr=lhipa_stds, capsize=5, color=bar_colors, alpha=0.7, edgecolor='black')

    ax2.set_title(f'{rating_variable} 评分的LHIPA值\n'
                 f'({reference_event} 前 {time_before}ms 到前 {time_before - window_duration}ms)',
                 fontsize=14, fontweight='bold')
    ax2.set_xlabel(f'{rating_variable} 评分等级')
    ax2.set_ylabel('LHIPA值 (低/高频瞳孔活动指数)')
    ax2.set_xticks(ratings)
    ax2.grid(True, alpha=0.3, axis='y')
    # ax2.set_ylim(35, 42)

    # 在每个柱子上显示数值和样本量
    for i, (rating, mean_val, std_val, count) in enumerate(zip(ratings, lhipa_means, lhipa_stds, lhipa_counts)):
        ax2.text(rating, mean_val + std_val + (max(lhipa_means) * 0.02),
                f'{mean_val:.4f}±{std_val:.4f}\n(n={count})',
                ha='center', va='bottom', fontsize=10, fontweight='bold')

    # 添加总体样本量信息
    total_samples = sum(ipa_counts)
    legend_text = f'总样本量: n={total_samples}'

    ax1.text(0.02, 0.98, legend_text, transform=ax1.transAxes,
            fontsize=12, fontweight='bold',
            bbox=dict(boxstyle='round', facecolor='white', alpha=0.8),
            verticalalignment='top')

    ax2.text(0.02, 0.98, legend_text, transform=ax2.transAxes,
            fontsize=12, fontweight='bold',
            bbox=dict(boxstyle='round', facecolor='white', alpha=0.8),
            verticalalignment='top')

    plt.tight_layout()

    # 保存图表
    if config['lhipa_analysis']['save_plots']:
        output_dir = config['output_dir']
        os.makedirs(output_dir, exist_ok=True)
        save_path = os.path.join(output_dir, f'lhipa_analysis_{rating_variable}.png')
        plt.savefig(save_path, dpi=300, bbox_inches='tight')
        logger.info(f"LHIPA分析图表已保存到: {save_path}")

    # 显示图表
    if config['lhipa_analysis']['show_plots']:
        plt.show()

    plt.close()


def main_analysis(config: Dict):
    """
    主分析函数，执行完整的EDF数据处理、分析和绘图流程。
    """
    logger.info("--- 开始主分析流程 ---")
    
    preprocessor = EyeDataPreprocessor(**config['preprocessing_params'])
    all_subject_segments = []

    # 1. 读取和处理每个EDF文件
    for edf_path in config['data_sources']:
        if not os.path.exists(edf_path):
            logger.error(f"EDF文件未找到: {edf_path}，跳过该文件。")
            continue
        
        subject_id = os.path.splitext(os.path.basename(edf_path))[0]
        logger.info(f"--- 正在处理受试者: {subject_id} ---")

        # 加载数据
        samples, _, messages = preprocessor.load_edf_data(edf_path)
        if samples is None or messages is None:
            logger.error(f"无法从 {edf_path} 加载数据，跳过。")
            continue

        # 预处理数据
        processed_data = preprocessor.preprocess_edf(edf_path)
        if processed_data.empty:
            logger.error(f"预处理失败: {edf_path}，跳过。")
            continue
        
        # 解析试次信息
        trial_info = parse_trial_messages(messages, config)
        if not trial_info:
            logger.error(f"未能从 {edf_path} 解析试次信息，跳过。")
            continue
            
        # 提取数据段
        subject_segments = extract_all_trial_segments(subject_id, processed_data, trial_info, config)
        if subject_segments is not None:
            all_subject_segments.append(subject_segments)
            logger.info(f"受试者 {subject_id} 处理完成，提取了 {len(subject_segments['trial_id'].unique())} 个有效试次片段。")

    if not all_subject_segments:
        logger.error("所有文件处理完毕，但未提取到任何有效数据。分析终止。")
        return

    # 2. 合并所有受试者的数据
    final_df = pd.concat(all_subject_segments, ignore_index=True)
    logger.info(f"所有数据处理完成，共合并 {len(final_df['subject_id'].unique())} 个受试者的数据。")

    # 收集所有试次信息用于时间窗口分析
    all_trial_info = []
    for edf_path in config['data_sources']:
        if not os.path.exists(edf_path):
            continue
        preprocessor = EyeDataPreprocessor(**config['preprocessing_params'])
        samples, _, messages = preprocessor.load_edf_data(edf_path)
        if messages is not None:
            trial_info = parse_trial_messages(messages, config)
            all_trial_info.extend(trial_info)

    # 3. 按评分类别进行分析和绘图
    for rating_var in config['rating_variables']:
        if rating_var in final_df.columns:
            logger.info(f"--- 正在分析评分类别: {rating_var} ---")

            # 检查是否需要进行评分聚合
            if 'rating_aggregation' in config and rating_var in config['rating_aggregation']:
                logger.info(f"对评分变量 '{rating_var}' 应用自定义聚合")
                aggregated_df = aggregate_ratings(final_df, rating_var, config['rating_aggregation'][rating_var])
                # 使用聚合后的评分变量进行绘图
                aggregated_var = f'{rating_var}_aggregated'
                plot_analysis_results(aggregated_df, aggregated_var, config)

                # 时间窗口分析（使用聚合后的数据）
                if config['time_window_analysis']['enabled']:
                    analyze_time_window_pupil_means(aggregated_df, all_trial_info, aggregated_var, config)

                # LHIPA分析（使用聚合后的数据）
                if config['lhipa_analysis']['enabled']:
                    analyze_lhipa_by_rating(aggregated_df, all_trial_info, aggregated_var, config)
            else:
                # 使用原始评分进行绘图
                plot_analysis_results(final_df, rating_var, config)

                # 时间窗口分析（使用原始数据）
                if config['time_window_analysis']['enabled']:
                    analyze_time_window_pupil_means(final_df, all_trial_info, rating_var, config)

                # LHIPA分析（使用原始数据）
                if config['lhipa_analysis']['enabled']:
                    analyze_lhipa_by_rating(final_df, all_trial_info, rating_var, config)
        else:
            logger.warning(f"在最终数据中未找到评分类别 '{rating_var}'，跳过分析。")

    logger.info("--- 主分析流程结束 ---")

if __name__ == "__main__":
    # 在运行前，请确保 CONFIG 字典中的 `data_sources` 指向了您本地的EDF文件。
    main_analysis(CONFIG) 