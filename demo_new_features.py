#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
演示新功能：动态答案显示和漂移校正
"""

import sys
import os
import time

# 添加当前目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from experiment_display import ExperimentDisplay
from eyelink_manager import EyeLinkManager

def demo_dynamic_answer():
    """演示动态答案显示功能"""
    print("="*60)
    print("演示功能：动态答案显示")
    print("="*60)
    print("新功能说明：")
    print("1. 答案显示时间不再固定")
    print("2. 屏幕下方显示'读完看我哦'按钮")
    print("3. 当眼动持续注视按钮区域1秒后自动继续")
    print("4. 可调节注视时间阈值参数")
    print()
    
    # 创建显示管理器（虚拟模式演示）
    display = ExperimentDisplay(fullscreen=True)
    
    if display.dummy_mode:
        print("当前为虚拟模式演示")
        print("在实际使用中，会显示PsychoPy窗口和眼动检测")
        print()
        
        # 模拟答案显示
        test_answer = """这是一个示例答案，展示了动态时间控制功能。
        
在实际实验中：
• 被试会看到完整的答案文本
• 屏幕下方会显示蓝色的"读完看我哦"按钮
• EyeLink会实时监测眼动位置
• 当被试注视按钮区域持续1秒后，自动进入下一阶段
• 这确保被试真正读完了答案内容

可调节参数：
• gaze_duration_threshold: 注视时间阈值（默认1.0秒）
• 按钮位置和大小可以调整
• 支持Esc键手动跳过"""
        
        print("模拟显示答案...")
        start_time = time.time()
        
        # 调用新的动态答案显示功能
        result = display.show_answer(
            answer=test_answer,
            eyelink_manager=None,  # 虚拟模式下传入None
            gaze_duration_threshold=1.0
        )
        
        duration = time.time() - start_time
        print(f"✓ 答案显示完成，用时: {duration:.2f}秒")
        print(f"返回结果: {'正常完成' if result else '被中断'}")
        input("按回车键继续...")
    
    display.close()

def demo_drift_correction():
    """演示漂移校正功能"""
    print("\n" + "="*60)
    print("演示功能：十字准星漂移校正")
    print("="*60)
    print("新功能说明：")
    print("1. 在显示十字准星时可以进行漂移校正")
    print("2. 调用EyeLink的doDriftCorrect()函数")
    print("3. 提示被试注视中央十字准星")
    print("4. 自动执行漂移校正并显示结果")
    print()
    
    # 创建显示管理器和EyeLink管理器
    display = ExperimentDisplay(fullscreen=False)
    eyelink = EyeLinkManager("demo", "test_data", (1920, 1080), dummy_mode=True)
    eyelink.connect()
    
    if display.dummy_mode:
        print("当前为虚拟模式演示")
        print("在实际使用中，会执行真实的EyeLink漂移校正")
        print()
        
        print("模拟十字准星显示和漂移校正...")
        start_time = time.time()
        
        # 调用新的十字准星显示功能（带漂移校正）
        result = display.show_fixation_cross(
            duration=3.0,
            eyelink_manager=eyelink,
            perform_drift_correction=True
        )
        
        duration = time.time() - start_time
        print(f"✓ 十字准星显示完成，用时: {duration:.2f}秒")
        print(f"返回结果: {'正常完成' if result else '被中断'}")
    
    eyelink.close()
    display.close()

def demo_integration():
    """演示集成到实验流程中的效果"""
    print("\n" + "="*60)
    print("演示功能：实验流程集成")
    print("="*60)
    print("修改说明：")
    print("1. experiment_flow.py中的试次流程已更新")
    print("2. 基线校准阶段会自动执行漂移校正")
    print("3. 答案显示阶段使用动态时间控制")
    print("4. 新增gaze_duration_threshold参数")
    print()
    
    try:
        from experiment_flow import CuriosityExperiment
        
        # 创建实验对象
        experiment = CuriosityExperiment("demo", use_eyelink=False, fullscreen=False)
        
        print("实验时间参数配置：")
        for key, value in experiment.timing.items():
            if value is None:
                print(f"  {key}: 动态控制")
            else:
                print(f"  {key}: {value}")
        
        print(f"\n✓ 实验流程已成功集成新功能")
        print(f"✓ 注视时间阈值: {experiment.timing['gaze_duration_threshold']}秒")
        
    except Exception as e:
        print(f"✗ 实验流程集成测试失败: {e}")

def main():
    """主函数"""
    print("新功能演示程序")
    print("="*80)
    print("本程序演示以下新增功能：")
    print("1. 动态答案显示时间控制")
    print("2. 十字准星漂移校正")
    print("3. 实验流程集成")
    print("="*80)
    
    try:
        # 演示1：动态答案显示
        demo_dynamic_answer()
        
        # 演示2：漂移校正
        demo_drift_correction()
        
        # 演示3：实验流程集成
        demo_integration()
        
        print("\n" + "="*80)
        print("🎉 所有新功能演示完成！")
        print("="*80)
        print("使用说明：")
        print("• 在实际实验中，请确保EyeLink设备已连接")
        print("• 设置use_eyelink=True以启用真实眼动检测")
        print("• 可以通过timing参数调节gaze_duration_threshold")
        print("• 漂移校正会在每个试次的基线阶段自动执行")
        
    except KeyboardInterrupt:
        print("\n演示被用户中断")
    except Exception as e:
        print(f"\n演示过程中出现错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
