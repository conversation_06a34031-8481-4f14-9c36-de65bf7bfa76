{"cells": [{"cell_type": "markdown", "id": "3d00cd80", "metadata": {}, "source": ["# <center>Eye Tracking Project</center>\n", "\n", "## Table of Contents\n", "\n", "1. [Import Requirements](#Import-Requirements)\n", "2. [Context Information](#Context-information)\n", "3. [Paper 1: IPA](#The-Index-of-Pupillary-Activity)\n", "    1. [Version 1](#Version-1) Hardcoded Version "]}, {"cell_type": "markdown", "id": "bb50dd25", "metadata": {}, "source": ["## Import Requirements\n", "These are the required packages to make the program work, I tried to limit it to only what was found within the original pseudocode with only a few additional ones where I thought they would be best used."]}, {"cell_type": "code", "execution_count": 66, "id": "e88a2d89", "metadata": {}, "outputs": [], "source": ["import math\n", "import numpy as np\n", "import pandas as pd\n", "import pywt\n", "\n", "from matplotlib import pyplot as plt\n", "from scipy.integrate import quad"]}, {"cell_type": "markdown", "id": "ba9ff16d", "metadata": {}, "source": ["## Context information\n", "I will be trying to implement three pupillometric measures from <PERSON><PERSON><PERSON>'s papers regarding: \n", "- 1) Index of Pupillary Activity (IPA), \n", "- 2) Low/High Index of Pupillary Activity (LHIPA), \n", "- 3) Reat-Time-Index of Pupillary Activity (RIPA) \n", "\n", "Which are all loosely based on <PERSON>'s Index of Cognitive Activity (ICA) from 2002."]}, {"cell_type": "markdown", "id": "d2bdf0b4", "metadata": {}, "source": ["## The Index of Pupillary Activity\n", "The paper <PERSON><PERSON><PERSON> et al. (2018) is the basis for this and it is the first iteration of their eye-tracked measure of the requency of pupil diameter oscillation. What this paper achieves is creating/replicating the Index of Cognitive Activity (ICA) and in general improving upon it without necessarily being copyrighted. \n", "\n", "\n", "## Version 1\n", "This version was created before I was aware that PyWavelet could do 90% of what I needed for the code. I kept this part here as part of the coding process but it is ultimately left out of the final version and is only found in the PDF version of the code.\n", "\n", "### IPA Implementation\n", "There are roughly six formulas that are used within the paper to calculate IPA. Below we will go through each one and how they are implemented, what their variables are, and how they connect to one another. \n", "\n", "\n", "#### Formula 1: Dyadic Wavelet Function\n", "We will do this with the following variables:\n", "- $x(t)$: pupil diameter signal over time\n", "- $\\psi(t)$: the mother wavelet function \n", "    - (Daubechies-4 if 60Hz or Daubechies-16 if 250Hz)\n", "- $j$: dilation parameter, integers from a set that can represent any number\n", "- $k$: translation parameter, integers from a set that can represent any number\n", "- $2^{j/2}$: Scaling factor w.r.t. time domain\n", "- $\\psi(2^j t - k)$: Shifted and scaled wavelet function\n", "\n", "$$\n", "\\psi_{j,k}(t) = 2^{j/2} \\psi(2^j t - k), \\quad \\text{where } j, k \\in \\mathbb{Z} \\qquad \\text{(0)}\n", "$$"]}, {"cell_type": "code", "execution_count": 61, "id": "a1cc1b9b", "metadata": {}, "outputs": [], "source": ["# Notable concerns:\n", "# - None\n", "\n", "def check_for_integers(list_given):\n", "    \"\"\"\n", "    Function Description: This will check if the list provided is made only of integers\n", "    \n", "    Parameters:\n", "        list_given = a list\n", "        \n", "    Returns:\n", "        Verification that the list is only Integers\n", "    \"\"\"\n", "    try:\n", "        import numpy as np\n", "    except ImportError:\n", "        raise ImportError(\"<PERSON><PERSON><PERSON> is not imported.\")\n", "    return np.all(np.array(list_given, dtype=np.int64)) | np.all(np.array(list_given, dtype=np.int32))\n", "\n", "\n", "# Notable concerns:\n", "# - We will need more than one input feature as we don't have j or k\n", "# - We are unsure if we're using 60Hz or 250Hz so we'll assume 60Hz\n", "\n", "def compute_dyadic_wavelet(t, j, k, w):\n", "    \"\"\"\n", "    Function Description: This will calculate the dyadic wavelet transformation.\n", "    \n", "    Parameters:\n", "        t = pupil diameter (float)\n", "        j = dilation parameter (integer)\n", "        k = translation parameter (integer)\n", "        w = wavelet (string)\n", "    \n", "    Returns: \n", "        This returns the calculation of the dyadic wavelet transformation.\n", "    \n", "    \"\"\"\n", "    try:\n", "        import pywt\n", "    except ImportError:\n", "        raise ImportError(\"Py Wavelet is not imported.\")\n", "        \n", "    try:\n", "        # This will make sure that 𝑗,𝑘∈ℤ\n", "        if not (check_for_integers(j) and check_for_integers(k)):\n", "            raise ValueError(\"j and k must be lists of integers.\")\n", "        \n", "        # This will make sure that we're only inputting the correct db\n", "        if not (isinstance(w, str) and w in [\"db4\", \"db16\"]):\n", "            raise ValueError(\"w must be either Daubechies-4 or Daubechies-16\")\n", "        \n", "    \n", "        # Determine what wavelet we are using\n", "        wf = pywt.<PERSON><PERSON>(w)\n", "\n", "        # Calculation of the Scaling Factor and Translation Factor\n", "        psi_t = 2**(j/2) * wf.wavefun(level=2)[0]  \n", "        interior_calc = psi_t(2**j * t - k) \n", "\n", "        return interior_calc\n", "    \n", "    except ValueError as ve:\n", "        print(\"ValueError:\", ve)\n", "        return None"]}, {"cell_type": "markdown", "id": "405c7b87", "metadata": {}, "source": ["#### Formula 2: Integral Transformation of Wavelet Coefficients\n", "This step is far more complicated as it goes through several iterations of it within the <PERSON><PERSON><PERSON> et al. (2018) paper, but we'll mainly focus on the steps taht we can complete here.\n", "\n", "(For the sake of space, repeated variables will not be given descriptions)\n", "\n", "##### Decomposition of the Wavelet Analysis w.r.t. coefficients\n", "This formula gives us the wavelet function as a linear combination that allows us to do wavelet decomposition. \n", "\n", "Variable representation:\n", "- $L^2(\\mathbb{R})$: Noting that $x(t)$ is a square-integrable function\n", "    - (meaning it won't become infinitely large when squared or integrated)\n", "- $\\sum_{j,k = -\\infty}^{\\infty}$: represents the sum over all possible combinations of j and k that are integers.\n", "- $c_{j,k}$: wavelet coefficients of $\\psi_{j,k}(t)$\n", "- $x(t)$: pupil diameter signal\n", "- $\\psi_{j,k}(t)$: the mother wavelet function \n", "    - (Daubechies-4 if 60Hz or Daubechies-16 if 250Hz)\n", "\n", "\n", "$$\n", "x \\in L^2(\\mathbb{R}): x(t) = \\sum_{j,k = -\\infty}^{\\infty} c_{j,k} \\psi_{j,k}(t), \\quad j,k \\in \\mathbb{Z} \\qquad \\text{(1)}\n", "$$\n", "\n", "##### Inner Product Calculation of the Wavelet Coefficients\n", "This formula computes the wavelet coefficients by taking the inner product of the input $x(t)$ and tells us how well the function performs at different scales and translations.\n", "\n", "Variable representation:\n", "- $c_{j,k}$: already noted\n", "- $\\int_{-\\infty}^{\\infty}$: Integral of the formula, represents area under the curve\n", "- $x(t)$: already noted\n", "- $\\overline{\\psi_{j,k}(t)}$: We are taking the complex conjugate of our previous wavelet function. \n", "    - (this ensures that the result is a real value)\n", "- $dt$: The integration of the variable t\n", "\n", "$$\n", "c_{j,k} = \\int_{-\\infty}^{\\infty} x(t) \\overline{\\psi_{j,k}(t)} dt, \\quad x \\in L^2(\\mathbb{R}), \\quad j,k \\in \\mathbb{Z} \\qquad \\text{(2)}\n", "$$\n", "\n", "##### Substitution Property for Formula 1 and Formula 2\n", "This formula incorporates Formula 1 into Formula 2 where the $\\psi$ was located.\n", "\n", "Variables inlcuded from Formula 1:\n", "- $j$: dilation parameter, integers from a set that can represent any number\n", "- $k$: translation parameter, integers from a set that can represent any number\n", "- $2^{j/2}$: Scaling factor w.r.t. time domain\n", "- $\\psi(2^j t - k)$: Shifted and scaled wavelet function\n", "\n", "$$\n", "c_{j,k} = 2^{j/2} \\int_{-\\infty}^{\\infty} x(t) \\overline{\\psi(2^j t - k)} \\, dt, \\quad x \\in L^2(\\mathbb{R}), \\quad j,k \\in \\mathbb{Z}\n", "\\qquad \\text{(3)}\n", "$$"]}, {"cell_type": "code", "execution_count": 60, "id": "e24dfcfc", "metadata": {}, "outputs": [], "source": ["# Notable concerns:\n", "# - We will need more than one input feature as we don't have j or k\n", "# - We are unsure if we're using 60Hz or 250Hz so we'll assume 250Hz\n", "\n", "\n", "\n", "def compute_wavelet_coefficients(t, j2, k2, w):\n", "    \"\"\"\n", "    Function Description: Compute the wavelet coefficients c_{j,k} for the given signal x(t).\n", "    \n", "    Parameters:\n", "        t = pupil diameter (float)\n", "        j2 = dilation parameter (integer), name is j2 to differentiate from j further within\n", "        k2 = translation parameter (integer), name is k2 to differentiate from k further within\n", "        w = wavelet (string)\n", "        \n", "    Returns:\n", "        c_jk: A dictionary containing the computed wavelet coefficients c_{j,k}\n", "    \"\"\"\n", "    \n", "    try:\n", "        from scipy.integrate import quad\n", "    except ImportError:\n", "        raise ImportError(\"Scipy.integrate is not imported.\")\n", "        \n", "    try:\n", "        # This makes sure that j2 and k2 are lists of integers\n", "        if not (check_for_integers(j_values) and check_for_integers(k_values)):\n", "            raise ValueError(\"j and k must be lists of integers.\")\n", "\n", "        # This will make sure that we're only inputting the correct db\n", "        if not (isinstance(w, str) and w in [\"db4\", \"db16\"]):\n", "            raise ValueError(\"w must be either Daubechies-4 or Daubechies-16\")\n", "       \n", "        # Initialize an empty dictionary for the wavelet coefficients\n", "        c_jk = {}\n", "\n", "        # Computation of the wavelet coefficient for both j and k\n", "        for j in j2:\n", "            for k in k2:\n", "                # Compute the integrand for the inner product <x(t), ψ(j, k, t)>\n", "                compute_integrand = lambda t: x(t) * np.conj(dyadic_wavelet(t, j, k, w))\n", "                \n", "                # Computation of the Inner Product, needs <PERSON><PERSON>y\n", "                inner_product, _ = quad(compute_integrand, -np.inf, np.inf)\n", "\n", "                # Creates it into a wavelet coefficient\n", "                c_jk[(j, k)] = 2**(j/2) * inner_product\n", "\n", "        return c_jk\n", "    \n", "    except ValueError as ve:\n", "        print(\"ValueError:\", ve)\n", "        return None"]}, {"cell_type": "markdown", "id": "692f11e9", "metadata": {}, "source": ["##### The Wavelet Coefficients in their final form\n", "This formula gives us the similarity between the signal and the wavelet function at each scale and position.\n", "\n", "Variable representation:\n", "- $\\{W_\\psi x(t)\\}(j, k)$: The wavelet transformation of $x(t)$ using $\\psi$\n", "- $\\langle x(t), \\psi_{j,k}(t) \\rangle$: The inner product reprsented in another way from formula (3)\n", "\n", "$$\n", "= \\{W_\\psi x(t)\\}(j, k) = \\langle x(t), \\psi_{j,k}(t) \\rangle\n", "\\qquad \\text{(4)}\n", "$$\n", "\n", "# Version 2\n", "At this point, I had finally read through the majority of PyWavelet and realized that I spent probably 20+ hours trying to understand and to hard code items that were already calculated within the library itself because I didn't fully understand it.\n", "\n"]}, {"cell_type": "code", "execution_count": null, "id": "854d4293", "metadata": {}, "outputs": [], "source": ["class IPA:\n", "    def __init__(self, pupil_diam_data, wavelet):\n", "        self.pupil_diam_data = pupil\n", "        self.wavelet = wavelet\n", "        self.scaling = wavelet\n", "        \n", "    def wavelet_decomposition(self):\n", "        "]}, {"cell_type": "code", "execution_count": 62, "id": "e8c12375", "metadata": {"scrolled": true}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Index of Pupillary Activity (IPA) from wavelet analysis: 2.0636182902584492\n", "Index of Pupillary Activity (IPA) from scaling function analysis: 2997.0\n"]}], "source": ["import numpy as np\n", "import pywt\n", "\n", "class PupilAnalyzer:\n", "    def __init__(self, pupil_data):\n", "        self.pupil_data = pupil_data\n", "        self.wavelet = 'db4'  # Default wavelet\n", "        self.scaling_function = 'db4'  # Default scaling function\n", "        self.sampling_rate = 1  # Placeholder value, actual sampling rate should be known\n", "    \n", "    def preprocess_data(self):\n", "        # Preprocessing step to remove data around blinks\n", "        # Placeholder implementation, actual blink detection not included\n", "        pass  # You can implement actual preprocessing steps here\n", "    \n", "    def wavelet_decomposition(self):\n", "        # Wavelet decomposition\n", "        self.wavelet_coeffs = pywt.wavedec(self.pupil_data, self.wavelet)\n", "    \n", "    def scaling_function_analysis(self):\n", "        # Scaling function analysis using Stationary Wavelet Transform (SWT)\n", "        self.scaling_coeffs = pywt.swt(self.pupil_data, self.scaling_function)\n", "    \n", "    def compute_ipa_from_coeffs(self, coeffs):\n", "        # Counting abrupt discontinuities in coefficients\n", "        abrupt_changes = 0\n", "        for c in coeffs:\n", "            diffs = np.diff(c)\n", "            abrupt_changes += np.count_nonzero(diffs)\n", "        \n", "        # Computing IPA as frequency of abrupt discontinuities\n", "        time_duration = len(coeffs[-1]) / self.sampling_rate\n", "        ipa = abrupt_changes / time_duration\n", "        \n", "        return ipa\n", "    \n", "    def analyze(self):\n", "        self.preprocess_data()\n", "        self.wavelet_decomposition()\n", "        self.scaling_function_analysis()\n", "        \n", "        ipa_from_wavelet = self.compute_ipa_from_coeffs(self.wavelet_coeffs)\n", "        ipa_from_scaling = self.compute_ipa_from_coeffs(self.scaling_coeffs)\n", "        \n", "        return ipa_from_wavelet, ipa_from_scaling\n", "\n", "# Example usage\n", "pupil_data = np.random.rand(1000)  # Example pupil diameter data\n", "analyzer = PupilAnalyzer(pupil_data)\n", "ipa_from_wavelet, ipa_from_scaling = analyzer.analyze()\n", "print(\"Index of Pupillary Activity (IPA) from wavelet analysis:\", ipa_from_wavelet)\n", "print(\"Index of Pupillary Activity (IPA) from scaling function analysis:\", ipa_from_scaling)\n"]}, {"cell_type": "markdown", "id": "5c06d838", "metadata": {}, "source": ["# Tester"]}, {"cell_type": "code", "execution_count": 67, "id": "89230a48", "metadata": {"scrolled": false}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>world_timestamp</th>\n", "      <th>world_index</th>\n", "      <th>eye_id</th>\n", "      <th>diameter</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>260229.466438</td>\n", "      <td>2</td>\n", "      <td>0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>260229.474838</td>\n", "      <td>2</td>\n", "      <td>0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>260229.483238</td>\n", "      <td>2</td>\n", "      <td>0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>260229.491638</td>\n", "      <td>3</td>\n", "      <td>0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>260229.500038</td>\n", "      <td>3</td>\n", "      <td>0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["   world_timestamp  world_index  eye_id  diameter\n", "0    260229.466438            2       0       0.0\n", "1    260229.474838            2       0       0.0\n", "2    260229.483238            2       0       0.0\n", "3    260229.491638            3       0       0.0\n", "4    260229.500038            3       0       0.0"]}, "execution_count": 67, "metadata": {}, "output_type": "execute_result"}], "source": ["df1 = pd.read_csv('left_eye_pupil_measures.csv')\n", "df1.head()"]}, {"cell_type": "code", "execution_count": 64, "id": "12dbdac8", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Index of Pupillary Activity (IPA): 0.0\n"]}], "source": ["import math\n", "import numpy as np\n", "import pywt\n", "\n", "class PupilAnalyzer:\n", "    def __init__(self, pupil_data):\n", "        self.pupil_data = pupil_data\n", "        self.sampling_rate = 1  # Placeholder value, actual sampling rate should be known\n", "        self.wavelet = 'sym16'  # Wavelet for DWT\n", "        self.level = 2  # Number of levels for DWT\n", "\n", "    def wavelet_decomposition(self):\n", "        # Perform 2-level DWT of pupil diameter signal\n", "        try:\n", "            coeffs = pywt.wavedec(self.pupil_data, self.wavelet, mode='per', level=self.level)\n", "            return coeffs\n", "        except ValueError:\n", "            return None\n", "\n", "    def normalize_coefficients(self, coeffs):\n", "        # Normalize DWT coefficients\n", "        for i in range(1, self.level + 1):\n", "            coeffs[i] = [x / math.sqrt(2 ** i) for x in coeffs[i]]\n", "        return coeffs\n", "\n", "    def modmax(self, d):\n", "        # Compute signal modulus\n", "        m = [math.fabs(x) for x in d]\n", "        # Compute local maxima\n", "        t = [0.0] * len(d)\n", "        for i in range(len(d)):\n", "            ll = m[i - 1] if i >= 1 else m[i]\n", "            oo = m[i]\n", "            rr = m[i + 1] if i < len(d) - 1 else m[i]\n", "            if (ll <= oo and oo >= rr) and (ll < oo or oo > rr):\n", "                # Compute magnitude\n", "                t[i] = math.sqrt(d[i] ** 2)\n", "        return t\n", "\n", "    def thresholding(self, coeffs):\n", "        # Thresholding using universal threshold\n", "        cD2m = self.modmax(coeffs[-1])  # Assuming the last level contains detail coefficients\n", "        λuniv = np.std(cD2m) * math.sqrt(2.0 * np.log2(len(cD2m)))\n", "        cD2t = pywt.threshold(coeffs[-1], λuniv, mode=\"hard\")\n", "        coeffs[-1] = cD2t\n", "        return coeffs\n", "\n", "    def compute_ipa(self, coeffs):\n", "        # Compute IPA\n", "        ctr = sum(math.fabs(x) > 0 for x in coeffs[-1])\n", "        tt = len(self.pupil_data) / self.sampling_rate\n", "        ipa = float(ctr) / tt\n", "        return ipa\n", "\n", "    def analyze(self):\n", "        # Perform analysis\n", "        coeffs = self.wavelet_decomposition()\n", "        if coeffs is None:\n", "            return None\n", "        coeffs = self.normalize_coefficients(coeffs)\n", "        coeffs = self.thresholding(coeffs)\n", "        ipa = self.compute_ipa(coeffs)\n", "        return ipa\n", "\n", "# Example usage\n", "pupil_data = np.random.rand(1000)  # Example pupil diameter data\n", "analyzer = PupilAnalyzer(pupil_data)\n", "ipa = analyzer.analyze()\n", "print(\"Index of Pupillary Activity (IPA):\", ipa)\n"]}, {"cell_type": "code", "execution_count": 70, "id": "5c7ee6bf", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Index of Pupillary Activity (IPA): 0.0030872859949480774\n"]}], "source": ["analyzer = PupilAnalyzer(df1.diameter)\n", "ipa = analyzer.analyze()\n", "print(\"Index of Pupillary Activity (IPA):\", ipa)"]}, {"cell_type": "code", "execution_count": null, "id": "7126e71e", "metadata": {}, "outputs": [], "source": ["class PyPil:\n", "    def __init__(self, pupil_data, wavelet=\"sym16\", level = \"2\"):\n", "        self.pupil_data = pupil_data\n", "        self.wavelet = wavelet\n", "        self.level = level\n", "    "]}, {"cell_type": "code", "execution_count": null, "id": "5f791394", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "0c2c7195", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.16"}}, "nbformat": 4, "nbformat_minor": 5}