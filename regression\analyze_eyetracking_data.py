"""
分析现有眼动数据格式
了解DataFrame结构、列名、数据类型等
"""

import os
import sys
import pandas as pd
import numpy as np
from pathlib import Path

# 添加项目根目录到路径
project_root = Path(__file__).parent.parent
sys.path.append(str(project_root))

from analysis.preprocess_edf import EyeDataPreprocessor

def find_edf_files(data_dir: str) -> list:
    """查找数据目录中的EDF文件"""
    edf_files = []
    for root, dirs, files in os.walk(data_dir):
        for file in files:
            if file.endswith('.edf'):
                edf_files.append(os.path.join(root, file))
    return edf_files

def analyze_single_edf(edf_path: str):
    """分析单个EDF文件的数据结构"""
    print(f"\n{'='*60}")
    print(f"分析文件: {os.path.basename(edf_path)}")
    print(f"{'='*60}")
    
    # 创建预处理器
    preprocessor = EyeDataPreprocessor()
    
    # 加载原始数据
    samples, events, messages = preprocessor.load_edf_data(edf_path)
    
    if samples is None:
        print("❌ 无法加载数据")
        return None
    
    # 分析samples数据
    print("\n📊 SAMPLES数据分析:")
    print(f"  数据形状: {samples.shape}")
    print(f"  列名: {list(samples.columns)}")
    print(f"  数据类型:")
    for col in samples.columns:
        print(f"    {col}: {samples[col].dtype}")
    
    # 检查瞳孔数据列
    pupil_columns = [col for col in samples.columns if 'pa_' in col or 'pupil' in col.lower()]
    print(f"  瞳孔数据列: {pupil_columns}")
    
    # 检查坐标数据列
    gaze_columns = [col for col in samples.columns if any(prefix in col for prefix in ['gx_', 'gy_', 'px_', 'py_'])]
    print(f"  注视坐标列: {gaze_columns}")
    
    # 检查时间列
    time_columns = [col for col in samples.columns if 'time' in col.lower()]
    print(f"  时间列: {time_columns}")
    
    # 数据统计
    if pupil_columns:
        print(f"\n📈 瞳孔数据统计:")
        for col in pupil_columns:
            if col in samples.columns:
                valid_data = samples[col].dropna()
                if len(valid_data) > 0:
                    print(f"  {col}:")
                    print(f"    有效数据点: {len(valid_data)}/{len(samples)} ({len(valid_data)/len(samples)*100:.1f}%)")
                    print(f"    范围: {valid_data.min():.1f} - {valid_data.max():.1f}")
                    print(f"    均值: {valid_data.mean():.1f}")
                    print(f"    标准差: {valid_data.std():.1f}")
    
    # 分析events数据
    print(f"\n📋 EVENTS数据分析:")
    print(f"  数据形状: {events.shape}")
    print(f"  列名: {list(events.columns)}")
    if 'type' in events.columns:
        event_types = events['type'].value_counts()
        print(f"  事件类型分布:")
        for event_type, count in event_types.items():
            print(f"    {event_type}: {count}")
    
    # 分析messages数据
    print(f"\n💬 MESSAGES数据分析:")
    print(f"  数据形状: {messages.shape}")
    print(f"  列名: {list(messages.columns)}")
    if 'text' in messages.columns:
        # 查找评分相关的消息
        rating_messages = messages[messages['text'].str.contains('rating|curiosity|interest', case=False, na=False)]
        print(f"  评分相关消息数量: {len(rating_messages)}")
        if len(rating_messages) > 0:
            print(f"  评分消息示例:")
            for i, (_, msg) in enumerate(rating_messages.head(3).iterrows()):
                print(f"    {i+1}. {msg['text']}")
    
    # 采样率分析
    if 'time' in samples.columns:
        time_diffs = samples['time'].diff().dropna()
        sampling_rate = 1000 / time_diffs.median()  # 假设时间单位是毫秒
        print(f"\n⏱️ 采样率分析:")
        print(f"  估计采样率: {sampling_rate:.1f} Hz")
        print(f"  时间间隔中位数: {time_diffs.median():.2f} ms")
    
    return {
        'samples': samples,
        'events': events,
        'messages': messages,
        'pupil_columns': pupil_columns,
        'gaze_columns': gaze_columns,
        'time_columns': time_columns
    }

def analyze_data_for_aeon(data_info: dict):
    """分析数据如何转换为aeon格式"""
    print(f"\n🔄 AEON格式转换分析:")
    
    samples = data_info['samples']
    pupil_columns = data_info['pupil_columns']
    gaze_columns = data_info['gaze_columns']
    
    # 确定通道数
    channels = []
    if 'pa_left' in pupil_columns:
        channels.append('pa_left')
    if 'pa_right' in pupil_columns:
        channels.append('pa_right')
    if 'gx_left' in gaze_columns:
        channels.append('gx_left')
    if 'gy_left' in gaze_columns:
        channels.append('gy_left')
    if 'gx_right' in gaze_columns:
        channels.append('gx_right')
    if 'gy_right' in gaze_columns:
        channels.append('gy_right')
    
    print(f"  可用通道: {channels}")
    print(f"  通道数: {len(channels)}")
    print(f"  时间点数: {len(samples)}")
    
    # 检查数据完整性
    print(f"\n📊 数据完整性分析:")
    for channel in channels:
        if channel in samples.columns:
            valid_ratio = samples[channel].notna().sum() / len(samples)
            print(f"  {channel}: {valid_ratio*100:.1f}% 有效数据")
    
    # 建议的aeon数据格式
    print(f"\n💡 建议的aeon数据格式:")
    print(f"  形状: (n_trials, {len(channels)}, n_timepoints)")
    print(f"  通道顺序: {channels}")
    print(f"  数据类型: float32")
    print(f"  标签: 从messages中提取的兴趣评分")

def extract_interest_ratings(messages: pd.DataFrame) -> dict:
    """从消息中提取兴趣评分"""
    ratings = {}
    
    if 'text' in messages.columns:
        # 查找评分消息
        for _, msg in messages.iterrows():
            text = str(msg['text']).lower()
            
            # 查找好奇心评分
            if 'curiosity' in text and 'rating' in text:
                try:
                    # 提取数字
                    import re
                    numbers = re.findall(r'\d+', text)
                    if numbers:
                        ratings['curiosity'] = int(numbers[-1])  # 取最后一个数字
                except:
                    pass
            
            # 查找兴趣评分
            if 'interest' in text and 'rating' in text:
                try:
                    import re
                    numbers = re.findall(r'\d+', text)
                    if numbers:
                        ratings['interest'] = int(numbers[-1])
                except:
                    pass
    
    return ratings

def main():
    """主函数"""
    print("🔍 眼动数据格式分析工具")
    print("="*60)
    
    # 查找数据文件
    data_dir = "data"
    if not os.path.exists(data_dir):
        print(f"❌ 数据目录不存在: {data_dir}")
        return
    
    edf_files = find_edf_files(data_dir)
    if not edf_files:
        print(f"❌ 在 {data_dir} 中未找到EDF文件")
        return
    
    print(f"📁 找到 {len(edf_files)} 个EDF文件:")
    for i, file in enumerate(edf_files[:5]):  # 只显示前5个
        print(f"  {i+1}. {os.path.basename(file)}")
    if len(edf_files) > 5:
        print(f"  ... 还有 {len(edf_files)-5} 个文件")
    
    # 分析第一个文件作为示例
    print(f"\n🔬 详细分析第一个文件作为示例:")
    data_info = analyze_single_edf(edf_files[0])
    
    if data_info:
        # 分析aeon转换
        analyze_data_for_aeon(data_info)
        
        # 提取评分信息
        ratings = extract_interest_ratings(data_info['messages'])
        if ratings:
            print(f"\n🎯 提取的评分信息:")
            for rating_type, value in ratings.items():
                print(f"  {rating_type}: {value}")
        else:
            print(f"\n⚠️ 未找到评分信息")
    
    print(f"\n✅ 分析完成")

if __name__ == "__main__":
    main()
