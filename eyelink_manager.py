#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
EyeLink眼动仪管理模块
处理EyeLink连接、校准、数据记录等功能
集成外置摄像头录像功能
"""

import os
import time
import threading
from datetime import datetime
from typing import Optional, Tuple

try:
    import pylink
    PYLINK_AVAILABLE = True
except ImportError:
    PYLINK_AVAILABLE = False
    print("警告：PyLink未安装，将使用模拟模式")

# 尝试导入EyeLinkCoreGraphicsPsychoPy，如果失败则使用简化模式
try:
    from EyeLinkCoreGraphicsPsychoPy import EyeLinkCoreGraphicsPsychoPy
    GRAPHICS_AVAILABLE = True
except ImportError:
    GRAPHICS_AVAILABLE = False
    print("警告：无法导入EyeLinkCoreGraphicsPsychoPy，校准将使用简化模式")

# 尝试导入OpenCV用于摄像头录像
try:
    import cv2
    OPENCV_AVAILABLE = True
except ImportError:
    OPENCV_AVAILABLE = False
    print("警告：OpenCV未安装，摄像头录像功能将不可用")

class CameraRecorder:
    """外置摄像头录像管理类"""

    def __init__(self, data_dir: str, camera_index: int = 2, dummy_mode: bool = False):
        """
        初始化摄像头录像器

        Args:
            data_dir: 视频保存目录
            camera_index: 摄像头索引（通常0为默认摄像头）
            dummy_mode: 是否使用虚拟模式
        """
        self.data_dir = data_dir
        self.camera_index = camera_index
        self.dummy_mode = dummy_mode or not OPENCV_AVAILABLE
        self.cap = None
        self.out = None
        self.is_recording = False
        self.recording_thread = None
        self.stop_recording_flag = False

        # 录像参数
        self.fps = 30
        self.width = 1920  # 1080p
        self.height = 1080
        # 使用avc1编码，无掉帧且高压缩率，节省存储空间
        self.fourcc = cv2.VideoWriter_fourcc(*'MJPG') if OPENCV_AVAILABLE else None
        #hev1,avc1,MJPG ->h265,h264,avi

    def initialize_camera(self) -> bool:
        """
        初始化摄像头

        Returns:
            初始化是否成功
        """
        if self.dummy_mode:
            print("虚拟模式：跳过摄像头初始化")
            return True

        if not OPENCV_AVAILABLE:
            print("错误：OpenCV不可用，无法初始化摄像头")
            return False

        try:
            # 初始化摄像头
            self.cap = cv2.VideoCapture(self.camera_index)

            if not self.cap.isOpened():
                print(f"错误：无法打开摄像头 {self.camera_index}")
                return False

            # 设置摄像头参数
            self.cap.set(cv2.CAP_PROP_FRAME_WIDTH, self.width)
            self.cap.set(cv2.CAP_PROP_FRAME_HEIGHT, self.height)
            self.cap.set(cv2.CAP_PROP_FPS, self.fps)

            # 优化设置以减少掉帧
            self.cap.set(cv2.CAP_PROP_BUFFERSIZE, 10)  # 减少缓冲区大小
            # self.cap.set(cv2.CAP_PROP_FOURCC, cv2.VideoWriter_fourcc(*'MJPG'))  # 使用MJPG格式提高采集速度

            # 验证设置
            actual_width = int(self.cap.get(cv2.CAP_PROP_FRAME_WIDTH))
            actual_height = int(self.cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
            actual_fps = self.cap.get(cv2.CAP_PROP_FPS)

            print(f"摄像头初始化成功: {actual_width}x{actual_height} @ {actual_fps}fps")
            return True

        except Exception as e:
            print(f"摄像头初始化失败: {e}")
            return False

    def start_recording(self, participant_id: str, trail_id: int) -> bool:
        """
        开始录像

        Args:
            participant_id: 被试ID，用于生成文件名
            trail_id： 轮次id
        Returns:
            录像是否开始成功
        """
        if self.is_recording:
            print("警告：摄像头已在录像中")
            return True

        if self.dummy_mode:
            print(f"虚拟模式：开始录像 {participant_id}")
            self.is_recording = True
            return True

        if not self.cap or not self.cap.isOpened():
            print("错误：摄像头未初始化或不可用")
            return False

        try:
            import datetime
            now = datetime.datetime.now()
            timestamp = now.strftime("%Y-%m-%d_%H-%M-%S")
            
            video_folder = os.path.join(self.data_dir, "video")
            os.makedirs(video_folder, exist_ok=True)
            # video_filename = f"{participant_id}_facial_expression.avi"
            video_filename = f"{participant_id}_{trail_id}.avi"
            video_path = os.path.join(video_folder, video_filename)

            # 确保目录存在
            os.makedirs(self.data_dir, exist_ok=True)

            # 初始化视频写入器
            self.out = cv2.VideoWriter(video_path, self.fourcc, self.fps, (self.width, self.height))

            if not self.out.isOpened():
                print("错误：无法创建视频文件")
                return False

            # 重置停止标志
            self.stop_recording_flag = False

            # 启动录像线程
            self.recording_thread = threading.Thread(target=self._recording_loop)
            self.recording_thread.daemon = True
            self.recording_thread.start()

            self.is_recording = True
            print(f"开始录像: {video_path}")
            return True

        except Exception as e:
            print(f"开始录像失败: {e}")
            return False

    def _recording_loop(self):
        """录像循环（在单独线程中运行）"""
        while not self.stop_recording_flag and self.cap and self.cap.isOpened():
            ret, frame = self.cap.read()
            if ret and self.out:
                # 确保帧尺寸正确
                if frame.shape[:2] != (self.height, self.width):
                    frame = cv2.resize(frame, (self.width, self.height))

                # 添加时间戳到右下角
                frame = self._add_timestamp_to_frame(frame)

                self.out.write(frame)
            else:
                break

        print("录像循环结束")

    def _add_timestamp_to_frame(self, frame):
        """
        在帧的右下角添加白色时间戳（精度到毫秒）

        Args:
            frame: 输入的视频帧

        Returns:
            添加了时间戳的视频帧
        """
        if self.dummy_mode or not OPENCV_AVAILABLE:
            return frame

        try:
            # 获取当前时间，精度到毫秒
            current_time = datetime.now()
            timestamp_str = current_time.strftime("%Y-%m-%d %H:%M:%S.%f")[:-3]  # 保留3位毫秒

            # 设置字体参数
            font = cv2.FONT_HERSHEY_SIMPLEX
            font_scale = 0.7
            color = (255, 255, 255)  # 白色
            thickness = 2

            # 获取文本尺寸
            (text_width, text_height), baseline = cv2.getTextSize(timestamp_str, font, font_scale, thickness)

            # 计算右下角位置（留一些边距）
            margin = 10
            x = frame.shape[1] - text_width - margin
            y = frame.shape[0] - margin

            # 添加黑色背景矩形，提高可读性
            cv2.rectangle(frame,
                         (x - 5, y - text_height - 5),
                         (x + text_width + 5, y + baseline + 5),
                         (0, 0, 0), -1)

            # 绘制白色时间戳文本
            cv2.putText(frame, timestamp_str, (x, y), font, font_scale, color, thickness)

            return frame

        except Exception as e:
            print(f"添加时间戳失败: {e}")
            return frame

    def stop_recording(self) -> bool:
        """
        停止录像

        Returns:
            停止是否成功
        """
        if not self.is_recording:
            return True

        if self.dummy_mode:
            print("虚拟模式：停止录像")
            self.is_recording = False
            return True

        try:
            # 设置停止标志
            self.stop_recording_flag = True

            # 等待录像线程结束
            if self.recording_thread and self.recording_thread.is_alive():
                self.recording_thread.join(timeout=2.0)

            # 释放视频写入器
            if self.out:
                self.out.release()
                self.out = None

            self.is_recording = False
            print("停止录像")
            return True

        except Exception as e:
            print(f"停止录像失败: {e}")
            return False

    def close(self):
        """关闭摄像头并清理资源"""
        if self.is_recording:
            self.stop_recording()

        if self.cap:
            self.cap.release()
            self.cap = None

        print("摄像头资源已释放")

class EyeLinkManager:
    """EyeLink眼动仪管理类 - 基于EyeLink Python API用户指南，集成摄像头录像功能"""

    def __init__(self, participant_id: str, data_dir: str, screen_size, dummy_mode: bool = False, display_window=None, enable_camera: bool = True, camera_index: int = 2):
        """
        初始化EyeLink管理器

        Args:
            participant_id: 被试ID
            data_dir: 数据保存目录
            dummy_mode: 是否使用虚拟模式（用于测试）
            display_window: PsychoPy显示窗口对象（用于校准）
            enable_camera: 是否启用摄像头录像功能
            camera_index: 摄像头索引（通常0为默认摄像头）
        """
        # EDF文件名限制8个字符，使用被试ID前8位
        self.edf_filename = participant_id[:8].upper()
        self.participant_id = participant_id
        self.data_dir = data_dir
        self.dummy_mode = dummy_mode or not PYLINK_AVAILABLE
        self.display_window = display_window
        self.tracker = None
        self.is_connected = False
        self.is_recording = False
        self.current_trial = 0

        # 校准图形环境
        self.genv = None

        # 屏幕参数（需要根据实际显示器调整）
        (self.screen_width,self.screen_height) = screen_size

        # 事件计数器
        self.event_counter = 0

        # 摄像头录像功能
        self.enable_camera = enable_camera
        self.camera_recorder = None
        if self.enable_camera:
            # 摄像头录像器不使用EyeLink的dummy_mode，可以独立工作
            camera_dummy_mode = False  # 摄像头始终尝试真实工作
            self.camera_recorder = CameraRecorder(data_dir, camera_index, camera_dummy_mode)
            print(f"摄像头录像功能已启用，摄像头索引: {camera_index}")
        
    def connect(self, host_ip: str = "*********") -> bool:
        """
        连接到EyeLink眼动仪并初始化摄像头

        Args:
            host_ip: EyeLink主机IP地址

        Returns:
            连接是否成功
        """
        if not PYLINK_AVAILABLE:
            print("PyLink不可用，使用模拟模式")
            self.dummy_mode = True
            self.is_connected = True
        else:
            try:
                if self.dummy_mode:
                    # 虚拟模式，用于测试
                    self.tracker = pylink.EyeLink(None)
                    print("EyeLink虚拟模式连接成功")
                else:
                    # 真实连接
                    self.tracker = pylink.EyeLink(host_ip)
                    print(f"EyeLink连接成功，主机IP: {host_ip}")

                self.is_connected = True

            except Exception as e:
                print(f"EyeLink连接失败: {e}")
                self.is_connected = False
                return False

        # 初始化摄像头
        if self.enable_camera and self.camera_recorder:
            if self.camera_recorder.initialize_camera():
                print("摄像头初始化成功")
            else:
                print("警告：摄像头初始化失败，录像功能将不可用")

        return self.is_connected
    
    def setup_tracker(self) -> bool:
        """
        配置眼动仪参数 - 基于EyeLink Python API指南

        Returns:
            配置是否成功
        """
        if not self.is_connected:
            print("错误：EyeLink未连接")
            return False

        if self.dummy_mode:
            print("虚拟模式：跳过眼动仪配置")
            return True

        try:
            # 打开EDF文件进行数据记录
            self.tracker.openDataFile(self.edf_filename + ".EDF")
                       
            # 设置屏幕分辨率和坐标系
            self.tracker.sendCommand(f"screen_pixel_coords = 0 0 {self.screen_width-1} {self.screen_height-1}")
            self.tracker.sendMessage(f"DISPLAY_COORDS 0 0 {self.screen_width-1} {self.screen_height-1}")

            # 设置采样率（1000Hz用于瞳孔数据）
            self.tracker.sendCommand("sample_rate 1000")

            # 配置记录到EDF文件的数据类型
            # 包含瞳孔大小、注视点、眼跳、眨眼等所有重要数据
            self.tracker.sendCommand("file_event_filter = LEFT,RIGHT,FIXATION,SACCADE,BLINK,MESSAGE,BUTTON,INPUT")
            self.tracker.sendCommand("file_sample_data = LEFT,RIGHT,GAZE,HREF,RAW,AREA,GAZERES,BUTTON,STATUS,INPUT")

            # 配置实时链接数据类型
            self.tracker.sendCommand("link_event_filter = LEFT,RIGHT,FIXATION,SACCADE,BLINK,MESSAGE,BUTTON,INPUT")
            self.tracker.sendCommand("link_sample_data = LEFT,RIGHT,GAZE,GAZERES,AREA,BUTTON,STATUS,INPUT")

            # 设置解析器配置（用于事件检测）
            self.tracker.sendCommand("saccade_velocity_threshold = 30")
            self.tracker.sendCommand("saccade_acceleration_threshold = 9500")
            self.tracker.sendCommand("saccade_motion_threshold = 0.15")

            # 设置瞳孔检测阈值
            self.tracker.sendCommand("pupil_size_diameter = NO")

            # 添加实验信息到EDF文件头
            self.tracker.sendMessage(f"PARTICIPANT_ID {self.participant_id}")
            self.tracker.sendMessage(f"EXPERIMENT_TYPE CURIOSITY_PUPIL")
            self.tracker.sendMessage(f"SCREEN_RESOLUTION {self.screen_width}x{self.screen_height}")

            print("EyeLink配置完成")
            return True

        except Exception as e:
            print(f"EyeLink配置失败: {e}")
            return False

    def setup_graphics_environment(self) -> bool:
        """
        设置校准图形环境

        Returns:
            设置是否成功
        """
        if self.dummy_mode or not self.display_window:
            print("虚拟模式或无显示窗口，跳过图形环境设置")
            return True

        try:
            if GRAPHICS_AVAILABLE:
                # 创建图形环境
                self.genv = EyeLinkCoreGraphicsPsychoPy(self.tracker, self.display_window)

                # 设置校准颜色
                foreground_color = (-1, -1, -1)  # 黑色
                background_color = self.display_window.color
                self.genv.setCalibrationColors(foreground_color, background_color)

                # 设置校准目标类型为圆形
                self.genv.setTargetType('circle')

                # 设置校准提示音（使用默认声音）
                self.genv.setCalibrationSounds('', '', '')

                # 请求PyLink使用配置好的图形环境
                pylink.openGraphicsEx(self.genv)

                print("✓ 图形环境配置成功")
            else:
                print("✓ 使用简化模式（无图形环境）")

            return True

        except Exception as e:
            print(f"✗ 图形环境配置失败: {e}")
            return False

    def calibrate(self) -> bool:
        """
        执行眼动仪校准

        Returns:
            校准是否成功
        """
        if not self.is_connected:
            print("错误：EyeLink未连接")
            return False

        if self.dummy_mode:
            print("虚拟模式：模拟校准完成")
            time.sleep(2)  # 模拟校准时间
            return True

        try:
            print("\n开始EyeLink校准...")

            # 设置图形环境（如果还没有设置）
            if self.genv is None and self.display_window:
                if not self.setup_graphics_environment():
                    print("警告：图形环境设置失败，使用默认校准")

            if GRAPHICS_AVAILABLE and self.display_window:
                print("操作说明:")
                print("- 按 C 开始校准")
                print("- 按 V 开始验证")
                print("- 按 Enter 查看相机图像")
                print("- 按 Esc 退出校准")
                print("- 校准时请注视屏幕上出现的目标点")

                # 显示提示信息
                try:
                    from psychopy import visual, event
                    instruction_text = visual.TextStim(
                        self.display_window,
                        text="准备开始校准\n\n按Enter键继续...",
                        font='SimHei',
                        height=40,
                        color='white',
                        pos=(0, 0)
                    )
                    instruction_text.draw()
                    self.display_window.flip()
                    # self.display_window.winHandle.activate()
                    # 等待按键
                    # print(1)
                    # event.waitKeys()
                    # print(2)
                except:
                    print("无法显示校准提示，直接进入校准")

            #加入比例参数
            use_screen_ratio = 0.5
            self.tracker.sendCommand(f"calibration_area_proportion = {use_screen_ratio} {use_screen_ratio}")
            self.tracker.sendCommand(f"validation_area_proportion  = {use_screen_ratio} {use_screen_ratio}")
            
            # 进入校准设置
            self.tracker.sendCommand("calibration_type = HV9")
            self.tracker.doTrackerSetup()
            print("✓ EyeLink校准完成")
            return True

        except RuntimeError as err:
            print(f"✗ EyeLink校准失败: {err}")
            if hasattr(self.tracker, 'exitCalibration'):
                self.tracker.exitCalibration()
            return False
        except Exception as e:
            print(f"✗ EyeLink校准出错: {e}")
            return False
    
    def start_recording(self, trial_id: int = 0) -> bool:
        """
        开始记录眼动数据并同步开始摄像头录像

        Args:
            trial_id: 试次ID

        Returns:
            记录是否开始成功
        """
        if not self.is_connected:
            print("错误：EyeLink未连接")
            return False

        # 先开始摄像头录像
        camera_success = True
        if self.enable_camera and self.camera_recorder:
            camera_success = self.camera_recorder.start_recording(self.participant_id, trial_id)
            if camera_success:
                print("摄像头录像已开始")
            else:
                print("警告：摄像头录像启动失败")

        if self.dummy_mode:
            print(f"虚拟模式：开始记录试次 {trial_id}")
            self.is_recording = True
            return True

        try:
            # 开始记录
            self.tracker.startRecording(1, 1, 1, 1)

            # 等待记录开始
            pylink.msecDelay(100)

            # 发送试次开始消息
            self.tracker.sendMessage(f"TRIAL_START {trial_id}")

            # 记录摄像头状态
            if self.enable_camera:
                camera_status = "SUCCESS" if camera_success else "FAILED"
                self.tracker.sendMessage(f"CAMERA_RECORDING {camera_status}")

            self.is_recording = True
            print(f"开始记录眼动数据，试次: {trial_id}")
            return True

        except Exception as e:
            print(f"开始记录失败: {e}")
            # 如果EyeLink记录失败，也停止摄像头录像
            if self.enable_camera and self.camera_recorder and camera_success:
                self.camera_recorder.stop_recording()
            return False
    
    def stop_recording(self) -> bool:
        """
        停止记录眼动数据并同步停止摄像头录像

        Returns:
            停止是否成功
        """
        if not self.is_recording:
            return True

        if self.dummy_mode:
            print("虚拟模式：停止记录")
            self.is_recording = False
            # 停止摄像头录像
            if self.enable_camera and self.camera_recorder:
                self.camera_recorder.stop_recording()
            return True

        try:
            # 发送试次结束消息
            self.tracker.sendMessage("TRIAL_END")

            # 记录摄像头停止状态
            if self.enable_camera:
                self.tracker.sendMessage("CAMERA_RECORDING_STOP")

            # 停止记录
            self.tracker.stopRecording()

            self.is_recording = False
            print("停止记录眼动数据")

            # 停止摄像头录像
            if self.enable_camera and self.camera_recorder:
                if self.camera_recorder.stop_recording():
                    print("摄像头录像已停止")
                else:
                    print("警告：摄像头录像停止失败")

            return True

        except Exception as e:
            print(f"停止记录失败: {e}")
            # 即使EyeLink停止失败，也尝试停止摄像头录像
            if self.enable_camera and self.camera_recorder:
                self.camera_recorder.stop_recording()
            return False
    
    def send_message(self, message: str):
        """
        发送消息到EDF文件

        Args:
            message: 要发送的消息
        """
        if not self.is_connected:
            return

        if self.dummy_mode:
            print(f"虚拟模式消息: {message}")
            return

        try:
            # 添加时间戳和事件计数器
            self.event_counter += 1
            timestamped_message = f"EVENT_{self.event_counter:03d} {message}"
            self.tracker.sendMessage(timestamped_message)
            print(f"EyeLink消息: {timestamped_message}")
        except Exception as e:
            print(f"发送消息失败: {e}")

    def log_experiment_start(self):
        """记录实验开始"""
        self.send_message("EXPERIMENT_START")
        self.send_message(f"PARTICIPANT_ID {self.participant_id}")

    def log_trial_start(self, trial_num: int, question: str, question_id: int = None):
        """记录试次开始"""
        self.current_trial = trial_num
        self.send_message(f"TRIAL_START {trial_num}")
        # 发送题目序号而不是题目内容，避免中文字符
        if question_id is not None:
            self.send_message(f"QUESTION_ID question_{question_id}")
        else:
            self.send_message(f"QUESTION_ID question_{trial_num}")
        # 在控制台打印中文内容供调试
        print(f"题目内容: {question[:50]}...")

    def log_baseline_start(self, baseline_type: str = "FIXATION"):
        """记录基线测量开始"""
        self.send_message(f"BASELINE_START {baseline_type}")
        self.send_message("DISPLAY_FIXATION_CROSS")

    def log_baseline_end(self, baseline_type: str = "FIXATION"):
        """记录基线测量结束"""
        self.send_message(f"BASELINE_END {baseline_type}")

    def log_question_display(self, question: str, question_id: int = None):
        """记录题目显示"""
        self.send_message("QUESTION_DISPLAY_START")
        # 发送题目序号而不是题目内容，避免中文字符
        if question_id is not None:
            self.send_message(f"QUESTION_CONTENT question_{question_id}")
        else:
            self.send_message(f"QUESTION_CONTENT question_{self.current_trial}")
        # 在控制台打印中文内容供调试
        print(f"显示题目: {question[:50]}...")

    def log_question_end(self):
        """记录题目显示结束"""
        self.send_message("QUESTION_DISPLAY_END")

    def log_input_start(self):
        """记录答案输入开始"""
        self.send_message("INPUT_START")
        self.send_message("DISPLAY_INPUT_BOX")

    def log_input_end(self, response: str = ""):
        """记录答案输入结束"""
        self.send_message("INPUT_END")
        if response:
            # 发送参与者回答序号而不是实际内容，避免中文字符
            self.send_message(f"PARTICIPANT_RESPONSE answer_{self.current_trial}")
            # 在控制台打印中文内容供调试
            print(f"参与者回答: {response[:50]}...")

    def log_rating_start(self, rating_type: str):
        """记录评分开始"""
        self.send_message(f"RATING_START {rating_type}")
        self.send_message(f"DISPLAY_RATING_SCALE {rating_type}")

    def log_rating_end(self, rating_type: str, rating_value: int = None):
        """记录评分结束"""
        self.send_message(f"RATING_END {rating_type}")
        if rating_value is not None:
            self.send_message(f"RATING_VALUE {rating_type} {rating_value}")

    def log_answer_display(self, answer: str, question_id: int = None):
        """记录答案显示"""
        self.send_message("ANSWER_DISPLAY_START")
        # 发送答案序号而不是答案内容，避免中文字符
        if question_id is not None:
            self.send_message(f"ANSWER_CONTENT answer_{question_id}")
        else:
            self.send_message(f"ANSWER_CONTENT answer_{self.current_trial}")
        # 在控制台打印中文内容供调试
        print(f"显示答案: {answer[:50]}...")
        

    def log_answer_end(self):
        """记录答案显示结束"""
        self.send_message("ANSWER_DISPLAY_END")

    def log_trial_end(self, trial_num: int):
        """记录试次结束"""
        self.send_message(f"TRIAL_END {trial_num}")

    def log_experiment_end(self):
        """记录实验结束"""
        self.send_message("EXPERIMENT_END")
        self.send_message(f"TOTAL_TRIALS {self.current_trial}")
        self.send_message(f"TOTAL_EVENTS {self.event_counter}")

    def log_calibration_start(self):
        """记录校准开始"""
        self.send_message("CALIBRATION_START")

    def log_calibration_end(self, success: bool = True):
        """记录校准结束"""
        status = "SUCCESS" if success else "FAILED"
        self.send_message(f"CALIBRATION_END {status}")

    def get_current_gaze_position(self):
        """
        获取当前眼动位置

        Returns:
            tuple: (x, y) 眼动位置坐标，如果获取失败返回None
        """
        if not self.tracker or self.dummy_mode:
            return None

        try:
            # 获取最新的眼动样本
            sample = self.tracker.getNewestSample()
            if sample is not None:
                # 检查左眼数据
                if sample.isLeftSample():
                    gaze_x = sample.getLeftEye().getGaze()[0]
                    gaze_y = sample.getLeftEye().getGaze()[1]
                    return (gaze_x, gaze_y)
                # 检查右眼数据
                elif sample.isRightSample():
                    gaze_x = sample.getRightEye().getGaze()[0]
                    gaze_y = sample.getRightEye().getGaze()[1]
                    return (gaze_x, gaze_y)

            return None

        except Exception as e:
            print(f"获取眼动位置失败: {e}")
            return None

    def get_current_pupil_data(self):
        """获取当前瞳孔数据"""
        if not self.tracker or self.dummy_mode:
            return None

        try:
            # 获取最新的眼动样本
            sample = self.tracker.getNewestSample()
            if sample is not None:
                pupil_data = {}

                # 检查左眼数据
                if sample.isLeftSample():
                    left_eye = sample.getLeftEye()
                    pupil_data['pupil_left'] = left_eye.getPupilSize()
                    pupil_data['gaze_x_left'] = left_eye.getGaze()[0]
                    pupil_data['gaze_y_left'] = left_eye.getGaze()[1]

                # 检查右眼数据
                if sample.isRightSample():
                    right_eye = sample.getRightEye()
                    pupil_data['pupil_right'] = right_eye.getPupilSize()
                    pupil_data['gaze_x_right'] = right_eye.getGaze()[0]
                    pupil_data['gaze_y_right'] = right_eye.getGaze()[1]

                return pupil_data if pupil_data else None

            return None

        except Exception as e:
            print(f"获取瞳孔数据失败: {e}")
            return None

    def collect_pupil_samples(self, duration_ms=1000, sample_rate=10):
        """
        收集一段时间内的瞳孔数据样本

        Args:
            duration_ms: 收集时长（毫秒）
            sample_rate: 采样率（每秒采样次数）

        Returns:
            list: 瞳孔数据样本列表
        """
        if not self.tracker or self.dummy_mode:
            return []

        samples = []
        interval = 1.0 / sample_rate  # 采样间隔（秒）
        end_time = time.time() + duration_ms / 1000.0

        try:
            while time.time() < end_time:
                pupil_data = self.get_current_pupil_data()
                if pupil_data:
                    pupil_data['timestamp'] = time.time()
                    samples.append(pupil_data)

                time.sleep(interval)

            return samples

        except Exception as e:
            print(f"收集瞳孔样本失败: {e}")
            return []

    def close(self):
        """关闭EyeLink连接并接收EDF文件，同时关闭摄像头"""
        if self.is_recording:
            self.stop_recording()

        # 关闭摄像头
        if self.enable_camera and self.camera_recorder:
            self.camera_recorder.close()
            print("摄像头资源已释放")

        if self.dummy_mode:
            print("虚拟模式：关闭连接")
            # 在虚拟模式下创建一个假的EDF文件用于测试
            dummy_edf_path = os.path.join(self.data_dir, f"{self.participant_id}.edf")
            try:
                with open(dummy_edf_path, 'w') as f:
                    f.write("# 虚拟EDF文件 - 仅用于测试\n")
                    f.write(f"# 被试ID: {self.participant_id}\n")
                    f.write(f"# 事件数量: {self.event_counter}\n")
                print(f"虚拟EDF文件已创建: {dummy_edf_path}")
            except Exception as e:
                print(f"创建虚拟EDF文件失败: {e}")
            return

        if self.tracker:
            try:
                # 记录实验结束
                self.log_experiment_end()

                # 关闭EDF文件
                self.tracker.closeDataFile()
                print("EDF文件已关闭")

                # 接收EDF文件到本地
                local_edf_path = os.path.join(self.data_dir, f"{self.participant_id}.edf")
                print(f"正在接收EDF文件到: {local_edf_path}")

                # 使用receiveDataFile接收文件
                result = self.tracker.receiveDataFile(self.edf_filename + ".EDF", local_edf_path)

                if result > 0:
                    print(f"✓ EDF文件接收成功: {local_edf_path}")
                else:
                    print(f"⚠ EDF文件接收有问题，返回码: {result}")

                # 关闭连接
                self.tracker.close()
                print("EyeLink连接已关闭")

            except Exception as e:
                print(f"关闭EyeLink时出错: {e}")
                # 即使出错也尝试关闭连接
                try:
                    if self.tracker:
                        self.tracker.close()
                except:
                    pass

        self.is_connected = False
        self.tracker = None

def test_eyelink():
    """测试EyeLink功能"""
    print("测试EyeLink功能...")

    # 创建EyeLink管理器（虚拟模式）
    eyelink = EyeLinkManager("test001", "test_data", (1920, 1080), dummy_mode=True, enable_camera=True)

    # 连接
    if eyelink.connect():
        print("✓ 连接成功")

        # 配置
        if eyelink.setup_tracker():
            print("✓ 配置成功")

            # 校准
            eyelink.log_calibration_start()
            if eyelink.calibrate():
                print("✓ 校准成功")
                eyelink.log_calibration_end(True)

                # 记录实验开始
                eyelink.log_experiment_start()

                # 模拟一个试次
                eyelink.log_trial_start(1, "测试题目：1+1等于几？", question_id=1)

                # 基线测量
                eyelink.log_baseline_start("FIXATION")
                time.sleep(1)
                eyelink.log_baseline_end("FIXATION")

                # 题目显示
                eyelink.log_question_display("1+1等于几？", question_id=1)
                time.sleep(1)
                eyelink.log_question_end()

                # 答案输入
                eyelink.log_input_start()
                time.sleep(1)
                eyelink.log_input_end("2")

                # 好奇心评分
                eyelink.log_rating_start("CURIOSITY")
                time.sleep(1)
                eyelink.log_rating_end("CURIOSITY", 3)

                # 瞳孔基线
                eyelink.log_baseline_start("PUPIL")
                time.sleep(1)
                eyelink.log_baseline_end("PUPIL")

                # 答案显示
                eyelink.log_answer_display("答案是2", question_id=1)
                time.sleep(1)
                eyelink.log_answer_end()

                # 愉悦度评分
                eyelink.log_rating_start("PLEASURE")
                time.sleep(1)
                eyelink.log_rating_end("PLEASURE", 4)

                # 意外程度评分
                eyelink.log_rating_start("SURPRISE")
                time.sleep(1)
                eyelink.log_rating_end("SURPRISE", 1)

                # 试次结束
                eyelink.log_trial_end(1)

                print("✓ 完整试次模拟完成")

        # 关闭连接
        eyelink.close()
        print("✓ 关闭连接")

    print("EyeLink测试完成")

if __name__ == "__main__":
    test_eyelink()
