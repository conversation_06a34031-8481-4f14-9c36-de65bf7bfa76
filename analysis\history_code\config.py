# EyeLink数据分析配置文件

import os

# 路径配置
# EDF2ASC_PATH = r"C:\Program Files (x86)\SR Research\EyeLink\bin\edf2asc.exe"
EDF2ASC_PATH = r"edf2asc.exe"
DATA_DIR = "../data"
OUTPUT_DIR = "./output"
FIGURES_DIR = "./figures"

# 数据处理配置
TARGET_PARTICIPANT = "zhaojingyi"  # 指定处理的参与者，设为None处理所有参与者
LATEST_DATA_ONLY = False  # 是否只处理最新数据

# 评分配置
RATING_TYPES = {
    'curiosity_rating': '好奇心评分',
    'pleasure_rating': '有趣度评分',
    'surprise_rating': '惊讶度评分'
}

# 可视化配置
PLOT_RATINGS = ['curiosity_rating', 'pleasure_rating']  # 指定要画图的评分类型
RATING_VALUES_TO_PLOT = [1, 2, 3, 4, 5]  # 指定要画的评分值

# 瞳孔数据分析配置
PUPIL_CONFIG = {
    'event_start': 'QUESTION_DISPLAY_START',  # 分析开始事件
    'event_end': 'QUESTION_DISPLAY_END',      # 分析结束事件
    'eyes_to_use': 'both',                    # 'left', 'right', 'both' (双眼平均)
    'baseline_duration': 2000,                 # 基线时长(ms)
    'sampling_rate': 1000,                    # 采样率(Hz)
    'filter_blinks': True,                    # 是否过滤眨眼
    'interpolate_missing': True,              # 是否插值缺失数据
    'smooth_data': True,                      # 是否平滑数据
    'smooth_window': 50,                      # 平滑窗口大小(ms)
    # pyedfread特定配置
    'min_pupil_size': 100.0,                 # 最小瞳孔直径（像素）
    'max_pupil_size': 8000.0,                # 最大瞳孔直径（像素）
    'use_simulated_data': False,             # 是否使用模拟数据（当真实数据不可用时）
    'simulation_noise_level': 0.1            # 模拟数据噪声水平
}

# 图形配置
PLOT_CONFIG = {
    'figure_size': (12, 8),
    'dpi': 300,
    'font_size': 12,
    'line_width': 2,
    'alpha': 0.8,
    'colormap': 'viridis',                   # 配色方案：viridis, plasma, inferno, magma等
    'colors': ['#1f77b4', '#ff7f0e', '#2ca02c', '#d62728', '#9467bd',
               '#8c564b', '#e377c2', '#7f7f7f', '#bcbd22', '#17becf'],  # 备用颜色
    'show_std': True,                       # 是否显示标准差
    'std_alpha': 0.3,                        # 标准差区域透明度
    'save_format': 'png',                    # 保存格式
    'chinese_font': 'SimHei',                # 中文字体
    'create_both_plots': True,               # 是否同时创建均值图和均值+标准差图
    'mean_only_suffix': '_mean_only',        # 只有均值的图的文件名后缀
    'mean_std_suffix': '_mean_std',          # 均值+标准差图的文件名后缀
    'baseline_correct': True                 # 是否进行基线校正（让所有曲线从0开始）
}

# EDF转换配置
EDF_CONVERSION_CONFIG = {
    'include_samples': True,                  # 包含样本数据
    'include_events': True,                   # 包含事件数据
    'include_input': True,                    # 包含输入数据
    'time_format': 'ELAPSED',                # 时间格式
    'resolution': 0.01                       # 时间分辨率
}

# 数据质量配置
DATA_QUALITY_CONFIG = {
    'min_valid_samples': 0.7,                # 最小有效样本比例
    'max_pupil_change': 2.0,                 # 最大瞳孔变化倍数
    'blink_threshold': 100,                  # 眨眼检测阈值
    'outlier_threshold': 3.0                 # 异常值检测阈值(标准差倍数)
}

# 创建输出目录
os.makedirs(OUTPUT_DIR, exist_ok=True)
os.makedirs(FIGURES_DIR, exist_ok=True)
