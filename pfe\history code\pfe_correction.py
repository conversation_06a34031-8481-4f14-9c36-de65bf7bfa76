#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
PFE (Pupil Foreshortening Error) 矫正模块
基于论文: Hayes & Petrov (2016) Behav Res

功能：
1. 基于几何模型的PFE矫正
2. 基于标定数据的插值矫正
3. 混合矫正方法
"""

import os
import sys
import numpy as np
import pandas as pd
import logging
from typing import Dict, List, Tuple, Optional, Union
from scipy import interpolate
from scipy.optimize import minimize
import warnings
import json

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from pfe.config import *

# 配置日志
logging.basicConfig(
    level=getattr(logging, LOG_LEVEL),
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger('PFE矫正')

class PFECorrector:
    """PFE瞳孔前缩误差矫正器"""
    
    def __init__(self, calibration_data_path: Optional[str] = None,
                 camera_position: Optional[Dict] = None,
                 screen_position: Optional[Dict] = None,
                 screen_width: int = DEFAULT_SCREEN_WIDTH,
                 screen_height: int = DEFAULT_SCREEN_HEIGHT):
        """
        初始化PFE矫正器
        
        Args:
            calibration_data_path: 标定数据文件路径
            camera_position: 相机位置坐标 {'x': x, 'y': y, 'z': z}
            screen_position: 屏幕位置坐标 {'x': x, 'y': y, 'z': z}
            screen_width: 屏幕宽度
            screen_height: 屏幕高度
        """
        self.calibration_data_path = calibration_data_path
        self.camera_position = camera_position or CAMERA_POSITION
        self.screen_position = screen_position or SCREEN_POSITION
        self.screen_width = screen_width
        self.screen_height = screen_height
        
        # 标定数据
        self.calibration_data = None
        self.interpolator_left = None
        self.interpolator_right = None
        
        # 几何模型参数
        self.geometric_model_ready = False
        self.optimized_parameters = None
        self.optimization_results = None
        
        logger.info(f"PFE矫正器初始化完成")
        logger.info(f"相机位置: {self.camera_position}")
        logger.info(f"屏幕位置: {self.screen_position}")
        logger.info(f"屏幕尺寸: {screen_width}×{screen_height}")
        
        # 加载标定数据
        if calibration_data_path and os.path.exists(calibration_data_path):
            self.load_calibration_data(calibration_data_path)
    
    def load_calibration_data(self, calibration_data_path: str) -> bool:
        """加载标定数据"""
        try:
            self.calibration_data = pd.read_csv(calibration_data_path, encoding='utf-8-sig')
            logger.info(f"加载标定数据: {len(self.calibration_data)} 个点")
            
            # 验证数据完整性
            required_cols = ['screen_x', 'screen_y', 'pupil_left', 'pupil_right']
            missing_cols = [col for col in required_cols if col not in self.calibration_data.columns]
            
            if missing_cols:
                logger.warning(f"标定数据缺少列: {missing_cols}")
                return False
            
            # 建立插值器
            self._build_interpolators()
            
            return True
            
        except Exception as e:
            logger.error(f"加载标定数据失败: {e}")
            return False
    
    def _build_interpolators(self):
        """建立插值器"""
        try:
            # 过滤有效数据
            valid_data = self.calibration_data.dropna(subset=['screen_x', 'screen_y', 'pupil_left', 'pupil_right'])
            
            if len(valid_data) < MIN_VALID_POINTS:
                logger.warning(f"有效标定点不足: {len(valid_data)} < {MIN_VALID_POINTS}")
                return
            
            # 提取坐标和瞳孔数据
            x_coords = valid_data['screen_x'].values
            y_coords = valid_data['screen_y'].values
            pupil_left = valid_data['pupil_left'].values
            pupil_right = valid_data['pupil_right'].values
            
            # 建立插值器
            if INTERPOLATION_METHOD == 'rbf':
                # 径向基函数插值
                from scipy.interpolate import RBFInterpolator
                
                points = np.column_stack([x_coords, y_coords])
                self.interpolator_left = RBFInterpolator(
                    points, pupil_left, 
                    kernel=RBF_FUNCTION, 
                    smoothing=RBF_SMOOTH
                )
                self.interpolator_right = RBFInterpolator(
                    points, pupil_right, 
                    kernel=RBF_FUNCTION, 
                    smoothing=RBF_SMOOTH
                )
                
            elif INTERPOLATION_METHOD == 'cubic':
                # 三次样条插值
                self.interpolator_left = interpolate.CloughTocher2DInterpolator(
                    np.column_stack([x_coords, y_coords]), pupil_left
                )
                self.interpolator_right = interpolate.CloughTocher2DInterpolator(
                    np.column_stack([x_coords, y_coords]), pupil_right
                )
                
            else:
                # 线性插值
                self.interpolator_left = interpolate.LinearNDInterpolator(
                    np.column_stack([x_coords, y_coords]), pupil_left
                )
                self.interpolator_right = interpolate.LinearNDInterpolator(
                    np.column_stack([x_coords, y_coords]), pupil_right
                )
            
            logger.info(f"插值器建立完成，使用方法: {INTERPOLATION_METHOD}")
            
        except Exception as e:
            logger.error(f"建立插值器失败: {e}")
    
    def calculate_geometric_correction_factor(self, gaze_x: float, gaze_y: float) -> float:
        """
        计算几何模型的矫正系数
        
        Args:
            gaze_x: 注视点X坐标
            gaze_y: 注视点Y坐标
            
        Returns:
            float: 矫正系数 (1/√cos(θ))
        """
        try:
            # 相机向量 (从瞳孔中心指向相机)
            C = np.array([self.camera_position['x'], self.camera_position['y'], self.camera_position['z']])
            
            # 目标向量 (从瞳孔中心指向注视点)
            # 将屏幕坐标转换为3D坐标
            target_x = gaze_x - self.screen_position['x']
            target_y = gaze_y - self.screen_position['y']
            target_z = self.screen_position['z']
            T = np.array([target_x, target_y, target_z])
            
            # 计算向量长度
            C_magnitude = np.linalg.norm(C)
            T_magnitude = np.linalg.norm(T)
            
            if C_magnitude == 0 or T_magnitude == 0:
                logger.warning("向量长度为零，返回默认矫正系数")
                return 1.0
            
            # 计算余弦值
            cos_theta = np.dot(C, T) / (C_magnitude * T_magnitude)
            
            # 确保余弦值在有效范围内
            cos_theta = np.clip(cos_theta, 0.01, 1.0)  # 避免除零和负值
            
            # 计算矫正系数 (论文公式: 1/√cos(θ))
            correction_factor = 1.0 / np.sqrt(cos_theta)
            
            # 限制矫正系数范围
            correction_factor = np.clip(correction_factor, MIN_CORRECTION_FACTOR, MAX_CORRECTION_FACTOR)
            
            return correction_factor
            
        except Exception as e:
            logger.error(f"计算几何矫正系数失败: {e}")
            return 1.0
    
    def get_interpolated_baseline_pupil(self, gaze_x: float, gaze_y: float) -> Tuple[Optional[float], Optional[float]]:
        """
        通过插值获取基线瞳孔大小
        
        Args:
            gaze_x: 注视点X坐标
            gaze_y: 注视点Y坐标
            
        Returns:
            Tuple[left_pupil, right_pupil]: 插值得到的瞳孔大小
        """
        try:
            if self.interpolator_left is None or self.interpolator_right is None:
                return None, None
            
            # 进行插值
            with warnings.catch_warnings():
                warnings.simplefilter("ignore")
                
                left_pupil = self.interpolator_left(gaze_x, gaze_y)
                right_pupil = self.interpolator_right(gaze_x, gaze_y)
            
            # 检查插值结果
            if np.isnan(left_pupil) or np.isnan(right_pupil):
                logger.debug(f"插值结果包含NaN: ({gaze_x}, {gaze_y})")
                return None, None
            
            return float(left_pupil), float(right_pupil)
            
        except Exception as e:
            logger.error(f"插值计算失败: {e}")
            return None, None
    
    def correct_pupil_data(self, samples: pd.DataFrame, method: str = 'auto') -> pd.DataFrame:
        """
        矫正瞳孔数据
        
        Args:
            samples: 包含瞳孔和注视数据的DataFrame
            method: 矫正方法 ('geometric', 'interpolation', 'hybrid', 'auto')
            
        Returns:
            pd.DataFrame: 矫正后的数据
        """
        try:
            corrected_samples = samples.copy()
            
            # 检查必要的列
            required_cols = ['gx_left', 'gy_left', 'pa_left']
            missing_cols = [col for col in required_cols if col not in samples.columns]
            
            if missing_cols:
                logger.error(f"缺少必要的列: {missing_cols}")
                return corrected_samples
            
            # 自动选择方法
            if method == 'auto':
                if self.geometric_model_ready and OPTIMIZE_PARAMETERS:
                    method = 'optimized'
                elif self.interpolator_left is not None and USE_DATA_BASED_CORRECTION:
                    method = 'interpolation'
                elif USE_GEOMETRIC_MODEL:
                    method = 'geometric'
                else:
                    logger.warning("没有可用的矫正方法")
                    return corrected_samples
            
            logger.info(f"使用PFE矫正方法: {method}")
            
            # 应用矫正
            if method == 'geometric':
                corrected_samples = self._apply_geometric_correction(corrected_samples)
            elif method == 'optimized':
                corrected_samples = self._apply_optimized_correction(corrected_samples)
            elif method == 'interpolation':
                corrected_samples = self._apply_interpolation_correction(corrected_samples)
            elif method == 'hybrid':
                corrected_samples = self._apply_hybrid_correction(corrected_samples)
            
            logger.info("PFE矫正完成")
            return corrected_samples
            
        except Exception as e:
            logger.error(f"PFE矫正失败: {e}")
            return samples
    
    def _apply_geometric_correction(self, samples: pd.DataFrame) -> pd.DataFrame:
        """应用几何模型矫正"""
        try:
            corrected_samples = samples.copy()
            
            # 为每个样本计算矫正系数
            correction_factors = []
            for _, row in samples.iterrows():
                gaze_x = row['gx_left']
                gaze_y = row['gy_left']

                if pd.notna(gaze_x) and pd.notna(gaze_y):
                    factor = self.calculate_geometric_correction_factor(gaze_x, gaze_y)
                else:
                    factor = 1.0

                correction_factors.append(factor)
            
            # 应用矫正 - 直接覆盖原始数据
            corrected_samples['pa_left'] = samples['pa_left'] * correction_factors

            # 如果有右眼数据也进行矫正
            if 'pa_right' in samples.columns:
                corrected_samples['pa_right'] = samples['pa_right'] * correction_factors
            
            logger.info(f"几何矫正完成，平均矫正系数: {np.mean(correction_factors):.3f}")
            
            return corrected_samples
            
        except Exception as e:
            logger.error(f"几何矫正失败: {e}")
            return samples

    def _apply_optimized_correction(self, samples: pd.DataFrame) -> pd.DataFrame:
        """应用优化后的几何模型矫正"""
        try:
            if not self.geometric_model_ready:
                logger.warning("几何模型未优化，回退到基础几何矫正")
                return self._apply_geometric_correction(samples)

            corrected_samples = samples.copy()

            # 为每个样本计算优化矫正系数
            correction_factors = []
            for _, row in samples.iterrows():
                gaze_x = row['gx_left']
                gaze_y = row['gy_left']

                if pd.notna(gaze_x) and pd.notna(gaze_y):
                    factor = self.calculate_optimized_correction_factor(gaze_x, gaze_y)
                else:
                    factor = 1.0

                correction_factors.append(factor)

            # 应用矫正 - 直接覆盖原始数据
            corrected_samples['pa_left'] = samples['pa_left'] * correction_factors

            # 如果有右眼数据也进行矫正
            if 'pa_right' in samples.columns:
                corrected_samples['pa_right'] = samples['pa_right'] * correction_factors

            logger.info(f"优化矫正完成，平均矫正系数: {np.mean(correction_factors):.3f}")

            return corrected_samples

        except Exception as e:
            logger.error(f"优化矫正失败: {e}")
            return samples
    
    def _apply_interpolation_correction(self, samples: pd.DataFrame) -> pd.DataFrame:
        """应用插值矫正"""
        try:
            corrected_samples = samples.copy()
            
            # 为每个样本计算矫正后的瞳孔大小
            corrected_left = []
            corrected_right = []
            
            for _, row in samples.iterrows():
                gaze_x = row['gx_left']
                gaze_y = row['gy_left']
                current_left = row['pa_left']
                current_right = row.get('pa_right', np.nan)
                
                if pd.notna(gaze_x) and pd.notna(gaze_y):
                    # 获取该位置的基线瞳孔大小
                    baseline_left, baseline_right = self.get_interpolated_baseline_pupil(gaze_x, gaze_y)
                    
                    if baseline_left is not None and pd.notna(current_left):
                        # 计算矫正系数
                        correction_factor = baseline_left / current_left if current_left != 0 else 1.0
                        correction_factor = np.clip(correction_factor, MIN_CORRECTION_FACTOR, MAX_CORRECTION_FACTOR)
                        corrected_left.append(current_left * correction_factor)
                    else:
                        corrected_left.append(current_left)
                    
                    if baseline_right is not None and pd.notna(current_right):
                        correction_factor = baseline_right / current_right if current_right != 0 else 1.0
                        correction_factor = np.clip(correction_factor, MIN_CORRECTION_FACTOR, MAX_CORRECTION_FACTOR)
                        corrected_right.append(current_right * correction_factor)
                    else:
                        corrected_right.append(current_right)
                else:
                    corrected_left.append(current_left)
                    corrected_right.append(current_right)
            
            # 更新数据 - 直接覆盖原始数据
            corrected_samples['pa_left'] = corrected_left
            if 'pa_right' in samples.columns:
                corrected_samples['pa_right'] = corrected_right
            
            logger.info("插值矫正完成")
            
            return corrected_samples
            
        except Exception as e:
            logger.error(f"插值矫正失败: {e}")
            return samples
    
    def _apply_hybrid_correction(self, samples: pd.DataFrame) -> pd.DataFrame:
        """应用混合矫正方法"""
        try:
            # 先应用几何矫正
            geometric_corrected = self._apply_geometric_correction(samples)
            
            # 再应用插值微调
            if self.interpolator_left is not None:
                final_corrected = self._apply_interpolation_correction(geometric_corrected)
            else:
                final_corrected = geometric_corrected
            
            logger.info("混合矫正完成")
            return final_corrected
            
        except Exception as e:
            logger.error(f"混合矫正失败: {e}")
            return samples

    def optimize_geometric_parameters(self, calibration_data: pd.DataFrame,
                                    save_results: bool = True,
                                    output_dir: Optional[str] = None) -> Dict:
        """
        使用Nelder-Mead算法优化几何模型参数

        这是论文中的核心方法：
        "The optimized model was fit to the pupillometric data separately for each layout
        using an unconstrained Nelder–Mead optimization routine that minimized the root
        mean squared error."

        Args:
            calibration_data: 标定数据，包含screen_x, screen_y, pupil_left列
            save_results: 是否保存优化结果

        Returns:
            Dict: 优化结果，包含优化后的参数和统计信息
        """
        try:
            logger.info("开始Nelder-Mead参数优化...")

            # 验证数据
            required_cols = ['screen_x', 'screen_y', 'pupil_left']
            missing_cols = [col for col in required_cols if col not in calibration_data.columns]
            if missing_cols:
                raise ValueError(f"标定数据缺少必要列: {missing_cols}")

            # 过滤有效数据
            valid_data = calibration_data.dropna(subset=required_cols)
            if len(valid_data) < MIN_VALID_POINTS:
                raise ValueError(f"有效标定点不足: {len(valid_data)} < {MIN_VALID_POINTS}")

            logger.info(f"使用 {len(valid_data)} 个有效标定点进行优化")

            # 提取数据
            screen_x = valid_data['screen_x'].values
            screen_y = valid_data['screen_y'].values
            measured_pupil = valid_data['pupil_left'].values

            # 计算基线瞳孔大小（所有测量值的几何平均）
            baseline_pupil = np.exp(np.mean(np.log(np.array(measured_pupil))))
            logger.info(f"基线瞳孔大小: {baseline_pupil:.2f}")

            # 初始参数（来自物理测量）
            initial_params = np.array([
                self.camera_position['x'],    # Cx
                self.camera_position['y'],    # Cy
                self.screen_position['x'],    # Sx
                self.screen_position['y'],    # Sy
                self.screen_position['z']     # Sz
            ])
            # Cz保持固定（论文中的做法）
            fixed_cz = self.camera_position['z']

            logger.info(f"初始参数: Cx={initial_params[0]}, Cy={initial_params[1]}, "
                       f"Cz={fixed_cz}(固定), Sx={initial_params[2]}, "
                       f"Sy={initial_params[3]}, Sz={initial_params[4]}")

            # 定义目标函数（RMSE最小化）
            def objective_function(params):
                """
                目标函数：计算几何模型预测值与实际测量值的RMSE

                Args:
                    params: [Cx, Cy, Sx, Sy, Sz]

                Returns:
                    float: RMSE值
                """
                try:
                    Cx, Cy, Sx, Sy, Sz = params

                    # 计算每个点的预测瞳孔大小
                    predicted_pupil = []

                    for i in range(len(screen_x)):
                        x, y = screen_x[i], screen_y[i]

                        # 相机向量 (从瞳孔中心指向相机)
                        C = np.array([Cx, Cy, fixed_cz])

                        # 目标向量 (从瞳孔中心指向注视点)
                        target_x = x - Sx
                        target_y = y - Sy
                        target_z = Sz
                        T = np.array([target_x, target_y, target_z])

                        # 计算余弦值
                        C_magnitude = np.linalg.norm(C)
                        T_magnitude = np.linalg.norm(T)

                        if C_magnitude == 0 or T_magnitude == 0:
                            cos_theta = 1.0
                        else:
                            cos_theta = np.dot(C, T) / (C_magnitude * T_magnitude)
                            cos_theta = np.clip(cos_theta, 0.1, 1.0)  # 更保守的范围

                        # 根据论文公式计算预测瞳孔大小
                        # φ(x,y) = φ0 * √cos(θ)
                        predicted = baseline_pupil * np.sqrt(cos_theta)
                        predicted_pupil.append(predicted)

                    predicted_pupil = np.array(predicted_pupil)

                    # 计算RMSE
                    rmse = np.sqrt(np.mean((predicted_pupil - measured_pupil) ** 2))

                    return rmse

                except Exception as e:
                    logger.warning(f"目标函数计算失败: {e}")
                    return 1e6  # 返回大值表示失败

            # 计算初始RMSE
            initial_rmse = objective_function(initial_params)
            logger.info(f"初始RMSE: {initial_rmse:.4f}")

            # 定义参数边界（合理的物理范围）
            bounds = [
                (-500, 500),    # Cx: 相机X位置
                (-500, 500),    # Cy: 相机Y位置
                (-1000, 1000),  # Sx: 屏幕X位置
                (-500, 1000),   # Sy: 屏幕Y位置
                (200, 1500)     # Sz: 屏幕Z位置
            ]

            # 使用L-BFGS-B算法（支持边界约束）
            logger.info("开始有约束优化...")
            optimization_result = minimize(
                objective_function,
                initial_params,
                method='L-BFGS-B',
                bounds=bounds,
                options={
                    'maxiter': 1000,
                    'ftol': 1e-9,
                    'gtol': 1e-6
                }
            )

            # 如果L-BFGS-B失败，尝试Nelder-Mead
            if not optimization_result.success:
                logger.info("有约束优化失败，尝试Nelder-Mead...")
                optimization_result = minimize(
                    objective_function,
                    initial_params,
                    method='Nelder-Mead',
                    options={
                        'maxiter': 3000,
                        'maxfev': 6000,
                        'xatol': 1e-6,
                        'fatol': 1e-6,
                        'adaptive': True
                    }
                )

            if optimization_result.success:
                logger.info("优化成功完成")
                optimized_params = optimization_result.x
                final_rmse = optimization_result.fun

                logger.info(f"优化后参数: Cx={optimized_params[0]:.2f}, "
                           f"Cy={optimized_params[1]:.2f}, Cz={fixed_cz}(固定), "
                           f"Sx={optimized_params[2]:.2f}, Sy={optimized_params[3]:.2f}, "
                           f"Sz={optimized_params[4]:.2f}")
                logger.info(f"最终RMSE: {final_rmse:.4f}")
                logger.info(f"RMSE改善: {(1 - final_rmse/initial_rmse)*100:.1f}%")

                # 更新内部参数
                self.camera_position = {
                    'x': optimized_params[0],
                    'y': optimized_params[1],
                    'z': fixed_cz
                }
                self.screen_position = {
                    'x': optimized_params[2],
                    'y': optimized_params[3],
                    'z': optimized_params[4]
                }

                # 保存优化结果
                results = {
                    'success': True,
                    'initial_parameters': {
                        'Cx': float(initial_params[0]),
                        'Cy': float(initial_params[1]),
                        'Cz': float(fixed_cz),
                        'Sx': float(initial_params[2]),
                        'Sy': float(initial_params[3]),
                        'Sz': float(initial_params[4])
                    },
                    'optimized_parameters': {
                        'Cx': float(optimized_params[0]),
                        'Cy': float(optimized_params[1]),
                        'Cz': float(fixed_cz),
                        'Sx': float(optimized_params[2]),
                        'Sy': float(optimized_params[3]),
                        'Sz': float(optimized_params[4])
                    },
                    'initial_rmse': float(initial_rmse),
                    'final_rmse': float(final_rmse),
                    'rmse_improvement_percent': float((1 - final_rmse/initial_rmse)*100),
                    'baseline_pupil': float(baseline_pupil),
                    'n_calibration_points': len(valid_data),
                    'optimization_iterations': optimization_result.nit,
                    'optimization_function_evaluations': optimization_result.nfev
                }

                self.optimized_parameters = results['optimized_parameters']
                self.optimization_results = results
                self.geometric_model_ready = True

                # 保存结果到文件
                if save_results:
                    self._save_optimization_results(results, output_dir)

                return results

            else:
                logger.error(f"优化失败: {optimization_result.message}")
                return {
                    'success': False,
                    'message': optimization_result.message,
                    'initial_rmse': float(initial_rmse)
                }

        except Exception as e:
            logger.error(f"参数优化失败: {e}")
            return {'success': False, 'error': str(e)}

    def _save_optimization_results(self, results: Dict, output_dir: Optional[str] = None):
        """保存优化结果到文件"""
        try:
            # 创建保存目录
            if output_dir is None:
                save_dir = os.path.join(os.path.dirname(__file__), 'optimization_results')
            else:
                save_dir = output_dir
            os.makedirs(save_dir, exist_ok=True)

            # 生成文件名
            from datetime import datetime
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')

            # 如果有标定文件路径，使用其基础名称
            if hasattr(self, 'calibration_data_path') and self.calibration_data_path:
                calibration_basename = os.path.splitext(os.path.basename(self.calibration_data_path))[0]
                filename = f'pfe_optimization_{calibration_basename}_{timestamp}.json'
            else:
                filename = f'pfe_optimization_{timestamp}.json'

            filepath = os.path.join(save_dir, filename)

            # 保存结果
            with open(filepath, 'w', encoding='utf-8') as f:
                json.dump(results, f, indent=2, ensure_ascii=False)

            logger.info(f"优化结果已保存到: {filepath}")
            return filepath

        except Exception as e:
            logger.error(f"保存优化结果失败: {e}")
            return None

    def load_optimization_results(self, filepath: str) -> bool:
        """加载优化结果"""
        try:
            with open(filepath, 'r', encoding='utf-8') as f:
                results = json.load(f)

            if results.get('success', False):
                # 更新参数
                opt_params = results['optimized_parameters']
                self.camera_position = {
                    'x': opt_params['Cx'],
                    'y': opt_params['Cy'],
                    'z': opt_params['Cz']
                }
                self.screen_position = {
                    'x': opt_params['Sx'],
                    'y': opt_params['Sy'],
                    'z': opt_params['Sz']
                }

                self.optimized_parameters = opt_params
                self.optimization_results = results
                self.geometric_model_ready = True

                logger.info(f"已加载优化结果: RMSE改善 {results.get('rmse_improvement_percent', 0):.1f}%")
                return True
            else:
                logger.error("加载的优化结果无效")
                return False

        except Exception as e:
            logger.error(f"加载优化结果失败: {e}")
            return False

    def calculate_optimized_correction_factor(self, gaze_x: float, gaze_y: float) -> float:
        """
        使用优化后的参数计算矫正系数

        Args:
            gaze_x: 注视点X坐标
            gaze_y: 注视点Y坐标

        Returns:
            float: 矫正系数 (1/√cos(θ))
        """
        if not self.geometric_model_ready or self.optimized_parameters is None:
            logger.warning("几何模型未优化，使用默认参数")
            return self.calculate_geometric_correction_factor(gaze_x, gaze_y)

        try:
            # 使用优化后的参数
            C = np.array([
                self.optimized_parameters['Cx'],
                self.optimized_parameters['Cy'],
                self.optimized_parameters['Cz']
            ])

            # 目标向量
            target_x = gaze_x - self.optimized_parameters['Sx']
            target_y = gaze_y - self.optimized_parameters['Sy']
            target_z = self.optimized_parameters['Sz']
            T = np.array([target_x, target_y, target_z])

            # 计算向量长度
            C_magnitude = np.linalg.norm(C)
            T_magnitude = np.linalg.norm(T)

            if C_magnitude == 0 or T_magnitude == 0:
                return 1.0

            # 计算余弦值
            cos_theta = np.dot(C, T) / (C_magnitude * T_magnitude)
            cos_theta = np.clip(cos_theta, 0.01, 1.0)

            # 计算矫正系数
            correction_factor = 1.0 / np.sqrt(cos_theta)
            correction_factor = np.clip(correction_factor, MIN_CORRECTION_FACTOR, MAX_CORRECTION_FACTOR)

            return correction_factor

        except Exception as e:
            logger.error(f"计算优化矫正系数失败: {e}")
            return 1.0

def optimize_and_apply_pfe_correction(samples: pd.DataFrame,
                                     calibration_data_path: str,
                                     method: str = 'optimized',
                                     enable_optimization: bool = True) -> Tuple[pd.DataFrame, Dict]:
    """
    执行完整的PFE优化和矫正流程

    Args:
        samples: 瞳孔数据DataFrame
        calibration_data_path: 标定数据文件路径
        method: 矫正方法 ('optimized', 'geometric', 'interpolation', 'auto')
        enable_optimization: 是否启用参数优化

    Returns:
        Tuple[pd.DataFrame, Dict]: (矫正后的数据, 优化结果)
    """
    try:
        # 创建矫正器
        corrector = PFECorrector(calibration_data_path)

        optimization_results = {'success': False}

        # 如果启用优化且有标定数据
        if enable_optimization and corrector.calibration_data is not None:
            logger.info("开始执行Nelder-Mead参数优化...")
            optimization_results = corrector.optimize_geometric_parameters(
                corrector.calibration_data, save_results=True
            )

            if optimization_results.get('success', False):
                logger.info(f"优化成功，RMSE改善: {optimization_results.get('rmse_improvement_percent', 0):.1f}%")
            else:
                logger.warning("优化失败，将使用默认几何模型")
                method = 'geometric' if method == 'optimized' else method

        # 应用矫正
        corrected_samples = corrector.correct_pupil_data(samples, method)

        return corrected_samples, optimization_results

    except Exception as e:
        logger.error(f"PFE优化和矫正失败: {e}")
        return samples, {'success': False, 'error': str(e)}

def apply_pfe_correction(samples: pd.DataFrame,
                        calibration_data_path: Optional[str] = None,
                        method: str = 'auto',
                        enable_correction: bool = True) -> pd.DataFrame:
    """
    应用PFE矫正的便捷函数

    Args:
        samples: 瞳孔数据DataFrame
        calibration_data_path: 标定数据文件路径
        method: 矫正方法
        enable_correction: 是否启用矫正

    Returns:
        pd.DataFrame: 矫正后的数据
    """
    if not enable_correction:
        logger.info("PFE矫正已禁用")
        return samples

    try:
        corrector = PFECorrector(calibration_data_path)
        return corrector.correct_pupil_data(samples, method)
    except Exception as e:
        logger.error(f"PFE矫正应用失败: {e}")
        return samples

def main():
    """
    PFE矫正主函数
    输入标定文件，进行优化，输出优化后的参数
    """
    import argparse
    from datetime import datetime

    # 解析命令行参数
    parser = argparse.ArgumentParser(description='PFE瞳孔前缩误差矫正优化')
    parser.add_argument('--calibration', '-c', type=str, required=True,
                       help='标定数据文件路径 (.csv)')
    parser.add_argument('--output', '-o', type=str, default=None,
                       help='输出目录 (默认: pfe/optimization_results)')
    parser.add_argument('--method', '-m', type=str, default='optimized',
                       choices=['geometric', 'optimized', 'interpolation', 'hybrid'],
                       help='矫正方法 (默认: optimized)')
    parser.add_argument('--test', '-t', action='store_true',
                       help='运行测试模式')

    args = parser.parse_args()

    print("=" * 60)
    print("PFE瞳孔前缩误差矫正优化程序")
    print("=" * 60)

    # 检查标定文件是否存在
    if not os.path.exists(args.calibration):
        logger.error(f"标定文件不存在: {args.calibration}")
        return

    print(f"标定文件: {args.calibration}")
    print(f"矫正方法: {args.method}")

    try:
        # 加载标定数据
        print("\n1. 加载标定数据...")
        calibration_data = pd.read_csv(args.calibration, encoding='utf-8-sig')
        print(f"加载了 {len(calibration_data)} 个标定点")

        # 验证数据完整性
        required_cols = ['screen_x', 'screen_y', 'pupil_left']
        missing_cols = [col for col in required_cols if col not in calibration_data.columns]

        if missing_cols:
            logger.error(f"标定数据缺少必要列: {missing_cols}")
            return

        # 显示数据统计
        valid_data = calibration_data.dropna(subset=required_cols)
        print(f"有效标定点: {len(valid_data)}")
        print(f"瞳孔大小范围: {valid_data['pupil_left'].min():.0f} - {valid_data['pupil_left'].max():.0f}")

        # 创建PFE矫正器
        print("\n2. 初始化PFE矫正器...")
        corrector = PFECorrector(calibration_data_path=args.calibration)

        # 执行优化
        if args.method == 'optimized':
            print("\n3. 执行Nelder-Mead参数优化...")
            optimization_results = corrector.optimize_geometric_parameters(
                calibration_data, save_results=True
            )

            if optimization_results.get('success', False):
                print(f"✓ 优化成功!")
                print(f"  初始RMSE: {optimization_results['initial_rmse']:.4f}")
                print(f"  最终RMSE: {optimization_results['final_rmse']:.4f}")
                print(f"  改善程度: {optimization_results['rmse_improvement_percent']:.1f}%")
                print(f"  优化迭代: {optimization_results['optimization_iterations']} 次")
                print(f"  函数评估: {optimization_results['optimization_function_evaluations']} 次")

                # 显示优化后的参数
                opt_params = optimization_results['optimized_parameters']
                print(f"\n优化后的参数:")
                print(f"  相机位置: Cx={opt_params['Cx']:.2f}, Cy={opt_params['Cy']:.2f}, Cz={opt_params['Cz']:.2f}")
                print(f"  屏幕位置: Sx={opt_params['Sx']:.2f}, Sy={opt_params['Sy']:.2f}, Sz={opt_params['Sz']:.2f}")

                # 保存到指定目录
                if args.output:
                    output_dir = args.output
                else:
                    output_dir = os.path.join(os.path.dirname(__file__), 'optimization_results')

                os.makedirs(output_dir, exist_ok=True)

                # 生成输出文件名
                timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
                calibration_basename = os.path.splitext(os.path.basename(args.calibration))[0]
                output_filename = f'pfe_optimization_{calibration_basename}_{timestamp}.json'
                output_filepath = os.path.join(output_dir, output_filename)

                # 保存优化结果
                with open(output_filepath, 'w', encoding='utf-8') as f:
                    json.dump(optimization_results, f, indent=2, ensure_ascii=False)

                print(f"\n✓ 优化结果已保存到: {output_filepath}")

            else:
                print("✗ 优化失败:")
                print(f"  错误: {optimization_results.get('error', '未知错误')}")
                return

        # 如果是测试模式，创建测试数据并验证矫正效果
        if args.test:
            print("\n4. 测试矫正效果...")

            # 创建测试数据（使用标定数据中的一些点）
            test_indices = [0, len(valid_data)//4, len(valid_data)//2, 3*len(valid_data)//4, -1]
            test_data = pd.DataFrame({
                'gx_left': [valid_data.iloc[i]['screen_x'] for i in test_indices],
                'gy_left': [valid_data.iloc[i]['screen_y'] for i in test_indices],
                'pa_left': [valid_data.iloc[i]['pupil_left'] for i in test_indices],
                'pa_right': [valid_data.iloc[i].get('pupil_right', valid_data.iloc[i]['pupil_left']) for i in test_indices]
            })

            print("测试数据:")
            print(test_data[['gx_left', 'gy_left', 'pa_left']])

            # 应用矫正
            corrected_data = corrector.correct_pupil_data(test_data, method=args.method)

            print(f"\n{args.method}矫正后:")
            print(corrected_data[['gx_left', 'gy_left', 'pa_left']])

            # 计算矫正系数
            correction_factors = corrected_data['pa_left'] / test_data['pa_left']
            print(f"\n矫正系数范围: {correction_factors.min():.3f} - {correction_factors.max():.3f}")
            print(f"平均矫正系数: {correction_factors.mean():.3f}")

        print("\n" + "=" * 60)
        print("PFE矫正优化完成")
        print("=" * 60)

    except Exception as e:
        logger.error(f"程序执行失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
