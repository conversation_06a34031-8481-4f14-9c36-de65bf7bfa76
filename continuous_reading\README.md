# 连续文本阅读实验

这是一个基于眼动追踪的连续文本阅读实验程序，用于研究阅读过程中的认知加工。

## 功能特点

- **连续文本阅读**: 逐句呈现文本内容
- **多维度评分**: 支持好奇心、有趣度、理解程度、费劲程度等评分
- **基线标注**: 可配置的十字星基线显示
- **眼动集成**: 完整的EyeLink眼动仪支持
- **灵活配置**: 所有参数都可通过配置文件调整
- **数据记录**: 完整的实验数据和评分记录

## 文件结构

```
continuous_reading/
├── continuous_reading_config.py    # 实验配置文件
├── text_materials.py              # 文本材料管理
├── continuous_reading_experiment.py # 主实验程序
├── run_experiment.py              # 启动脚本
├── reading_text.txt               # 示例阅读文本
└── README.md                      # 说明文档
```

## 快速开始

### 1. 准备文本材料

将你的阅读文本保存为 `reading_text.txt` 文件，放在 `continuous_reading` 文件夹中。文本应该：
- 使用UTF-8编码
- 每个句子以句号（。）结尾
- 句子长度适中（5-200字符）

### 2. 配置实验参数

编辑 `continuous_reading_config.py` 文件，调整以下主要参数：

```python
# 基线配置
BASELINE_CONFIG = {
    'baseline_interval': 5,        # 每隔5个句子显示基线
    'baseline_duration': 2.0,      # 基线显示2秒
}

# 评分配置
RATING_CONFIG = {
    'curiosity_rating': {
        'question': '此刻, 你有多好奇，多想接着读下去?',
        'scale': (1, 5),
        'labels': {1: '完全不好奇,不想读', ...}
    },
    # ... 其他评分类型
}
```

### 3. 运行实验

```bash
cd continuous_reading
python run_experiment.py
```

或者直接运行主程序：

```bash
python continuous_reading_experiment.py
```

## 配置说明

### 文本配置 (TEXT_CONFIG)

- `text_file`: 文本文件名
- `sentence_delimiter`: 句子分隔符（默认：。）
- `min_sentence_length`: 最小句子长度
- `max_sentence_length`: 最大句子长度

### 基线配置 (BASELINE_CONFIG)

- `baseline_interval`: 基线显示间隔（句子数）
- `baseline_duration`: 基线显示时长（秒）
- `fixation_cross_size`: 十字星大小（像素）

### 评分配置 (RATING_CONFIG)

支持四种评分类型：
1. `curiosity_rating`: 好奇心评分（1-5分）
2. `interest_rating`: 有趣度评分（1-5分）
3. `comprehension_rating`: 理解程度评分（1-4分）
4. `difficulty_rating`: 费劲程度评分（1-3分）

每种评分都可以配置：
- `question`: 评分问题
- `scale`: 评分范围
- `labels`: 各分值的标签
- `instruction`: 操作说明

### 显示配置 (DISPLAY_CONFIG)

- `fullscreen`: 是否全屏显示
- `font_size`: 字体大小
- `font_name`: 字体名称（支持中文）
- `text_color`: 文字颜色

### EyeLink配置 (EYELINK_CONFIG)

- `use_eyelink`: 是否使用EyeLink
- `sampling_rate`: 采样率
- `file_prefix`: EDF文件前缀

## 实验流程

1. **初始化**: 加载文本材料，初始化显示和EyeLink
2. **校准**: EyeLink眼动仪校准
3. **说明**: 显示实验说明
4. **主循环**: 
   - 显示基线（如果配置）
   - 呈现句子
   - 收集评分
   - 重复直到文本结束
5. **结束**: 保存数据，清理资源

## 数据输出

实验会在 `data/` 目录下创建以时间戳和被试ID命名的文件夹，包含：

- `experiment_settings.json`: 实验设置
- `ratings_data.json`: 评分数据
- `ratings_data.csv`: CSV格式评分数据
- `experiment_summary.json`: 实验摘要
- `*.edf`: EyeLink数据文件（如果使用）

## 评分数据格式

```json
{
  "sentence_id": 1,
  "timestamp": 12.34,
  "curiosity_rating": 4,
  "interest_rating": 3,
  "comprehension_rating": 1,
  "difficulty_rating": 2,
  "text": "句子内容..."
}
```

## 自定义评分

要添加新的评分类型：

1. 在 `RATING_CONFIG` 中添加新的评分配置
2. 在 `RATING_ORDER` 中指定评分顺序
3. 重新运行实验

示例：
```python
RATING_CONFIG['emotion_rating'] = {
    'question': '这个句子让你感到什么情绪?',
    'scale': (1, 7),
    'labels': {1: '非常消极', 4: '中性', 7: '非常积极'},
    'instruction': '请按数字键1-7进行评分',
    'required': True
}

RATING_ORDER = ['curiosity_rating', 'interest_rating', 'emotion_rating', 'comprehension_rating', 'difficulty_rating']
```

## 调试模式

在 `DEBUG_CONFIG` 中可以启用调试功能：

- `debug_mode`: 开启调试模式
- `test_mode`: 测试模式（限制句子数量）
- `simulate_ratings`: 模拟评分（自动生成随机评分）
- `max_test_sentences`: 测试模式最大句子数

## 注意事项

1. **文本准备**: 确保文本文件使用UTF-8编码，句子以句号结尾
2. **字体支持**: 使用支持中文的字体（如SimHei）
3. **EyeLink**: 如果不使用EyeLink，设置 `use_eyelink=False`
4. **屏幕分辨率**: 根据实际屏幕调整 `screen_size` 配置
5. **评分时间**: 可以设置评分超时时间避免实验卡住

## 故障排除

### 常见问题

1. **文本文件不存在**: 程序会自动创建示例文本
2. **字体不支持中文**: 修改 `font_name` 为系统支持的中文字体
3. **EyeLink连接失败**: 检查EyeLink设备连接或使用dummy模式
4. **评分界面显示异常**: 检查屏幕分辨率和字体大小设置

### 日志信息

程序运行时会输出详细的日志信息，包括：
- 组件初始化状态
- 文本加载结果
- 实验进度
- 错误信息

## 扩展功能

这个实验框架可以轻松扩展：

1. **新的评分维度**: 在配置文件中添加
2. **不同的文本格式**: 修改文本解析逻辑
3. **额外的生理信号**: 集成其他设备
4. **实时分析**: 添加在线数据分析功能

## 技术支持

如有问题，请检查：
1. Python环境和依赖包
2. 配置文件语法
3. 文本文件格式
4. EyeLink设备状态
