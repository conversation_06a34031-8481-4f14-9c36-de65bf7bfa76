# Pupillometric-measures-of-cognitive-load

## Description
This project takes the pupillary measurement known as Index of Pupillary Activity (IPA) and Low-/High- Index of Pupillary Activity (LHIPA) from the <PERSON><PERSON><PERSON> et al. (2018) and (2020) papers respectively and implements them into a far more "pythonic" way.

## Experiment
Aside from the implementation of the IPA and LHIPA, I will compare the baseline IPA calculations to the the <PERSON><PERSON><PERSON><PERSON> et al. (2019) paper to see how they perform and then implement the LHIPA for the very same paper. 

## How to install
The project has two primary files PyPil_Datapreprocessor and PyPil itself. These are both fully functional classes and can be called into any python file as long as you are properly referncing them. requirements.txt files are available for those who may have problems, but the classes will throw you ImportErrors if you are missing essential imports.

*Note: The only items not found within the classes are my own testing with visualizations. These are not necessary to run either of the files although you will not have access to the sliding window dataframe function.

## How to use the Project
You can use this project on any dataset that has both time and pupil diameter of a person! The only difficult part of this is the ability to determine what the sliding window you want for your data to be.

## Credits
Github Author: <PERSON><PERSON><PERSON>.
Paper Authors: <AUTHORS>
               <PERSON><PERSON><PERSON> et al (2020)
               <PERSON><PERSON><PERSON> et al (2022)
               <PERSON><PERSON><PERSON><PERSON> et al (2019)
Advisors: I<PERSON> and <PERSON> :D
