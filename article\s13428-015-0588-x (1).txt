﻿<PERSON><PERSON><PERSON> (2016) 48:510–527
DOI 10.3758/s13428-015-0588-x

Mapping and correcting the influence of gaze position
on pupil size measurements
Taylor R<PERSON> Hayes1 ·Alexander A. Petrov1

Published online: 8 May 2015
© Psychonomic Society, Inc. 2015

Abstract Pupil size is correlated with a wide variety of the model parameters were pre-set to the physical layout
important cognitive variables and is increasingly being used dimensions, and by 97.5 % when they were optimized to fit
by cognitive scientists. Pupil data can be recorded inexpen- the empirical error surface.
sively and non-invasively by many commonly used video-
based eye-tracking cameras. Despite the relative ease of Keywords Pupillometry · Pupil foreshortening error ·
data collection and increasing prevalence of pupil data in Eye tracking · Artificial eye
the cognitive literature, researchers often underestimate the
methodological challenges associated with controlling for
confounds that can result in misinterpretation of their data. The human pupillary response correlates with a wide
One serious confound that is often not properly controlled range of important cognitive variables including mental
is pupil foreshortening error (PFE)—the foreshortening of workload (e.g., <PERSON> & <PERSON>, 1964; <PERSON><PERSON>, 1973,
the pupil image as the eye rotates away from the camera. <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, & <PERSON>, 2011), emotional valence
Here we systematically map PFE using an artificial eye (<PERSON><PERSON> & <PERSON>, 2003), attention (<PERSON><PERSON>, 1982), work-
model and then apply a geometric model correction. Three ing memory (<PERSON><PERSON> & <PERSON><PERSON>, 1966), arousal (<PERSON>,
artificial eyes with different fixed pupil sizes were used to <PERSON>, Balsters, & <PERSON>’<PERSON>, 2011), decision-making
systematically measure changes in pupil size as a function (Einhäuser, Koch, & Carter, 2010), surprise (Preuschoff,
of gaze position with a desktop EyeLink 1000 tracker. A Marius, & Einhäuser, 2011), and uncertainty (Nassar et al.,
grid-based map of pupil measurements was recorded with 2012). While the underlying mechanisms that drive cog-
each artificial eye across three experimental layouts of the nitive pupillary effects are still an active topic of inquiry,
eye-tracking camera and display. Large, systematic devia- recent data suggests that the pupillary response may reflect
tions in pupil size were observed across all nine maps. The noradrenergic activity in the brain (Murphy, O’Connell,
measured PFE was corrected by a geometric model that O’Sullivan, Robertson, & Balsters, 2014; Aston-Jones &
expressed the foreshortening of the pupil area as a function Cohen, 2005; Koss, 1986; Samuels & Szabadi, 2008). As
of the cosine of the angle between the eye-to-camera axis a result of these recent developments, pupil size is increas-
and the eye-to-stimulus axis. The model reduced the root ingly being used by cognitive scientists as an important
mean squared error of pupil measurements by 82.5 % when measure of cognitive processing.

Pupil data can be collected inexpensively and non-
invasively by most modern image-based eye-tracking sys-
tems, but researchers often underestimate the methodolog-

 Taylor R. Hayes
<EMAIL> ical challenges in producing well-controlled studies that

allow for cognitive interpretations of the pupillary response
1 Department of Psychology, Ohio State University, Columbus, (Holmqvist et al., 2011). Most researchers are aware of the

OH 43210, USA most serious confound—the pupillary light reflex. The pupil



Behav Res (2016) 48:510–527 511

diameter depends first and foremost on the luminance of providers (SR Research, 2010, p. 98) is stimulus-position
the stimulus and the ambient illuminance of the experimen- counterbalancing. In these designs, there are several posi-
tal room. Changes in either of these variables can produce tions where the stimulus can appear, but the number of pre-
strong light reflexes that modulate pupil size by as much sentations is counterbalanced across trials. This method still
as 50 % (Miller & Newman, 2005; Loewenfeld, 1993). does not allow the study of tasks that require free viewing
Cognitive researchers control for this confound by using of the stimulus. Neither does it allow for comparisons of
isoluminant stimuli and maintaining constant room illumi- physical pupil size among different gaze locations because
nation. A lesser-known yet equally problematic confound PFE varies across the visual field (Gagl, Hawelka, &
is pupil foreshortening error (PFE). Changes in gaze posi- Hutzler, 2011). In sum, though effective at mitigating the
tion produce foreshortening of the pupillary image because PFE, both methods have serious limitations.
the eye-tracking camera is fixed but the eye rotates. Specif- A completely different approach to the problem is to
ically, as the eye rotates away from the camera, the pupil measure PFE and then correct the pupillometric data prior to
image becomes more elliptical and shrinks in apparent area analysis (Gagl et al., 2011; Brisson et al., 2013). Gagl et al.
by as much as 10 %. This is a large margin compared to the (2011) recently pioneered this approach with the aid of an
magnitude of the cognitive effects, which rarely exceed 5 % artificial eye model with a fixed pupil size. The artificial eye
change in pupil size. Despite its potential seriousness, the was moved across a single horizontal scan line, emulating
PFE is not corrected in many commonly used remote eye- a sentence reading task. The PFE could thus be measured
tracking systems (e.g., Tobii, Tobii Technology AB, 2010, explicitly and a correction formula was developed. The hor-
and EyeLink, SR Research, 2010). All too often, the PFE izontal extent of the sentence stimuli spanned from −17◦
is not controlled by the experimental design either (Brisson to +9◦ from the screen center. This resulted in systematic
et al., 2013). The combination of these factors has led to pupillometric error from +5 % to −13 %, respectively, rel-
an increasing number of published studies with inadequate ative to the true pupil area of the artificial eye. These data
control for PFE. allowed the development of a mathematical model of the

There are currently two different methods aimed at PFE. This model was used to correct the empirical data
circumventing PFE using experimental design. The most from a sentence-reading task and a “Z-string”- reading task
conservative method—and the one recommended by most in which participants scanned words composed entirely of
commercial eye-tracking providers—is to have participants the letter Z. When the measurement error was corrected,
maintain constant fixation throughout each trial (e.g., SR
Research, 2010, p.28). The constant fixation method is
implemented by defining a small fixation boundary area
and verifying that the eye position remains within this area

Pupil calibration 
throughout the pretrial fixation baseline and stimulus pre- tape in window 
sentation periods. This minimizes PFE by keeping the opti- device
cal axis of the eye at a fixed angle from the camera. Standard
baseline normalization is typically used in conjunction with
the constant fixation method, where the task-evoked pupil-
lary response is measured as the absolute difference in
millimeters or percent change between the stimulus pre-
sentation and pretrial baseline periods (Beatty & Lucero-
Wagoner, 2000). However, the constant fixation method has
two serious drawbacks. First, it limits the types of tasks that Additional model in 

ocular socket
can be studied to those with a constant fixation location. models
Unfortunately, this rules out studies of important topics such
as visual search or reading. Second, it is hard for participants
to consistently maintain fixation for long periods of time. In
addition, the pupillary response lags behind the stimulus by
about 400 ms, peaking around 1-2 s post-stimulus, which
necessitates longer baseline and stimulus durations where
participants must maintain fixation (Partala & Surakka,
2003; Murphy et al., 2011; Hayes & Petrov, 2015). This Fig. 1 Photograph of the artificial eye, ocular socket, and pupil cali-

results in a high number of invalid trials that increases bration devices. Each artificial eye was measured in a constant left-eye
position within the chin- and forehead-rest. The pupil calibration appa-

as the stimulus duration increases. Another PFE-mitigation ratus was not present during artificial-eye data collection as shown, but
method that is sometimes recommended by eye-tracking rather replaced the ocular socket during the pupil calibration procedure



512 Behav Res (2016) 48:510–527

previously reported discrepancies between the pupillary were pre-set to the physical dimensions of the experimen-
response during word recognition (Kuchinke, Võ, Hofmann, tal layout, and by 97.5 % when they were optimized to
& Jacobs, 2007; Võ et al., 2008) and sentence-reading fit the empirical error surface. Thus, our calibration data
tasks (Just & Carpenter, 1993; Raisig, Welke, Hagendorf, & and correction procedure allow for an unprecedented reduc-
Meer, 2010) were revealed to be artifacts of PFE (Gagl et al., tion in PFE without sacrificing data quality or experimental
2011). This pioneering study illustrates the importance of flexibility in the process.
accounting for PFE and how it can lead to incorrect cogni-
tive interpretations if not properly controlled or corrected.
One major shortcoming of this study is that it only mapped a Experiment 1
single horizontal scan line. The resulting correction formula
is applicable to this special case only. The EyeLink 1000 User Manual (SR Research, 2010, p. 98)

Brisson and colleagues 2013 used an object pursuit task states that, “The pupil size data is not calibrated, and the
to characterize the PFE of three popular eye-tracking sys- units of pupil measurement will vary with subject setup.
tems (Tobii X120, Tobii T120, and EyeLink 1000) across Pupil size is an integer number, in arbitrary units. . . . Pupil
both horizontal and vertical changes in gaze position. Forty- size measurements are affected by up to 10 % by pupil posi-
four human participants tracked a circle moving counter- tion, due to the optical distortion of the cornea of the eye,
clockwise across the display in an elliptical pattern that and camera-related factors”.
covered up to 22◦ of horizontal visual angle and up to 14◦ of This statement is very vague. It does not even specify
vertical visual angle (depending on the system). The results the type of scale for these “arbitrary units”. A measurement
showed substantial measurement errors in pupil diameter procedure establishes a correspondence between a set of
as a function of gaze position in all three systems. A lin- numbers and a set of objects with respect to some attribute
ear regression model using X and Y gaze coordinates as of interest—pupil diameter in our case. It matters greatly
predictors was able to explain between 9 % and 20 % of what types of relationships are preserved by this mapping
the pupil variance depending on the system and the max- (Torgerson, 1958). An ordinal scale only preserves com-
imum visual angle. While these data provided important parative relations (<, =, >). An interval scale preserves
new insights into the extent of PFE with human partici- distances but not ratios. Thus, it is meaningful to calculate
pants across multiple eye-tracking systems, the regression- means, differences, and standard deviations among interval-
based correction procedure only accounted for a small scale measurements, but they do not warrant inferences of
amount of the total PFE. Furthermore, even low-effort the form, “the pupil diameter in condition A is 10 % greater
tasks such as object tracking require attentional mecha- than that in condition B”. The latter kind of statement asserts
nisms and cognitive effort that induce pupillary variability a multiplicative relationship (A = 1.1×B) that is warranted
(Alnaes et al., 2014; Beatty, 1982; 1982b; Kahneman, only for a ratio scale—that is, for an interval scale with a
1973) that complicates the estimation of the foreshortening true origin. Ratio scales map the number zero to the (possi-
error. bly hypothetical) object that altogether lacks the attribute in

In the present article, we systematically map and cor- question.
rect pupil foreshortening error across the full display using Ideally, we want to measure pupil diameter on a ratio
a set of artificial eyes with fixed, known pupil diameters scale with standard units such as millimeters. The next
(Fig. 1). We report two studies. The first verified the ratio best method is to measure it on a ratio scale whose units
scale between the “arbitrary units” of the EyeLink 1000 and are proportional to millimeters, although the coefficient of
physical units. The second study systematically maps PFE proportionality is not specified. This is one possible inter-
and develops both a parameter-free and a parameterized pretation of the phrase “arbitrary units”. The coefficient of
geometric model correction that virtually eliminates PFE. proportionality may vary with experimental setup, as the
Three artificial eyes were measured across the full extent of Manual warns, but as long as all measurements within a
a 21” monitor in each of three separate experimental layouts given setup form a ratio scale, they still support multi-
varying the relative distances between the camera, moni- plicative operations. This is important because, as we show
tor, and eye. The measurements revealed large distortions below, the geometric foreshortening law is multiplicative:
in recorded pupil diameter as a function of gaze position. The foreshortened diameter equals the true diameter times
A simple geometric model that estimates pupil foreshorten- the cosine of a certain angle.
ing as the cosine of the angle between the eye-to-camera The Manual provides very little information about how
axis and the eye-to-stimulus axis produced excellent fits the pupillometric data are acquired and processed by the
to the data (cf. Atchison & Smith, 2000; Spring & Stiles, proprietary software on the host computer. This raises the
1948). The model reduced the root mean squared error of disconcerting possibility that the “arbitrary units” may only
pupil measurements by 82.5 % when the model parameters form an interval scale. This could occur, for instance, if the



Behav Res (2016) 48:510–527 513

a b d e

c

Fig. 2 Schematic illustrations of the calibration apparatus, artificial pupil diameters 3.17, 4.76, and 7.14 mm, respectively. The gaze direc-
eye sizes, laser orientation apparatus, ocular socket, and target dis- tion of the artificial eyes were precisely guided using an attached laser
plays. The windowed pupil calibration apparatus (a) allowed steady pointer (c) whose collimated beam illuminated a bright dot on the tar-
presentation of each of seven black dots of known diameter printed on get display screen (e). A simple socket (d) allowed the artificial eye to
a paper slip. b Three spherical artificial eyes were constructed with rotate to “fixate” a target while maintaining constant physical position

software added an unspecified constant before writing the opaque white piece of plastic (50 × 95 mm) with a window
numbers to the data file. This would render invalid all multi- (13 × 13 mm) served to present the printed pupils at a con-
plicative operations with these measurements. In particular, stant position (see Fig. 2a). The seven pupils were printed
it would undermine the common practice in the literature on a single tape that could be placed underneath the plastic
to report the pupillometric data in terms of percent change pouch and translated horizontally.
from baseline. We explored three separate geometric layouts—near,

Finally, the “arbitrary units” may form merely an ordinal medium, and far—that varied the relative positions of the
scale. This could happen, for instance, due to “the optical camera, artificial eye, and monitor, as specified in Fig. 3,
distortion of the cornea . . . and camera-related factors”. panels a, b, and c, respectively. At each layout we made
In this case, the information content of the pupillometric pupillometric recordings with each printed pupil for 5 s at
data would be very low and they should be analyzed using 1000 Hz, thereby obtaining 5000 pupil-area samples. The
ordinal statistical methods (Agresti, 1984). area data were converted to linear (diameter) data by taking

The purpose of this preliminary experiment is to collect square roots, and then averaged across the 5000 samples.
calibration data to characterize the type of scale used for This procedure produced a calibration data set of 33 pupil
EyeLink 1000 pupillometry. The results indicate that the diameter measurements (3 layouts × 11 pupils).
“arbitrary units” form a ratio scale with a layout-dependent
coefficient of proportionality to millimeters. Results and discussion

Method Figure 4 plots the calibration data in EyeLink’s “arbitrary
units” against the true diameter of the physical pupil in mil-

We used an EyeLink 1000 desktop eye tracker (SR limeters. The results clearly indicate that the arbitrary units
Research, 2010) at a sampling rate of 1000 Hz. The tracking form a ratio scale within each layout, but the coefficient of
mode was set to “pupil only,” the pupil threshold parameter proportionality to the true pupil diameter varies across lay-
to 110, and the eye-to-track parameter to “Left”. Pupil area outs. Specifically, this coefficient was kn = 10.07 for near,
was measured using centroid mode throughout the study. km = 9.65 for medium, and kf = 8.43 for the far layout in
Centroid mode computes pupil area using a center-of-mass Fig. 3. Each of these linear regressions accounted for essen-
algorithm that identifies the number of black pixels in the tially all the variance (R2 > .999) of the 11 measurements
thresholded pupil and its center on the camera image. The within a given layout. Importantly, adding a free intercept
User Manual recommends the center-of-mass algorithm parameter to the regression equation did not significantly
over the ellipse fitting algorithm because “it has very low improve the fits, and the intercept estimates were negligible
noise” (SR Research, 2010, p. 71). (< 0.5 AU). This indicates that 0 arbitrary units always cor-

A series of 11 black discs with diameters ranging from 2 respond to 0 mm regardless of layout, thereby satisfying the
to 7 mm in 0.5-mm increments were printed on white paper true-origin requirement for ratio scales.
using a laser printer. The accuracy of the printed diameter Furthermore, the slope coefficients k were themselves
was verified with Neiko digital calipers. An apparatus con- approximately inversely proportional to the eye-to-camera
sisting of a flat piece of wood (157 × 88 × 36 mm) and an distances, which were Ln = 584, Lm = 610, and Lf =



514 Behav Res (2016) 48:510–527

Top Side Front 
58

410 163
305 305

173
245 245 410

740 110 250
160

495 310
495

163

Z 92 Z 420 Y

X Y X

a
Top Side Front 

58
410 305

173
310 310 163 305

410

835 110
255

310 155
525 525

163

Z 92 Z 420 Y

X Y X

b
Top Side Front 

410 305 58
310 173

310
163 305

410
935 110

310
625 625 255

155

163

Z 92 Z 420 Y

X Y X

c
Target Relative to 
Origin [Tx Ty Tz]

Y
Z X Pupil Origin

[0 0 0]

Camera Relative to 
Origin [Cx Cy Cz]

d
Fig. 3 Experimental layouts and geometric model. Three separate (d) estimates the foreshortening of the pupil area as a function of the
geometric layouts (panels a–c) varied the relative positions of the cosine of the angle θ between the eye-to-camera axis OC and the eye-
camera, artificial eye, and monitor. Each layout is diagrammed from to-target axis OT. The originO of the coordinate system is at the center
three vantage points (top, side, and front). All distances are in mil- of the artificial pupil. The x-axis is horizontal and parallel to the bottom
limeters. Pupil calibration (experiment 1) and pupil foreshortening edge of the screen, growing rightward. The y-axis is vertical, growing
(experiment 2) were measured for each layout. The geometric model upward. The z-axis is perpendicular to the screen, growing inward



Behav Res (2016) 48:510–527 515

the eye-to-camera distance in millimeters, and the re-scaling
parameter α is in radians per arbitrary unit. We estimate
α from the relationship dj /(φijLi) = const across the
11 pupils j and three layouts i. This ratio has a mean of
α = 1.70 × 10−4 rad/AU ≈ 35.1 arcsec/AU in our data set
(SD = 7.20 × 10−7 rad/AU ≈ 0.149 arcsec/AU). With just
one free parameter, (1) accounts for 99.99 % of the variance
of the 33 calibration measurements.

In conclusion, experiment 1 established that EyeLink’s
“arbitrary units” form a ratio scale with coefficient αL
depending on the eye-to-camera distance L for each fixed
layout. Also, the pupil-size data seem to track the visual
angle subtended by the pupil. Consequently, the units can be
converted across layouts according to Eq. 1. These results

Fig. 4 Ratio scale between arbitrary units and physical units. The were obtained with very simple artificial pupils printed on a
pupil-diameter data recorded by EyeLink 1000 in arbitrary units are flat surface. The next experiment investigates the pupil fore-
proportional to the true diameter of the physical pupil, but the coeffi- shortening error caused by the rotation of spherical artificial
cient of proportionality depends on the distance between the camera eyes relative to the camera.
and the artificial eye

698 mm for the three layouts.1 To verify this inverse Experiment 2
proportionality, we calculated products of the form pi =
αkiLi , where i ∈ {n,m, f } and α ≈ 1.70 × 10−4 is When an eye is photographed from an oblique angle, the
a re-scaling parameter determined in the next paragraphs. image of the pupil becomes elliptical and the apparent pupil
If each slope coefficient is inversely proportional to the area decreases (e.g., Jay, 1962; Spring & Stiles, 1948). The
eye-to-camera distance regardless of layout, then pi should resulting pupil foreshortening error (PFE) depends on the
equal unity for any i. In our data, we obtain pn = 0.9999, viewing angle. In a typical eye-tracking setup, the camera
pm = 1.0002, and pf = 0.9998. is stationary relative to the screen but the eye rotates in its

This suggests that the “arbitrary units” are not really units socket, thereby varying the angle between the optical axis of
of length but of visual angle. Apparently, the EyeLink 1000 the camera and the plane of the pupil. We constructed spher-
in centroid mode reports the angular area subtended by ical artificial eyes that could rotate in artificial sockets and
the pupil. A straightforward algorithm for estimating this could be pointed to “fixate” arbitrary points on the screen.
angular area—and probably the algorithm implemented by The purpose of experiment 2 was to map the relationship
the EyeLink software—is to count the “pupil” pixels in the between EyeLink’s pupillometric data and the screen coor-
image of the eye. This interpretation is consistent with the dinates of a grid of fixation points. The results indicated
statement in the User Manual that the pupil area is recorded that the measurements were contaminated by substantial
in “scaled image pixels” (p. 17). Taking the square root PFE, but the magnitude of the error depended systematically
transforms the angular area into a linear visual angle φ, on the cosine of the angle between the eye-to-camera axis
which is approximately 2 proportional to the ratio of the true and the eye-to-stimulus axis. Because the PFE was system-
pupil diameter d and the viewing distance: φ ∝ d/L. atic, it could be corrected very well by a simple geometric

On the basis of this invariant relationship, Eq. 1 provides model.
a straightforward formula for converting “arbitrary units” to
millimeters at any viewing distance: Method

d ≈ αLφ (1) Apparatus
In this equation, the angle φ subtended by the pupil is

reported by the EyeLink 1000 in “arbitrary units,”L denotes All EyeLink 1000 settings were the same as those in experi-
ment 1, with the exception of the pupil threshold parameter.
The latter was lowered from 110 to 60 as the spherical arti-

1In Fig. 3, these are the hypotenuses of the triangle with vertical side
Cy = 310 mm giving the height of the eye relative to the camera and ficial eyes with pupillary wells required a lower threshold
with horizontal side Cz = 495, 525, and 625 mm, respectively. than the flat pupils printed on paper. These settings pro-
2We use the paraxial approximation tanφ ≈ sinφ ≈ φ for small vided a reliable thresholded pupil signal across all measured
angles measured in radians (Atchison and Smith, 2000). conditions.



516 Behav Res (2016) 48:510–527

Artificial eye model Gaze position map and procedure

Three artificial eyes were manufactured from three solid Pupil size measurements were collected with each artifi-
wooden spheres, each 31 mm in diameter. The production cial eye on a rectangular grid of fixation targets for each
of each eye began by drilling a 0.79-mm pilot hole all the experimental layout. For the near and far layouts, we used
way through the center of the wooden ball using a Skil 3320 16 × 12 = 192 targets spaced 64 pixels apart (see Fig. 2e
10” benchtop drill press and DeWalt titanium pilot drill bits. top); for the medium layout, we used 8 × 6 = 48 targets
Then a well was drilled 20 mm deep into each eye to form spaced 128 pixels apart (at screen resolution 1024 × 768).
the artificial pupil (see Fig. 2b, c). The three eyes had well On each trial, we rotated the eye until the pointer beam
diameters of 3.17, 4.76, and 7.14 mm, respectively. The illuminated the appropriate target on the screen. A box
inside of each well was painted black and the rest of the eye shielded the eye tracking camera as a precaution against
was painted white using tempera washable paint. Finally, a accidental exposure to the beam. As in experiment 1, pupil-
pressure-switch laser sight (<5 mW) was collimated within area measurements were recorded for 5 s at 1000 Hz, con-
a 100-mm section of PVC pipe (25 mm in diameter) that was verted to linear units by taking square roots, and averaged
attached to a second 30-mm section of PVC pipe (15 mm across the 5000 samples. This produced one pupil-diameter
in diameter) that was firmly cemented to each artificial eye datum per trial.
using epoxy cement (Fig. 2c). The laser beam originated
approximately 75 mm behind the eyeball and traveled to Geometric model
the screen through the .79-mm hole. The pressure switch
allowed us to (de)activate the laser without disturbing the The results from experiment 1 suggested that the EyeLink
eye orientation in the socket. 1000 (operating in centroid mode) reports the angular area

The artificial eye was kept at a constant position within subtended by the pupil. Based on simple geometric princi-
the headrest using a simple ocular socket mechanism (see ples, (1) accounted extremely well for the pupillometric data
Fig. 1). This mechanism was composed of four pieces of when the distance between the camera and the eye was var-
wood (sides 131 × 13 × 28 mm; top and bottom 13 x 63 ied. Our working hypothesis is that the same principles can
x 38 mm), two steel mending plates (140 × 0.9 × 13 mm), account for the pupillometric data when the angle between
two threaded steel rods (152 mm long), four wing nuts, and the camera axis and the eye axis is varied. In this section,
two rubber grommets. Two holes were drilled through the we develop a geometric model that formalizes this hypoth-
wooden sides for the threaded rods—one 14 mm from the esis and provides explicit formulas for correcting the pupil
top and one 26 mm from the bottom. Two circular recessed foreshortening error.
holes (25 mm in diameter) were drilled 4 mm deep into the The key term in the model is the oblique angle θ
inside of each wooden side piece (centered horizontally and between the eye-to-camera axis and the eye-to-stimulus axis
vertically) to hold the rubber grommets firmly in place. The (Fig. 3d). To simplify the trigonometric calculations, the
ocular socket was then assembled as shown in Fig. 2d. The model assumes the camera is pointed directly at the eye and
threaded rods were placed through each side piece with a thus the eye-to-camera axis coincides with the optical axis
mending plate on each outside edge held in place by the four of the camera. Let us consider first the baseline configura-
wing nuts. The wing nuts could then be tightened down to tion in which the eye too is pointed directly at the camera.
firmly hold the current artificial eye in place and loosened to Then the eye axis coincides with the camera axis and θ = 0.
remove and replace the eye when needed. The elasticity of Let A0 denote the angular area subtended by the pupil as
the rubber grommets allowed us to manipulate the elevation viewed from the camera in the baseline configuration.
and azimuth of the artificial eye within the socket, while at Our goal is to quantify the foreshortening effect, whereby
the same time holding firmly when the desired orientation the apparent angular area A(x, y) diminishes when the eye
was reached. rotates away from the camera to fixate a target with coor-

The standard EyeLink 1000 nine-point calibration and dinates x and y on the screen. The derivation proceeds in
validation procedures were used to quantify the accu- two steps: First, we argue that the foreshortening is multi-
racy of the artificial eye and socket apparatus. The nine- plicative and depends on the oblique angle θ according to
point validation data indicated high gaze-position accu- Eq. 2. Second, we express cosθ as a function of x and y
racy across the display (average mean error M=0.18, using simple vector calculus.
SD=0.04; maximum error M=0.26, SD=0.04 degrees of

A(x, y) = A(θ(x, y)) = A0 cos θ(x, y) (2)
visual angle). The validation data verified that the laser
pointers were collimated accurately and the ocular socket We consider a circular pupil for concreteness, although
kept the artificial eyes in a stable orientation throughout the the mathematical derivation generalizes to arbitrary planar
procedure. shapes. When a circular disk is viewed from an oblique



Behav Res (2016) 48:510–527 517

√
angle, it is projected into an ellipse and its apparent area point (x, y) is cos θ(x, y). For an artificial eye with a con-
decreases (Gagl et al., 2011; Jay, 1962; Mathur et al., 2013; stant pupil, the geometric model predicts that the ratios in
Spring & Stiles, 1948). Importantly, the projected pupil Eq. 4 will be invariant for all points (x, y). This prediction
becomes narrower in the direction of view (the tangential is tested below.
section) but remains unchanged in the perpendicular direc- Two versions of the geometric model were compared:
tion (the sagittal section, (Equation 3.2) in Atchison & a parameter-free model and an optimized model. The
Smith, 2000). This is why the multiplier in Eq. 2 above is parameter-free model simply calculated cos θ according to
cosθ rather than cos2θ . To simplify the analysis, we have Eq. 3 using the physical measurements taken directly from
assumed that the eyeball diameter is negligible relative to each experimental layout (Fig. 3). The relevant measure-
the distance to the camera and, consequently, the rotational ments are the camera lens coordinates (Cx , Cy , Cz) and
center of the eyeball lies approximately in the pupil plane. the coordinates (Sx , Sy , Sz) of the upper left-hand corner
This seems a reasonable assumption for desktop trackers of the screen for the respective layout. The parameter-free
such as the one used in this article, but probably becomes model is an idealization that does not take into account the
very crude for head-mounted trackers. Taking the eyeball error of the layout measurements or the optics of the camera
geometry into account does not change any of the principles lens itself, which can alter the effective geometric layout.
here, but complicates the trigonometry. Another assumption In order to account for these additional sources of error, an
that is implicit in Eq. 2 is that the camera has negligible optimized model was also considered. The optimized model
optical aberrations near the center of its field of view. fit five parameters Cx , Cy , Sx , Sy , and Sz; the Cz parame-

The second step of the derivation is to express cosθ as a ter was fixed to the respective physical layout measurement.
function of the target coordinates x and y. It is convenient The optimized model was fit to the pupillometric data sepa-
to work in a coordinate system with an origin O at the pupil rately for each layout using an unconstrained Nelder–Mead
center and axes as described in the caption of Fig. 3d. The optimization routine that minimized the root mean squared
camera lens is at point C with coordinates Cx , Cy , and Cz, error. The initial values for the iterative parameter search
which are parameters of the physical layout (Fig. 3). The were set to the physical layout measurements.
other parameters of the layout are the coordinates Sx , Sy ,
and Sz of the upper left-hand corner of the screen. Then a
fixation target T with screen coordinates x and y mm has
coordinates Tx = x − Sx , Ty = y − Sy , and Tz = S Results and discussion

z in the
eye-centered system. In this notation, the oblique angle θ is
the angle COT in Fig. 3d and its cosine can be calculated Pupil measurements for the three spherical artificial pupil
via the dot product of the vectors OC and OT: diameters (3.17, 4.76, and 7.14 mm) and three experimen-

tal layouts (near, medium, far) resulted in a total of nine
empirical maps. Figure 5 shows the measured pupil diam-

cos θ(x, y) = √ CxTx + Cy√Ty + CzTz
(3) eter as a function of visual angle for each unique map.

C2 Pupil diameter is expressed on a common scale across all
x + C2

y + C2
z T 2

x + T 2
y + T 2

z
nine maps as the deviation from the map-specific geometric

For ease of comparison to the physical pupil diameter of mean. Despite the fixed pupil diameter of the artificial eye
the artificial eye models, the angular area A√is converted to models, the average range in deviation from the geometric
arc length φ using the relationship φ = 2 A/π . Taking mean for the near, medium, and far layouts were 14.4, 10.1,
square roots of both sides in Eq. 2 and rearranging leads to and 8.4 %, respectively (see Table 1). Pupil diameter sys-
Eq. 4, in which φ0 denotes the angle subtended by the pupil tematically decreased as the eye rotated further away from
diameter in the baseline configuration and φ(x, y) denotes the camera lens—producing larger variability in pupil diam-
the apparent angle when the artificial eye points at a target eter as viewing distance decreased and visual angles to the
with screen coordinates x and y. target grid points on screen increased. These results estab-

lish the existence of large PFE as a result of changes in gaze
φ0 = √ φ(x, y) = const for all x, y. (4) position for each measured combination of pupil diameter

cos θ(x, y) and experimental layout.
The true pupil diameter d can be determined by pointing PFE was invariant across the three different artificial eye
the artificial eye directly at the camera (or asking the human diameters within a given experimental layout. Principal-
participant to look at the camera), obtaining EyeLink mea- component analysis (PCA, Everitt & Dunn, 2001) was
surements of the subtended angle φ0 in “arbitrary units,” and performed to estimate the intra-map similarity across the
converting them to millimeters according to Eq. 1. The pupil three pupil diameter maps within the near, medium, and far
foreshortening error PFE(x, y) = φ(x, y)/φ0 at fixation layouts. The first principal component accounted for nearly



518 Behav Res (2016) 48:510–527

all the variance across the three pupil diameter maps (near which is well within the uncertainty of measurement of a
99.8 %; medium 98.9 %; far 98.9 %). The invariance across stationary pupil across trials. This indicates that the PFE can
changes in pupil diameter is a critical finding as it estab- be eliminated entirely for practical purposes, at least for the
lishes that the PFE surface is not affected by the changes in artificial eyes considered here, provided enough calibration
pupil diameter that occur when measuring a dynamic, bio- measurements are available to constrain the parameters of
logical eye. Due to the invariance across pupil diameter the the geometric model.
maps were collapsed by taking the geometric mean across It is instructive to compare the optimized parameter val-
the three different pupil diameter maps and normalizing rel- ues in Table 3 to their physical counterparts. The distance
ative to the geometric mean within a given experimental Sz from the eye to the screen was constrained well by the
layout, resulting in one aggregate map for each experimen- data but there were surprisingly large deviations in the other
tal layout. The remaining results focus on modeling and four parameters. The optimized values of Cx and Sx would
correcting PFE in the aggregate layout maps. be veridical if the artificial eye were located ≈ 60 mm

We evaluated the validity of the geometric model in closer to the left edge of the screen (on average across
Eqs. 3 and 4, as well as its ability to correct the PFE and the three layouts). The optimized values of Cy and Sy

recover the true pupil diameter φ0. As a result of the normal- would be veridical if the eye were located ≈ 90 mm lower.
ization and aggregation described above, φ0 = 1 = const The overall effect was to produce greater variation in the
for each map and all residual variation is due to foreshorten- oblique angle θ in Eq. 3 across each map, which in turn
ing error. The parameter-free model reduced the root mean produced stronger correction multipliers. The within-map
squared error of the aggregated measurements by an aver- RMSE of the optimized model multipliers were larger than
age of 82.5 %. The correction is applied according to Eq. 4 their parameter-free counterparts and better matched the
by dividing the measured pupil diameter at each grid target RMSE of the calibration data (Table 2). We speculate that
location by the square root of the cosine of its corresponding the optimized parameters captured the magnification effect
angle. Figure 6 shows the empirical aggregate layout maps of the optics inside the eye-tracking camera. Equations 3
before correction, the geometric model corrective multipli- and 4 essentially model a pinhole camera, whereas the real
ers, and the corrected maps. There was a strong correspon- device includes lenses on the optical path. This is a topic
dence between the three aggregate maps and their respective for further research. The theoretical significance of the opti-
multipliers (near R2=.99, medium R2=.98, and far R2=.98). mized model fit is that the PFE surfaces were smooth and
The parameter-free geometric correction greatly reduced the could be parameterized with a few numbers that could be
RMSE to 18.7, 15.8, and 18.2 % of its original value for the estimated from calibration data.
near, medium, and far layout. Table 2 shows the descriptive Finally, it is useful to evaluate whether binocular pupil
statistics before and after applying the parameter-free recording can alter these conclusions. In the absence of
correction. binocular calibration data, we rely on predictions from the

While the parameter-free geometric model reduced the parameter-free geometric model to estimate the extent to
RMSE in pupil diameter substantially, it left a small amount which the PFE for the two eyes would cancel out in a
of residual error that was systematic and thus could be binocular average. All calibration measurements and model
corrected further. Specifically, close inspection of the third calculations thus far used monocular recording from the
column in Fig. 6 suggests that the correction generated by left-eye position. To simulate the corresponding right-eye
the parameter-free model was not strong enough to fully position, it is sufficient to subtract the interpupillary dis-
correct the “tilt” of the uncorrected maps in the first col- tance from the Cx and Sx model parameters (Fig. 3). A
umn. Thus, even after the parameter-free correction, the recent survey of 3976 adults (Dodgson, 2004) reports a
pupil diameter remained slightly underestimated for fixa- median interpupillary distance of 63 mm. We thus assume
tion targets near the left edge of the screen and slightly Cx,right = Cx,lef t − 63 and Sx,right = Sx,lef t − 63.
overestimated for targets near the right edge. Table 4 summarizes the predicted PFE for the left and right

To evaluate the extent to which this residual error can monocular cases and their binocular average. Binocular
be corrected still further, the parameters of the geometric pupil recording would have decreased the pupil foreshort-
model were optimized to fit each aggregate map. Figure 7 ening RMSE only by about 1 % relative to the monocular
shows the three empirical maps, the optimized corrective recording from the left eye. It should be noted that if we had
multipliers, and the optimized correction maps. The RMSE used the right eye position, binocular averaging would have
was reduced to 2.0, 2.5, and 3.2 % of its original value for increased the RMSE of the PFE by 1 %. This is because
the near, medium, and far layout, respectively, using the there is less PFE for whichever eye is closer to the camera,
optimized parameters listed in Table 3. As can be seen in all else being equal. In sum, the geometric model pre-
Table 2, the deviation from the geometric mean was less dicts that binocular data acquisition would have a negligible
than 0.1 % across the three optimized correction maps, impact on the PFE.



Behav Res (2016) 48:510–527 519

Fig. 5 Deviation in pupil diameter by pupil size and layout. Each despite the fixed pupil of each artificial eye. Deviations in measured
panel shows the deviation in measured pupil diameter from the geo- pupil size grew as layout distance became shorter and visual angles
metric mean for a unique combination of pupil size (3.17, 4.76, and increased. Deviations were relatively invariant across pupil diameter
7.14 mm) and experimental layout (near, medium, far). Large devi- within a given experimental layout. Gaze positions are in degrees of
ations in measured pupil diameter were observed across each map visual angle

Table 1 Descriptive statistics
of the deviation in pupil RMSE Min Max
diameter from the geometric
mean for the nine empirical Near layout
maps (3 experimental layouts x Pupil diameter 3.17 0.034 0.916 1.058
3 pupil sizes). RMSE root mean Pupil diameter 4.76 0.032 0.918 1.053
squared error

Pupil diameter 7.14 0.038 0.905 1.060
Medium layout
Pupil diameter 3.17 0.025 0.940 1.044
Pupil diameter 4.76 0.026 0.938 1.043
Pupil diameter 7.14 0.024 0.944 1.037

Far layout
Pupil diameter 3.17 0.022 0.945 1.039
Pupil diameter 4.76 0.019 0.951 1.030
Pupil diameter 7.14 0.019 0.951 1.030



520 Behav Res (2016) 48:510–527

Fig. 6 Aggregate empirical maps, parameter-free geometric model column shows the corrected data produced by dividing the aggregate
multipliers, and parameter-free correction. Each panel shows the devi- empirical data by its respective corrective multiplier. The parameter-
ation in pupil diameter from the geometric mean. The first column of free geometric correction reduced the within-map RMSE to 18.7, 15.8,
panels shows the aggregate empirical maps collapsed across pupil size and 18.2 % of its original value for the near, medium, and far layout.
for each experimental layout. The second column shows the corrective Gaze positions are in degrees of visual angle
multipliers produced by the parameter-free geometric model. The third

General discussion data are expressed not in units of linear length but of the
angle subtended by the pupil as viewed from the camera.

This article reports two experiments that investigate and These units can be converted across layouts according to
calibrate the pupillometric measurements of a desktop Eq. 1.
EyeLink 1000 eye tracker (SR Research, 2010) using arti- Experiment 2 mapped the pupil foreshortening error
ficial eyes with known pupil diameter. Experiment 1 estab- (PFE) when the artificial eye rotated in its socket to fixate
lished that EyeLink’s “arbitrary units” form a ratio scale a grid of target points on the screen. Data were collected
with a true zero. This result justifies the common practice across three experimental layouts with spherical artificial
in the field to express relative pupil diameters as dimen- eyes with three pupil diameters. The nine resulting maps
sionless ratios—e.g., percent change from baseline. When showed large PFE that increased as a monotonic function of
absolute measurements are needed, they can be calculated the oblique angle between the eye-to-camera axis and the
for a given arrangement of the eye tracking camera, head- eye-to-target axis. The results supported three major conclu-
rest, and monitor: The “arbitrary units” are proportional to sions as follows: First, the relative PFE maps were highly
millimeters but the coefficient of proportionality depends on replicable across different pupil sizes, as evident in the high
the eye-to-camera distance (Eq. 1). Furthermore, the results (R2 > .98) correlation among maps collected with dif-
of experiment 1 strongly suggest that EyeLink’s pupil-size ferent artificial eyes in a given experimental layout. This



Behav Res (2016) 48:510–527 521

Table 2 Descriptive statistics
of the deviation in pupil RMSE Min Max
diameter from the geometric
mean for the aggregate layout Near layout
maps, the corrective multipliers Aggregate data 0.034 0.913 1.056
generated by the parameter-free Model multipliers (PF) 0.029 0.925 1.046
(PF) model and the model with
optimized (opt.) parameters, Corrected data (PF) 0.006 0.987 1.014
and the corrected data Model multipliers (opt.) 0.034 0.912 1.056
(parameter-free and optimized). Corrected data (opt.) <0.001 0.998 1.002
RMSE root mean squared error

Medium layout
Aggregate data 0.025 0.941 1.040
Model multipliers (PF) 0.025 0.944 1.038
Corrected data (PF) 0.004 0.993 1.009
Model multipliers (opt.) 0.025 0.941 1.039
Corrected data (opt.) <0.001 0.998 1.002

Far layout
Aggregate data 0.020 0.949 1.033
Model multipliers (PF) 0.019 0.952 1.030
Corrected data (PF) 0.004 0.992 1.008
Model multipliers (opt.) 0.020 0.947 1.033
Corrected data (opt.) <0.001 0.998 1.002

suggests that the relative PFE varies only as a function of value. This suggests that the PFE is largely due to geo-
the orientation of the eye with respect to the camera but metric foreshortening of the type captured by Eqs. 3 and
does not depend on the pupil diameter. In other words, the 4. Note that the foreshortening follows a multiplicative law
eye-tracking hardware and software operate linearly with (Eq. 4). The third major conclusion of experiment 2 was that
respect to pupil diameter. Second, a parameter-free geomet- model-based optimization could reduce the error to levels
ric model reduced the error to <18 % of its uncorrected comparable to the measurement precision for a stationary

Table 3 Comparison of
geometric models (parameter- Model Parameters Near Medium Far
free physical measurements vs.
optimized parameters) for near, Camera-to-pupil, Cx

medium, and far layouts. All Physical 92 92 92
values are distances in mm. Cx, Optimized 130 165 183
Cy, Cz are the coordinates of
the camera; Sx, Sy, Sz are the Camera-to-pupil, Cy

coordinates of the upper left- Physical -310 -310 -310
hand corner of the screen (cf. Optimized -215 -239 -230
Fig. 3). An asterisk indicates

Camera-to-pupil, C
the parameter was fixed to the z

physical layout measurement Physical 495 525 625
Optimized 495∗ 525∗ 625∗

Screen-NW-corner, Sx

Physical -163 -163 -163
Optimized -142 -87 -76

Screen-NW-corner, Sy

Physical 58 58 58
Optimized 206 140 156

Screen-NW-corner, Sz

Physical 740 835 935
Optimized 736 851 937



522 Behav Res (2016) 48:510–527

Fig. 7 Aggregate empirical maps, optimized geometric model multi- The third column shows the corrected data produced by dividing the
pliers, and optimized correction. Each panel shows the deviation in aggregate empirical data by its respective corrective multiplier. The
pupil diameter from the geometric mean. The first column of panels optimized geometric correction virtually eliminated pupil foreshorten-
shows the aggregate empirical maps collapsed across pupil size for ing error, reducing the within-map RMSE to 2.0, 2.5, and 3.2 % of its
each experimental layout. The second column shows the corrective original value for the near, medium, and far layout. Gaze positions are
multipliers produced the geometric model with optimized parameters. in degrees of visual angle

pupil. In other words, the foreshortening error induced by revealed when one compares data from different experimen-
the rotation of the eye could be eliminated almost entirely. tal layouts as was done here. Second, the foreshortening
Such accurate correction is possible because the PFE sur- law is multiplicative in nature (Eq. 2). Therefore, division
face is smooth and can be described with five parameters rather than subtraction is the appropriate corrective opera-
that can be estimated from calibration data, at least for tion (Eq. 4), and the geometric rather than arithmetic mean
artificial eyes. is the appropriate measure of central tendency. Additive

On the basis of these empirical results, we formulate operations are justified only approximately for normalized
three theoretical principles. They seem obvious in hindsight data on the basis of the approximation log(1 + x) ≈ x
but were neglected in earlier PFE research (e.g., Brisson for small x. The third principle is the cosine relation-
et al., 2013; Gagl et al., 2011). First, PFE analysis must be ship embedded in the geometric model (Eqs. 3 and 4) that
based on the three-way relationship between the eye, the grounds the PFE in the physics of the data acquisition pro-
fixation point on the screen, and the eye-tracking camera. cess. With parameters that could have been fixed before
Leaving the camera out of the equation leads to an ill-posed any calibration data were collected, the geometric model
problem that does not permit a general solution. This is accounted for over 82 % of the variance in these data. In



Behav Res (2016) 48:510–527 523

Table 4 Predicted deviation in
pupil diameter from the RMSE Min Max
geometric mean for each
experimental layout for Near layout
monocular (either left or right Left eye (PF) 0.0294 0.9247 1.0463
eye only) and binocular Right eye (PF) 0.0289 0.9305 1.0453
recording. The corrective
multipliers were generated by Binocular (PF) 0.0291 0.9276 1.0457
the parameter-free (PF) model Medium layout
assuming an interpupillary Left eye (PF) 0.0251 0.9436 1.0381
distance of 63 mm. RMSE root

Right eye (PF) 0.0246 0.9484 1.0368
mean squared error

Binocular (PF) 0.0248 0.9460 1.0374
Far layout
Left eye (PF) 0.0190 0.9519 1.0303
Right eye (PF) 0.0186 0.9554 1.0294
Binocular (PF) 0.0188 0.9536 1.0298

agreement with the first two principles, the model specifies One plausible explanation for the different PFE function
a three-way multiplicative relationship between the coor- obtained by Gagl et al. (2011) is that they used a tower
dinates of the eye, fixation target, and camera. The same mount EyeLink 1000, whereas we used a desktop Eye-
principles are widely used in computer vision (Forsyth & Link 1000. Unlike the latter, the tower mount system does
Ponce, 2011). not record a direct image of the eye. Instead, the camera is

mounted above the observer and records a reflected image
Comparison with previous research of the eye from an angled infrared hot mirror (SR Research,

2010, p. 7). 3 The infrared mirror reflects infrared light but
Our PFE measurements are broadly consistent with pub- is transparent to visible light, allowing participants to per-
lished results that measured a single horizontal scan line form tasks on screen that require them to reach in front
using an artificial eye (Gagl et al., 2011), and a circu- of them without occluding the camera’s image of the eye.
lar object pursuit task performed by human participants The data of Gagl et al. (2011) suggest that the addition of
(Brisson et al., 2013). During the object pursuit task of this angled infrared mirror alters the PFE surface in ways
Brisson et al. (2013), the pupil diameter systematically that are not well captured by our simple geometric model.
decreased as the object receded vertically and horizontally The presence of an adjustable mirror violates the model’s
from the camera. The geometric model is consistent with assumption that the eye-to-camera axis coincides with the
this pattern of results. Gagl et al. (2011) reported a piece- optical axis of the camera. The unknown optical properties
wise linear and quadratic function in their artificial eye of the mirror introduce additional complications. The geo-
measurements. Whereas our PFE maps exhibited a smooth metric model is best suited to eye-tracking systems in which
quadratic trend, (Gagl et al., 2011) reported that their artifi- the camera records a direct image of the eye. Researchers
cial eye measurements showed a sharp linear increase near that use eye-tracking systems with intermediary mirrors are
the left edge of the screen that then decreased quadratically advised to map the PFE surface of their mirrored system
as the gaze position moved rightward across the display. The explicitly using an artificial eye. The model probably can
quadratic pattern is consistent with the geometric model and be modified to account for the mirror by “unfolding” the
predicts that the camera lens was on the left side of their optical path, but this should be tested on explicit calibration
EyeLink 1000 since the right eye position was measured. data.
We applied our Eqs. 3 and 4 to a reconstruction of the layout
of Gagl et al. (2011) based on their Method description and Recommendations
the EyeLink specifications (SR Research, 2010, p. 7). The
geometric model reproduced the qualitative nonmonotonic One key objective of the current article is to provide the
shape of their data, although our reconstruction located the research community with a technique to correct for PFE
peak close to the center of the display rather than at the
left edge. The abrupt drop in recorded pupil size near the
left edge is anomalous and does not fit the geometric-model 3The typical camera-to-eye distance in the tower mount system is 380
predictions. mm, which is comparable to our near layout (425 mm).



524 Behav Res (2016) 48:510–527

in a manner that does not sacrifice experimental flexibil- On the other hand, it increases the error in estimating the
ity. The present methodology allows for unprecedentedly fixation location. This creates a trade-off between pupil-
accurate PFE correction while preserving the freedom to lometric accuracy and eye-tracking accuracy because the
study tasks such as reading or visual search that involve free latter deteriorates when the screen is far from the partic-
viewing of the display. Both the parameter-free and the opti- ipant. An elegant solution is possible when the research
mized geometric model reduced PFE enough that cognitive question requires that the stimuli appear at multiple loca-
effects >2 % should be detectable. One key advantage of tions but do not constrain the spatial relationship among
the parameter-free geometric correction is that it does not these locations. Our recommendation for such situations is
require the researcher to collect any artificial-eye measure- to spread the stimuli along an arc that maintains a constant
ments. One weakness is that it does not account for the oblique angle θ with the camera-to-eye axis. The coordi-
effects of the camera optics that alter the effective geomet- nates of these locations can be calculated from Eq. 3 under
ric layout. The optimized model virtually eliminates PFE the constraint cosθ(x, y) = k = const for a suitably cho-
by accounting for all sources of error, but requires calibra- sen k. The geometric model predicts identical PFE for all
tion data to constrain the model parameters. For researchers points along an arc of this type. Analogous circular con-
that wish to use the optimized geometric model and use tours of equal PFE are prominent in the calibration data
the EyeLink 1000, Table 3 lists the best-fitting parameters (Fig. 5). Another general recommendation is to place the
for the three layouts in our study. For researchers that use camera as close to the eye-to-stimulus line of sight as the
other eye trackers and/or layouts, the detailed description of camera dimensions allow. Such placement makes cosθ as
our artificial eye and socket apparatus can help them cali- close to 1 as possible. Recording monocularly from the eye
brate their respective systems. Desktop or monitor mounted closest to the camera is predicted to provide modest (≈2 %)
trackers (e.g., Tobii T60 and T120, Tobii Technology, 2011; reductions in PFE relative to the more distant eye.
Tobii X60 and X120, Tobii Technology, 2008; SMI RED To facilitate effective PFE correction, the following best
series, SensoMotoric Instruments, 2009) that record direct practices are recommended for the data collection phase:
images of the eye are more likely to correspond well to the First, we strongly discourage the participation of observers
geometric model. Eye-tracking systems that do not directly whose eyelids partially occlude their pupils as well as partic-
image the eye but rely on reflected infrared images of the ipants that wear glasses or contact lenses, as these can each
eye (e.g., tower mount EyeLink 1000 and SMI iView X affect the PFE in unpredictable ways. For desktop or screen-
Hi-Speed, SensoMotoric Instruments, 2009) are more likely mounted eye-tracking systems, a chin-and-forehead rest
to deviate from the geometric model due to the additional should be used to keep the eye in a constant position. When
optics of the intermediary infrared mirror (e.g., Gagl et al. possible, the rest height should be kept constant across par-
(2011)). The geometric relationships formalized in Eqs. 2–4 ticipants. In our lab, we adjust the height of the chair to
apply to all camera-based trackers, but the proprietary post- accommodate participants of different stature, leaving the
processing software of specific manufacturers may render chin-and-forehead rest fixed at all times. Thus, a common
these equations unsuitable for PFE correction purposes. 4 geometric model can be applied to the entire data set during
Finally, some of the simplifying assumptions used in the post-processing. If the chin-and-forehead rest height must
derivation of these equations will have to be re-examined for be adjusted on an individual basis, height measurements
head-mounted trackers that put the camera much closer to should be logged for each participant and an individualized
the eye and thus the diameter of the eyeball is no longer neg- geometric model should be applied. Finally, regardless of
ligible. These assumptions were made explicit in the Model whether the proprietary image-based eye-tracking software
section above. uses pixel counting or ellipse fitting algorithms to estimate

The magnitude of the pupil foreshortening error depends pupil size, the pupil threshold parameter should be set at the
greatly on the geometric configuration of the screen and beginning of the experimental session and not altered during
camera relative to the eye (Fig. 3). The following general the course of the session. Changing the threshold parameter
recommendations can be used to inform the choice of lay- in mid-session will result in large changes in recorded pupil
out. Putting the display far from the participant tends to size.
reduce the range of oblique angles θ and hence the PFE. Last but not least, we offer guidelines for evaluating pub-

lished pupillometric data for possible PFE artifacts. The
4We urge the eye-tracking manufacturers to provide clearer descrip- main danger is the possibility for systematic pupil fore-
tions of the broad type of algorithms used to estimate pupil size (e.g., shortening errors. Nonsystematic errors merely increase
pixel counting vs. ellipse fitting) and the scale type (e.g., interval vs. the measurement noise and cannot alter the substantive
ratio) of the resulting measurements. We understand the motivation to
keep the detailed algorithms proprietary, but the manual should pro- conclusions of an experiment, even though they are still
vide enough information to allow the user community to interpret the undesirable because they can cause type II errors. System-
data. atic PFE, however, is very dangerous because it can cause



Behav Res (2016) 48:510–527 525

type I errors and cannot be mitigated through averaging. For the example above shows that they are not as foolproof,
example, a systematic PFE confound would arise if all emo- as it is commonly assumed. The stimuli in many experi-
tional stimuli were presented at one location, whereas all ments are presented near the center of the screen where the
neutral stimuli at another. Such obvious cases of bad experi- PFE is smaller compared to the far periphery. Even imper-
mental design would be screened out during the peer-review fect measurements can provide a wealth of evidence that
process, but there are subtler cases that are very easy to is relevant and replicable. Still, at best the PFE erodes the
miss. Consider the following example based on an exper- statistical power and complicates the interpretation, and at
iment from our lab. We presented all stimuli at a single worst it can introduce spurious regularities and/or change
location and analyzed only trials in which the participants the magnitude of the real effects. The main purpose of this
maintained fixation on the stimulus, as instructed. Follow- article is not to throw a dark shadow on the pupillometry
ing the widespread practice in the literature, we believed literature, but to provide a method for correcting the PFE
that we had thereby eliminated any possibility for PFE. We and encourage other researchers to adopt it in their future
were wrong, due to a subtle interaction between certain work.
features of our experimental procedure and certain reg-
ularities in the participants’ behavior. It is instructive to Limitations
consider this case in some detail. Our procedure included a
free viewing period from the stimulus offset until the end One potential limitation of our study is that we do not
of the trial and through the inter-trial interval. Such free explicitly model the refracting effect of the cornea—the
viewing periods are very common in eye-tracking research transparent curved layers in front of the iris (Atchison &
because it is very uncomfortable to maintain constant fix- Smith, 2000). The image recorded by video-based eye track-
ation for an extended period of time. Also, mindful of the ers measures the entrance pupil—the aperture stop of the
time lag between the stimulus and the task-evoked pupillary iris as viewed through the refractive surfaces of the cornea.
response (Partala and Surakka, 2003; Murphy et al., 2011; The refractive power of the cornea is considerable; it is
Hayes & Petrov, 2015), we analyzed the pupillary data approximately two times stronger than that of the lens
from the entire trial, including the free period. This is also of a relaxed eye (Atchison & Smith, 2000). The absolute
standard practice, but it allowed systematic PFE to creep magnification provided by the cornea does not affect the rel-
in. The problem was that the participants’ pattern of free ative PFE. Rather, our concern is about corneal anisotropy.
viewing depended systematically on the preceding stimu- If different segments of the cornea have different refrac-
lus. After an easy stimulus, their eyes roamed the screen tive indices, the pupil diameter can appear to change when
more or less at random. After a difficult stimulus, however, the eye rotates, turning different segments towards the cam-
the participants often checked the bonus score that was dis- era. There are empirical data that bear on this issue (e.g.,
played at the top of the screen at all times. Apparently, they Spring & Stiles, 1948; Jay, 1962; Jennings & Charman,
were less confident in their responses on difficult trials and 1978). These studies photograph human eyes from a range
tended to look at the accuracy-contingent score to reassure of oblique angles, fit ellipses to the pupil images, and esti-
themselves. This behavior produced a preponderance of fix- mate the foreshortening coefficient as a function of the
ations near the top of the screen on difficult trials, which angle. A recent article (Mathur et al., 2013) re-analyzed the
in turn introduced a systematic PFE masquerading as a data from six published studies plus sophisticated new mea-
cognitive-load effect in (apparent) pupil diameter a few hun- surements. The overall conclusion was that, “Off-axis pupil
dreds of milliseconds after the stimulus offset. This example shape is well described by a cosine function that is both
illustrates that systematic PFE confounds can arise through decentered by a few degrees [towards the temporal side] and
subtle interactions among seemingly unrelated features of flatter by about 12 % than the cosine of the viewing angle”.
the experimental setup. Free viewing periods are particu- (Mathur et al., 2013, p. 7).
larly vulnerable to PFE artifacts because the participants can The empirical foreshortening function of Mathur et al.
introduce unexpected regularities through their patterns of (2013) can easily be incorporated into our geometric model.
free looking. Therefore, presenting the stimuli at a constant To that end, we substitute their Eq. 9 for the term cosθ in
location (or at counterbalanced locations) is guaranteed to our Eq. 4, yielding:
protect against PFE only if all subsequent pupillometric

φ0 = √ φ(x, y)
analyses are based entirely on data collected while fixation ( ) , (5)
was maintained within the controlled location(s). 0.992 cos θ(x,y)+5.3

1.121
Note that we do not claim that all published pupillomet-

ric research is infested with PFE artifacts and is thereby where θ(x, y) is determined from Eq. 3, in degrees. Note
useless. The constant-fixation and counterbalancing meth- that the data of Mathur et al. (2013), as well as the earlier
ods are widely used and do mitigate the PFE, although data re-analyzed therein (Spring and Stiles, 1948, etc.Jay,



526 Behav Res (2016) 48:510–527

1962; Jennings & Charman, 1978), were all collected along Brisson, J., Mainville, M., Mailloux, D., Beaulieu, C., Serres, J., &
the horizontal equatorial line. One area for future research Sirois, S. (2013). Pupil diameter measurement errors as a func-

would be to systematically map the apparent pupil fore- tion of gaze direction in corneal reflection eyetrackers. Behavior
Research Methods, 45, 1322–1331.

shortening across the entire visual field in participants Dodgson, N.A. (2004). Variation and extrema of human interpupillary
with dilated eyes, and compare the human eye data to our distance. Stereoscopic Displays and Virtual Reality Systems XI,
artificial eye data to quantify the effect of corneal refraction. 5291, 36–46.

Einhäuser, W., Koch, C., & Carter, O.L. (2010). Pupil dilation betrays
the timing of decisions. Frontiers in Human Neuroscience, 4(18),

Conclusions 1–9.
Everitt, B.S., & Dunn, G. (2001). Applied multivariate analysis. New

Pupil foreshortening error is a potentially large confound York: Oxford University Press.

that should be taken into account prior to interpreting Forsyth, D.A., & Ponce, J. (2011). Computer vision: A modern
approach, 2nd. Upper Saddle River: NJ: Prentice Hall.

pupillary data from image-based eye trackers. We intro- Gagl, B., Hawelka, S., & Hutzler, F. (2011). Systematic influence of
duced an artificial eye and socket model and systematically gaze position on pupil size measurement: analysis and correction.
mapped the PFE across the horizontal and vertical visual Behavior Research Methods, 43(4), 1171–1181.
field in three separate experimental layouts. In light of previ- Hayes, T.R., & Petrov, A.A. (2015). Learning is in the eye of the

beholder: phasic pupil diameter decreases during perceptual learn-
ous cognitive pupillometric research, our data indicate that ing. (Manuscript submitted for publication).
the PFE can be larger than many cognitive pupillometric Hess, E.H., & Polt, J.M. (1964). Pupil size in relation to mental activity
effects. The PFE is not currently corrected by popular com- during simple problem-solving. Science, 143(3611), 1190–1192.
mercial image-based eye-tracking systems. We formulated Holmqvist, K., Nyström, M., Andersson, R., Dewhurst, R., Jarodzka,

a simple parameter-free geometrical model that reduced the H., & Weijer, J.v.an.de. (2011). Eye tracking: A comprehensive
guide to methods and measures. Oxford: Oxford University Press.

deviation of the PFE by 82.5 % and an optimized model that Jay, B.S. (1962). The effective pupillary area at varying perimetric
reduced it by 97.5 %. Thus, very accurate PFE correction is angles. Vision Research, 1, 418–424.
possible and the corrected pupillometric data have the preci- Jennings, J.A., & Charman, W.N. (1978). Optical image quality in the
sion necessary to measure typical cognitive effects without peripheral retina. American Journal of Optometry and Physiolog-

ical Optics, 55(8), 582–590.
limiting the types of tasks that can be studied by researchers. Just, M.A., & Carpenter, P.A. (1993). The intensity dimension of

thought: pupillometric indices of sentence processing. Canadian
Journal of Experimental Psychology, 47(2), 310–339.

Kahneman, D. (1973). Attention and effort. Englewood Cliffs, NJ:
Prentice Hall.

Acknowledgments This research was supported by the National Klingner, J., Tversky, B., & Hanrahan, P. (2011). Effects of visual and
Institutes of Health National Eye Institute Grant R21 EY022745. verbal presentation on cognitive load in vigilance, memory and

arithmetic tasks. Psychophysiology, 48(3), 323–332.
Koss, M. (1986). Pupillary dilation as an index of central nervous

system α2-adrenoceptor activation. Journal of Pharmacological
References Methods, 15(1), 1–19.

Kuchinke, L., Võ, M.L., Hofmann, M., & Jacobs, A.M. (2007). Pupil-
Agresti, A. (1984). Analysis of ordinal categorical data. New York: lary responses during lexical decisions vary with word frequency

John Wiley and Sons. but not emotional valence. International Journal of Psychophysi-
Alnaes, D., Sneve, M.H., Espeseth, T., Endestad, T., Pavert, S.H.P. ology, 65(132–140).

van de, & Laeng, B. (2014). Pupil size signals mental effort Loewenfeld, I. (1993). The pupil: anatomy, physiology and clinical
deployed during multiple object tracking and predicts brain activ- applications. Detroit: MI: Wayne State University Press.
ity in the dorsal attention network and locus coeruleus. Journal of Mathur, A., Gehrmann, J., & Atchison, D.A. (2013). Pupil shape as
Vision, 14(4), 1–20. viewed along the horizontal visual field. Journal of Vision, 13(6),

Aston-Jones, G., & Cohen, J. (2005). An integrative theory of locus 1–8.
Coeruleus-norepinephrine function: adaptive gain and optimal Miller, N.R., & Newman, N.J. (2005). Walsh & Hoyt’s clinical neuro-
performance. Annual Review of Neuroscience, 28, 403–450. ophthalmology: Volume one (6th ed.)Philadelphia, PA: Lippincott

Atchison, D., & Smith, G. (2000). Optics of the human eye. Oxford, Williams & Wilkins.
UK: Butterworth-Heinemann. Murphy, P.R., O’Connell, R.G., O’Sullivan, M., Robertson, I.H., &

Beatty, J. (1982). Phasic not tonic pupillary responses vary with audi- Balsters, J.H. (2014). Pupil diameter covaries with bold activity in
tory vigilance performance. Psychophysiology, 19(2), 167–172. human locus coeruleus. Human Brain Mapping, Advance online

Beatty, J. (1982b). Task-evoked pupillary responses, processing load, publication. doi:10.1002/hbm.22466
and the structure of processing resources. Psychological Bulletin, Murphy, P.R., Robertson, I.H., Balsters, J.H., & O’Connell, R.G.
91(2), 276–292. (2011). Pupillometry and P3 index of locus coeruleus nora-

Beatty, J., & Kahneman, D. (1966). Pupillary changes in two memory drenergic arousal function in humans. Psychophysiology, 48(11),
tasks. Psychonomic Science, 5, 371–372. 1531–1542.

Beatty, J., & Lucero-Wagoner, B. (2000). The Pupillary System. Nassar, M.R., Rumsey, K.M., Wilson, R.C., Parikh, K., Heasly, B.,
Cacioppo, J.T., Tassinary, L.G., & Berntson, G.G. (Eds.) Hand- & Gold, J.I. (2012). Rational regulation of learning dynamics by
book of Psychophysiology(2nd ed, (pp. 142–162). USA: Cam- pupil-linked arousal systems. Nature Neuroscience, 15(7), 1040–
bridge University Press. 1046.



Behav Res (2016) 48:510–527 527

Partala, T., & Surakka, V. (2003). Pupil size variation as an indi- Spring, K.H., & Stiles, W.S. (1948). Apparent shape and size of the
cation of affective processing. International Journal of Human- pupil viewed obliquely. British Journal of Ophthalmology, 32,
Computer Studies, 59, 185–198. 347–354.

Preuschoff, K., Marius, B., & Einhäuser, W. (2011). Pupil SR Research (2010). Eyelink 1000 users manual, version 1.5.2.
dilation signals surprise: evidence for noradrenaline’s role Mississauga, ON : SR Research Ltd.
in decision-making. Frontiers in Neuroscience, 5(115), Tobii Technology (2008). Tobii X60 & X120 User Manual, Revision
1–12. 3. Danderyd, SE : Tobii Technology AB.

Raisig, S., Welke, T., Hagendorf, H., & Meer, E.v.an.der. (2010). I Tobii Technology (2011). Tobii T60 & T120 User Manual, Revision 4.
spy with my little eye: detection of temporal violations in event Danderyd, SE : Tobii Technology AB.
sequences and the pupillary response. International Journal of Tobii Technology AB (2010). Tobii eye tracking: an introduction
Psychophysiology, 76, 1–8. to eye tracking and Tobii eye trackers. (white paper). Retrieved

Samuels, E.R., & Szabadi, E. (2008). Functional neuroanatomy from http://www.tobii.com/eye-tracking-research/global/library/
of the noradrenergic locus coeruleus: its roles in the reg- white-papers/tobii-eye-tracking-white-paper/.
ulation of arousal and autonomic function Part I: Princi- Torgerson, W.S. (1958). Theory and Methods of Scaling. New York:
ples of functional organization. Current Neuropharmacology, 6 Wiley.
(3), 235–253. Võ, M.L., Jacobs, A.M., Kuchinke, L., Hofmann, M., Conrad, M.,

SensoMotoric Instruments (2009). iView X User’s Manual, & Schacht, A. (2008). The coupling of emotion and cognition in
Version 2.4. Boston, MA : Senso Motoric Instruments the eye: introducing the pupil old/new effect. Psychophysiology,
GmbH. 45(1), 130–140.