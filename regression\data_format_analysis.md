# 眼动数据格式分析报告

## 数据源概述

### 数据文件结构
- **EDF文件**: EyeLink原始数据文件
- **评分数据**: JSON格式的兴趣评分数据
- **预处理缓存**: data_cache目录中的处理后数据

### 数据类型
1. **连续阅读实验数据** (continuous_reading)
   - 包含句子组和对应的兴趣评分
   - 评分范围: 1-5 (兴趣程度)
   - 数据量: 26个实验文件

2. **其他实验数据** (circular_motion, reading_motion等)
   - 眼动轨迹数据
   - 暂无评分信息

## 眼动数据结构 (基于preprocess_edf.py分析)

### DataFrame列结构
```python
# 样本数据 (samples)
columns = [
    'time',           # 时间戳 (毫秒)
    'pa_left',        # 左眼瞳孔面积
    'pa_right',       # 右眼瞳孔面积  
    'gx_left',        # 左眼注视点X坐标
    'gy_left',        # 左眼注视点Y坐标
    'gx_right',       # 右眼注视点X坐标
    'gy_right',       # 右眼注视点Y坐标
    # 其他列...
]

# 事件数据 (events)
event_types = ['blink', 'fixation', 'saccade', ...]

# 消息数据 (messages)
# 包含实验事件标记和评分信息
```

### 数据特征
- **采样率**: 1000 Hz
- **瞳孔数据范围**: 100-6000 像素
- **坐标系**: 屏幕像素坐标
- **数据完整性**: 存在缺失值和异常值

## 评分数据结构

### JSON格式示例
```json
{
  "sentence_group_ids": [1, 2, 3],
  "representative_sentence_id": 1,
  "sentence_count": 3,
  "timestamp": 1754993457.6889992,
  "ratings": {
    "interest_rating": 3
  },
  "sentence_texts": ["句子1", "句子2", "句子3"],
  "combined_text": "合并的文本内容"
}
```

### 评分特征
- **评分类型**: interest_rating (兴趣评分)
- **评分范围**: 1-5
- **评分对象**: 句子组 (多个句子的组合)
- **时间戳**: 与眼动数据对应的时间点

## AEON格式转换需求

### 目标数据格式
```python
# 3D numpy数组格式
X.shape = (n_trials, n_channels, n_timepoints)

# 通道定义
channels = [
    'pa_left',      # 左眼瞳孔面积
    'pa_right',     # 右眼瞳孔面积
    'gx_left',      # 左眼X坐标
    'gy_left',      # 左眼Y坐标
    'gx_right',     # 右眼X坐标  
    'gy_right'      # 右眼Y坐标
]

# 标签格式
y = [1, 2, 3, 4, 5]  # 兴趣评分 (1-5)
```

### 数据预处理需求
1. **时间窗口分割**: 根据句子组时间戳分割时间序列
2. **缺失值处理**: 插值或掩码处理
3. **标准化**: 瞳孔数据和坐标数据的归一化
4. **特征工程**: 可能需要计算衍生特征

## 分类任务定义

### 目标变量
- **主要目标**: 兴趣评分分类 (1-5级)
- **可能的二分类**: 高兴趣(4-5) vs 低兴趣(1-3)
- **可能的三分类**: 低(1-2) vs 中(3) vs 高(4-5)

### 特征变量
1. **瞳孔特征**:
   - 瞳孔直径变化
   - 瞳孔直径均值和方差
   - 瞳孔直径变化率

2. **眼动特征**:
   - 注视点轨迹
   - 注视稳定性
   - 眼跳频率和幅度

3. **时间特征**:
   - 阅读时长
   - 注视时长分布
   - 眨眼频率

## 数据可用性评估

### 优势
- 高采样率数据 (1000Hz)
- 多通道信息 (双眼 + 多特征)
- 真实的兴趣评分标签
- 较大的数据量 (26个实验)

### 挑战
- 数据缺失和噪声
- 个体差异较大
- 时间序列长度不一致
- 评分数据分布可能不平衡

## 下一步工作建议

1. **数据预处理模块开发**
   - 实现EDF到aeon格式转换
   - 开发时间窗口分割算法
   - 实现特征提取和标准化

2. **数据质量分析**
   - 统计各类评分的数据分布
   - 分析数据完整性
   - 评估个体差异

3. **基准模型建立**
   - 使用aeon的简单分类器建立基准
   - 评估不同特征组合的效果
   - 确定最优的时间窗口长度

4. **算法选择和优化**
   - 测试多种aeon分类算法
   - 参数调优和交叉验证
   - 模型解释性分析
