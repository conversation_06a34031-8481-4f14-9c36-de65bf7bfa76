{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Set Up and Declare Files"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["# ParseEyeLinkAsc_script.ipynp\n", "#\n", "# Created 8/15/18 by DJ.\n", "\n", "# Import packages\n", "import os\n", "import pandas as pd\n", "import time\n", "from ParseEyeLinkAsc import ParseEyeLinkAsc\n", "\n", "# Declare filenames\n", "dataDir = \"/Users/<USER>/Documents/PRJ05_AXCPT_<PERSON>-<PERSON>/ExportExample_Good/23615\" # folder where the data sits\n", "elFilename = 'AXCPT-23615-1-EyeLink.asc' # filename of the EyeLink file (.asc)\n", "dataDir = \"/Users/<USER>/Documents/PRJ05_AXCPT_<PERSON>-<PERSON>/Data_fromSdan1/23787\" # folder where the data sits\n", "elFilename = 'AXCPT-23787-1-EyeLink.asc' # filename of the EyeLink file (.asc)\n", "outDir = dataDir"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Load Data into Pandas Dataframes"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Reading in EyeLink file AXCPT-23787-1-EyeLink.asc...\n", "Done! Took 0.680170 seconds.\n", "Sorting lines...\n", "Done! Took 3.457492 seconds.\n", "Parsing trial markers...\n", "150 trials found.\n", "Parsing stimulus messages...\n", "Done! Took 0.015682 seconds.\n", "Parsing fixations...\n", "Done! Took 0.597165 seconds.\n", "Parsing saccades...\n", "Done! Took 0.603481 seconds.\n", "Parsing blinks...\n", "Done! Took 1.210366 seconds.\n", "Parsing samples...\n", "Done! Took 3.6 seconds.\n"]}], "source": ["# Navigate to data directory\n", "os.chdir(dataDir)\n", "# Load file in\n", "dfTrial,dfMsg,dfFix,dfSacc,dfBlink,dfSamples = ParseEyeLinkAsc(elFilename)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Write Results to CSV Files\n", "Helpful if you want to analyze them in another language or just save them for later."]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Saving results...\n", "AXCPT-23787-1-EyeLink\n", "   Saving Trial output as /Users/<USER>/Documents/PRJ05_AXCPT_<PERSON>-<PERSON>/Data_fromSdan1/23787/AXCPT-23787-1-EyeLink_Trial.csv...\n", "   Saving Message output as /Users/<USER>/Documents/PRJ05_AXCPT_<PERSON>-<PERSON>/Data_fromSdan1/23787/AXCPT-23787-1-EyeLink_Message.csv...\n", "   Saving Fixation output as /Users/<USER>/Documents/PRJ05_AXCPT_<PERSON>-<PERSON>/Data_fromSdan1/23787/AXCPT-23787-1-EyeLink_Fixation.csv...\n", "   Saving Saccade output as /Users/<USER>/Documents/PRJ05_AXCPT_<PERSON>-<PERSON>/Data_fromSdan1/23787/AXCPT-23787-1-EyeLink_Saccade.csv...\n", "   Saving Blink output as /Users/<USER>/Documents/PRJ05_AXCPT_<PERSON>-<PERSON>/Data_fromSdan1/23787/AXCPT-23787-1-EyeLink_Blink.csv...\n", "   Saving Sample output as /Users/<USER>/Documents/PRJ05_AXCPT_<PERSON>-<PERSON>/Data_fromSdan1/23787/AXCPT-23787-1-EyeLink_Sample.csv...\n", "Done! Took 13.817039 seconds.\n"]}], "source": ["print('Saving results...')\n", "t = time.time()\n", "# Get file prefix from original filename\n", "elFileStart = os.path.splitext(elFilename)[0]\n", "print(elFileStart)\n", "\n", "# Make master list of dataframes to write\n", "allDataFrames = [dfTrial,dfMsg,dfFix,dfSacc,dfBlink,dfSamples] # the dataframes\n", "allNames = ['Trial','Message','Fixation','Saccade','Blink','Sample'] # what they're called\n", "# Write dataframes to .csv files\n", "for i in range(len(allNames)):\n", "    outFilename = '%s/%s_%s.csv'%(outDir,elFileStart,allNames[i])\n", "    print('   Saving %s output as %s...'%(allNames[i],outFilename))\n", "    allDataFrames[i].to_csv(outFilename,float_format='%.1f',index=False)\n", "print('Done! Took %f seconds.'%(time.time()-t))"]}], "metadata": {"kernelspec": {"display_name": "Python 2", "language": "python", "name": "python2"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 2}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython2", "version": "2.7.15"}}, "nbformat": 4, "nbformat_minor": 2}