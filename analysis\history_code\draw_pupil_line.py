#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
使用pyedfread绘制瞳孔直径曲线
从EDF文件中提取指定时间段的瞳孔数据并绘制时间曲线
"""

import os
import sys
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import matplotlib
from typing import List, Tuple, Dict, Optional
import warnings

# 设置matplotlib中文字体
matplotlib.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei']
matplotlib.rcParams['axes.unicode_minus'] = False

# ==================== 配置参数 ====================
# EDF文件路径
# EDF_FILE_PATH = "C:/Users/<USER>/Desktop/curiosity_pupil/data/20250711_134117_gyd20/gyd20.edf"
EDF_FILE_PATH = "C:/Users/<USER>/Desktop/curiosity_pupil/data/20250718_151616_dulu/dulu.edf"

# 要分析的试次
TRIALS_TO_ANALYZE = "all"  # 分析试次1，可以设置为 [1, 2] 分析多个试次，或 "all" 分析所有试次
# TRIALS_TO_ANALYZE = [8,9]  # 分析试次1，可以设置为 [1, 2] 分析多个试次，或 "all" 分析所有试次

# 要分析的数据段
# 格式: [(开始消息, 结束消息, 段名称), ...]
DATA_SEGMENTS = [
    ("BASELINE_START_FIXATION", "QUESTION_DISPLAY_END", "基线注视+问题显示"),
    ("QUESTION_DISPLAY_START", "QUESTION_DISPLAY_END", "问题显示"),
    # ("BASELINE_START_PUPIL", "ANSWER_DISPLAY_END", "瞳孔基线+答案显示"),
    # ("ANSWER_DISPLAY_START", "ANSWER_DISPLAY_END", "答案显示"),
    # 可以添加更多段，例如：
    # ("RATING_START_CURIOSITY", "RATING_END_CURIOSITY", "好奇心评分"),
    # ("RATING_START_PLEASURE", "RATING_END_PLEASURE", "愉悦度评分"),
    # ("RATING_START_SURPRISE", "RATING_END_SURPRISE", "惊讶度评分"),
]

# 瞳孔数据处理选项
MISSING_DATA_HANDLING = "interpolate"  # "interpolate" 或 "keep_missing"
INTERPOLATION_METHOD = "linear"  # "linear", "cubic", "nearest"

# 绘图参数
FIGURE_SIZE = (12, 8)
DPI = 300
SAVE_FORMAT = "png"  # "png", "pdf", "svg"
OUTPUT_DIR = "analysis/figures/pupil_curves"

# 数据过滤参数
MIN_PUPIL_SIZE = 2000.0  # 最小瞳孔直径（像素）
MAX_PUPIL_SIZE = 7000.0  # 最大瞳孔直径（像素）
BASELINE_WINDOW = 200  # 基线窗口（毫秒）

# 模拟数据选项（当真实瞳孔数据不可用时）
USE_SIMULATED_DATA = False  # 是否使用模拟数据
SIMULATION_NOISE_LEVEL = 0.1  # 模拟数据噪声水平

# 时间轴参数
TIME_UNIT = "ms"  # "ms" 或 "s"
RELATIVE_TIME = True  # True: 相对于段开始时间, False: 绝对时间

# 纵坐标范围参数
Y_AXIS_MODE = "fixed"  # "auto" 自动定义范围, "fixed" 固定范围
Y_AXIS_FIXED_MIN = 4000  # 固定范围的最小值
Y_AXIS_FIXED_MAX = 5000  # 固定范围的最大值

# ==================== 主要功能 ====================

def check_pyedfread():
    """检查pyedfread是否可用"""
    try:
        import pyedfread
        print("✓ pyedfread 可用")
        return True
    except ImportError:
        print("❌ pyedfread 未安装")
        print("请安装: pip install git+https://github.com/s-ccs/pyedfread")
        return False

def load_edf_data(edf_path: str) -> Tuple[pd.DataFrame, pd.DataFrame, pd.DataFrame]:
    """
    使用pyedfread加载EDF文件

    Returns:
        samples: 样本数据
        events: 事件数据
        messages: 消息数据
    """
    try:
        import pyedfread
        print(f"正在读取EDF文件: {edf_path}")

        # 读取EDF文件
        samples, events, messages = pyedfread.read_edf(edf_path)

        print(f"✓ 成功读取EDF文件")
        print(f"  - 样本数: {len(samples)}")
        print(f"  - 事件数: {len(events)}")
        print(f"  - 消息数: {len(messages)}")

        # 打印样本数据的列名，帮助调试
        print(f"  - 样本数据列名: {list(samples.columns)}")

        # 打印前几行样本数据
        print(f"  - 样本数据前5行:")
        print(samples.head())

        # 检查瞳孔数据
        print(f"  - 左眼瞳孔数据统计:")
        print(f"    最小值: {samples['pa_left'].min()}")
        print(f"    最大值: {samples['pa_left'].max()}")
        print(f"    非零值数量: {(samples['pa_left'] > 0).sum()}")

        print(f"  - 右眼瞳孔数据统计:")
        print(f"    最小值: {samples['pa_right'].min()}")
        print(f"    最大值: {samples['pa_right'].max()}")
        print(f"    非零值数量: {(samples['pa_right'] > 0).sum()}")

        return samples, events, messages

    except Exception as e:
        print(f"❌ 读取EDF文件失败: {e}")
        return None, None, None

def parse_messages(messages: pd.DataFrame) -> Dict:
    """解析消息数据，提取试次和事件信息"""
    parsed_messages = {}
    experiment_info = {}

    for _, row in messages.iterrows():
        timestamp = row['time']
        message = row['message']

        # 解析实验基本信息
        if 'EXPERIMENT_TYPE' in message:
            experiment_info['experiment_type'] = message.split()[-1]
        elif 'PARTICIPANT_ID' in message:
            experiment_info['participant_id'] = message.split()[-1]
        elif 'SCREEN_RESOLUTION' in message:
            parts = message.split()
            if len(parts) >= 3:
                experiment_info['screen_resolution'] = f"{parts[-2]}x{parts[-1]}"

        # 解析试次开始（包括直接的TRIAL_START和EVENT_XXX TRIAL_START）
        if 'TRIAL_START' in message:
            if message.startswith('TRIAL_START'):
                # 直接的TRIAL_START消息
                trial_num = int(message.split()[-1])
            elif 'EVENT_' in message and 'TRIAL_START' in message:
                # EVENT_XXX TRIAL_START 格式
                trial_num = int(message.split()[-1])
            else:
                continue

            if trial_num not in parsed_messages:
                parsed_messages[trial_num] = {}
            parsed_messages[trial_num]['TRIAL_START'] = timestamp

        # 解析试次结束
        elif 'TRIAL_END' in message:
            if message.startswith('TRIAL_END'):
                # 直接的TRIAL_END消息 - 不包含试次号，需要找到当前试次
                current_trial = find_current_trial(parsed_messages, timestamp)
                if current_trial:
                    parsed_messages[current_trial]['TRIAL_END'] = timestamp
            elif 'EVENT_' in message and 'TRIAL_END' in message:
                # EVENT_XXX TRIAL_END 格式
                trial_num = int(message.split()[-1])
                if trial_num in parsed_messages:
                    parsed_messages[trial_num]['TRIAL_END'] = timestamp

        # 解析基线事件
        elif 'BASELINE_START' in message:
            current_trial = find_current_trial(parsed_messages, timestamp)
            if current_trial:
                # 提取基线类型（FIXATION或PUPIL）
                baseline_type = message.split()[-1] if len(message.split()) > 1 else 'UNKNOWN'
                event_key = f'BASELINE_START_{baseline_type}'
                parsed_messages[current_trial][event_key] = timestamp
                # 同时保存通用的BASELINE_START
                if 'BASELINE_START' not in parsed_messages[current_trial]:
                    parsed_messages[current_trial]['BASELINE_START'] = timestamp

        elif 'BASELINE_END' in message:
            current_trial = find_current_trial(parsed_messages, timestamp)
            if current_trial:
                baseline_type = message.split()[-1] if len(message.split()) > 1 else 'UNKNOWN'
                event_key = f'BASELINE_END_{baseline_type}'
                parsed_messages[current_trial][event_key] = timestamp
                # 同时保存通用的BASELINE_END
                parsed_messages[current_trial]['BASELINE_END'] = timestamp

        # 解析问题显示事件
        elif 'QUESTION_DISPLAY_START' in message:
            current_trial = find_current_trial(parsed_messages, timestamp)
            if current_trial:
                parsed_messages[current_trial]['QUESTION_DISPLAY_START'] = timestamp

        elif 'QUESTION_DISPLAY_END' in message:
            current_trial = find_current_trial(parsed_messages, timestamp)
            if current_trial:
                parsed_messages[current_trial]['QUESTION_DISPLAY_END'] = timestamp

        # 解析答案显示事件
        elif 'ANSWER_DISPLAY_START' in message:
            current_trial = find_current_trial(parsed_messages, timestamp)
            if current_trial:
                parsed_messages[current_trial]['ANSWER_DISPLAY_START'] = timestamp

        elif 'ANSWER_DISPLAY_END' in message:
            current_trial = find_current_trial(parsed_messages, timestamp)
            if current_trial:
                parsed_messages[current_trial]['ANSWER_DISPLAY_END'] = timestamp

        # 解析评分事件
        elif 'RATING_START' in message:
            current_trial = find_current_trial(parsed_messages, timestamp)
            if current_trial:
                # 提取评分类型（CURIOSITY, PLEASURE, SURPRISE等）
                rating_type = message.split()[-1] if len(message.split()) > 1 else 'UNKNOWN'
                event_key = f'RATING_START_{rating_type}'
                parsed_messages[current_trial][event_key] = timestamp

        elif 'RATING_END' in message:
            current_trial = find_current_trial(parsed_messages, timestamp)
            if current_trial:
                rating_type = message.split()[-1] if len(message.split()) > 1 else 'UNKNOWN'
                event_key = f'RATING_END_{rating_type}'
                parsed_messages[current_trial][event_key] = timestamp

        # 解析具体的EVENT_XXX消息
        elif message.startswith('EVENT_'):
            current_trial = find_current_trial(parsed_messages, timestamp)
            if current_trial:
                parts = message.split()
                if len(parts) >= 3:
                    event_num = parts[0]  # EVENT_XXX
                    event_type = parts[1]  # 事件类型
                    event_content = ' '.join(parts[2:]) if len(parts) > 2 else ''  # 事件内容

                    # 根据事件类型进行分类存储
                    if event_type == 'QUESTION_ID':
                        parsed_messages[current_trial]['question_id'] = event_content
                    elif event_type == 'QUESTION_CONTENT':
                        parsed_messages[current_trial]['question_content'] = event_content
                    elif event_type == 'ANSWER_CONTENT':
                        parsed_messages[current_trial]['answer_content'] = event_content
                    elif event_type == 'PARTICIPANT_RESPONSE':
                        parsed_messages[current_trial]['participant_response'] = event_content
                    elif event_type == 'RATING_VALUE':
                        # 评分值，格式：RATING_VALUE CURIOSITY 4
                        if len(parts) >= 4:
                            rating_type = parts[2]
                            rating_value = parts[3]
                            rating_key = f'rating_value_{rating_type.lower()}'
                            parsed_messages[current_trial][rating_key] = int(rating_value)
                    elif event_type == 'DISPLAY_FIXATION_CROSS':
                        parsed_messages[current_trial]['display_fixation_cross'] = timestamp
                    elif event_type == 'DISPLAY_INPUT_BOX':
                        parsed_messages[current_trial]['display_input_box'] = timestamp
                    elif event_type == 'DISPLAY_RATING_SCALE':
                        rating_type = event_content
                        event_key = f'display_rating_scale_{rating_type.lower()}'
                        parsed_messages[current_trial][event_key] = timestamp
                    elif event_type == 'INPUT_START':
                        parsed_messages[current_trial]['INPUT_START'] = timestamp
                    elif event_type == 'INPUT_END':
                        parsed_messages[current_trial]['INPUT_END'] = timestamp
                    elif event_type == 'EXPERIMENT_END':
                        experiment_info['experiment_end'] = timestamp
                    elif event_type == 'TOTAL_TRIALS':
                        experiment_info['total_trials'] = int(event_content)
                    elif event_type == 'TOTAL_EVENTS':
                        experiment_info['total_events'] = int(event_content)

                    # 存储原始事件信息
                    if 'events' not in parsed_messages[current_trial]:
                        parsed_messages[current_trial]['events'] = []
                    parsed_messages[current_trial]['events'].append({
                        'timestamp': timestamp,
                        'event_num': event_num,
                        'event_type': event_type,
                        'event_content': event_content,
                        'raw_message': message
                    })

        # 解析系统配置消息（可选，用于调试）
        elif message.startswith('RECCFG') or message.startswith('ELCLCFG') or message.startswith('!MODE'):
            # 这些是EyeLink系统配置消息，通常不需要用于数据分析
            # 但可以存储用于调试
            if 'system_messages' not in experiment_info:
                experiment_info['system_messages'] = []
            experiment_info['system_messages'].append({
                'timestamp': timestamp,
                'message': message
            })

    # 将实验信息添加到返回结果中
    if experiment_info:
        parsed_messages['experiment_info'] = experiment_info

    return parsed_messages

def print_parsed_messages_summary(parsed_messages: Dict):
    """打印解析后的消息摘要"""
    print("\n" + "="*60)
    print("解析后的消息摘要")
    print("="*60)

    # 显示实验信息
    if 'experiment_info' in parsed_messages:
        exp_info = parsed_messages['experiment_info']
        print(f"\n实验信息:")
        for key, value in exp_info.items():
            if key != 'system_messages':  # 系统消息太多，不显示
                print(f"  {key}: {value}")

    # 显示试次信息
    trial_nums = [k for k in parsed_messages.keys() if isinstance(k, int)]
    trial_nums.sort()

    print(f"\n找到 {len(trial_nums)} 个试次: {trial_nums}")

    # 显示每个试次的详细信息
    for trial_num in trial_nums[:3]:  # 只显示前3个试次的详细信息
        trial_data = parsed_messages[trial_num]
        print(f"\n试次 {trial_num} 详细信息:")

        # 显示主要时间点
        main_events = ['TRIAL_START', 'BASELINE_START', 'QUESTION_DISPLAY_START',
                      'QUESTION_DISPLAY_END', 'ANSWER_DISPLAY_START', 'ANSWER_DISPLAY_END', 'TRIAL_END']
        for event in main_events:
            if event in trial_data:
                print(f"  {event}: {trial_data[event]}")

        # 显示内容信息
        content_keys = ['question_id', 'question_content', 'answer_content', 'participant_response']
        for key in content_keys:
            if key in trial_data:
                content = trial_data[key]
                if len(str(content)) > 50:
                    content = str(content)[:50] + "..."
                print(f"  {key}: {content}")

        # 显示评分信息
        rating_keys = [k for k in trial_data.keys() if k.startswith('rating_value_')]
        if rating_keys:
            ratings = []
            for key in rating_keys:
                rating_type = key.replace('rating_value_', '')
                ratings.append(f"{rating_type}={trial_data[key]}")
            print(f"  评分: {', '.join(ratings)}")

        # 显示事件数量
        if 'events' in trial_data:
            print(f"  总事件数: {len(trial_data['events'])}")

    if len(trial_nums) > 3:
        print(f"\n... (还有 {len(trial_nums) - 3} 个试次)")

def find_current_trial(parsed_messages: Dict, timestamp: int) -> Optional[int]:
    """根据时间戳找到当前试次"""
    # 只考虑数字键（试次号）
    trial_nums = [k for k in parsed_messages.keys() if isinstance(k, int)]

    for trial_num in trial_nums:
        trial_data = parsed_messages[trial_num]
        if 'TRIAL_START' in trial_data and trial_data['TRIAL_START'] <= timestamp:
            # 检查是否有下一个试次，如果有，确保时间戳在当前试次范围内
            next_trial_start = None
            for other_trial in trial_nums:
                if (other_trial > trial_num and 'TRIAL_START' in parsed_messages[other_trial]):
                    other_start = parsed_messages[other_trial]['TRIAL_START']
                    if next_trial_start is None or other_start < next_trial_start:
                        next_trial_start = other_start

            if next_trial_start is None or timestamp < next_trial_start:
                return trial_num
    return None

def extract_pupil_data(samples: pd.DataFrame, start_time: int, end_time: int) -> pd.DataFrame:
    """提取指定时间段的瞳孔数据"""
    # 过滤时间范围
    segment_data = samples[(samples['time'] >= start_time) & (samples['time'] <= end_time)].copy()

    if len(segment_data) == 0:
        print(f"警告: 时间段 {start_time}-{end_time} 没有数据")
        return pd.DataFrame()

    # 提取瞳孔直径数据（左眼和右眼）
    pupil_data = pd.DataFrame({
        'time': segment_data['time'],
        'left_pupil': segment_data.get('pa_left', np.nan),
        'right_pupil': segment_data.get('pa_right', np.nan)
    })

    # 检查是否有有效的瞳孔数据
    has_valid_left = (pupil_data['left_pupil'] > 0).any()
    has_valid_right = (pupil_data['right_pupil'] > 0).any()


    if not has_valid_left and not has_valid_right and USE_SIMULATED_DATA:
        print("  警告: 没有有效的瞳孔数据，生成模拟数据用于演示")
        pupil_data = generate_simulated_pupil_data(pupil_data)
    else:
        # 数据清理：过滤异常值
        left_before = (~pupil_data['left_pupil'].isna()).sum()
        right_before = (~pupil_data['right_pupil'].isna()).sum()

        pupil_data.loc[pupil_data['left_pupil'] < MIN_PUPIL_SIZE, 'left_pupil'] = np.nan
        pupil_data.loc[pupil_data['left_pupil'] > MAX_PUPIL_SIZE, 'left_pupil'] = np.nan
        pupil_data.loc[pupil_data['right_pupil'] < MIN_PUPIL_SIZE, 'right_pupil'] = np.nan
        pupil_data.loc[pupil_data['right_pupil'] > MAX_PUPIL_SIZE, 'right_pupil'] = np.nan

        left_after = (~pupil_data['left_pupil'].isna()).sum()
        right_after = (~pupil_data['right_pupil'].isna()).sum()

        print(f"  数据过滤结果:")
        print(f"    左眼: {left_before} -> {left_after} (过滤了 {left_before - left_after} 个异常值)")
        print(f"    右眼: {right_before} -> {right_after} (过滤了 {right_before - right_after} 个异常值)")

    # 计算双眼平均
    pupil_data['both_eyes'] = pupil_data[['left_pupil', 'right_pupil']].mean(axis=1)

    return pupil_data

def generate_simulated_pupil_data(pupil_data: pd.DataFrame) -> pd.DataFrame:
    """生成模拟瞳孔数据用于演示"""
    n_samples = len(pupil_data)
    time_points = np.arange(n_samples)

    # 基础瞳孔大小（像素）
    base_pupil_size = 50.0

    # 生成模拟的瞳孔变化模式
    # 添加缓慢的趋势变化
    trend = np.sin(time_points / n_samples * 2 * np.pi) * 5

    # 添加随机噪声
    noise_left = np.random.normal(0, SIMULATION_NOISE_LEVEL * base_pupil_size, n_samples)
    noise_right = np.random.normal(0, SIMULATION_NOISE_LEVEL * base_pupil_size, n_samples)

    # 生成左眼和右眼数据（略有不同）
    pupil_data['left_pupil'] = base_pupil_size + trend + noise_left
    pupil_data['right_pupil'] = base_pupil_size + trend * 0.9 + noise_right

    # 确保数据在合理范围内
    pupil_data['left_pupil'] = np.clip(pupil_data['left_pupil'], 20, 80)
    pupil_data['right_pupil'] = np.clip(pupil_data['right_pupil'], 20, 80)

    return pupil_data

def process_missing_data(pupil_data: pd.DataFrame) -> pd.DataFrame:
    """处理缺失数据"""
    if MISSING_DATA_HANDLING == "interpolate":
        # 插值处理
        pupil_data['left_pupil'] = pupil_data['left_pupil'].interpolate(method=INTERPOLATION_METHOD)
        pupil_data['right_pupil'] = pupil_data['right_pupil'].interpolate(method=INTERPOLATION_METHOD)
        pupil_data['both_eyes'] = pupil_data['both_eyes'].interpolate(method=INTERPOLATION_METHOD)
        print(f"✓ 使用{INTERPOLATION_METHOD}插值处理缺失数据")
    else:
        print("✓ 保留缺失数据")
    
    return pupil_data

def plot_pupil_curve(pupil_data: pd.DataFrame, trial_num: int, segment_name: str, 
                    start_time: int, end_time: int):
    """绘制瞳孔直径曲线"""
    if len(pupil_data) == 0:
        print(f"警告: 试次{trial_num} {segment_name} 没有数据可绘制")
        return
    
    # 创建图形
    fig, ax = plt.subplots(figsize=FIGURE_SIZE, dpi=DPI)
    
    # 准备时间轴
    if RELATIVE_TIME:
        time_axis = (pupil_data['time'] - start_time)
        if TIME_UNIT == "s":
            time_axis = time_axis / 1000.0
            xlabel = "时间 (秒)"
        else:
            xlabel = "时间 (毫秒)"
    else:
        time_axis = pupil_data['time']
        xlabel = "绝对时间 (毫秒)"
    
    # 绘制曲线
    lines_plotted = 0
    if not pupil_data['left_pupil'].isna().all():
        ax.plot(time_axis, pupil_data['left_pupil'], 'b-', label='左眼', alpha=0.7, linewidth=1.5)
        lines_plotted += 1

    if not pupil_data['right_pupil'].isna().all():
        ax.plot(time_axis, pupil_data['right_pupil'], 'r-', label='右眼', alpha=0.7, linewidth=1.5)
        lines_plotted += 1

    if not pupil_data['both_eyes'].isna().all():
        ax.plot(time_axis, pupil_data['both_eyes'], 'k-', label='双眼平均', linewidth=2)
        lines_plotted += 1
    
    # 设置图形属性
    ax.set_xlabel(xlabel, fontsize=12)
    ax.set_ylabel('瞳孔直径 (像素)', fontsize=12)
    ax.set_title(f'试次 {trial_num} - {segment_name} 瞳孔直径变化', fontsize=14, fontweight='bold')

    # 设置纵坐标范围
    if Y_AXIS_MODE == "fixed":
        ax.set_ylim(Y_AXIS_FIXED_MIN, Y_AXIS_FIXED_MAX)
        print(f"  使用固定纵坐标范围: {Y_AXIS_FIXED_MIN} - {Y_AXIS_FIXED_MAX}")
    else:
        # 自动范围，但可以显示当前范围
        current_ylim = ax.get_ylim()
        print(f"  自动纵坐标范围: {current_ylim[0]:.1f} - {current_ylim[1]:.1f}")

    # 只有在有数据线时才显示图例
    if lines_plotted > 0:
        ax.legend(fontsize=10)
    else:
        ax.text(0.5, 0.5, '无有效瞳孔数据', transform=ax.transAxes,
                ha='center', va='center', fontsize=16, color='red')

    ax.grid(True, alpha=0.3)
    
    # 添加统计信息
    stats_text = f"数据点数: {len(pupil_data)}\n"
    if not pupil_data['both_eyes'].isna().all():
        mean_pupil = pupil_data['both_eyes'].mean()
        std_pupil = pupil_data['both_eyes'].std()
        stats_text += f"平均直径: {mean_pupil:.2f}±{std_pupil:.2f}"
    
    ax.text(0.02, 0.98, stats_text, transform=ax.transAxes, 
            verticalalignment='top', bbox=dict(boxstyle='round', facecolor='wheat', alpha=0.8))
    
    plt.tight_layout()

    # 保存图形
    os.makedirs(OUTPUT_DIR, exist_ok=True)
    filename = f"trial_{trial_num}_{segment_name.replace(' ', '_')}.{SAVE_FORMAT}"
    filepath = os.path.join(OUTPUT_DIR, filename)
    plt.savefig(filepath, dpi=DPI, bbox_inches='tight')
    print(f"✓ 保存图形: {filepath}")

    # 关闭图形以释放内存
    plt.close(fig)

    # plt.show()

def main():
    """主函数"""
    print("瞳孔直径曲线绘制工具")
    print("=" * 50)
    
    # 检查依赖
    if not check_pyedfread():
        return
    
    # 检查文件是否存在
    if not os.path.exists(EDF_FILE_PATH):
        print(f"❌ EDF文件不存在: {EDF_FILE_PATH}")
        return
    
    # 读取EDF数据
    samples, events, messages = load_edf_data(EDF_FILE_PATH)
    if samples is None:
        return
    
    # 解析消息
    print("\n解析消息数据...")
    parsed_messages = parse_messages(messages)

    # 计算试次数量（排除experiment_info）
    trial_count = len([k for k in parsed_messages.keys() if isinstance(k, int)])
    print(f"✓ 解析完成，找到 {trial_count} 个试次")

    # 显示解析摘要
    print_parsed_messages_summary(parsed_messages)
    
    # 确定要分析的试次
    if TRIALS_TO_ANALYZE == "all":
        trials_to_process = list(parsed_messages.keys())
        print(f"分析所有试次: {trials_to_process}")
    else:
        trials_to_process = TRIALS_TO_ANALYZE
        print(f"分析试次: {trials_to_process}")

    # 处理每个试次的每个数据段
    for trial_num in trials_to_process:
        if trial_num not in parsed_messages:
            print(f"警告: 试次 {trial_num} 未找到")
            continue
        
        trial_data = parsed_messages[trial_num]
        print(f"\n处理试次 {trial_num}...")
        
        for start_event, end_event, segment_name in DATA_SEGMENTS:
            if start_event not in trial_data or end_event not in trial_data:
                print(f"  警告: {segment_name} 事件未找到")
                continue
            
            start_time = trial_data[start_event]
            end_time = trial_data[end_event]
            
            print(f"  分析 {segment_name}: {start_time} - {end_time}")
            
            # 提取瞳孔数据
            pupil_data = extract_pupil_data(samples, start_time, end_time)
            if len(pupil_data) == 0:
                continue
            
            # 处理缺失数据
            pupil_data = process_missing_data(pupil_data)
            
            # 绘制曲线
            plot_pupil_curve(pupil_data, trial_num, segment_name, start_time, end_time)
    
    print(f"\n✓ 分析完成！图形保存在: {OUTPUT_DIR}")

if __name__ == "__main__":
    main()
