# AEON数据格式要求详解

## 核心数据格式

### 3D numpy数组格式
```python
X.shape = (n_cases, n_channels, n_timepoints)
```

- **n_cases**: 样本数量（试次数量）
- **n_channels**: 通道数量（特征维度）
- **n_timepoints**: 时间点数量（序列长度）

### 标签格式
```python
y.shape = (n_cases,)
y.dtype = int 或 str
```

## 眼动数据映射方案

### 通道定义
```python
channels = [
    'pa_left',      # 通道0: 左眼瞳孔面积
    'pa_right',     # 通道1: 右眼瞳孔面积
    'gx_left',      # 通道2: 左眼X坐标
    'gy_left',      # 通道3: 左眼Y坐标
    'gx_right',     # 通道4: 右眼X坐标
    'gy_right'      # 通道5: 右眼Y坐标
]
n_channels = 6
```

### 时间窗口设计
```python
# 基于句子组的时间窗口
window_duration = 5000  # 5秒窗口
sampling_rate = 1000    # 1000Hz采样率
n_timepoints = window_duration  # 5000个时间点

# 或者固定长度窗口
fixed_length = 3000     # 3秒固定窗口
```

## 数据预处理流程

### 1. 时间窗口提取
```python
def extract_time_windows(samples, rating_data, window_duration=5000):
    """
    根据评分时间戳提取时间窗口
    
    Args:
        samples: 眼动样本数据
        rating_data: 评分数据
        window_duration: 窗口长度(ms)
    
    Returns:
        X: (n_trials, n_channels, n_timepoints)
        y: (n_trials,) 评分标签
    """
    pass
```

### 2. 数据标准化
```python
def normalize_features(X):
    """
    对不同类型的特征进行标准化
    
    瞳孔数据: Z-score标准化
    坐标数据: Min-Max标准化到[0,1]
    """
    # 瞳孔通道 (0, 1)
    X[:, [0, 1], :] = (X[:, [0, 1], :] - mean) / std
    
    # 坐标通道 (2, 3, 4, 5)
    X[:, [2, 3, 4, 5], :] = (X[:, [2, 3, 4, 5], :] - min) / (max - min)
    
    return X
```

### 3. 缺失值处理
```python
def handle_missing_values(X):
    """
    处理缺失值
    
    策略:
    1. 线性插值
    2. 前向填充
    3. 零填充
    4. 掩码标记
    """
    pass
```

## 标签编码方案

### 兴趣评分映射
```python
# 原始评分: 1, 2, 3, 4, 5
# 目标编码: 0, 1, 2, 3, 4 (从0开始)

def encode_labels(ratings):
    """将1-5评分转换为0-4编码"""
    return np.array(ratings) - 1

# 或者分组编码
def group_labels(ratings, strategy='binary'):
    """
    分组编码策略
    
    binary: 低兴趣(1-3) -> 0, 高兴趣(4-5) -> 1
    ternary: 低(1-2) -> 0, 中(3) -> 1, 高(4-5) -> 2
    """
    if strategy == 'binary':
        return (np.array(ratings) >= 4).astype(int)
    elif strategy == 'ternary':
        labels = np.zeros_like(ratings)
        labels[np.array(ratings) == 3] = 1
        labels[np.array(ratings) >= 4] = 2
        return labels
```

## 数据验证

### 格式检查
```python
def validate_aeon_format(X, y):
    """
    验证数据格式是否符合aeon要求
    
    检查项:
    1. X是3D numpy数组
    2. y是1D数组
    3. 形状匹配
    4. 数据类型正确
    5. 无无穷大或NaN值
    """
    assert isinstance(X, np.ndarray), "X必须是numpy数组"
    assert isinstance(y, np.ndarray), "y必须是numpy数组"
    assert X.ndim == 3, "X必须是3D数组"
    assert y.ndim == 1, "y必须是1D数组"
    assert X.shape[0] == y.shape[0], "样本数量必须匹配"
    assert np.isfinite(X).all(), "X不能包含无穷大或NaN"
    assert np.isfinite(y).all(), "y不能包含无穷大或NaN"
    
    print(f"✓ 数据格式验证通过")
    print(f"  样本数: {X.shape[0]}")
    print(f"  通道数: {X.shape[1]}")
    print(f"  时间点数: {X.shape[2]}")
    print(f"  标签类别: {np.unique(y)}")
```

## 兼容性考虑

### 多变量时间序列支持
```python
# aeon支持多变量时间序列
# 我们的6通道眼动数据完全兼容

from aeon.classification.distance_based import KNeighborsTimeSeriesClassifier

# 创建分类器
clf = KNeighborsTimeSeriesClassifier(n_neighbors=3)

# 训练 (自动处理多变量数据)
clf.fit(X_train, y_train)

# 预测
y_pred = clf.predict(X_test)
```

### 不等长序列处理
```python
# 如果时间序列长度不一致，有两种处理方式:

# 方案1: 固定长度截断/填充
def pad_or_truncate(X, target_length):
    """统一序列长度"""
    if X.shape[2] > target_length:
        return X[:, :, :target_length]  # 截断
    elif X.shape[2] < target_length:
        pad_width = ((0, 0), (0, 0), (0, target_length - X.shape[2]))
        return np.pad(X, pad_width, mode='constant')  # 填充
    return X

# 方案2: 使用aeon的不等长序列支持
# 将数据存储为list of 2D arrays
X_unequal = [X[i] for i in range(X.shape[0])]  # List[np.ndarray]
```

## 性能优化建议

### 内存优化
```python
# 使用float32减少内存占用
X = X.astype(np.float32)

# 批量处理大数据集
def process_in_batches(data, batch_size=1000):
    """分批处理数据"""
    for i in range(0, len(data), batch_size):
        yield data[i:i+batch_size]
```

### 计算优化
```python
# 预计算常用统计量
def precompute_statistics(X):
    """预计算统计特征"""
    stats = {
        'mean': np.mean(X, axis=2),      # (n_cases, n_channels)
        'std': np.std(X, axis=2),       # (n_cases, n_channels)
        'min': np.min(X, axis=2),       # (n_cases, n_channels)
        'max': np.max(X, axis=2),       # (n_cases, n_channels)
    }
    return stats
```

## 总结

AEON库的数据格式要求相对简单但严格：
1. **3D numpy数组** 用于多变量时间序列
2. **1D numpy数组** 用于标签
3. **数据类型** 必须是数值型
4. **无缺失值** 或正确处理缺失值

我们的眼动数据完全符合这些要求，只需要适当的预处理和格式转换即可。
