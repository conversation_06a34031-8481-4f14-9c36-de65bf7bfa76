#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
LHIPA计算器 - 连接用户的EDF预处理模块和PyPil LHIPA算法
功能：
1. 数据格式转换函数
2. 实时LHIPA计算
3. 滑动窗口LHIPA分析
"""

import sys
import os
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
from typing import Optional, Tuple, List
import warnings

# 添加PyPil代码路径
pypil_path = os.path.join(os.getcwd(), 'Pupillometric-measures-of-cognitive-load-main', 'Maintained Code')
sys.path.insert(0, pypil_path)

# 添加用户预处理模块路径
analysis_path = os.path.join(os.getcwd(), 'analysis')
sys.path.insert(0, analysis_path)

# 导入PyPil类（从test_pypil_basic.py中复制）
import glob
import math
import numpy as np
import pandas as pd
import pywt

class PyPil:
    """PyPil基类"""
    def __init__(self, pupil_data, wavelet="sym16", periodization="per", level = 2, specification="world_timestamp"):
        try:
            import numpy as np
            import math
            import pywt
        except ImportError as e:
            raise ImportError("A required library is not imported, verify that numpy, math, and pywt are imported.")
        
        self.pupil_data = pupil_data
        self.pupil_data_diam = list(pupil_data['diameter'])
        self.pupil_data_time_WI = list(pupil_data['world_index'])
        self.pupil_data_time_WT = list(pupil_data['world_timestamp'])
        self.wavelet = wavelet
        self.periodization = periodization
        self.level = level
        self.specification = specification
        
    def calculate_timestamps(self):
        tt = self.pupil_data[self.specification].iloc[-1] - self.pupil_data[self.specification].iloc[0]
        return tt
        
    def compute_modmax(self, d):
        m = [0.0] * len(d)
        for i in range(len(d)):
            m[i] = math.fabs(d[i])
            
        t = [0.0] * len(d)
        for i in range(len(d)):    
            ll = m[i-1] if i >= 1 else m[i]
            oo = m[i]
            rr = m[i+1] if i < len(d)-2 else m[i]
            
            if (ll <= oo and oo >= rr) and (ll < oo or oo > rr):
                t[i] = math.sqrt(d[i]**2)
            else:
                t[i] = 0.0
        return t

    def compute_threshold(self, detect, mode="hard"):
        thresh = np.std(detect) * math.sqrt(2.0*np.log2(len(detect)))
        cD2t = pywt.threshold(detect,thresh,mode)
        return cD2t
    
    def calculate_ipa(self, cD2t):
        ctr = 0
        for i in range(len(cD2t)):
            if math.fabs(cD2t[i]) > 0: 
                ctr += 1
        ipa = float(ctr)/ self.calculate_timestamps()
        return ipa

class IPA(PyPil):
    """IPA子类"""
    def __init__(self, pupil_data, wavelet="sym16", periodization="per", level=2, specification="world_timestamp"):
        super().__init__(pupil_data, wavelet, periodization, level, specification)
        
        cA2, cD2, cD1 = self.ipa_wavelet_decomposition()
        cA2[:], cD2[:], cD1[:] = self.ipa_normalize_coefficients(cA2, cD2, cD1, self.level)
        detect = self.compute_modmax(cD2[:])
        cD2t = self.compute_threshold(detect)
        self.ipa = self.calculate_ipa(cD2t)

    def ipa_wavelet_decomposition(self):
        cA2, cD2, cD1 = pywt.wavedec(self.pupil_data_diam, self.wavelet, mode=self.periodization, level=self.level)
        return cA2, cD2, cD1 
        
    def ipa_normalize_coefficients(self, cA2, cD2, cD1, level):
        cA2[:] = [x / math.sqrt(2.0 * level) for x in cA2] 
        cD2[:] = [x / math.sqrt(2.0 * level) for x in cD2]
        cD1[:] = [x / math.sqrt(1.0 * level) for x in cD1] 
        return cA2[:], cD2[:], cD1[:]

class LHIPA(PyPil):
    """LHIPA子类"""
    def __init__(self, pupil_data, wavelet="sym16", periodization="per", level=2, specification="world_timestamp"):
        super().__init__(pupil_data, wavelet, periodization, level, specification)
        
        self.max_level = self.lhipa_wavelet_decomposition(self.pupil_data_diam)
        self.hif, self.lof = 1, int(self.max_level/2)
        cD_H, cD_L  = self.lhipa_normalize_coefficients(self.pupil_data_diam, self.max_level)
        cD_LH = self.lhipa_ratio(cD_H[:], cD_L[:])
        cD_LHm = self.compute_modmax(cD_LH)
        cD_LHt = self.compute_threshold(cD_LHm, mode="less")
        self.lhipa = self.calculate_ipa(cD_LHt)
        
    def lhipa_wavelet_decomposition(self, d):
        w = pywt.Wavelet('sym16')
        self.max_level = pywt.dwt_max_level(len(d),filter_len=w.dec_len)
        return self.max_level
        
    def lhipa_normalize_coefficients(self, d, max_level):
        cD_H = pywt.downcoef('d',self.pupil_data_diam, 'sym16', 'per', level=self.hif)
        cD_L = pywt.downcoef('d',self.pupil_data_diam, 'sym16', 'per', level=self.lof)
        cD_H[:] = [x / math.sqrt(2**self.hif) for x in cD_H]
        cD_L[:] = [x / math.sqrt(2**self.lof) for x in cD_L]
        return cD_H[:], cD_L[:]   
    
    def lhipa_ratio(self, cD_H, cD_L):
        cD_LH = cD_L.copy()
        for i in range(len(cD_L)):
            den = cD_H[((2**self.lof)//(2**self.hif))*i]
            if den != 0:
                cD_LH[i] = cD_L[i] / den
            else: 
                cD_LH[i] = cD_L[i] / 0.00000000001
        return cD_LH

def convert_to_pypil_format(processed_data: pd.DataFrame, 
                           time_col: str = 'time',
                           pupil_col: str = 'pupil_avg') -> pd.DataFrame:
    """
    将用户的预处理EDF数据格式转换为PyPil类期望的DataFrame格式
    
    Args:
        processed_data: 用户预处理后的数据（包含time, pupil_avg等列）
        time_col: 时间列名（默认：'time'）
        pupil_col: 瞳孔数据列名（默认：'pupil_avg'）
    
    Returns:
        pd.DataFrame: PyPil格式的数据（包含diameter, world_index, world_timestamp列）
    """
    print(f"🔄 转换数据格式...")
    print(f"输入数据形状: {processed_data.shape}")
    print(f"输入列名: {list(processed_data.columns)}")
    
    # 创建PyPil格式的DataFrame
    pypil_data = pd.DataFrame()
    
    # 使用指定的瞳孔数据列作为直径
    if pupil_col in processed_data.columns:
        pypil_data['diameter'] = processed_data[pupil_col]
        print(f"✅ 使用 {pupil_col} 列作为瞳孔直径")
    else:
        # 如果没有pupil_avg，尝试其他可能的列
        possible_cols = ['pa_left', 'pa_right', 'pupil_diameter']
        for col in possible_cols:
            if col in processed_data.columns:
                pypil_data['diameter'] = processed_data[col]
                print(f"✅ 使用 {col} 列作为瞳孔直径")
                break
        else:
            raise ValueError(f"未找到瞳孔数据列。期望列名: {pupil_col}, 可用列: {list(processed_data.columns)}")
    
    # 创建world_index（简单的序列索引）
    pypil_data['world_index'] = range(len(processed_data))
    
    # 转换时间戳
    if time_col in processed_data.columns:
        # 如果时间是毫秒，转换为秒
        time_data = processed_data[time_col]
        if time_data.max() > 1000:  # 假设大于1000的是毫秒
            pypil_data['world_timestamp'] = time_data / 1000.0
            print(f"✅ 时间从毫秒转换为秒")
        else:
            pypil_data['world_timestamp'] = time_data
            print(f"✅ 时间保持原格式（秒）")
    else:
        # 如果没有时间列，创建假的时间戳
        pypil_data['world_timestamp'] = np.linspace(0, len(processed_data)/1000, len(processed_data))
        print(f"⚠️ 未找到时间列 {time_col}，创建假时间戳")
    
    # 移除NaN值
    original_length = len(pypil_data)
    pypil_data = pypil_data.dropna()
    final_length = len(pypil_data)
    
    if original_length != final_length:
        print(f"⚠️ 移除了 {original_length - final_length} 个NaN值")
    
    print(f"✅ 转换完成，输出数据形状: {pypil_data.shape}")
    print(f"瞳孔直径范围: {pypil_data['diameter'].min():.1f} - {pypil_data['diameter'].max():.1f}")
    print(f"时间范围: {pypil_data['world_timestamp'].min():.3f} - {pypil_data['world_timestamp'].max():.3f} 秒")
    
    return pypil_data

def calculate_lhipa_from_edf(edf_path: str, 
                            preprocess_params: Optional[dict] = None) -> Tuple[float, float, pd.DataFrame]:
    """
    从EDF文件计算LHIPA系数
    
    Args:
        edf_path: EDF文件路径
        preprocess_params: 预处理参数字典
    
    Returns:
        Tuple[ipa_value, lhipa_value, processed_data]: IPA值、LHIPA值和处理后的数据
    """
    try:
        # 导入用户的预处理模块
        from preprocess_edf import preprocess_edf_file
        
        # 设置默认预处理参数
        if preprocess_params is None:
            preprocess_params = {
                'smooth_window': 5,
                'blink_interpolation_window': 40,
                'interpolation_method': 'quadratic',
                'velocity_threshold': 500.0,
                'eye_selection': 'binocular'
            }
        
        print(f"🔄 预处理EDF文件: {edf_path}")
        
        # 预处理EDF文件
        processed_data = preprocess_edf_file(edf_path, **preprocess_params)
        
        if processed_data.empty:
            raise ValueError("预处理后的数据为空")
        
        print(f"✅ 预处理完成，数据形状: {processed_data.shape}")
        
        # 转换数据格式
        pypil_data = convert_to_pypil_format(processed_data)
        
        if len(pypil_data) < 100:
            raise ValueError(f"数据点太少（{len(pypil_data)}），无法进行可靠的LHIPA计算")
        
        # 计算IPA和LHIPA
        print(f"🔄 计算IPA和LHIPA...")
        ipa_calculator = IPA(pypil_data)
        lhipa_calculator = LHIPA(pypil_data)
        
        ipa_value = ipa_calculator.ipa
        lhipa_value = lhipa_calculator.lhipa
        
        print(f"✅ 计算完成")
        print(f"IPA值: {ipa_value:.6f}")
        print(f"LHIPA值: {lhipa_value:.6f}")
        
        return ipa_value, lhipa_value, processed_data
        
    except ImportError as e:
        print(f"❌ 导入预处理模块失败: {e}")
        print("请确保 analysis/preprocess_edf.py 文件存在且可导入")
        raise
    except Exception as e:
        print(f"❌ 计算LHIPA时出错: {e}")
        raise

def calculate_sliding_lhipa(processed_data: pd.DataFrame, 
                           window_size: int = 7000, 
                           step_size: int = 1000) -> Tuple[List[float], List[float], List[float]]:
    """
    使用滑动窗口计算实时LHIPA系数
    
    Args:
        processed_data: 预处理后的数据
        window_size: 窗口大小（数据点数）
        step_size: 步长（数据点数）
    
    Returns:
        Tuple[time_points, ipa_values, lhipa_values]: 时间点、IPA值列表、LHIPA值列表
    """
    print(f"🔄 计算滑动窗口LHIPA...")
    print(f"窗口大小: {window_size} 个数据点")
    print(f"步长: {step_size} 个数据点")
    
    ipa_values = []
    lhipa_values = []
    time_points = []
    
    total_windows = (len(processed_data) - window_size) // step_size + 1
    print(f"总窗口数: {total_windows}")
    
    for i in range(0, len(processed_data) - window_size + 1, step_size):
        # 提取窗口数据
        window_data = processed_data.iloc[i:i+window_size]
        
        # 计算窗口中心时间点
        if 'time' in window_data.columns:
            center_time = window_data['time'].iloc[window_size//2] / 1000.0  # 转换为秒
        else:
            center_time = i / 1000.0  # 假设1000Hz采样率
        
        time_points.append(center_time)
        
        try:
            # 转换格式
            pypil_data = convert_to_pypil_format(window_data)
            
            if len(pypil_data) > 100:  # 确保有足够的数据点
                # 计算LHIPA
                with warnings.catch_warnings():
                    warnings.simplefilter("ignore")
                    lhipa_calc = LHIPA(pypil_data)
                    ipa_calc = IPA(pypil_data)
                    
                    ipa_values.append(ipa_calc.ipa)
                    lhipa_values.append(lhipa_calc.lhipa)
            else:
                ipa_values.append(np.nan)
                lhipa_values.append(np.nan)
                
        except Exception as e:
            print(f"⚠️ 窗口 {i//step_size + 1}/{total_windows} 计算失败: {e}")
            ipa_values.append(np.nan)
            lhipa_values.append(np.nan)
        
        # 显示进度
        if (i//step_size + 1) % 10 == 0:
            print(f"进度: {i//step_size + 1}/{total_windows} 窗口")
    
    print(f"✅ 滑动窗口计算完成")
    print(f"有效IPA值: {np.sum(~np.isnan(ipa_values))}/{len(ipa_values)}")
    print(f"有效LHIPA值: {np.sum(~np.isnan(lhipa_values))}/{len(lhipa_values)}")
    
    return time_points, ipa_values, lhipa_values

if __name__ == "__main__":
    # 测试数据格式转换功能
    print("🧪 测试数据格式转换功能...")
    
    # 创建模拟的预处理数据
    n_points = 5000
    time_data = np.linspace(0, 5000, n_points)  # 5秒数据，毫秒
    pupil_data = 4000 + 100 * np.sin(2 * np.pi * 0.5 * time_data / 1000) + np.random.normal(0, 20, n_points)
    
    mock_processed_data = pd.DataFrame({
        'time': time_data,
        'pupil_avg': pupil_data,
        'pa_left': pupil_data + np.random.normal(0, 10, n_points),
        'pa_right': pupil_data + np.random.normal(0, 10, n_points)
    })
    
    print(f"模拟数据形状: {mock_processed_data.shape}")
    
    # 测试转换函数
    pypil_data = convert_to_pypil_format(mock_processed_data)
    
    # 测试LHIPA计算
    print(f"\n🧪 测试LHIPA计算...")
    ipa_calc = IPA(pypil_data)
    lhipa_calc = LHIPA(pypil_data)
    
    print(f"IPA值: {ipa_calc.ipa:.6f}")
    print(f"LHIPA值: {lhipa_calc.lhipa:.6f}")
    
    # 测试滑动窗口计算
    print(f"\n🧪 测试滑动窗口计算...")
    time_points, ipa_series, lhipa_series = calculate_sliding_lhipa(
        mock_processed_data, 
        window_size=1000, 
        step_size=500
    )
    
    print(f"✅ 所有测试完成！")
    print(f"数据格式转换函数已准备就绪，可以处理您的EDF预处理数据。")
