#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
测试实时瞳孔监测程序的数据获取功能
专门用于调试数据获取问题
"""

import os
import sys
import time

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from pfe.real_time_pupil_monitor import (
    RealTimePupilMonitor, 
    find_latest_calibration_file
)

def test_eyelink_connection():
    """测试EyeLink连接和数据获取"""
    print("="*60)
    print("测试EyeLink连接和数据获取")
    print("="*60)
    
    try:
        # 查找标定文件
        calibration_path = find_latest_calibration_file()
        if not calibration_path:
            print("✗ 未找到标定文件")
            return False
        
        print(f"✓ 找到标定文件: {calibration_path}")
        
        # 创建监测器（非虚拟模式）
        print("\n1. 创建实时监测器（非虚拟模式）...")
        monitor = RealTimePupilMonitor(
            calibration_data_path=calibration_path,
            dummy_mode=False  # 使用真实硬件
        )
        
        # 设置显示
        print("2. 设置显示窗口...")
        if not monitor.setup_display():
            print("✗ 显示设置失败")
            return False
        print("✓ 显示设置成功")
        
        # 设置EyeLink
        print("3. 设置EyeLink连接...")
        if not monitor.setup_eyelink():
            print("✗ EyeLink设置失败")
            return False
        print("✓ EyeLink设置成功")
        
        # 设置PFE校正器
        print("4. 设置PFE校正器...")
        if not monitor.setup_pfe_corrector():
            print("✗ PFE校正器设置失败")
            return False
        print("✓ PFE校正器设置成功")
        
        # 测试数据获取（在开始记录前）
        print("\n5. 测试数据获取（记录前）...")
        for i in range(5):
            pupil_data = monitor._get_current_pupil_data()
            if pupil_data:
                print(f"  第{i+1}次: ✓ 获取到数据: {pupil_data}")
            else:
                print(f"  第{i+1}次: ✗ 未获取到数据")
            time.sleep(0.5)
        
        # 开始记录
        print("\n6. 开始EyeLink记录...")
        if monitor.eyelink:
            if monitor.eyelink.start_recording(trial_id=999):
                print("✓ EyeLink记录已启动")
                monitor.eyelink.send_message("DEBUG_TEST_START")
                
                # 测试数据获取（在记录后）
                print("\n7. 测试数据获取（记录后）...")
                for i in range(10):
                    pupil_data = monitor._get_current_pupil_data()
                    if pupil_data:
                        print(f"  第{i+1}次: ✓ 获取到数据: {pupil_data}")
                    else:
                        print(f"  第{i+1}次: ✗ 未获取到数据")
                    time.sleep(0.5)
                
                # 停止记录
                monitor.eyelink.send_message("DEBUG_TEST_END")
                monitor.eyelink.stop_recording()
                print("✓ EyeLink记录已停止")
            else:
                print("✗ EyeLink记录启动失败")
                return False
        
        # 清理资源
        monitor.cleanup()
        print("\n✓ 测试完成")
        return True
        
    except Exception as e:
        print(f"✗ 测试失败: {e}")
        return False

def test_dummy_mode():
    """测试虚拟模式数据获取"""
    print("\n" + "="*60)
    print("测试虚拟模式数据获取")
    print("="*60)
    
    try:
        # 查找标定文件
        calibration_path = find_latest_calibration_file()
        if not calibration_path:
            print("✗ 未找到标定文件")
            return False
        
        # 创建监测器（虚拟模式）
        monitor = RealTimePupilMonitor(
            calibration_data_path=calibration_path,
            dummy_mode=True
        )
        
        # 设置PFE校正器
        if not monitor.setup_pfe_corrector():
            print("✗ PFE校正器设置失败")
            return False
        
        # 测试虚拟数据获取
        print("测试虚拟数据获取...")
        for i in range(5):
            pupil_data = monitor._get_current_pupil_data()
            if pupil_data:
                print(f"  第{i+1}次: ✓ 虚拟数据: {pupil_data}")
            else:
                print(f"  第{i+1}次: ✗ 未获取到虚拟数据")
            time.sleep(0.2)
        
        print("✓ 虚拟模式测试完成")
        return True
        
    except Exception as e:
        print(f"✗ 虚拟模式测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("实时瞳孔监测数据获取调试")
    print("="*60)
    
    # 首先测试虚拟模式
    dummy_result = test_dummy_mode()
    
    # 然后测试真实硬件模式
    real_result = test_eyelink_connection()
    
    # 显示结果
    print("\n" + "="*60)
    print("测试结果汇总")
    print("="*60)
    print(f"虚拟模式测试: {'✓ 通过' if dummy_result else '✗ 失败'}")
    print(f"真实硬件测试: {'✓ 通过' if real_result else '✗ 失败'}")
    
    if real_result:
        print("\n🎉 EyeLink数据获取正常！可以运行实时监测程序。")
        print("\n运行命令:")
        print("  conda activate et; python pfe/real_time_pupil_monitor.py --auto")
    else:
        print("\n⚠️ EyeLink数据获取有问题，请检查:")
        print("  1. EyeLink硬件连接")
        print("  2. EyeLink软件运行状态")
        print("  3. 眼动仪校准是否完成")
        print("  4. 是否需要重新启动EyeLink软件")
        
        if dummy_result:
            print("\n可以使用虚拟模式测试:")
            print("  conda activate et; python pfe/real_time_pupil_monitor.py --auto --dummy")

if __name__ == "__main__":
    main()
