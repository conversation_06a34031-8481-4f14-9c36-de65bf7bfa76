#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
连续文本阅读材料管理模块
从文本文件中提取句子，创建阅读序列
"""

import os
import re
from typing import List, Dict, Optional
from continuous_reading_config import TEXT_CONFIG

class TextMaterials:
    """文本材料管理类"""
    
    def __init__(self, text_file: Optional[str] = None):
        """
        初始化文本材料
        
        Args:
            text_file: 文本文件路径，如果为None则使用配置文件中的路径
        """
        self.text_file = text_file or TEXT_CONFIG['text_file']
        self.sentences = []
        self.raw_text = ""
        self.load_text()
    
    def load_text(self):
        """从文件加载文本"""
        try:
            # 构建完整路径
            if not os.path.isabs(self.text_file):
                # 相对路径，相对于continuous_reading文件夹
                current_dir = os.path.dirname(os.path.abspath(__file__))
                self.text_file = os.path.join(current_dir, self.text_file)
            
            with open(self.text_file, 'r', encoding=TEXT_CONFIG['encoding']) as f:
                self.raw_text = f.read()
            
            # 解析句子
            self._parse_sentences()
            print(f"成功加载文本，共 {len(self.sentences)} 个句子")
            
        except FileNotFoundError:
            print(f"错误：找不到文本文件 {self.text_file}")
            # 创建示例文本
            self._create_example_text()
        except Exception as e:
            print(f"加载文本时出错：{e}")
    
    def _parse_sentences(self):
        """解析文本为句子列表"""
        if not self.raw_text:
            return

        # 使用正则表达式按多种句子分隔符分割
        delimiters = TEXT_CONFIG['sentence_delimiters']
        # 创建正则表达式模式，匹配任何一个分隔符
        delimiter_pattern = '|'.join(re.escape(d) for d in delimiters)

        # 使用正则表达式分割，保留分隔符
        parts = re.split(f'({delimiter_pattern})', self.raw_text)

        self.sentences = []
        sentence_id = 1

        # 重新组合句子和分隔符
        i = 0
        while i < len(parts):
            if i + 1 < len(parts) and parts[i + 1] in delimiters:
                # 当前部分是句子内容，下一部分是分隔符
                raw_sentence = parts[i]
                delimiter = parts[i + 1]
                i += 2
            else:
                # 当前部分是句子内容，没有分隔符（可能是最后一句）
                raw_sentence = parts[i]
                delimiter = ''
                i += 1

            # 清理句子
            sentence = self._clean_sentence(raw_sentence)

            # 检查句子长度
            if (TEXT_CONFIG['min_sentence_length'] <= len(sentence) <=
                TEXT_CONFIG['max_sentence_length']):

                sentence_data = {
                    'id': sentence_id,
                    'text': sentence + delimiter,  # 保留原始标点符号
                    'original_text': raw_sentence + delimiter,
                    'length': len(sentence),
                    'word_count': len(sentence.split()),
                    'delimiter': delimiter  # 记录使用的分隔符
                }

                self.sentences.append(sentence_data)
                sentence_id += 1

        print(f"解析完成，有效句子数：{len(self.sentences)}")
    
    def _clean_sentence(self, sentence: str) -> str:
        """清理句子文本"""
        if TEXT_CONFIG['strip_whitespace']:
            sentence = sentence.strip()

        if TEXT_CONFIG['skip_empty_lines'] and not sentence:
            return ""

        # 移除多余的空白字符
        sentence = re.sub(r'\s+', ' ', sentence)

        # 确保中英文逗号和句号后面都有空格（但不包括句子末尾的标点）
        # 处理中文逗号（不在句子末尾）
        sentence = re.sub(r'，(?!\s)(?!$)', '， ', sentence)
        # 处理中文句号（不在句子末尾）
        sentence = re.sub(r'。(?!\s)(?!$)', '。 ', sentence)
        # 处理英文逗号（不在句子末尾）
        sentence = re.sub(r',(?!\s)(?!$)', ', ', sentence)
        # 处理英文句号（不在句子末尾）
        sentence = re.sub(r'\.(?!\s)(?!$)', '. ', sentence)

        return sentence
    
    def _create_example_text(self):
        """创建示例文本文件"""
        example_text = """这是一个连续文本阅读实验的示例文本。
每个句子都会单独显示给被试阅读。
被试需要对每个句子进行多维度的评分。
包括好奇心、有趣度、理解程度和费劲程度。
实验会记录被试的眼动数据和评分数据。
这些数据将用于分析阅读过程中的认知加工。
文本可以是任何类型的材料。
比如小说、科普文章、新闻报道等。
重要的是文本要有足够的长度。
以便收集到充分的数据。
每个句子的长度应该适中。
太短的句子可能不够有意义。
太长的句子可能增加阅读负担。
实验设计需要平衡多个因素。
包括文本难度、句子长度、评分维度等。
这个示例文本展示了基本的实验流程。
实际实验中可以使用更丰富的材料。"""
        
        try:
            # 保存示例文本
            with open(self.text_file, 'w', encoding=TEXT_CONFIG['encoding']) as f:
                f.write(example_text)
            
            print(f"已创建示例文本文件：{self.text_file}")
            
            # 重新加载
            self.raw_text = example_text
            self._parse_sentences()
            
        except Exception as e:
            print(f"创建示例文本失败：{e}")
    
    def get_sentence(self, sentence_id: int) -> Optional[Dict]:
        """
        获取指定ID的句子
        
        Args:
            sentence_id: 句子ID（从1开始）
            
        Returns:
            句子数据字典，如果不存在则返回None
        """
        for sentence in self.sentences:
            if sentence['id'] == sentence_id:
                return sentence
        return None
    
    def get_sentences_range(self, start_id: int, end_id: Optional[int] = None) -> List[Dict]:
        """
        获取指定范围的句子

        Args:
            start_id: 开始句子ID
            end_id: 结束句子ID，如果为None则到最后

        Returns:
            句子列表
        """
        if end_id is None:
            end_id = len(self.sentences)

        result = []
        for sentence in self.sentences:
            if start_id <= sentence['id'] <= end_id:
                result.append(sentence)

        return result

    def get_multiple_sentences(self, start_id: int, count: int) -> List[Dict]:
        """
        获取从指定ID开始的多个句子

        Args:
            start_id: 开始句子ID
            count: 要获取的句子数量

        Returns:
            句子列表
        """
        end_id = start_id + count - 1
        return self.get_sentences_range(start_id, end_id)

    def get_combined_sentences_text(self, start_id: int, count: int, separator: str = '\n\n') -> Optional[str]:
        """
        获取多个句子的组合文本

        Args:
            start_id: 开始句子ID
            count: 要获取的句子数量
            separator: 句子间的分隔符

        Returns:
            组合后的文本，如果没有找到句子则返回None
        """
        sentences = self.get_multiple_sentences(start_id, count)
        if not sentences:
            return None

        texts = [sentence['text'] for sentence in sentences]
        return separator.join(texts)

    def calculate_sentences_by_character_count(self, start_id: int, target_char_count: int) -> int:
        """
        根据目标字数计算需要显示的句子数量（向上取整）

        Args:
            start_id: 开始句子ID
            target_char_count: 目标字数

        Returns:
            需要显示的句子数量
        """
        if target_char_count <= 0:
            return 1

        current_char_count = 0
        sentence_count = 0

        # 从start_id开始累计字数
        for sentence in self.sentences:
            if sentence['id'] >= start_id:
                current_char_count += sentence['length']
                sentence_count += 1

                # 如果达到或超过目标字数，返回当前句子数量
                if current_char_count >= target_char_count:
                    return sentence_count

        # 如果所有剩余句子的字数都不够，返回剩余的所有句子数量
        return sentence_count if sentence_count > 0 else 1

    def get_sentences_by_character_count(self, start_id: int, target_char_count: int) -> List[Dict]:
        """
        根据目标字数获取句子列表

        Args:
            start_id: 开始句子ID
            target_char_count: 目标字数

        Returns:
            句子列表
        """
        sentence_count = self.calculate_sentences_by_character_count(start_id, target_char_count)
        return self.get_multiple_sentences(start_id, sentence_count)
    
    def get_total_sentences(self) -> int:
        """获取总句子数"""
        return len(self.sentences)
    
    def get_text_statistics(self) -> Dict:
        """获取文本统计信息"""
        if not self.sentences:
            return {}
        
        lengths = [s['length'] for s in self.sentences]
        word_counts = [s['word_count'] for s in self.sentences]
        
        return {
            'total_sentences': len(self.sentences),
            'total_characters': sum(lengths),
            'total_words': sum(word_counts),
            'avg_sentence_length': sum(lengths) / len(lengths),
            'avg_words_per_sentence': sum(word_counts) / len(word_counts),
            'min_sentence_length': min(lengths),
            'max_sentence_length': max(lengths),
            'text_file': self.text_file
        }
    
    def preview_sentences(self, num_sentences: int = 5) -> None:
        """预览前几个句子"""
        print(f"\n文本预览（前{num_sentences}个句子）：")
        print("=" * 60)
        
        for i, sentence in enumerate(self.sentences[:num_sentences]):
            print(f"{sentence['id']:2d}. {sentence['text']}")
            print(f"    长度: {sentence['length']} 字符, {sentence['word_count']} 词")
            print()
    
    def save_sentences_to_file(self, filename: str = "parsed_sentences.txt"):
        """保存解析后的句子到文件"""
        try:
            output_path = os.path.join(os.path.dirname(self.text_file), filename)
            with open(output_path, 'w', encoding='utf-8') as f:
                f.write("解析后的句子列表\n")
                f.write("=" * 40 + "\n\n")
                
                for sentence in self.sentences:
                    f.write(f"句子 {sentence['id']:3d}: {sentence['text']}\n")
                    f.write(f"长度: {sentence['length']} 字符, {sentence['word_count']} 词\n\n")
            
            print(f"句子列表已保存到：{output_path}")
            
        except Exception as e:
            print(f"保存句子列表失败：{e}")

def test_text_materials():
    """测试文本材料加载功能"""
    print("测试文本材料加载...")
    
    # 创建材料管理器
    materials = TextMaterials()
    
    if materials.sentences:
        # 显示统计信息
        stats = materials.get_text_statistics()
        print(f"\n文本统计信息：")
        print(f"总句子数: {stats['total_sentences']}")
        print(f"总字符数: {stats['total_characters']}")
        print(f"总词数: {stats['total_words']}")
        print(f"平均句子长度: {stats['avg_sentence_length']:.1f} 字符")
        print(f"平均每句词数: {stats['avg_words_per_sentence']:.1f} 词")
        print(f"最短句子: {stats['min_sentence_length']} 字符")
        print(f"最长句子: {stats['max_sentence_length']} 字符")
        
        # 预览句子
        materials.preview_sentences(3)
        
        # 测试获取特定句子
        print("\n测试获取特定句子：")
        sentence = materials.get_sentence(1)
        if sentence:
            print(f"第1个句子: {sentence['text']}")
        
        # 测试获取句子范围
        print("\n测试获取句子范围（1-3）：")
        sentences_range = materials.get_sentences_range(1, 3)
        for sentence in sentences_range:
            print(f"  {sentence['id']}. {sentence['text'][:50]}...")

        # 测试按字数控制功能
        print("\n测试按字数控制功能：")
        target_chars = [30, 50, 100]
        for target in target_chars:
            sentence_count = materials.calculate_sentences_by_character_count(1, target)
            sentences = materials.get_sentences_by_character_count(1, target)
            actual_chars = sum(s['length'] for s in sentences)
            print(f"  目标字数: {target}, 需要句子数: {sentence_count}, 实际字数: {actual_chars}")
            for s in sentences:
                print(f"    句子{s['id']}: {s['text'][:30]}... (长度: {s['length']})")

        # 保存句子列表
        materials.save_sentences_to_file()
        
    else:
        print("未能加载任何句子")

if __name__ == "__main__":
    test_text_materials()
