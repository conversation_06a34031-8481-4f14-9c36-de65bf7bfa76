#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
基于pyedfread的瞳孔数据分析器 - 直接从EDF文件读取瞳孔数据
"""

import os
import pandas as pd
import numpy as np
import logging
from typing import Dict, List, Tuple, Optional
from analysis.history_code.config import OUTPUT_DIR, PUPIL_CONFIG, DATA_QUALITY_CONFIG

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(os.path.join(OUTPUT_DIR, 'pyedfread_analysis.log')),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger('pyedfread_analyzer')

class PyEDFReadAnalyzer:
    """基于pyedfread的瞳孔数据分析器"""
    
    def __init__(self):
        self.samples = None
        self.events = None
        self.messages = None
        self.trial_pupil_data = {}
        
    def check_pyedfread(self) -> bool:
        """检查pyedfread是否可用"""
        try:
            import pyedfread
            logger.info("pyedfread 可用")
            return True
        except ImportError:
            logger.error("❌ pyedfread 未安装")
            logger.error("请安装: pip install git+https://github.com/s-ccs/pyedfread")
            return False
    
    def load_edf_data(self, edf_path: str) -> bool:
        """
        使用pyedfread加载EDF文件
        
        Args:
            edf_path: EDF文件路径
            
        Returns:
            bool: 是否成功加载
        """
        if not self.check_pyedfread():
            return False
            
        try:
            import pyedfread
            logger.info(f"正在读取EDF文件: {edf_path}")
            
            # 读取EDF文件
            self.samples, self.events, self.messages = pyedfread.read_edf(edf_path)
            
            logger.info(f"成功读取EDF文件")
            logger.info(f"  - 样本数: {len(self.samples)}")
            logger.info(f"  - 事件数: {len(self.events)}")
            logger.info(f"  - 消息数: {len(self.messages)}")
            
            # 打印样本数据的列名，帮助调试
            logger.info(f"  - 样本数据列名: {list(self.samples.columns)}")
            
            # 检查瞳孔数据
            if 'pa_left' in self.samples.columns:
                logger.info(f"  - 左眼瞳孔数据统计:")
                logger.info(f"    最小值: {self.samples['pa_left'].min()}")
                logger.info(f"    最大值: {self.samples['pa_left'].max()}")
                logger.info(f"    非零值数量: {(self.samples['pa_left'] > 0).sum()}")
            
            if 'pa_right' in self.samples.columns:
                logger.info(f"  - 右眼瞳孔数据统计:")
                logger.info(f"    最小值: {self.samples['pa_right'].min()}")
                logger.info(f"    最大值: {self.samples['pa_right'].max()}")
                logger.info(f"    非零值数量: {(self.samples['pa_right'] > 0).sum()}")
            
            return True
            
        except Exception as e:
            logger.error(f"❌ 读取EDF文件失败: {e}")
            return False
    
    def parse_messages(self) -> Dict:
        """解析消息数据，提取试次和事件信息"""
        if self.messages is None or self.messages.empty:
            logger.warning("没有消息数据可解析")
            return {}
            
        parsed_messages = {}
        
        for _, row in self.messages.iterrows():
            timestamp = row['time']
            message = row['message']
            
            # 解析消息内容
            if 'TRIAL_START' in message:
                # 提取试次号
                parts = message.split()
                if len(parts) >= 2:
                    try:
                        trial_num = int(parts[-1])
                        if trial_num not in parsed_messages:
                            parsed_messages[trial_num] = {}
                        parsed_messages[trial_num]['trial_start'] = timestamp
                    except ValueError:
                        continue
                        
            elif 'QUESTION_DISPLAY_START' in message:
                current_trial = self._find_current_trial(parsed_messages, timestamp)
                if current_trial:
                    parsed_messages[current_trial]['QUESTION_DISPLAY_START'] = timestamp
                    
            elif 'QUESTION_DISPLAY_END' in message:
                current_trial = self._find_current_trial(parsed_messages, timestamp)
                if current_trial:
                    parsed_messages[current_trial]['QUESTION_DISPLAY_END'] = timestamp
                    
            elif 'ANSWER_DISPLAY_START' in message:
                current_trial = self._find_current_trial(parsed_messages, timestamp)
                if current_trial:
                    parsed_messages[current_trial]['ANSWER_DISPLAY_START'] = timestamp
                    
            elif 'ANSWER_DISPLAY_END' in message:
                current_trial = self._find_current_trial(parsed_messages, timestamp)
                if current_trial:
                    parsed_messages[current_trial]['ANSWER_DISPLAY_END'] = timestamp
        
        logger.info(f"解析完成，找到 {len(parsed_messages)} 个试次")
        return parsed_messages
    
    def _find_current_trial(self, parsed_messages: Dict, timestamp: int) -> Optional[int]:
        """根据时间戳找到当前试次"""
        for trial_num, trial_data in parsed_messages.items():
            if 'trial_start' in trial_data and trial_data['trial_start'] <= timestamp:
                # 检查是否有下一个试次，如果有，确保时间戳在当前试次范围内
                next_trial_start = None
                for other_trial, other_data in parsed_messages.items():
                    if (other_trial > trial_num and 'trial_start' in other_data and 
                        (next_trial_start is None or other_data['trial_start'] < next_trial_start)):
                        next_trial_start = other_data['trial_start']
                
                if next_trial_start is None or timestamp < next_trial_start:
                    return trial_num
        return None
    
    def extract_pupil_data(self, start_time: int, end_time: int) -> pd.DataFrame:
        """提取指定时间段的瞳孔数据"""
        if self.samples is None or self.samples.empty:
            logger.warning("没有样本数据可提取")
            return pd.DataFrame()
        
        # 过滤时间范围
        segment_data = self.samples[
            (self.samples['time'] >= start_time) & 
            (self.samples['time'] <= end_time)
        ].copy()
        
        if len(segment_data) == 0:
            logger.warning(f"时间段 {start_time}-{end_time} 没有数据")
            return pd.DataFrame()
        
        # 提取瞳孔直径数据（左眼和右眼）
        pupil_data = pd.DataFrame({
            'time': segment_data['time'],
            'left_pupil': segment_data.get('pa_left', np.nan),
            'right_pupil': segment_data.get('pa_right', np.nan)
        })
        
        # 数据清理：过滤异常值
        min_pupil_size = PUPIL_CONFIG.get('min_pupil_size', 100.0)
        max_pupil_size = PUPIL_CONFIG.get('max_pupil_size', 8000.0)
        
        left_before = (~pupil_data['left_pupil'].isna()).sum()
        right_before = (~pupil_data['right_pupil'].isna()).sum()
        
        pupil_data.loc[pupil_data['left_pupil'] < min_pupil_size, 'left_pupil'] = np.nan
        pupil_data.loc[pupil_data['left_pupil'] > max_pupil_size, 'left_pupil'] = np.nan
        pupil_data.loc[pupil_data['right_pupil'] < min_pupil_size, 'right_pupil'] = np.nan
        pupil_data.loc[pupil_data['right_pupil'] > max_pupil_size, 'right_pupil'] = np.nan
        
        left_after = (~pupil_data['left_pupil'].isna()).sum()
        right_after = (~pupil_data['right_pupil'].isna()).sum()
        
        logger.info(f"  数据过滤结果:")
        logger.info(f"    左眼: {left_before} -> {left_after} (过滤了 {left_before - left_after} 个异常值)")
        logger.info(f"    右眼: {right_before} -> {right_after} (过滤了 {right_before - right_after} 个异常值)")
        
        # 计算双眼平均
        if PUPIL_CONFIG['eyes_to_use'] == 'both':
            pupil_data['pupil_size'] = pupil_data[['left_pupil', 'right_pupil']].mean(axis=1)
        elif PUPIL_CONFIG['eyes_to_use'] == 'left':
            pupil_data['pupil_size'] = pupil_data['left_pupil']
        elif PUPIL_CONFIG['eyes_to_use'] == 'right':
            pupil_data['pupil_size'] = pupil_data['right_pupil']
        
        # 处理缺失数据
        if PUPIL_CONFIG['interpolate_missing']:
            pupil_data['pupil_size'] = pupil_data['pupil_size'].interpolate(method='linear')
            logger.info("使用线性插值处理缺失数据")
        
        return pupil_data
    
    def analyze_trial_pupil_data(self, edf_file: str, trial_data: List[Dict]) -> Dict:
        """分析试次瞳孔数据"""
        logger.info(f"分析瞳孔数据: {edf_file}")
        
        # 加载EDF文件
        if not self.load_edf_data(edf_file):
            return {}
        
        # 解析消息
        parsed_messages = self.parse_messages()
        
        trial_pupil_results = {}
        
        for trial in trial_data:
            trial_num = trial['trial_num']
            
            # 查找试次事件
            if trial_num not in parsed_messages:
                logger.warning(f"试次{trial_num}未找到消息数据")
                continue
            
            trial_messages = parsed_messages[trial_num]
            
            # 查找问题显示时间段
            if 'QUESTION_DISPLAY_START' in trial_messages and 'QUESTION_DISPLAY_END' in trial_messages:
                start_time = trial_messages['QUESTION_DISPLAY_START']
                end_time = trial_messages['QUESTION_DISPLAY_END']
                
                # 提取瞳孔数据
                pupil_data = self.extract_pupil_data(start_time, end_time)
                
                if not pupil_data.empty:
                    # 相对时间（从开始时间算起）
                    pupil_data['relative_time'] = pupil_data['time'] - start_time
                    
                    # 保存结果
                    trial_pupil_results[trial_num] = {
                        'trial_info': trial,
                        'pupil_data': pupil_data,
                        'start_time': start_time,
                        'end_time': end_time,
                        'duration': end_time - start_time,
                        'segment': 'question_display'
                    }
                    
                    logger.info(f"试次{trial_num}问题显示段: {len(pupil_data)}个数据点")
            
            # 查找答案显示时间段
            if 'ANSWER_DISPLAY_START' in trial_messages and 'ANSWER_DISPLAY_END' in trial_messages:
                start_time = trial_messages['ANSWER_DISPLAY_START']
                end_time = trial_messages['ANSWER_DISPLAY_END']
                
                # 提取瞳孔数据
                pupil_data = self.extract_pupil_data(start_time, end_time)
                
                if not pupil_data.empty:
                    # 相对时间（从开始时间算起）
                    pupil_data['relative_time'] = pupil_data['time'] - start_time
                    
                    # 保存结果（使用不同的键来区分问题和答案段）
                    trial_key = f"{trial_num}_answer"
                    trial_pupil_results[trial_key] = {
                        'trial_info': trial,
                        'pupil_data': pupil_data,
                        'start_time': start_time,
                        'end_time': end_time,
                        'duration': end_time - start_time,
                        'segment': 'answer_display'
                    }
                    
                    logger.info(f"试次{trial_num}答案显示段: {len(pupil_data)}个数据点")
        
        logger.info(f"成功分析了{len(trial_pupil_results)}个数据段的瞳孔数据")
        return trial_pupil_results

if __name__ == "__main__":
    analyzer = PyEDFReadAnalyzer()
    # 测试代码可以在这里添加
