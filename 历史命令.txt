顺着main_experiement运行逻辑找下去，如果我开始运行，用的是几号摄像头，列出沿途所有相关代码
找到我选择选项的代码，修改逻辑为，按下一个键，然后变黄0.5s,然后切到下一个界面。现在是按完键就直接切了。代码尽量简短的改
在主程序录像时添加时间戳显示，白色，精度到ms，加在右下角
在continuous reading experiment Python文件中。他在实验开始和结束的时候会分别保存一个目录，把他们两个合并到开始保存的那个目录中，并且对于所有运行continuous reading experiment的文件，在保存的数据目录中，添加continuous reading的字样。
main_analysis中加一个函数，他可以自定义把某些评分聚合成一个，例如，我想把评分[1,2]的画成1，【3】就是3，【4,5】一起画成5.
在第七步增加阈值功能：只有30度(可设置)以内的角度才补偿，否则就按照离当前前后凝视位置最近的，并且满足阈值内的注视位置补偿
创建PFE校正算法模块 pfe/pfe_correction.py
测试PFE校正功能: conda activate et; python pfe/pfe_correction.py
测试PFE集成功能: conda activate et; python test_pfe_correction.py
运行PFE使用示例: conda activate et; python pfe/example_usage.py
修正PFE校正公式：从1/√(cos θ)改为1/cos θ，适用于面积数据
清理PFE接口：移除不需要的pfe_calibration_data_path和pfe_correction_method参数
conda activate et; python test_pupil_columns.py
在preprocess_edf.py中添加了eye_selection参数来选择使用哪种眼睛数据
在速度过滤后加入可选的一步用来保证有效区间长度（0代表关闭，有值代表区间长度）。检查在任意两个nan值区间的长度是否大于阈值，先设为50，如果小于就把这段都设为nan，否则保留
python debug_preprocess_edf.py
python debug_smoothing_issue.py
python debug_detailed_smoothing.py
python test_fixed_smoothing.py
在main_analysis中加一个参数，最后统计在指定时间段：例如在QUESTION_DISPLAY_END之前的x毫秒区间，每一个不同的好奇心或者有趣程度的瞳孔均值。（可以选择开启这个功能或否）。当已经画完所有数据的均值图等以后，直接用上面的数据从中取出特定的部分，我一定会选择之前画图的子序列。然后计算在这段时间内，这个类型不同程度的瞳孔大小的均值。例如好奇心有5个等级，在QUESTION_DISPLAY_END之前3s范围内，计算每一个等级这段时间的瞳孔均值，最后会有5个数字以及5个标准差。你用直方图画出均值，bar画出标准差
对于基线计算，修改功能，指定基线的开始时间，例如：QUESTION_DISPLAY_START前2秒，再指定持续时间，例如200ms，然后用这段时间的均值作为基线
修复main_analysis.py中事件解析bug：将endswith('_START', '_END')改为in操作，解决BASELINE_START PUPIL等事件无法被识别的问题
完整阅读preprocess edf代码，添加功能：画图可以选择指定y轴的画图范围在3500~5500
preprocess_edf用二次曲线拟合的时候加一个条件：如果需要被插值的数据在二次曲线拟合时超过最小最大瞳孔尺寸，改用线性插值
在preprocess_edf.py中的eyedatapreprocessor类的preprocess_edf中增加一个功能，如果要求处理的edf文件目录同目录已经有edf文件同名的data frame文件并且之前的处理的参数和现在的相同，就直接把它返回。否则计算完后保存edf同名的data frame格式内容，也保存init的处理的参数。
对于load_edf_data也添加同样的缓存机制。这个不需要存参数，只需要3个返回的dataframe存上就行
把以上两种缓存的内容以及Json文件都保存到叫data_cache的子目录中，注意修改所有保存和调用的路径确保兼容。
在continuous reading experiment中加一个参数，用总字数控制模式，控制屏幕上一共显示的字数（例如50字），从而自动计算应该显示的句子数量。显示的句子数量向上取整，保证总字数总是>=当前字数
在解析句子的时候，对于解析后的每一个句子，确保他的逗号和句号（中英文逗号句号）后面都有一个空格，如果没有你就手动加入
在main_analysis.py中修改事件位置查找逻辑，支持逗号分割的多个关键词匹配，只要消息中包含所有关键词就算匹配
修改连续阅读实验，将每一轮句子显示和评分过程改为独立的trial，在每个trial开始时start_recording，结束时stop_recording
把试次trial开始和结束也改成支持多字字符串匹配，例如"sentence_GROUP"，"STAR能在消息MSG	9516140 EVENT_179 sentence_GROUP_116_117_118_START中匹配到事件的开始
在最后展示每个rating的图标曲线以及某一时间段内不同rating的瞳孔均值评分中，都在图例上写出样本量n=多少
参考这个论文@`c:\Users\<USER>\Desktop\curiosity_pupil/article\s13428-015-0588-x (1).txt`，完整阅读后,单独写一个python程序，里面有两部分内容。第一部分是更精细的pfe标定数据，我已经粗略测量出相对相机的坐标CXyz=[40,-85,560],相对屏幕左上角坐标sxyz=[-200,155,710].我需要你模仿论文中的网格精细标定，依次出现不同的圆点（8*6的密度），我会注视它4s，然后你标定我看它的位置时，它的后3s的瞳孔平均值大小。第二部分是瞳孔大小矫正，我现在用的是area模式的瞳孔大小，你到时候会获得我的左眼的注视点，在读取后的data frame格式中，有'px_left'和'py_left'两列，你读取后按照论文中的公式把左眼瞳孔直径校准'pa_left'（功能可以选择是否开启）。在preprocessedf@`c:\Users\<USER>\Desktop\curiosity_pupil/analysis\preprocess_edf.py`第6步之后插入pfe矫正。你先完整阅读论文然后完成第一部分。注意看我eyeylink设置中@`c:\Users\<USER>\Desktop\curiosity_pupil/eyelink_manager.py`我的屏幕分辨率和屏幕使用比例等参数. 不需要改动第二部分包括preprocess edf的代码，先写第一部分.所有代码在pfe文件夹中操作。列一个todo list做，先阅读论文，用thinking模式推导矫正的核心公式，以及优化算法，然后再写代码
conda activate et; python pfe/test_pfe_calibration.py
conda activate et; python pfe/pfe_correction.py
conda activate et; python pfe/test_pfe_integration.py
conda activate et; python .\pfe\pfe_calibration.py
python debug_edf_structure.py --edf "C:\Users\<USER>\Desktop\curiosity_pupil\data\pfe_calibration\test_pfe_pfe.edf"
python .\pfe\pfe_data_analyzer.py --edf "C:\Users\<USER>\Desktop\curiosity_pupil\data\pfe_calibration\test_pfe_pfe.edf" --calibration "C:\Users\<USER>\Desktop\curiosity_pupil\data\pfe_calibration\test_pfe_pfe_calibration_20250731_165549.csv"
python show_edf_dataframe.py --edf "C:\Users\<USER>\Desktop\curiosity_pupil\data\pfe_calibration\test_pfe_pfe.edf"
mkdir pfe\calibration_data
move data\pfe_calibration\* pfe\calibration_data\
rmdir data\pfe_calibration
python .\pfe\pfe_data_analyzer.py --dir "pfe\calibration_data"
python .\pfe\pfe_calibration.py --participant test_new_location --dummy
del pfe\calibration_data\test_new_location_pfe.edf
del pfe\calibration_data\test_new_location_pfe_calibration_20250731_174323.csv
python .\pfe\pfe_data_analyzer.py --dir "pfe\calibration_data"
python test_pfe_integration.py
修改代码仓库中所有白色文字颜色为深灰色#202020，保留有特殊功能的彩色文字
检查这个代码@`c:\Users\<USER>\Desktop\curiosity_pupil/pfe_correction.py`的逻辑有没有用到论文中的Nelder–Mead优化方法优化。先阅读TXT论文@`c:\Users\<USER>\Desktop\curiosity_pupil/pfe\article\s13428-015-0588-x (1).txt`，然后总结出来一个大致的逻辑流程如何用如何获取6乘8的网格的标准标定数据以后，我用这个优化方法优化一个优化系数，然后再查看代码对应是否完成对应项，如果没有的话改进。
基于pfe_correction的代码，创建一个实时监测优化瞳孔的程序。读入之前采集的标定文件，然后在屏幕中央显示标定点作为瞳孔参照。之后随机显示不同位置的点，以及以不同高度从左往右慢速移动光点, 实时在屏幕上打出来校正后的瞳孔和中央点的瞳孔大小数值显示在中间，并且显示最大最小值的差值范围。
如果要是开启瞳孔的PFe校正以后直接把矫正完的数值存到原来的PA left和PA right的位置上。
把pfe_correction的主函数改成输入之前获取的标定文件，然后进行优化，优化完后在单独的优化目录输出优化后的对应的参数
把这个文件中以及这个代码仓库中所有。读取px_left,px_right,py_left,py_right都换成gx_left,gy_left,gx_right,gy_right。因为P是没有校正的数据，而我想要的是校正后的数据。他是G
在画不同rating随时间变化的曲线图中，同时标出每个rating的最值的差，以及每个rating不同时间点相对于均值的标准差
在pfe文件夹中，新增一个文件，基于continuous_reading的主程序，包括如何连接eyelink，标定，记录等。但是在屏幕上显示的内容变成一个原点，沿着屏幕逆时针运动，运动半径和速度可调，config直接在这个python最顶上设置，运动圈数可设置（3）。运动完了结束记录就行。
再加一个新模式，原点会蛇形从屏幕左上角开始横着走，连续的一直到屏幕最底下，速度和走多少行都可以控制，以及走多大的屏幕比例也可以控制
conda activate et; pip install PyWavelets numpy pandas matplotlib scipy
conda activate et; python test_pypil_basic.py
conda activate et; python lhipa_calculator.py
conda activate et; python test_real_edf_lhipa.py
在@`c:\Users\<USER>\Desktop\curiosity_pupil/analysis\main_analysis.py`中添加k可选lhipa分析功能，具体参数模仿   # 指定时间段瞳孔均值统计配置
    'time_window_analysis': {
        'enabled': False,                    # 是否启用指定时间段分析
        'reference_event': 'sentence,end',  # 参考事件
        'time_before_event_ms': 5000,        # 参考事件前多少毫秒开始统计
        'window_duration_ms': 5000,          # 统计窗口持续时间（毫秒）
        'save_plots': True,                  # 是否保存图表
        'show_plots': True,                  # 是否显示图表
    },。你先阅读@`c:\Users\<USER>\Desktop\curiosity_pupil/cognitive_load\LHIPA_使用说明.md`和@`c:\Users\<USER>\Desktop\curiosity_pupil/cognitive_load\lhipa_calculator.py`，然后在plot_analysis_results(final_df, rating_var, config)之后添加lhipa分析，可以用这些参数final_df, rating_var, config把不同分数的相同类别的rating分别计算指定时间的lhipa参数，然后计算均值和标准差，最后画出直方图，指示每一个rating的均值和标准差。写完后写测试代码测试，不通过接着修改，直到通过为止
修改snake模式在屏幕拐弯处不要突然变化到下一行，要连续的匀速竖着运动到下一行
创建reading模式，运动轨迹是只有横着的移动，从左到右，并且会突然从最右回到最左
蛇形模式和reading模式都增加参数循环几遍
在continuous_reading_experiment.py中增加一个listening模式。这个模式下文本中的所有内容不通过文字展示，而是调用edge-tts播放，6.py展示了如何朗读一段话并且返回是否已经朗读完的标志，注意在每次朗读的话前加入"11."因为这样防止声音被吞掉"mpv 支持用 - 从 stdin 读数据，但它需要在流的起始几 KB 里完成格式探测；对 MP3 这类帧流式格式，解码器在"找帧同步/建立缓冲"时经常会跳过最前面的帧。用管道分块发送时，越容易触发这种"开头被吃掉"的听感"。阅读期间在屏幕中间显示白点就行，阅读后的打分环节不变，阅读完自动切换到打分环节。新的设置加在continuous_reading_config.py
基于aeon库的hivecote2算法进行眼动和瞳孔数据的分类研究，分类阅读文字时的有趣程度
conda activate et; python regression/test_aeon_working.py
conda activate et; python regression/analyze_eyetracking_data.py
conda activate et; python regression/test_converter.py
conda activate et; python regression/simple_test.py
1.这些点的显示顺序应该是随机的，并且有一个参数设置所有点被显示的总次数，例如两次，就是每个点会被随机顺序 但是刚好显示两次 2加入参数：在程序统计并且校准完后，显示一个图，上面有对应的点，每个点标出这么多次平均的瞳孔大小，圆点的半径和瞳孔大小成正比（可设置）
1.在正式检测点数开始前加入10个（可设置）随机点数显示，用来让被试习惯实验过程，然后是正式点数，中间不要有间隔，只是在开头多几个点。2.在结束记录eyeylink以后正常关闭eyeylink接受完文件再开始分析（可选是否分析）3.同样分析每个点几次均值的最值的差并且标在点上
